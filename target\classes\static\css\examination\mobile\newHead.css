.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
  }
  .transforms {
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
  }
  textarea::-webkit-input-placeholder {
    font-size: 12px;
    color: #bcbcc5;
  }
  textarea:-moz-placeholder {
    font-size: 12px;
    color: #bcbcc5;
  }
  input[type=search]::-webkit-search-cancel-button {
    -webkit-appearance: none;
  }
  textarea:-ms-input-placeholder {
    font-size: 12px;
    color: #bcbcc5;
  }
  .clearfixs {
    zoom: 1;
  }
  .clearfixs:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  .textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .rightHead{
    float: left;
    width: 40px;
    height: 100%;
    background: url(../images/) center center/24px 24px no-repeat;
  }