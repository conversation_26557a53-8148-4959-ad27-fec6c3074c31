/* CSS Document */
body, form, body,form,textarea,select,option,ol,ul,li,h1,h2,h3,h4,h5,h6,p,th,td,dl,dt,dd,menu,blockquote,fieldset,label,i,em,header,footer,section,
legend,button,input,hr,pre,div,input,span,p,a{margin: 0;padding: 0;-webkit-tap-highlight-color:rgba(0,0,0,0);}
h1,h2,h3,h4,h5,h6{ font-weight:normal; font-size:100%;}
html{font-size: 62.5%;}
body, input, pre, select, textarea {
  font-size: 12px;
  line-height: 1.5;
  font-family: "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", 微软雅黑, Arial, sans-serif;
  color:#333;
}
li{list-style-type:none;}
input,select,textarea{vertical-align:middle;  color:#333; outline:none;-webkit-tap-highlight-color:rgba(0,0,0,0);}
a{text-decoration:none;color:#333;}
img{border:0;}
i,em{font-style:normal;}
b{ font-weight:normal;}
table{border-spacing:0; border-collapse:collapse;width:100%; border:0;}
.clearfix{overflow:hidden;}
.clear{clear:both; height:0; font-size:0; line-height:0; overflow:hidden;}
ul, ol, img {  border: 0px;  }
li {  list-style-type: none;  }
input,
select,
textarea {  vertical-align: middle;  outline: none;  }
textarea {  resize: none;  }
a {  text-decoration: none;  -webkit-tap-highlight-color: transparent;  -webkit-appearance: none;  }
input[type="button"]:hover {  cursor: pointer;  }
div:focus {  outline: none;  }
.clear {  clear: both;  }
.clearfix:after {  content: "";  display: block;  clear: both;  }
.fl{
  float: left;
}
.fr{
  float: right;
}
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 6px;
}
::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 6px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
  background: #b9b8b8;
}
::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  background: #EDEDED;
}
.systemMark {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 68px;
  left: 0;
  right: 0;
  z-index: 999;
  background: rgba(255, 255, 255, 0);
}

