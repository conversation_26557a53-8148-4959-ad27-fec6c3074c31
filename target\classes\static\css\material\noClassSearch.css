body {
  background-color: #F7F8FA;
  padding: 20px;
  font-size: 14px;
  color: #4E5969;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main {
  min-height: calc(100vh - 40px);
  background: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
}
.main .top {
  width: 100%;
  height: 60px;
  border-bottom: 1px solid #E8EBF1;
  position: relative;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  align-items: center;
}
.main .top .title {
  padding-left: 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 36px;
}
.main .top .title .back {
  padding-left: 22px;
  background: url(../../images/material/back-icon.png) no-repeat left center;
  background-size: 16px;
  color: #7D92B3;
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
}
.main .top .title .levelone {
  padding-left: 9px;
  position: relative;
  color: #6581BA;
  font-size: 16px;
  margin-right: 6px;
}
.main .top .title .levelone:after {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
}
.main .top .title .icon {
  width: 12px;
  height: 12px;
  background: url(../../images/material/arrow-right.png) no-repeat center;
  background-size: 12px;
  margin-right: 6px;
}
.main .top .title .leveltwo {
  color: #1D2129;
  font-weight: 700;
  font-size: 16px;
}
.main .top .btn {
  position: absolute;
  top: 17px;
  right: 28px;
  width: 116px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 14px;
  color: #FFFFFF;
  background: #4D88FF;
  box-shadow: 0px 0px 10px #4D88FF;
  border-radius: 4px;
}
.main .top h4 {
  position: relative;
  color: #6581BA;
  font-size: 16px;
  margin-left: 30px;
  padding-left: 9px;
}
.main .top h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}
.main .form-con {
  display: flex;
  font-size: 14px;
  padding: 20px 30px 20px;
  position: relative;
}
.main .form-con .sel-box {
  flex: 1;
}
.main .form-con .sel-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.main .form-con .sel-row .sel-item {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 32px;
}
.main .form-con .sel-row .sel-item .sel-title {
  color: #1D2129;
  font-size: 14px;
  width: 84px;
}
.main .form-con .sel-row .sel-item .sel-title span {
  display: inline-block;
  width: 14px;
  color: #F76560;
}
.sel {
  float: left;
  margin-bottom: 0;
}
.sel .select-input {
  margin-left: 0;
  margin-right: 0;
}
.sel .select-input .select-dropdown {
  position: absolute;
  left: -1px;
  top: 33px;
}
.sel .select-input .name {
  text-align: left;
}
.sel {
  flex: 1;
  width: 0;
  margin-right: 40px;
  height: 34px;
  line-height: 34px;
}
.sel em {
  float: left;
  font-size: 14px;
  color: #474C59;
}
.sel .select-input {
  height: 34px;
  border-radius: 4px;
  border: 1px solid #D4D6D9;
  background-color: #fff;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
}
.sel .select-input i {
  position: absolute;
  top: 11px;
  right: 11px;
  width: 12px;
  height: 12px;
  background: url(../../images/material/arrow-icon.png) no-repeat center;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.sel .select-input .name {
  font-size: 14px;
  color: #ACB4BF;
  padding-left: 13px;
  line-height: 32px;
  width: 86%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.sel .select-input .name.ckd {
  color: #131B26;
}
.sel .select-input.clicked i {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.sel .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
}
.sel .select-input .select-dropdown {
  width: 100%;
  left: -1px;
  margin: 5px 0;
  padding: 6px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: absolute;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
.sel .select-input .select-dropdown .search {
  margin: 8px;
  height: 36px;
  box-sizing: border-box;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.sel .select-input .select-dropdown .search input {
  border: none;
  flex: 1;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  font-size: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  float: left;
}
.sel .select-input .select-dropdown .search input::placeholder {
  color: #8F97A8;
}
.sel .select-input .select-dropdown .search span {
  cursor: pointer;
  float: left;
  width: 16px;
  height: 16px;
  background: url(../../images/material/search-icon.png) no-repeat center;
  margin: 9px;
}
.sel .select-input .select-dropdown .all-selects {
  color: #4E5969;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  margin: 0 20px;
  padding-left: 24px;
  background: url(../../images/material/check-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .all-selects.cur {
  background: url(../../images/material/checked-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .dropdown-lists {
  padding: 0 0 6px;
  max-height: 240px;
  overflow: auto;
}
.sel .select-input .select-dropdown .dropdown-lists li {
  margin: 0;
  line-height: normal;
  line-height: 40px;
  padding: 0 20px;
  clear: both;
  color: #4E5969;
  font-size: 14px !important;
  list-style: none;
  cursor: pointer;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.sel .select-input .select-dropdown .dropdown-lists li span {
  display: block;
  padding-left: 27px;
  background: url(../../images/material/check-icon.png) no-repeat left center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}
.sel .select-input .select-dropdown .dropdown-lists li:hover {
  background: #F5F7FA;
}
.sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #E1EBFF;
}
.sel .select-input .select-dropdown .dropdown-lists li.cur span {
  background: url(../../images/material/checked-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .dropdown-list li {
  margin: 0;
  line-height: normal;
  padding: 0 16px;
  clear: both;
  color: #4E5969;
  font-size: 14px !important;
  white-space: nowrap;
  list-style: none;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
.sel .select-input .select-dropdown .dropdown-list li:hover {
  background: #E1EBFF;
}
.sel.times .sel-time {
  width: 104px;
  height: 34px;
}
.sel.times .sel-time span {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 18px;
  height: 18px;
  background: url(../../images/material/time-icon.png) no-repeat center;
}
.table {
  overflow: hidden;
  margin: 0 30px 30px 30px;
}
.table table {
  overflow: hidden;
}
.table table tr {
  height: 80px;
}
.table table tr td {
  text-align: center;
}
.table table tr td p:first-child {
  margin-bottom: 6px;
}
.table table thead tr {
  background: #F1F3F6;
}
.table table thead td {
  border: 1px solid #E8EBF1;
  color: #86909C;
  font-size: 14px;
}
.table table tbody td {
  font-size: 14px;
  border: 1px solid #E8EBF1;
  color: #4E5969;
}
.table table tbody td span {
  padding-right: 16px;
}
.table table tbody td a {
  color: #4080FF;
  cursor: pointer;
}
.table table tbody td.idle {
  background: #E1EBFF;
}
.tableDetail {
  margin: 30px;
  margin-top: 0;
}
.tableDetail h1 {
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 76px;
  text-align: center;
  color: #6581BA;
}
.tableDetail .cons {
  overflow: hidden;
}
.tableDetail .cons .l-table {
  width: 71.5%;
  float: left;
}
.tableDetail .cons .r-table {
  width: 26.5%;
  height: auto;
  float: right;
}
.tableDetail .cons .r-table .grades-inform {
  margin-top: 16px;
  border: 1px solid #E8EBF1;
}
.tableDetail .cons .r-table .grades-inform ul li {
  width: 100%;
  height: 36px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  border-bottom: 1px solid #E8EBF1;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.tableDetail .cons .r-table .grades-inform ul li:last-child {
  border-bottom: none;
}
.tableDetail .cons .r-table .grades-inform ul li .name {
  flex: 1;
  background: #F1F3F6;
  font-size: 14px;
  color: #4E5969;
  height: 100%;
  line-height: 35px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0 16px;
}
.tableDetail .cons .r-table .grades-inform ul li .number {
  padding: 0 16px;
  flex: 1;
  font-size: 14px;
  text-align: center;
  color: #4E5969;
}
.tableDetail .inform-bottom {
  margin-top: 34px;
  overflow: hidden;
}
.tableDetail .inform-bottom .notes {
  width: 71.5%;
  float: left;
  border-radius: 4px;
  border: 1px dashed #6581BA;
  background: rgba(225, 235, 255, 0.5);
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 14px;
}
.tableDetail .inform-bottom .notes p {
  color: #6581BA;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 21px;
}
.tableDetail .inform-bottom .signature {
  width: 26.5%;
  border-radius: 4px;
  height: auto;
  float: right;
  border: 1px dashed #6581BA;
  background: rgba(225, 235, 255, 0.5);
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 14px;
  min-height: 135px;
}
.tableDetail .inform-bottom .signature p {
  color: #6581BA;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 21px;
}
.tableDetail table tr {
  height: 36px;
}
.tableDetail table tr td {
  border: 1px solid #E8EBF1;
  font-size: 14px;
  text-align: center;
}
.tableDetail table thead {
  background: #F1F3F6;
}
.tableDetail table thead tr td {
  color: #86909C;
}
.tableDetail table tbody tr {
  background-color: #fff;
}
.tableDetail table tbody tr:nth-child(2n) {
  background-color: #FAFBFC;
}
.tableDetail table tbody tr td {
  color: #4E5969;
}
#coursePage {
  text-align: center;
  margin-bottom: 30px;
}
.layui-laypage a,
.layui-laypage span,
.layui-laypage input,
.layui-laypage button,
.layui-laypage select {
  border: none;
}
.layui-laypage button {
  background: #6AA1FF;
  color: #ffffff;
  border: 4px;
}
.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  border-radius: 4px;
  color: #86909C;
  font-size: 14px;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #6AA1FF;
  border-radius: 4px;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #6AA1FF !important;
}
.layui-laypage a:hover {
  color: #6AA1FF;
}
.layui-laypage-prev {
  background-color: transparent !important;
}
.layui-laypage-next {
  background-color: transparent !important;
}
.layui-laypage-spr {
  background-color: transparent !important;
}
.layui-laypage-skip {
  background-color: transparent !important;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-laypage-skip input {
  border: 1px solid #e2e2e2 !important;
  background-color: transparent !important;
}
.layui-laypage-next {
  border: 1px solid #e2e2e2 !important;
}
.layui-laypage-prev {
  border: 1px solid #e2e2e2 !important;
}
.layui-disabled,
.layui-disabled:hover {
  border: 1px solid #d2d2d2 !important;
}
.layui-laypage .layui-laypage-limits {
  border: 1px solid #e2e2e2;
  height: 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.layui-laypage select {
  background-color: transparent;
}
