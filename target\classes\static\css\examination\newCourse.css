.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
.tellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
body {
  padding: 40px;
  font: 14px Helvetica Neue, Helvetica, PingFang SC, Tahoma, Arial, sans-serif;
}
.con .table-box {
  border-left: 1px solid #E8EBF1;
  border-top: 1px solid #E8EBF1;
}
.con .table-box .t-header {
  background-color: #F1F3F6;
  height: 52px;
  border-bottom: 1px solid #E8EBF1;
  line-height: 52px;
  text-align: center;
  color: #6581BA;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.con .table-box .t-header div {
  border-right: 1px solid #E8EBF1;
}
.con .table-box .t-header .checkbox {
  width: 50px;
  flex-shrink: 0;
  height: 52px;
  background: url(../examination/images/checkbox-bg.png) no-repeat center;
  background-size: 18px;
  cursor: pointer;
}
.con .table-box .t-header .checkbox.checked {
  background: url(../examination/images/checkbox-cur-bg.png) no-repeat center;
  background-size: 18px;
}
.con .table-box .t-header .checkbox.half {
  background: url(../examination/images/checkbox-half-cur-bg.png) no-repeat center;
  background-size: 18px;
}
.con .table-box .t-header .handle {
  width: 110px;
  flex-shrink: 0;
}
.con .table-box .t-header .kcmc,
.con .table-box .t-header .kkxqxq,
.con .table-box .t-header .kkyxyx,
.con .table-box .t-header .zy,
.con .table-box .t-header .jxbzc,
.con .table-box .t-header .jxbbh,
.con .table-box .t-header .jxbmc,
.con .table-box .t-header .kcbh
{
  flex: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;  padding:0 4px;
}

.con .table-box .t-header .jxbrs{
  width:75px;
  flex-shrink: 0;  padding:0 4px;
}
.con .table-box .t-header .njnj{
  width:65px;
  flex-shrink: 0;  padding:0 4px;
}

.con .table-box .t-body .item .kcmc,
.con .table-box .t-body .detail .kcmc,

.con .table-box .t-body .item .kkxqxq,
.con .table-box .t-body .detail .kkxqxq,
.con .table-box .t-body .item .kkyxyx,
.con .table-box .t-body .detail .kkyxyx,
.con .table-box .t-body .item .zy,
.con .table-box .t-body .detail .zy,
.con .table-box .t-body .item .jxbzc,
.con .table-box .t-body .detail .jxbzc,
.con .table-box .t-body .item .jxbbh,
.con .table-box .t-body .detail .jxbbh,
.con .table-box .t-body .item .jxbmc,
.con .table-box .t-body .detail .jxbmc,
.con .table-box .t-body .item .kcbh,
.con .table-box .t-body .detail .kcbh {
  flex: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding:0 4px;
}
.con .table-box .t-body .detail .njnj{
  width:65px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding:0 4px;
}
.con .table-box .t-body .item .njnj{
  width:65px;
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding:0 4px;
}
.con .table-box .t-body .detail .jxbrs{
  width:75px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding:0 4px;
}
.con .table-box .t-body .item .jxbrs{
  width:75px;
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding:0 4px;
}

.con .table-box .t-body {
  border-bottom: 1px solid #E8EBF1;
}
.con .table-box .t-body .detail-content {
  border-bottom: 1px solid #E8EBF1;
  background-color: #edf2fd;
}
.con .table-box .t-body .item,
.con .table-box .t-body .detail {
  height: 52px;
  border-bottom: 1px solid #E8EBF1;
  line-height: 52px;
  text-align: center;
  color: #333333;
  font-size: 14px;
  font-weight: normal;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.con .table-box .t-body .item:last-child,
.con .table-box .t-body .detail:last-child {
  border-bottom: none;
}
.con .table-box .t-body .item div,
.con .table-box .t-body .detail div {
  border-right: 1px solid #E8EBF1;
}
.con .table-box .t-body .item .checkbox,
.con .table-box .t-body .detail .checkbox {
  width: 50px;
  flex-shrink: 0;
  height: 52px;
  background: url(../examination/images/checkbox-bg.png) no-repeat center;
  background-size: 18px;
  cursor: pointer;
}
.con .table-box .t-body .item .checkbox.checked,
.con .table-box .t-body .detail .checkbox.checked {
  background: url(../examination/images/checkbox-cur-bg.png) no-repeat center;
  background-size: 18px;
}
.con .table-box .t-body .item .checkbox.half,
.con .table-box .t-body .detail .checkbox.half {
  background: url(../examination/images/checkbox-half-cur-bg.png) no-repeat center;
  background-size: 18px;
}
.con .table-box .t-body .item .handle,
.con .table-box .t-body .detail .handle {
  width: 110px;
  height: 52px;
  flex-shrink: 0;
  box-sizing: border-box;
  padding-left: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.con .table-box .t-body .item .handle .add,
.con .table-box .t-body .detail .handle .add {
  color: #4C88FF;
  margin: 0 4px;
  cursor: pointer;
}
.con .table-box .t-body .item .handle .switch,
.con .table-box .t-body .detail .handle .switch {
  color: #4C88FF;
  margin: 0 4px;
  cursor: pointer;
}






.all-select {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
.all-select .check {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
}
.all-select .check.checked i {
  background: url(../examination/images/checkbox-cur-bg.png) no-repeat center;
  background-size: 100%;
}
.all-select .check.half i {
  background: url(../examination/images/checkbox-half-cur-bg.png) no-repeat center;
  background-size: 100%;
}
.all-select .check i {
  width: 18px;
  height: 18px;
  background: url(../examination/images/checkbox-bg.png) no-repeat center;
  background-size: 100%;
}
.all-select .check span {
  font-size: 14px;
  color: #193b68;
  margin-left: 12px;
}
.all-select .total {
  color: rgba(0, 0, 0, 0.5);
  font-size: 14px;
  line-height: 18px;
}
#coursePage {
  text-align: right;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.layui-laypage {
  margin: 16px 0;
}
.layui-laypage a,
.layui-laypage span,
.layui-laypage input,
.layui-laypage button,
.layui-laypage select {
  border: none;
}
.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  background: #F1F3F6;
  border-radius: 4px;
  color: #484F5D;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #3381ff;
  border-radius: 4px;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #3381ff !important;
}
.layui-laypage a:hover {
  color: #3381ff;
}
.layui-laypage-prev {
  background-color: transparent !important;
}
.layui-laypage-next {
  background-color: transparent !important;
}
.layui-laypage-spr {
  background-color: transparent !important;
}
.layui-laypage-skip {
  background-color: transparent !important;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-laypage-skip input {
  border: 1px solid #e2e2e2 !important;
  background-color: transparent !important;
}
.layui-laypage select {
  border: 1px solid #e2e2e2 !important;
}
.layui-laypage button:hover {
  color: #4A7CFE;
}
