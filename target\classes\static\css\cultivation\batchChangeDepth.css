.dialog-mark {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.6);
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
}
.d-dialog {
  border-radius: 6px;
  background-color: #ffffff;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.d-dialog.dialog-set {
  width: 640px;
}
.d-dialog.dialog-set .dialog-con {
  padding: 0 30px;
  box-sizing: border-box;
  overflow: hidden;
  height: 440px;
}
.d-dialog.dialog-set .dialog-con .d-count {
  color: #999999;
  line-height: 30px;
  margin: 10px 0 16px;
  font-size: 14px;
}
.d-dialog.dialog-set .dialog-con .d-count em {
  color: #6C4BFF;
  padding: 0 2px;
}
.d-dialog.dialog-set .dialog-con .d-item {
  display: flex;
  font-size: 14px;
  margin: 0 30px 20px;
}
.d-dialog.dialog-set .dialog-con .d-item .item-title {
  line-height: 32px;
  width: 80px;
  text-align: right;
  color: #202020;
  margin-right: 10px;
}
.d-dialog.dialog-set .dialog-con .d-item .item-con {
  flex: 1;
}
.d-dialog.dialog-set .dialog-con .d-item .item-con input {
  width: 100%;
  height: 38px;
  border: 1px solid #DEDFE0;
  padding: 0 6px;
  box-sizing: border-box;
  font-size: 14px;
  border-radius: 4px;
  opacity: 0.6;
}
.d-dialog.dialog-set .dialog-con .d-item .item-con p {
  color: #AEAEAE;
  margin-top: 4px;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con {
  display: flex;
  align-items: center;
  position: relative;
  width: 430px;
  cursor: pointer;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-year {
  left: 0;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con input.depth {
  width: 430px;
  height: 34px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  padding: 0 20px 0 10px;
  box-sizing: border-box;
  font-size: 14px;
  cursor: pointer;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con input.depth::placeholder {
  color: #86909C;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-arrow {
  width: 20px;
  height: 20px;
  background: url(../../images/cultivation/icon-arrow.png) no-repeat center;
  position: absolute;
  right: 8px;
  top: 8px;
  background-size: 12px;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-arrow.j-arrow-slide {
  transform: rotate(180deg);
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-list {
  position: absolute;
  top: 40px;
  left: -1px;
  z-index: 9;
  width: 430px;
  max-height: 300px;
  overflow-y: auto;
  display: none;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-list.slideShow {
  display: block;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-list .j-select-search {
  height: 40px;
  border: none;
  border-bottom: 1px solid #E5E6EB;
  display: flex;
  overflow: hidden;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-list .j-select-search input {
  flex: 1;
  height: 38px;
  border: none;
  padding: 0 10px 0 16px;
  box-sizing: border-box;
  font-size: 14px;
  color: #4E5969;
  opacity: 1;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-list .j-select-search input:focus {
  height: 40px;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-list .j-select-search input::placeholder {
  color: #B5B5B5;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-list .j-select-search span {
  display: block;
  width: 20px;
  height: 20px;
  margin-right: 13px;
  margin-top: 8px;
  background: url(../../images/cultivation/icon-search.png) no-repeat center;
  background-size: 20px;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-list ul {
  max-height: 200px;
  overflow-y: auto;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-list ul li {
  line-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: #4E5969;
  box-sizing: border-box;
  padding-left: 20px;
}
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-list ul li.active,
.d-dialog.dialog-set .dialog-con .d-item .j-search-con .j-select-list ul li:hover {
  background: #E1EBFF;
  color: #4D88FF;
  font-weight: 500;
}
.d-dialog .dialog-title {
  height: 54px;
  text-align: center;
  line-height: 54px;
  border-bottom: 1px solid #E4E4EB;
  font-size: 16px;
}
.d-dialog .dialog-btn {
  height: 74px;
  border-top: 1px solid #E4E4EB;
  display: flex;
  align-items: center;
  justify-content: center;
}
.d-dialog .dialog-btn button {
  width: 100px;
  height: 32px;
  border-radius: 4px;
  border: none;
  outline: none;
  background: none;
  cursor: pointer;
}
.d-dialog .dialog-btn button.btn-cancel {
  border: 1px solid #8A8B90;
  color: #AEAEAE;
  margin-right: 30px;
}
.d-dialog .dialog-btn button.btn-submit {
  background-color: #6C4BFF;
  color: #ffffff;
}
.d-dialog.dialog-tip {
  width: 440px;
}
.d-dialog.dialog-tip .dialog-title {
  display: flex;
  align-items: center;
}
.d-dialog.dialog-tip .dialog-title h3 {
  margin-left: 20px;
  margin-right: 16px;
}
.d-dialog.dialog-tip .dialog-title p {
  flex: 1;
  color: #b4b4b4;
  text-align: left;
  font-size: 14px;
}
.d-dialog.dialog-tip .dialog-title p em {
  color: #6C4BFF;
}
.d-dialog.dialog-tip .dialog-title span {
  margin-right: 20px;
  width: 20px;
  height: 20px;
  background: url(../../images/cultivation/icon-close.png) no-repeat center;
  background-size: 16px;
  cursor: pointer;
}
.d-dialog.dialog-tip .dialog-con {
  height: 100px;
  text-align: center;
}
.d-dialog.dialog-tip .dialog-con p {
  color: #656A73;
  font-size: 16px;
  line-height: 100px;
}
.d-dialog.dialog-tip .dialog-btn button {
  border-radius: 20px;
  height: 36px;
}
.d-dialog.dialog-tip .dialog-btn button.btn-cancel {
  border: 1px solid #4C88FF;
  color: #4C88FF;
}
.d-dialog.dialog-tip .dialog-btn button.btn-submit {
  background-color: #4C88FF;
}
.d-dialog.dialog-modify {
  width: 440px;
  height: 230px;
  background-color: rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9;
  display: none;
}
.d-dialog.dialog-modify .modify {
  background-color: #ffffff;
  display: flex;
  align-items: center;
  padding: 20px;
  font-size: 14px;
  color: #979797;
}
.d-dialog.dialog-modify .modify img {
  width: 24px;
  margin-right: 10px;
  opacity: 0.7;
}
