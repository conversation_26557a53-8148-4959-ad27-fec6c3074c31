#exportRecord {
    min-width: 800px;
    width: 80%;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: #ffffff;
    border-radius: 10px;
}

#exportRecord .dialog-con {
    padding: 40px 80px 40px;
    position: relative;
}

#exportRecord .dialog-con .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

#exportRecord .dialog-con .item .label {
    color: #1d2129;
    font-size: 14px;
    margin-right: 14px;
}

#exportRecord .dialog-con .recordList {
    margin-bottom: 20px;
}

#exportRecord .dialog-con .recordList .layui-form {
    margin: 4px 0 0;
}

#exportRecord .dialog-con .recordList #recordList,
#exportRecord .dialog-con .recordList .layui-table-view {
    border: unset;
}

#exportRecord .dialog-con .recordList #recordList .layui-table-box,
#exportRecord .dialog-con .recordList .layui-table-view .layui-table-box {
    border: 1px solid #e8ebf3;
}

#exportRecord .dialog-con .recordList .layui-table-page {
    text-align: center;
    border-top: unset;
    padding: 0;
    margin-top: 20px;
}

#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage a,
#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage span {
    border: 1px solid #e5e6eb;
    border-radius: 0;
    line-height: 32px;
    margin-bottom: 0;
    width: 41px;
    height: 32px;
    box-sizing: border-box;
    font-size: 14px;
    color: #4e5969;
}

#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage a .layui-laypage-em,
#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage span .layui-laypage-em {
    border-radius: 0;
}

#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-prev,
#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-prev {
    width: auto;
    border-radius: 2px 0 0 2px;
}

#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-next,
#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-next {
    width: auto;
    border-radius: 0 2px 2px 0;
    margin-right: 16px;
}

#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-skip,
#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-skip {
    color: #86909c;
    margin-right: 16px;
}

#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-skip input,
#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-skip input {
    width: 41px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #e5e6eb;
    margin: 0 8px;
}

#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-skip .layui-laypage-btn,
#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-skip .layui-laypage-btn {
    border-radius: 2px;
    width: 60px;
    height: 32px;
    color: #4e5969;
}

#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-count,
#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-count {
    margin-right: 16px;
}

#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-limits select,
#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-limits select {
    width: 104px;
    height: 30px;
    color: #4e5969;
    padding: 0;
    text-align: center;
    border-radius: 2px;
    margin-top: -2px;
}

#exportRecord .dialog-con .recordList .layui-table-page .layui-laypage .layui-laypage-next ~ * {
    border: none;
    width: auto;
}

#exportRecord .dialog-con .recordList .selRecord {
    position: absolute;
    left: 80px;
    bottom: 65px;
    color: #86909c;
    font-size: 14px;
    line-height: 40px;
    display: flex;
    align-items: center;
}

#exportRecord .dialog-con .recordList .selRecord em,
#exportRecord .dialog-con .recordList .selRecord i {
    color: #4d88ff;
    padding: 0 4px;
}

#exportRecord .dialog-con .recordList .selRecord .z-check {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #4e5969;
    margin-right: 16px;
}

#exportRecord .dialog-con .recordList .selRecord .z-check .check {
    width: 28px;
    height: 28px;
    cursor: pointer;
    margin-right: 6px;
    background: url(../../images/cultivation/check.png) no-repeat center;
    background-size: 28px;
}

#exportRecord .dialog-con .recordList .selRecord .z-check .check.checked {
    background: url(../../images/cultivation/check1.png) no-repeat center;
}

#exportRecord .dialog-con .recordList .refresh {
    font-size: 14px;
    color: #4d88ff;
    padding-left: 20px;
    background: url('../../images/cultivation/refresh.png') no-repeat left center;
    background-size: 16px;
    position: absolute;
    right: 80px;
    bottom: 65px;
    line-height: 40px;
    cursor: pointer;
}

.progress-content {
    width: 96%;
    margin: 0 auto;
    display: flex;
    align-items: center;
}

.progress-content .progress-bar {
    flex: 1;
    border-radius: 3px;
    background: #E5E6EB;
    height: 3px;
    text-align: left;
}

.progress-content .progress-bar .progress {
    background-color: #F76560;
    border-radius: 3px;
    display: block;
    height: 3px;
}

.progress-content .progress-bar .progress.proSuccess {
    background-color: #00B42A;
}

.progress-content .progress-text {
    font-family: "Nunito Sans";
    font-size: 12px;
    color: #4E5969;
    margin-left: 8px;
}

.progress-content .progress-tip {
    display: none;
    width: 12px;
    height: 12px;
    background: url('../../images/cultivation/error-tip2.png') no-repeat center;
    background-size: 12px;
    margin-left: 4px;
    cursor: pointer;
}

.recordDetail {
    color: #4C88FF;
    cursor: pointer;
}

.j-search-con {
    display: flex;
    align-items: center;
    position: relative;
    width: 240px;
    cursor: pointer;
}

.j-search-con.slideShow {
    display: block;
}

.j-search-con.slideShow .j-arrow {
    transform: rotate(180deg);
    background: url(../../images/cultivation/down-icon.png) no-repeat center;
}

.j-search-con.slideShow .j-select-year {
    display: block;
}

.j-search-con.slideShow .schoolSel {
    border: 1px solid #4D88FF;
}

.j-search-con.slideShow .fuzzy-query-input {
    border: 1px solid #4D88FF;
}

.j-search-con.slideShow .j-select-search {
    display: block;
}

.j-search-con .j-select-year {
    left: 0;
}

.j-search-con input {
    width: 100%;
    height: 34px;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    padding: 0 20px 0 10px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    font-size: 14px;
    cursor: pointer;
}

.j-search-con input::placeholder {
    color: #86909c;
}

.j-search-con input:focus {
    border: 1px solid #4D88FF;
}

.j-search-con input.error {
    border-color: #F76560;
}

.j-search-con .j-arrow {
    width: 10px;
    height: 10px;
    background: url(../../images/cultivation/down-icon.png) no-repeat center;
    position: absolute;
    right: 12px;
    top: 12px;
    pointer-events: none;
}

.j-search-con .j-arrow.j-arrow-slide {
    transform: rotate(180deg);
}

.j-search-con .j-select-year {
    position: absolute;
    top: 40px;
    left: -1px;
    z-index: 999;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
    /*  &.slideShow {
              display: block;
          } */
}

.j-search-con .j-select-year.slideShowTop {
    display: block;
    top: unset;
    bottom: 40px;
}

.j-search-con .j-select-year .search {
    height: 36px;
    background: #f5f7fa;
    border-radius: 18px;
    margin: 11px 10px;
}

.j-search-con .j-select-year .search input {
    border: none;
    width: 84%;
    background: transparent;
    height: 36px;
    line-height: 36px;
    padding-left: 14px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    float: left;
}

.j-search-con .j-select-year .search span {
    cursor: pointer;
    float: left;
    width: 16px;
    height: 16px;
    background: url(../../images/cultivation/search-icon.png) no-repeat center;
    margin-top: 10px;
}

.j-search-con .j-select-year .all-selects {
    line-height: 17px;
    margin-bottom: 4px;
    height: 17px;
    padding: 0 14px;
    font-size: 12px;
    color: #6b89b3;
    cursor: pointer;
    user-select: none;
}

.j-search-con .j-select-year ul {
    overflow: hidden;
    max-height: 200px;
    overflow-y: auto;
}

.j-search-con .j-select-year ul li {
    line-height: 40px;
    text-align: left;
    text-indent: 16px;
    cursor: pointer;
    font-size: 14px;
    color: #4e5969;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-right: 30px;
    background-color: #ffffff;
    background-image: url("../../images/cultivation/check-icon.png");
    background-repeat: no-repeat;
    background-position: 96% center;
}

.j-search-con .j-select-year ul li:hover {
    background-color: #e1ebff;
    color: #4d88ff;
    font-weight: 500;
}

.j-search-con .j-select-year ul li.active {
    background-image: url("../../images/cultivation/check-cur.png");
    font-weight: 500;
}

.j-search-con .j-select-search {
    position: absolute;
    top: 40px;
    left: -1px;
    z-index: 999;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}

.j-search-con .j-select-search ul {
    overflow: hidden;
    max-height: 200px;
    overflow-y: auto;
}

.j-search-con .j-select-search ul li {
    line-height: 40px;
    text-align: left;
    text-indent: 16px;
    cursor: pointer;
    font-size: 14px;
    color: #4e5969;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-right: 30px;
    background-color: #ffffff;
}

.j-search-con .j-select-search ul li:hover {
    background-color: #e1ebff;
    color: #4d88ff;
    font-weight: 500;
}

.j-search-con .j-select-search ul li.active {
    color: #4d88ff;
    font-weight: 500;
}

.j-search-con.single-box .j-select-year ul li {
    background-image: url("../../images/cultivation/radio-icon.png");
}

.j-search-con.single-box .j-select-year ul li.active {
    background-image: url("../../images/cultivation/radio-cur-icon.png");
}

.layui-table-cell {
    padding: 0 10px;
    line-height: 38px;
}

.layui-table-header thead tr {
    background: #f1f3f6;
}

.layui-table-view .layui-table th {
    font-weight: normal;
    color: #86909c;
    font-size: 14px;
}

.layui-table-body tr:nth-child(2n) {
    background: #fafbfc;
}

.layui-table-view .layui-table td {
    color: #4e5969;
    height: 38px;
}

.layui-table-view .layui-table td,
.layui-table-view .layui-table th {
    border-color: #e8ebf3;
}

.layui-layer-page .layui-layer-content {
    overflow: unset;
}

.layui-form-checked.layui-checkbox-disabled:hover {
    border-color: #eee !important;
}

.layui-table-page .layui-laypage input:focus,
.layui-table-page .layui-laypage select:focus {
    border-color: #4d88ff !important;
}

.layui-laypage .layui-laypage-curr {
    border-color: #4d88ff !important;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: #4d88ff !important;
}

.layui-table-page .layui-laypage a:hover,
.layui-table-page .layui-laypage span.layui-laypage-curr:hover {
    color: #4d88ff !important;
}

.layui-table-page .layui-laypage a.layui-disabled:hover:hover,
.layui-table-page .layui-laypage span.layui-laypage-curr.layui-disabled:hover {
    color: #d2d2d2 !important;
}

.masker {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.dialog .dialog-footer {
    height: 70px;
    border-top: 1px solid #e5e6eb;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.dialog .dialog-footer button {
    width: 88px;
    height: 34px;
    border: 1px solid #c9cdd4;
    border-radius: 18px;
    color: #4e5969;
    font-size: 14px;
    background-color: #ffffff;
    cursor: pointer;
}

.dialog .dialog-footer button:last-child {
    background: #4d88ff;
    border-color: #4d88ff;
    box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
    color: #ffffff;
    margin: 0 30px 0 16px;
}

.search-btn {
    color: #fff;
    margin-left: 22px;
    border: 1px solid transparent;
    width: 64px;
    height: 34px;
    line-height: 32px;
    background: #4d88ff;
    border-radius: 6px;
}