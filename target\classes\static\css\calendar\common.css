.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #999999;
  font-size: 0.3rem;
}
.hide {
  display: none;
}
body {
  background: #F2F2F2;
}
.popup {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(16, 26, 41, 0.76);
  width: 100%;
  height: 100%;
  z-index: 100;
}
.popup .window {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  background: #FFFFFF;
  border-radius: 10px;
  /*width: 840px;*/
  /*height: 600px;*/
}
.popup .window .p-title {
  line-height: 60px;
  border-bottom: 1px solid #F2F2F2;
  height: 60px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popup .window .p-title span {
  font-size: 18px;
  font-weight: 500;
  color: #131B26;
  margin-left: 30px;
}
.popup .window .p-title .close {
  width: 18px;
  height: 18px;
  float: right;
  margin-top: 21px;
  margin-right: 30px;
  cursor: pointer;
}
.popup .window .p-content {
  /*padding: 30px;*/
  /*width: 100%;*/
  /*height: auto;*/
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popup .window .p-btns {
  width: 100%;
  position: absolute;
  bottom: 0;
  height: 70px;
  border-top: 1px solid #F2F2F2;
  text-align: right;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popup .window .p-btns .btn {
  display: inline-block;
  width: 92px;
  height: 36px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #3A8BFF;
  text-align: center;
  border: 1px solid #8CBCFF;
  box-sizing: border-box;
  border-radius: 20px;
  line-height: 34px;
  margin-right: 30px;
  margin-top: 17px;
  cursor: pointer;
}
.popup .window .p-btns .sure {
  background: #3A8BFF;
  border-radius: 50px;
  color: #FFFFFF;
  margin-right: 30px;
  font-size: 14px;
  border: none;
}
.mCSB_scrollTools {
  z-index: 999 !important;
}
.mCSB_inside > .mCSB_container {
  margin-right: 0px !important;
}
.mCSB_scrollTools .mCSB_draggerRail {
  background-color: transparent !important;
}
.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background: #DADFE5 !important;
  border-radius: 100px !important;
  width: 4px !important;
}
.mCSB_scrollTools {
  right: 6px !important;
}
.page-list {
  width: 100%;
  height: 54px;
  background-color: #fff;
  borDer-top: 1px solid #E0E0E3;
  line-height: 54px;
}
.success-tips {
  position: fixed;
  top: 142px;
  left: 50%;
  margin-left: -73px;
  width: 146px;
  height: 48px;
  z-index: 999;
  background-color: rgba(79, 87, 98, 0.98);
  border-radius: 8px;
  text-align: center;
  line-height: 48px;
  display: none;
}
.success-tips span {
  display: inline-block;
  padding-left: 28px;
  background: url(../image/succes-icon.png) no-repeat left center;
  font-size: 14px;
  color: #fff;
}
#coursePage {
  text-align: center;
  margin-bottom: 21px;
}
.layui-laypage a,
.layui-laypage span,
.layui-laypage input,
.layui-laypage button,
.layui-laypage select {
  border: none;
}
.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  background: #F1F3F6;
  border-radius: 4px;
  color: #484F5D;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #6AA1FF;
  border-radius: 4px;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #6AA1FF !important;
}
.layui-laypage a:hover {
  color: #6AA1FF;
}
.layui-laypage-prev {
  background-color: transparent !important;
}
.layui-laypage-next {
  background-color: transparent !important;
}
.layui-laypage-spr {
  background-color: transparent !important;
}
.layui-laypage-skip {
  background-color: transparent !important;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-laypage-skip input {
  border: 1px solid #e2e2e2 !important;
  background-color: transparent !important;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"] {
  -moz-appearance: textfield;
}
.j-material .j-search-item input {
  cursor: pointer;
}
.layui-body {
  overflow-x: scroll;
}
.layui-table tbody tr:hover,
.layui-table-hover {
  background-color: #edf2fd !important;
}
.layui-table-cell .warehouse {
  font-size: 14px;
  color: #4C85FA;
  cursor: pointer;
}
.layui-table-cell .already {
  display: inline-block;
  background: #5AB176;
  border-radius: 15px;
  width: 58px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 14px;
  color: #FFFFFF;
}
.layui-table-cell .wait {
  display: inline-block;
  background: #4080FF;
  border-radius: 15px;
  width: 58px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 14px;
  color: #FFFFFF;
}
.layui-table-cell .grant {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../../images/calendar/grant-icon.png) no-repeat center;
}
.layui-table-cell .ungrant {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../../images/calendar/ungrant-icon.png) no-repeat center;
}
.layui-laypage button {
  margin-left: 10px;
  padding: 0 10px;
  cursor: pointer;
  background: #6AA1FF;
  border-radius: 4px;
  color: #fff;
}
