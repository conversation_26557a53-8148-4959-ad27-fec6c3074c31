body {
  background-color: #f7f8fa;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
.hide {
  display: none !important;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  font-size: 14px;
  color: #8f97a8;
}
.hide {
  display: none;
}
.main {
  margin: 20px;
  background-color: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 40px);
  min-width: 1000px;
  overflow: hidden;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top {
  width: 100%;
  height: 57px;
  border-bottom: 1px solid #e8eaf1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top .back {
  width: 60px;
  height: 24px;
  background: url(../images/back-icon.png) no-repeat left center;
  cursor: pointer;
  margin-right: 10px;
}
.main .m-top .title {
  font-size: 16px;
  color: #1d2129;
  padding-left: 9px;
  position: relative;
}
.main .m-top .title::after {
  content: "";
  width: 3px;
  height: 16px;
  background-color: #4d88ff;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 3px;
}
.main .item {
  padding: 0 30px;
  overflow: hidden;
}
.main .item .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 32px;
  margin-top: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .item .i-top span {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #6581ba;
  position: relative;
  display: block;
  margin-right: 24px;
}
.main .item .i-top span:after {
  content: "";
  position: absolute;
  left: 0;
  top: 1px;
  background: #6581ba;
  border-radius: 2px;
  width: 3px;
  height: 18px;
}
.main .item .i-top .arrow {
  padding-right: 14px;
  position: relative;
  font-weight: 400;
  font-size: 14px;
  color: #4c88ff;
  cursor: pointer;
}
.main .item .i-top .arrow:after {
  content: "";
  position: absolute;
  right: 0;
  top: 5px;
  width: 10px;
  height: 10px;
  background: url(../images/stow-icon.png) no-repeat right center;
}
.main .item .i-con {
  overflow: hidden;
  width: 100%;
}
.main .item .i-con h4.i-title {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #484f5d;
  margin-bottom: 16px;
}
.main .item .i-con .course-inform {
  flex: 1;
}
.main .item .i-con .course-inform ul {
  overflow: hidden;
}
.main .item .i-con .course-inform ul li {
  width: 220px;
  height: 20px;
  margin-bottom: 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  float: left;
  font-size: 16px;
}
.main .item .i-con .course-inform ul li .name {
  color: #1d2129;
  flex-shrink: 0;
}
.main .item .i-con .course-inform ul li .tit {
  color: #4e5969;
  flex: 1;
  margin-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .item .i-con .class-box {
  min-width: 852px;
  flex: 1;
}
.main .item .i-con .class-box .set-con {
  border-radius: 8px;
}
.main .item .i-con .class-box .set-con .form-set {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
}
.main .item .i-con .class-box .set-con .layui-inline {
  margin-right: 120px;
  margin-bottom: 0;
}
.main .item .i-con .class-box .set-con .layui-form-label {
  width: 86px;
  text-align: right;
  font-size: 14px;
  line-height: 34px;
  color: #1d2129;
  padding: 0;
  margin-right: 12px;
}
.main .item .i-con .class-box .set-con .layui-input,
.main .item .i-con .class-box .set-con .layui-textarea,
.main .item .i-con .class-box .set-con .layui-select {
  height: 34px;
  font-size: 14px;
}
.main .item .i-con .class-box .set-con .layui-input {
  border-color: #d5d9e2;
  border-radius: 4px;
}
.main .item .i-con .class-box .set-con .layui-input::placeholder {
  color: #8f97a8 !important;
}
.main .item .i-con .class-box .set-con p.txt {
  font-size: 14px;
  line-height: 34px;
  color: #484f5d;
  width: 240px;
}
.main .item .i-con .class-box .set-con .layui-form-select {
  width: 240px;
}
.main .item.item-teacher .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 32px;
  margin-top: 20px;
}
.main .item.item-teacher .i-top span {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #6581ba;
  position: relative;
  display: block;
}
.main .item.item-teacher .i-top span:after {
  content: "";
  position: absolute;
  left: 0;
  top: 1px;
  background: #6581ba;
  border-radius: 2px;
  width: 3px;
  height: 18px;
}
.main .item.item-teacher .i-search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.main .item.item-teacher .i-search .i-add {
  width: 86px;
  height: 34px;
  border: 1px solid #4c88ff;
  background-color: #4d88ff;
  border-radius: 4px;
  font-size: 14px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 40px;
  box-sizing: border-box;
  cursor: pointer;
}
.main .item.item-teacher .i-search .i-add span {
  margin-right: 6px;
  font-size: 24px;
}
.main .item.item-teacher .i-search .layui-inline {
  margin-bottom: 0;
}
.main .item.item-teacher .i-search .layui-form-label {
  width: 57px;
  text-align: right;
  font-size: 14px;
  line-height: 34px;
  color: #717b91;
  padding: 0;
  margin-right: 12px;
}
.main .item.item-teacher .i-search .layui-input {
  border-color: #d5d9e2;
  border-radius: 4px;
  width: 240px;
  height: 34px;
  font-size: 14px;
}
.main .item.item-teacher .i-search .layui-input::placeholder {
  color: #8f97a8 !important;
}
.main .item.item-teacher .i-search .btnSubmit {
  width: 88px;
  height: 34px;
  line-height: 34px;
  padding: 0;
  border: 1px solid #4c88ff;
  border-radius: 4px;
  margin-left: 40px;
  background-color: #fff;
  font-size: 14px;
  color: #4c88ff;
}
.main .item.item-teacher .i-table {
  margin-bottom: 56px;
}
.main .item.item-teacher .layui-table tbody tr:nth-child(even) {
  background-color: #ffffff;
}
.main .item.item-teacher .layui-table tbody tr:nth-child(even):hover {
  background-color: #edf2fd;
}
.main .itemBtn {
  width: 118px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 6px;
  font-size: 14px;
  color: #ffffff;
  background-color: #6a9cff;
  margin: 20px 30px;
  cursor: pointer;
}
.layui-table-cell {
  padding: 0 17px;
  height: 30px;
}
.layui-table-cell .tmplOpt {
  display: flex;
  align-items: center;
  justify-content: center;
}
.layui-table-cell .tmplOpt h5 {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.layui-table-cell .tmplOpt span.set {
  color: #4c88ff;
  margin-left: 16px;
  cursor: pointer;
}
.layui-table-cell .layui-input {
  width: 220px;
  background: #ffffff;
  border: 1px solid #d5d9e2;
  border-radius: 4px;
}
.layui-table-cell .layui-input:focus {
  border: 1px solid #4c88ff !important;
}
.layui-table-cell .edit {
  display: inline-block;
  color: #4c88ff;
  margin-right: 17px;
  cursor: pointer;
}
.layui-table-cell .delet {
  display: inline-block;
  color: #ff5e5e;
  cursor: pointer;
}
.layui-table-view .layui-table th {
  font-size: 14px;
  color: #6581ba;
  height: 44px;
}
.layui-table-view .layui-table td {
  color: #484f5d;
  font-size: 14px;
  height: 44px;
}
.layui-laypage a,
.layui-laypage span {
  border: none;
}
.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  background: #f1f3f6;
  border-radius: 4px;
  color: #484f5d;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #4c85fa;
  border-radius: 4px;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #4c85fa !important;
}
.layui-laypage a:hover {
  color: #4c85fa;
}
.layui-laypage-prev {
  background-color: transparent !important;
}
.layui-laypage-next {
  background-color: transparent !important;
}
.layui-laypage-spr {
  background-color: transparent !important;
}
.layui-laypage-skip {
  background-color: transparent !important;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-laypage-skip input {
  border: 1px solid #e2e2e2;
  background-color: transparent;
}
.layui-laypage-skip input {
  background: #ffffff;
  border: 1px solid #e7eaf1;
  color: #4d4d4d;
  border-radius: 2px;
}
.layui-laypage button {
  background: #f5f8fb;
  border: 1px solid #e7eaf1;
  color: #595959;
  border-radius: 2px;
}
.layui-table-page > div {
  text-align: right;
}
.layui-form-item .layui-input-inline {
  width: 240px !important;
}
