/* CSS Document */
body, form, body,form,textarea,select,option,ol,ul,li,h1,h2,h3,h4,h5,h6,p,th,td,dl,dt,dd,menu,blockquote,fieldset,label,i,em,header,footer,section,
legend,button,input,hr,pre,div,input,span,p,a{margin: 0;padding: 0;-webkit-tap-highlight-color:rgba(0,0,0,0);}
h1,h2,h3,h4,h5,h6{ font-weight:normal; font-size:100%;}
html{font-size: 62.5%;}
body{font:12px/1 '微软雅黑',"Hiragino Sans GB","Helvetica Neue",Helvetica,STHeiTi,"Microsoft Yahei",sans-serif;color:#333;}
li{list-style-type:none;}
input,select,textarea{vertical-align:middle;  color:#333; outline:none;-webkit-tap-highlight-color:rgba(0,0,0,0);}
a{text-decoration:none;color:#333;}
img{border:0;}
i,em{font-style:normal;}
b{ font-weight:normal;}
table{border-spacing:0; border-collapse:collapse;width:100%; border:0;}
.clearfix{}
.clear{clear:both; height:0; font-size:0; line-height:0; overflow:hidden;}
ul, ol, img {  border: 0px;  }
li {  list-style-type: none;text-decoration: none;  }
input,
select,
textarea {  vertical-align: middle;  outline: none;  }
textarea {  resize: none;  }
a {  text-decoration: none;  -webkit-tap-highlight-color: transparent;  -webkit-appearance: none;  }
input[type="button"]:hover {  cursor: pointer;  }
div:focus {  outline: none;  }
.clear {  clear: both;  font-size: 0;  height: 0;  line-height: 0;}
.clearfix:after {  content: "";  display: block;  clear: both;  }
.fl{
  float: left;
}
.fr{
  float: right;
}
.mCSB_outside + .mCSB_scrollTools {
  right: -4px!important;
}

