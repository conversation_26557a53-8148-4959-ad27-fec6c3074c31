.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"] {
  -moz-appearance: textfield;
}
.hide {
  display: none !important;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  font-size: 14px;
  color: #8F97A8;
}
.hide {
  display: none;
}
body {
  background-color: #F7F8FA;
}
.main {
  margin: 20px;
  background-color: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 40px);
  min-width: 1000px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top {
  width: 100%;
  height: 57px;
  border-bottom: 1px solid #E8EAF1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top .back {
  width: 60px;
  height: 24px;
  background: url(../../images/cultivation/back-icon.png) no-repeat left center;
  cursor: pointer;
  margin-right: 10px;
}
.main .m-top .title {
  font-size: 16px;
  color: #6581BA;
}
.main .steps-box {
  width: 100%;
  height: 72px;
  padding: 24px 0;
  text-align: center;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .steps-box ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 40px;
}
.main .steps-box ul li {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-right: 116px;
  margin-right: 19px;
  position: relative;
}
.main .steps-box ul li:last-child {
  padding-right: 0;
  margin-right: 0;
}
.main .steps-box ul li:last-child:after {
  display: none;
}
.main .steps-box ul li:after {
  content: '';
  position: absolute;
  right: 0;
  top: 11px;
  width: 100px;
  height: 2px;
  background: #CDD4E2;
}
.main .steps-box ul li span {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #6581BA;
}
.main .step-box {
  width: 100%;
  height: 72px;
  padding: 24px 0;
  text-align: center;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .step-box ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
}
.main .step-box ul li {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-right: 116px;
  margin-right: 19px;
  position: relative;
  cursor: pointer;
}
.main .step-box ul li:last-child {
  padding-right: 0;
  margin-right: 0;
}
.main .step-box ul li:last-child:after {
  display: none;
}
.main .step-box ul li:after {
  content: '';
  position: absolute;
  right: 0;
  top: 11px;
  width: 100px;
  height: 2px;
  background: #DCDCDC;
}
.main .step-box ul li em {
  width: 24px;
  height: 24px;
  border: 1px solid #999999;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  text-align: center;
  line-height: 23px;
  font-size: 16px;
  color: #999999;
  margin-right: 16px;
  border-radius: 50%;
}
.main .step-box ul li span {
  font-size: 16px;
  line-height: 24px;
  color: #999999;
  font-weight: 400;
}
.main .step-box ul li.cur em {
  background: #4C88FF;
  border: 1px solid #4C88FF;
  color: rgba(255, 255, 255, 0.9);
}
.main .step-box ul li.cur span {
  color: #4C88FF;
  font-weight: 600;
}
.main .step-box ul li.completed:after {
  background: #4C88FF;
}
.main .step-box ul li.completed em {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  background: url(../../images/cultivation/stepper.png) no-repeat center;
  background-size: 24px;
}
.main .step-box ul li.completed em i {
  display: none;
}
.main .step-box ul li.completed span {
  color: #4C88FF;
  font-weight: 600;
}
.main .item {
  padding: 0 30px;
  padding-bottom: 40px;
}
.main .item:last-child {
  padding-bottom: 60px;
}
.main .item .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .item .i-top span {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #484F5D;
  position: relative;
  display: block;
  margin-right: 24px;
}
.main .item .i-top span:after {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  background: #4D88FF;
  border-radius: 1px;
  width: 3px;
  height: 14px;
}
.main .item .i-top .arrow {
  padding-right: 14px;
  position: relative;
  font-weight: 400;
  font-size: 14px;
  color: #4C88FF;
  cursor: pointer;
}
.main .item .i-top .arrow:after {
  content: '';
  position: absolute;
  right: 0;
  top: 5px;
  width: 10px;
  height: 10px;
  background: url(../../images/cultivation/stow-icon.png) no-repeat right center;
}
.main .item .i-top .arrow.clicked:after {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.main .item .i-con {
  overflow: hidden;
  display: flex;
  display: -webkit-flex;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
}
.main .item .i-con .course-inform {
  flex-shrink: 0;
  width: 452px;
  min-height: 524px;
  margin-right: 24px;
  padding: 20px 24px;
  background: #F9FAFB;
  border-radius: 4px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .item .i-con .course-inform h4 {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #484F5D;
  margin-bottom: 16px;
}
.main .item .i-con .course-inform ul {
  overflow: hidden;
}
.main .item .i-con .course-inform ul li {
  width: 50%;
  height: 20px;
  margin-bottom: 16px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  float: left;
}
.main .item .i-con .course-inform ul li .name {
  font-weight: 400;
  font-size: 14px;
  color: #717B91;
  flex-shrink: 0;
}
.main .item .i-con .course-inform ul li .tit {
  color: #484f5d;
  font-weight: 400;
  font-size: 14px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .item .i-con .mutate {
  width: 112px;
  height: 415px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  flex-wrap: wrap;
}
.main .item .i-con .mutate .up {
  width: 72px;
  height: 30px;
  background: url(../../images/cultivation/switchRight.png) no-repeat;
  background-size: 72px 30px;
  margin-bottom: 12px;
  cursor: pointer;
}
.main .item .i-con .mutate .down {
  width: 72px;
  height: 30px;
  background: url(../../images/cultivation/switchLeft.png) no-repeat;
  background-size: 72px 30px;
  cursor: pointer;
}
.main .item .i-con .class-box {
  flex: 1;
  padding: 20px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  background: #F9FAFB;
  border-radius: 4px;
  flex-shrink: 0;
  overflow: hidden;
}
.main .item .i-con .class-box .cb-top {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 20px;
  margin-bottom: 16px;
}
.main .item .i-con .class-box .cb-top.create {
  margin-bottom: 0;
  padding-bottom: 32px;
  position: relative;
}
.main .item .i-con .class-box .cb-top.create .oprate {
  position: absolute;
  top: 42px;
  right: 0;
  height: 34px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .item .i-con .class-box .cb-top.create .oprate .number {
  font-size: 14px;
  color: #717B91;
  margin-right: 20px;
}
.main .item .i-con .class-box .cb-top.create .oprate .btn {
  background: linear-gradient(0deg, #4C88FF, #4C88FF), #D9D9D9;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 4px;
  width: 132px;
  height: 34px;
  text-align: center;
  line-height: 34px;
  font-size: 14px;
  color: #FFFFFF;
  cursor: pointer;
}
.main .item .i-con .class-box .cb-top .tit {
  font-weight: 600;
  font-size: 14px;
  color: #484F5D;
  margin-right: 20px;
}
.main .item .i-con .class-box .cb-top .radio {
  overflow: hidden;
}
.main .item .i-con .class-box .cb-top .radio span {
  float: left;
  padding-left: 20px;
  background: url(../../images/cultivation/radio-icon.png) no-repeat left center;
  background-size: 14px ;
  font-size: 14px;
  color: #717B91;
  margin-right: 36px;
  cursor: pointer;
}
.main .item .i-con .class-box .cb-top .radio span.cur {
  background: url(../../images/cultivation/radio-cur.png) no-repeat left center;
  background-size: 14px ;
}
.main .item .i-con .class-box .cb-top .arrow {
  padding-right: 14px;
  position: relative;
  font-weight: 400;
  font-size: 14px;
  color: #4C88FF;
  cursor: pointer;
}
.main .item .i-con .class-box .cb-top .arrow:after {
  content: '';
  position: absolute;
  right: 0;
  top: 5px;
  width: 10px;
  height: 10px;
  background: url(../../images/cultivation/stow-icon.png) no-repeat right center;
}
.main .item .i-con .class-box .cb-top .arrow.clicked:after {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.main .item .i-con .class-box .button {
  border: 1px solid #4C88FF;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 4px;
  width: 88px;
  height: 34px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  color: #4C88FF;
  float: left;
  margin-bottom: 16px;
}
.main .item .i-con .class-box .j-search-item {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 22px;
  position: relative;
  float: left;
  margin-bottom: 16px;
}
.main .item .i-con .class-box .j-search-item .button {
  background: #4D88FF;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 6px;
  width: 92px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  font-size: 14px;
  color: #FFFFFF;
}
.main .item .i-con .class-box .j-search-item input {
  width: 220px;
  height: 30px;
  border: 1px solid #D5D9E2;
  border-radius: 4px;
  padding: 0 14px;
  box-sizing: border-box;
  font-size: 14px;
}
.main .item .i-con .class-box .j-search-item input::placeholder {
  color: #8F97A8;
}
.main .item .i-con .class-box .j-search-item .j-arrow {
  width: 10px;
  height: 10px;
  background: url(../../images/cultivation/drop-down-icon.png) no-repeat center;
  position: absolute;
  right: 12px;
  top: 12px;
}
.main .item .i-con .class-box .j-search-item .j-arrow.j-arrow-slide {
  transform: rotate(180deg);
}
.main .item .i-con .class-box .j-search-item .j-select-year {
  position: absolute;
  top: 36px;
  left: 80px;
  z-index: 999;
  width: 178px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #D5D9E2;
  border-radius: 4px;
  display: none;
  background-color: #fff;
}
.main .item .i-con .class-box .j-search-item .j-select-year.slideShow {
  display: block;
}
.main .item .i-con .class-box .j-search-item .j-select-year ul li {
  line-height: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  text-align: left;
  cursor: pointer;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-left: 15px;
}
.main .item .i-con .class-box .j-search-item .j-select-year ul li.active,
.main .item .i-con .class-box .j-search-item .j-select-year ul li:hover {
  background: #E1EBFF;
}
.main .item .i-con .class-box .j-search-item .j-search-con {
  position: relative;
  cursor: pointer;
}
.main .item .i-con .class-box .j-search-item .j-search-con .j-select-year {
  left: 0;
}
.main .item .i-con .class-box .j-search {
  position: relative;
  margin-bottom: 4px;
}
.main .item .i-con .class-box .j-search .radio {
  padding-left: 22px;
  background: url(../../images/cultivation/check-icon.png) no-repeat left center;
  background-size: 16px;
  cursor: pointer;
  line-height: 34px;
  font-size: 14px;
  color: #4E5969;
}
.main .item .i-con .class-box .j-search .radio.cur {
  background: url(../../images/cultivation/check-cur.png) no-repeat left center;
  background-size: 16px;
}
.main .item .i-con .class-box .j-search h5 {
  color: #717B91;
  font-size: 14px;
  margin-right: 12px;
}
.main .item .i-con .class-box .j-search input {
  width: 180px;
  height: 34px;
  border: 1px solid #D5D9E2;
  border-radius: 4px;
  padding: 0 12px;
  box-sizing: border-box;
  font-size: 14px;
  cursor: pointer;
}
.main .item .i-con .class-box .j-search input::placeholder {
  color: #8F97A8;
}
.main .item .i-con .class-box .j-search button {
  background: #4D88FF;
  border: 1px solid #4D88FF;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 6px;
  margin-left: 24px;
  width: 90px;
  height: 30px;
  color: #FFFFFF;
  cursor: pointer;
  font-weight: normal;
}
.main .item .i-con .class-box .j-search .j-select {
  position: absolute;
  top: 36px;
  left: 94px;
  z-index: 9;
  width: 218px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #D5D9E2;
  border-radius: 4px;
  display: none;
}
.main .item .i-con .class-box .j-search .j-select ul {
  background-color: #fff;
}
.main .item .i-con .class-box .j-search .j-select ul li {
  display: block;
  padding: 0 20px;
  overflow: hidden;
  cursor: pointer;
}
.main .item .i-con .class-box .j-search .j-select ul li.active,
.main .item .i-con .class-box .j-search .j-select ul li:hover {
  background: #E1EBFF;
}
.main .item .i-con .class-box .j-search .j-select ul li h3 {
  font-size: 14px;
  line-height: 20px;
  color: #4E5969;
  margin-top: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .item .i-con .class-box .j-search .j-select ul li h3 em {
  color: #4C85FA;
}
.main .item .i-con .class-box .j-search .j-select ul li p {
  color: #86909C;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .item .i-con .class-box .j-search .j-select ul li p span:first-child {
  padding-right: 16px;
}
.layui-table-cell {
  padding: 0 17px;
}
.layui-table-cell .edit {
  display: inline-block;
  color: #4C88FF;
  margin-right: 17px;
  cursor: pointer;
}
.layui-table-cell .delet {
  display: inline-block;
  color: #FF5E5E;
  cursor: pointer;
}
.layui-table-view .layui-table th {
  font-size: 14px;
  color: #6581BA;
  height: 44px;
}
.layui-table-view .layui-table td {
  color: #484F5D;
  font-size: 14px;
  height: 44px;
}
.layui-laypage a,
.layui-laypage span {
  border: none;
}
.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  background: #F1F3F6;
  border-radius: 4px;
  color: #484F5D;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #4C85FA;
  border-radius: 4px;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #4C85FA !important;
}
.layui-laypage a:hover {
  color: #4C85FA;
}
.layui-laypage-prev {
  background-color: transparent !important;
}
.layui-laypage-next {
  background-color: transparent !important;
}
.layui-laypage-spr {
  background-color: transparent !important;
}
.layui-laypage-skip {
  background-color: transparent !important;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-laypage-skip input {
  border: 1px solid #e2e2e2;
  background-color: transparent;
}
.layui-laypage-skip input {
  background: #FFFFFF;
  border: 1px solid #E7EAF1;
  color: #4D4D4D;
  border-radius: 2px;
}
.layui-laypage button {
  background: #F5F8FB;
  border: 1px solid #E7EAF1;
  color: #595959;
  border-radius: 2px;
}
.layui-table-page > div {
  text-align: right;
}
.layui-form-checkbox[lay-skin="primary"] {
  width: 14px;
  height: 14px;
}
.layui-table-view .layui-form-checkbox[lay-skin="primary"] i {
  width: 14px;
  height: 14px;
  background: url(../../images/cultivation/radio-icon.png) no-repeat center;
  background-size: 14px;
}
.layui-table-view .layui-form-checked[lay-skin="primary"] i {
  width: 14px;
  height: 14px;
  background: url(../../images/cultivation/radio-cur.png) no-repeat center;
  background-size: 14px;
}
.layui-icon-ok:before {
  display: none;
}
.layui-table-cell .inputs {
  height: 28px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
}
.layui-table-cell .inputs input {
  width: 70px;
  height: 28px;
  border: none;
  outline: none;
  background-color: transparent;
  padding-left: 5px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.layui-table-cell .inputs input:valid {
  border: none;
}
.layui-table-cell .inputs input:focus {
  border: 1px solid #D5D9E2;
  background-color: #fff;
  border-radius: 4px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .steps-box ul{
  justify-content: center;
}
.main .steps-box ul li span{
  color:rgba(165, 175, 195, 1);
}
.main .item .i-con .course-inform{
  min-height:410px;
}