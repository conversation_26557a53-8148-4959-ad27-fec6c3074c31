body {
  background-color: #f7f8fa;
}
.main .m-top {
  width: 100%;
  height: 57px;
  border-bottom: 1px solid #e8eaf1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: border-box;
}
.main .m-top .back {
  cursor: pointer;
  margin-right: 16px;
  padding-left: 22px;
  background: url(../../images/cultivation/back.png) no-repeat left center;
  background-size: 16px;
  color: #7d92b2;
  font-size: 14px;
}
.main .m-top .title {
  font-size: 16px;
  color: #1d2129;
  padding-left: 6px;
  position: relative;
}
.main .m-top .title::after {
  content: "";
  width: 3px;
  height: 16px;
  border-radius: 2px;
  background-color: #4d88ff;
  position: absolute;
  left: 0;
  top: 3px;
}
.main .m-top span {
  width: 12px;
  height: 12px;
  background: url(../../images/cultivation/icon-right.png) no-repeat center;
  background-size: 12px;
  margin: 0 4px;
}
.main .m-top h3 {
  font-size: 16px;
  color: #1d2129;
}
.main .item {
  padding: 0 30px;
}
.main .item-teacher .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 32px;
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
.main .item-teacher .i-top span {
  padding-left: 8px;
  font-size: 16px;
  line-height: 22px;
  color: #6581ba;
  position: relative;
  display: block;
}
.main .item-teacher .i-top span:after {
  content: "";
  position: absolute;
  left: 0;
  top: 4px;
  background: #4d88ff;
  border-radius: 1px;
  width: 3px;
  height: 14px;
}
.main .item-teacher .i-top .i-submit {
  width: 96px;
  height: 34px;
  background-color: #4d88ff;
  border-radius: 4px;
  border: 1px solid #4d88ff;
  color: #ffffff;
  font-size: 14px;
  overflow: hidden;
  cursor: pointer;
}
.main .item-teacher .i-search {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.main .item-teacher .i-search .i-add {
  width: 86px;
  height: 34px;
  border: 1px solid #4c88ff;
  background-color: #4d88ff;
  border-radius: 4px;
  font-size: 14px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 40px;
  box-sizing: border-box;
  cursor: pointer;
}
.main .item-teacher .i-search .i-add span {
  margin-right: 6px;
  font-size: 24px;
}
.main .item-teacher .i-search .layui-inline {
  margin-bottom: 0;
}
.main .item-teacher .i-search .layui-form-label {
  width: 57px;
  text-align: right;
  font-size: 14px;
  line-height: 34px;
  color: #1d2129;
  padding: 0;
  margin-right: 12px;
}
.main .item-teacher .i-search .layui-input {
  border-color: #d5d9e2;
  border-radius: 4px;
  width: 240px;
  height: 34px;
  font-size: 14px;
}
.main .item-teacher .i-search .layui-input::placeholder {
  color: #86909c !important;
}
.main .item-teacher .i-search .btnSubmit {
  width: 88px;
  height: 34px;
  line-height: 34px;
  padding: 0;
  border: 1px solid #4c88ff;
  margin-left: 40px;
  font-size: 14px;
  color: #4c88ff;
  border-radius: 4px;
  border: 1px solid #4d88ff;
  background-color: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
}
.main .item-teacher .layui-table tbody tr:nth-child(even) {
  background-color: #ffffff;
}
.main .item-teacher .layui-table tbody tr:nth-child(even):hover {
  background-color: #edf2fd;
}
.layui-table-cell {
  padding: 0 17px;
  height: 30px;
}
.layui-table-cell .tmplOpt {
  display: flex;
  align-items: center;
  justify-content: center;
}
.layui-table-cell .tmplOpt h5 {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.layui-table-cell .tmplOpt span.set {
  color: #4c88ff;
  margin-left: 16px;
  cursor: pointer;
}
.layui-table-cell .layui-input {
  width: 240px;
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
}
.layui-table-cell .select-demo-primary {
  width: 90px;
  height: 28px;
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
}
.layui-table-cell .layui-input:focus {
  border: 1px solid #4c88ff !important;
}
.layui-table-cell .edit {
  display: inline-block;
  color: #4c88ff;
  margin-right: 17px;
  cursor: pointer;
}
.layui-table-cell .delet {
  display: inline-block;
  color: #ff5e5e;
  cursor: pointer;
}
.layui-table-view .layui-table th {
  font-size: 14px;
  color: #6581ba;
  height: 44px;
}
.layui-table-view .layui-table td {
  color: #4e5969;
  font-size: 14px;
  height: 44px;
}
.layui-laypage a,
.layui-laypage span {
  border: none;
}
.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  background: #f1f3f6;
  border-radius: 4px;
  color: #484f5d;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #4c85fa;
  border-radius: 4px;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #4c85fa !important;
}
.layui-laypage a:hover {
  color: #4c85fa;
}
.layui-laypage-prev {
  background-color: transparent !important;
}
.layui-laypage-next {
  background-color: transparent !important;
}
.layui-laypage-spr {
  background-color: transparent !important;
}
.layui-laypage-skip {
  background-color: transparent !important;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-laypage-skip input {
  border: 1px solid #e2e2e2;
  background-color: transparent;
}
.layui-laypage-skip input {
  background: #ffffff;
  border: 1px solid #e7eaf1;
  color: #4d4d4d;
  border-radius: 2px;
}
.layui-laypage button {
  background: #f5f8fb;
  border: 1px solid #e7eaf1;
  color: #595959;
  border-radius: 2px;
}
.layui-table-page > div {
  text-align: right;
}
.layui-table-view .layui-form-radio {
  margin-right: 10px !important;
}
.layui-form-radioed > i {
  color: #4c85fa !important;
}
.layui-table-view .layui-form-radio > i {
  margin: 0 4px 0 0;
  font-size: 18px !important;
}
.layui-form-radio * {
  color: #4e5969;
}
.layui-set {
  display: flex;
  flex-wrap: wrap;
}
.layui-set .layui-form-item {
  margin-right: 100px;
}
.layui-set .layui-form-label {
  width: 70px !important;
}
.layui-set .layui-input-block {
  margin-left: 82px !important;
}
.main .item .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 32px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .item .i-top h3 {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #6581ba;
  position: relative;
  display: block;
  margin-right: 24px;
}
.main .item .i-top h3:after {
  content: "";
  position: absolute;
  left: 0;
  top: 1px;
  background: #6581ba;
  border-radius: 2px;
  width: 3px;
  height: 18px;
}
.main .item .i-top .arrow {
  position: relative;
  font-weight: 400;
  width: 12px;
  height: 12px;
  cursor: pointer;
  background: url(../../images/cultivation/arrow-icon.png) no-repeat right center;
  transform: rotate(-80deg);
}
.main .item .i-top .arrow.slide {
  transform: rotate(0);
}
.main .item .i-con .course-inform {
  margin-bottom: 16px;
}
.main .item .i-con .course-inform h4 {
  font-size: 16px;
  line-height: 20px;
  color: #1d2129;
  margin-bottom: 24px;
}
.main .item .i-con .course-inform ul li {
  width: 220px;
  height: 20px;
  margin-bottom: 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  float: left;
  font-size: 16px;
}
.main .item .i-con .course-inform ul li .name {
  color: #1d2129;
  flex-shrink: 0;
}
.main .item .i-con .course-inform ul li .tit {
  color: #4e5969;
  flex: 1;
  margin-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .item .i-con .course-inform ul li.textbook {
  position: relative;
}
.main .item .i-con .course-inform ul li.textbook:hover .tit {
  color: #6581BA;
}
.main .item .i-con .course-inform ul li.textbook:hover #textbookTips {
  display: block;
}
.main .item .i-con .course-inform ul li #textbookTips {
  background-color: #4E5969;
  border-radius: 8px;
  padding: 16px 30px;
  width: 265px;
  box-sizing: border-box;
  color: #ffffff;
  position: absolute;
  left: 80px;
  top: 40px;
  display: none;
  z-index: 99;
  font-size: 14px;
}
.main .item .i-con .course-inform ul li #textbookTips::after {
  content: "";
  border-bottom: 8px solid #4E5969;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  position: absolute;
  top: -8px;
  left: 30px;
}
.main .item .i-con .course-inform ul li #textbookTips .textbook-item {
  display: flex;
  align-items: center;
  line-height: 20px;
}
.main .item .i-con .course-inform ul li #textbookTips .textbook-item:last-child {
  margin-bottom: 0;
}
.main .item .i-con .course-inform ul li #textbookTips .textbook-item h5 {
  width: 80px;
  flex-shrink: 0;
}
.main .item .i-con .course-inform ul li #textbookTips .textbook-con {
  margin-bottom: 16px;
}
.main .item .i-con .course-inform ul li #textbookTips .textbook-con:last-child {
  margin-bottom: 0;
}
.main .item .i-con .course-inform ul li #textbookTips.textbookBot::after {
  content: "";
  border-top: 8px solid #4E5969;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: unset;
  position: absolute;
  bottom: -8px;
  left: 30px;
  top: unset;
}
@media screen and (max-width: 1660px) {
  .main {
    margin: 20px;
  }
  .main .item .i-con .class-box .j-search-item.j-search-class input {
    width: 120px;
  }
}
@media screen and (max-width: 1430px) {
  .main .item .i-con .class-box .j-search input {
    width: 200px;
  }
}
@media screen and (max-width: 1280px) {
  .main .item .i-con .class-box .j-search input {
    width: 160px;
  }
}
.main {
  margin: 20px auto;
  border-radius: 8px;
  min-height: calc(100vh - 40px);
  min-width: 1000px;
  background-color: #fff;
  max-width: 1660px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}