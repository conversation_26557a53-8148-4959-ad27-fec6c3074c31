.header {
	width: 100%;
	overflow: hidden;
	background: #ffffff;
	/* border-bottom: .02rem solid rgba(10, 10, 10, 0.05); */
	font-family: 'PingFangSCMedium';
	position: fixed;
	top: 0;
	z-index: 999;
}

.head-signal {
	width: 100%;
	height: .2rem;
}

.head-con {
	height: .44rem;
	overflow: hidden;
	position: relative;
	color: #2B333B;
}

.head-con:after{
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	border-bottom: 1px solid #EBEBEB;
	transform: scaleY(0.5); -webkit-transform: scaleY(0.5);
	-moz-transform: scaleY(0.5); -o-transform: scaleY(0.5);
} 

.head-return, .head-right {
	display: block;
	overflow: hidden;
	position: absolute;
	bottom: 0;
	top: 0;
	margin: auto;
	z-index: 2;
	height: .24rem;
}

.head-return {
	left: .08rem;
}

.head-right {
	width: .24rem;
	height: .24rem;
	right: .16rem;
}

.head-right img {
	width: .24rem;
	height: .24rem;
}

.head-return img {
	width: .24rem;
	height: .24rem;
	vertical-align: middle;
}

.head-return span {
	font-size: .16rem;
	color: #6581BA;
	vertical-align: middle;
	font-weight: normal;
}

.head-right span {
	font-size: .16rem;
	vertical-align: middle;
	color: #116AE4;
	font-weight: bold;
}

.head-con .head-title {
	width: 100%;
	text-align: center;
	line-height: .44rem;
	font-size: .18rem;
	color: #1D2129;
	display: block;
}

