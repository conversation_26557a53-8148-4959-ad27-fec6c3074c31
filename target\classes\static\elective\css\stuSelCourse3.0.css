body {
  background-color: #f7f8fa;
  font-family: "PingFang SC";
}
.z-main {
  width: 1660px;
  margin: 20px auto;
  background-color: #ffffff;
  overflow: hidden;
  height: calc(100vh - 40px);
  border-radius: 6px;
}
.z-main .z-title {
  height: 60px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e8ebf1;
  justify-content: space-between;
}
.z-main .z-title .left {
  display: flex;
  align-items: center;
}
.z-main .z-title h3 {
  font-size: 16px;
  line-height: 22px;
  color: #1d2129;
  padding-left: 9px;
  position: relative;
  margin-left: 30px;
  font-weight: bold;
}
.z-main .z-title h3::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4d88ff;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 3px;
}
.z-main .z-title #selType {
  display: flex;
  align-items: center;
  padding: 4px;
  background: #F1F3F6;
  border-radius: 4px;
  margin-right: 30px;
}
.z-main .z-title #selType li {
  padding: 3px 12px;
  font-size: 14px;
  color: #4E5969;
  border-radius: 4px;
  line-height: 20px;
  cursor: pointer;
}
.z-main .z-title #selType li.active {
  background: #FFF;
  box-shadow: 0px 0px 4px 0px rgba(77, 136, 255, 0.15);
  color: #4D88FF;
  font-weight: 500;
}
.z-main .selectBox {
  position: relative;
  padding: 24px 20px;
  box-sizing: content-box;
  text-align: center;
}
.z-main .selectBox .selectWeek {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #1D2129;
}
.z-main .selectBox .prevWeek,
.z-main .selectBox .nextWeek {
  float: left;
  width: 24px;
  height: 24px;
  cursor: pointer;
}
.z-main .selectBox .nextWeek {
  background: url('../images/prevnext1.png') no-repeat center;
  background-size: 24px;
}
.z-main .selectBox .nextWeek.disabled {
  opacity: 0.4;
  cursor: not-allowed;
}
.z-main .selectBox .prevWeek {
  background: url('../images/prevnext1.png') no-repeat center;
  background-size: 24px;
  transform: rotate(180deg);
  cursor: pointer;
}
.z-main .selectBox .prevWeek.disabled {
  opacity: 0.4;
  cursor: not-allowed;
}
.z-main .selectBox .week {
  float: left;
  font-size: 16px;
  color: #1d2129;
  margin: 0 14px;
  letter-spacing: 3px;
  cursor: pointer;
  vertical-align: bottom;
  height: 24px;
  line-height: 24px;
}
.z-main .selectBox .week strong {
  font-size: 24px;
  padding: 0 4px;
  font-family: "PingFang SC";
  font-weight: 1000;
}
.z-main .selectBox .week:hover {
  color: #3a4b87;
}
.z-main .selectBox.fixed {
  position: fixed;
  top: 40px;
  width: 100%;
  max-width: 1140px;
  min-width: 840px;
  background: #ffffff;
  z-index: 2;
}
.z-main .selectBox.fixed .headRight {
  right: 0px;
}
.z-main .main-con {
  position: relative;
}
.z-main .main-con .z-search .z-item {
  margin-right: 57px;
}
.z-main .main-con .z-search .z-item:nth-child(4n) {
  margin-right: 32px;
}
.z-main .category {
  position: absolute;
  left: 30px;
  top: 24px;
  display: flex;
  align-items: center;
  height: 24px;
  font-size: 14px;
  color: #86909C;
  z-index: 9;
}
.z-main .category li {
  margin-right: 14px;
  display: flex;
  align-items: center;
}
.z-main .category li span {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  border-radius: 2px;
}
.z-main .category li:nth-child( 2) span {
  background-color: #3EB35A;
}
.z-main .category li:nth-child( 1) span {
  background-color: #4D88FF;
}
.z-main .category li:nth-child( 0) span {
  background-color: extract(#4D88FF, #3EB35A, 0);
}
.z-main .selectMask {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1;
}
.z-main .selectMask .selectList {
  position: absolute;
  top: 146px;
  left: 50%;
  margin-left: -130px;
  width: 246px;
  padding: 12px 12px 8px 12px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0px 2px 12px 0px rgba(175, 187, 204, 0.75);
}
.z-main .selectMask .selectList li {
  margin-bottom: 4px;
  margin-right: 4px;
  width: 46px;
  height: 32px;
}
.z-main .selectMask .selectList li:nth-child(5n) {
  margin-right: 0;
}
.z-main .selectMask .selectList li.curWeek {
  position: relative;
}
.z-main .selectList {
  overflow: hidden;
}
.z-main .selectList li {
  float: left;
  width: 20%;
}
.z-main .selectList li p {
  width: 46px;
  height: 32px;
  margin: 0 auto;
  font-size: 14px;
  border-radius: 4px;
  text-align: center;
  line-height: 32px;
  color: #4E5969;
  cursor: pointer;
}
.z-main .selectList li p.disable {
  opacity: 0.3;
}
.z-main .selectList li p:hover {
  background: #DBEAFF;
}
.z-main .selectList li p.active {
  background: #4d88ff;
  font-weight: bold;
  color: #fff;
}
.z-main .z-table {
  margin: 0 30px 24px;
}
.z-main .z-table table {
  border-collapse: separate;
}
.z-main .z-table .thead {
  overflow: hidden;
}
.z-main .z-table .thead tr td {
  height: 64px;
  flex: 1;
  color: #86909c;
  font-size: 14px;
  background: #F1F3F6;
  min-width: 100px;
  width: calc(100% / 7);
}
.z-main .z-table .thead tr td:first-child {
  width: 108px;
  flex: unset;
  border-top-left-radius: 8px;
}
.z-main .z-table .thead tr td:last-child {
  border-top-right-radius: 8px;
}
.z-main .z-table .thead tr td span {
  display: block;
  text-align: center;
}
.z-main .z-table .thead tr td span.weekdate {
  color: #1D2129;
  font-size: 20px;
  line-height: 28px;
  font-weight: 600;
}
.z-main .z-table .thead tr td span.week {
  color: #86909C;
  font-size: 14px;
  line-height: 20px;
}
.z-main .z-table .thead tr td span.week.today {
  color: #4D88FF;
  font-weight: bold;
}
.z-main .z-table .tbody tr:nth-child(2n) {
  background-color: unset;
}
.z-main .z-table .tbody tr td {
  min-height: 56px;
  flex: 1;
  color: #4e5969;
  font-size: 14px;
  height: 72px;
  box-sizing: border-box;
  border-right: 1px solid #E8EBF1;
  border-bottom: 1px solid #E8EBF1;
}
.z-main .z-table .tbody tr td.line {
  background: url('../images/line.png') repeat-y top center;
}
.z-main .z-table .tbody tr td:first-child {
  width: 108px;
  flex: unset;
  border-left: 1px solid #E8EBF1;
  border-bottom: none;
  flex-direction: column;
}
.z-main .z-table .tbody tr td:first-child span {
  display: block;
  text-align: center;
  width: 100%;
}
.z-main .z-table .tbody tr td:first-child .section {
  color: #4E5969;
  font-size: 14px;
  line-height: 20px;
  font-family: "PingFang SC";
  font-weight: bold;
}
.z-main .z-table .tbody tr td:first-child .time {
  color: #86909C;
  font-size: 12px;
  line-height: 17px;
}
.z-main .z-table .tbody tr td .course {
  height: calc(100% - 16px);
  border-left: 3px solid #3EB35A;
  background: #E8FFEA;
  box-sizing: border-box;
  border-radius: 4px;
  margin: 8px;
  padding: 8px 12px 8px 9px;
  cursor: pointer;
}
.z-main .z-table .tbody tr td .course h5 {
  color: #3EB35A;
  font-family: "PingFang SC";
  font-size: 14px;
  padding-left: 24px;
  background: url('../images/course2.png') no-repeat left 2px;
  background-size: 16px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.z-main .z-table .tbody tr td .course span {
  display: none;
}
.z-main .z-table .tbody tr td .course.selCourse {
  border-left: 3px solid #4D88FF;
  background: #E1EBFF;
  position: relative;
}
.z-main .z-table .tbody tr td .course.selCourse h5 {
  color: #4D88FF;
  background: url('../images/course1.png') no-repeat left 2px;
  background-size: 16px;
}
.z-main .z-table .tbody tr td .course.selCourse span {
  display: block;
  width: 16px;
  height: 16px;
  background: url('../images/arrow-right.png') no-repeat center;
  background-size: 16px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
.z-main .z-table .tbody tr:last-child td:first-child {
  border-bottom: 1px solid #E8EBF1;
  border-bottom-left-radius: 8px;
}
.z-main .z-table .tbody tr:last-child td:last-child {
  border-bottom-right-radius: 8px;
}
.z-main .z-list {
  overflow: hidden;
  padding: 0 30px;
  cursor: pointer;
  height: calc(100vh - 360px);
  overflow-y: auto;
}
.z-main .z-list .z-course {
  padding: 20px 48px;
  margin-bottom: 20px;
  border-radius: 4px;
  background: #F6F7FE;
  position: relative;
  display: flex;
}
.z-main .z-list .z-course .z-course-num {
  position: absolute;
  top: 16px;
  left: -5px;
  width: 32px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  border-radius: 0px 8px 8px 0px;
  background: #E1EBFF;
  font-size: 16px;
  color: #4D88FF;
}
.z-main .z-list .z-course .z-course-num::after {
  content: "";
  border-top: 3px solid #5b92ff;
  border-right: 3px solid #5b92ff;
  border-left: 3px solid transparent;
  border-bottom: 3px solid transparent;
  position: absolute;
  left: 0;
  bottom: -5px;
}
.z-main .z-list .z-course .z-course-mes {
  margin-right: 120px;
  width: 77%;
}
.z-main .z-list .z-course .z-btn {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.z-main .z-list .z-course .z-btn span {
  width: 96px;
  height: 34px;
  border: 1px solid #4d88ff;
  box-sizing: border-box;
  background-color: #4D88FF;
  border-radius: 36px;
  font-size: 14px;
  text-align: center;
  line-height: 32px;
  border-radius: 22px;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.2);
  margin-right: 14px;
  color: #ffffff;
}
.z-main .z-list .z-course .z-btn span.btn-detail {
  background: #fff;
  text-align: center;
  margin-right: 14px;
  color: #4d88ff;
  font-size: 14px;
}
.z-main .z-list .z-course .z-btn span:last-child {
  margin-right: 0;
}
.z-main .z-list .z-course .z-btn .btn-review {
  border-radius: 18px;
  background: #B8D3FF;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  cursor: not-allowed;
  border-color: #B8D3FF;
  color: #ffffff;
}
.z-main .z-list .z-course .z-btn .btn-sel {
  background-color: #4D88FF;
  color: #ffffff;
}
.z-main .z-list .z-course .z-btn .btn-exit {
  display: none;
  border-radius: 22px;
  background: #F76560;
  border-color: #F76560;
  color: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(247, 101, 96, 0.3);
  margin-right: 0;
}
.z-main .z-list .z-course .z-btn .btn-full {
  border-radius: 18px;
  background: #B8D3FF;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  cursor: not-allowed;
  border-color: #B8D3FF;
  color: #ffffff;
}
.z-main .z-list .z-course .z-course-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.z-main .z-list .z-course .z-course-title h3 {
  font-size: 16px;
  color: #1d2129;
  font-weight: 600;
}
.z-main .z-list .z-course .z-course-title span {
  font-size: 14px;
  color: #4d88ff;
  padding-left: 8px;
}
.z-main .z-list .z-course .z-mes {
  color: #1d2129;
  font-size: 14px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 6px;
  margin-bottom: 8px;
  line-height: 20px;
}
.z-main .z-list .z-course .z-mes p {
  margin-right: 24px;
}
.z-main .z-list .z-course .z-mes p span {
  color: #4e5969;
}
.z-main .z-list .z-course .z-intro {
  display: flex;
  align-items: flex-start;
}
.z-main .z-list .z-course .z-intro h5 {
  flex-shrink: 0;
  font-size: 14px;
  color: #1D2129;
}
.z-main .z-list .z-course .z-intro p {
  font-size: 14px;
  color: #4E5969;
  text-align: justify;
}
.z-main .z-list .z-course:hover {
  background: #E1EBFF;
}
.z-main .z-list .z-course:hover::before {
  content: "";
  border-top: 4px solid #4D88FF;
  border-right: 4px solid #4D88FF;
  border-left: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-top-right-radius: 3px;
  position: absolute;
  right: 4px;
  top: 4px;
}
.z-main .z-list .z-course:hover .z-course-num {
  background-color: #B8D3FF;
}
.page-con {
  position: relative;
  overflow: hidden;
}
.page-con .course-total {
  color: #86909C;
  font-size: 14px;
  position: absolute;
  left: 30px;
  top: 20px;
  line-height: 32px;
}
.page-con .course-total span {
  color: #4D88FF;
  padding: 0 4px;
}
.z-tab-search {
  overflow: hidden;
  position: relative;
  margin: 0 80px 20px 80px;
}
.z-tab-search .z-search-switch {
  float: left;
  display: flex;
  align-items: center;
  line-height: 34px;
  color: #1D2129;
  font-size: 14px;
  font-weight: 500;
}
.z-tab-search .z-search-switch .z-switch {
  margin-left: 14px;
  display: flex;
  align-items: center;
}
.z-tab-search .z-search-switch .z-switch .switch {
  width: 28px;
  height: 14px;
  border-radius: 3px;
  background-color: #e0dfdf;
  position: relative;
  margin-right: 8px;
  cursor: pointer;
}
.z-tab-search .z-search-switch .z-switch .switch span {
  position: absolute;
  display: block;
  width: 12px;
  height: 10px;
  top: 2px;
  left: 2px;
  transition: all linear 100ms;
  border-radius: 2px;
  background-color: #fff;
}
.z-tab-search .z-search-switch .z-switch .switch.active {
  background-color: #4d88ff;
}
.z-tab-search .z-search-switch .z-switch .switch.active span {
  left: 14px;
}
.z-tab-search .z-search-switch .z-switch .witchState {
  color: #C9CDD4;
  font-weight: 400;
}
.z-tab-search input {
  display: block;
  float: right;
  width: 290px;
  height: 34px;
  border-radius: 4px;
  border: 1px solid #E5E6EB;
  padding: 0 28px 0 12px;
  box-sizing: border-box;
}
.z-tab-search input::placeholder {
  font-size: 14px;
  color: #86909c;
  font-weight: 300;
}
.z-tab-search img {
  display: block;
  position: absolute;
  right: 12px;
  top: 9px;
  width: 16px;
  height: 16px;
}
.z-search {
  display: flex;
  flex-wrap: wrap;
  padding: 40px 80px 8px;
  box-sizing: border-box;
}
.z-search .z-item-wrap {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  position: relative;
}
.z-search .z-item-wrap::after {
  content: "";
  width: 1px;
  height: calc(100% - 16px);
  background-color: #E8EBF3;
  position: absolute;
  top: 0;
  right: 0;
}
.z-search .z-item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-right: 24px;
  margin-bottom: 16px;
}
.z-search .z-item:nth-child(3) .item-title {
  width: 84px;
}
.z-search .z-item .item-title {
  margin-right: 14px;
  line-height: 34px;
  font-size: 14px;
  color: #1d2129;
  font-weight: 500;
}
.z-search .btn-search {
  width: 80px;
  height: 34px;
  border-radius: 4px;
  background: #4d88ff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  text-align: center;
  line-height: 34px;
  color: #ffffff;
  cursor: pointer;
}
.z-search .search-btn {
  display: block;
  position: relative;
  margin-right: 0;
  width: 128px;
}
.z-search .search-btn button {
  width: 96px;
  height: 34px;
  margin-left: 32px;
  border: none;
  outline: none;
}
.z-search .search-btn button:first-child {
  margin-bottom: 16px;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
}
.z-search .search-btn button:last-child {
  border-radius: 4px;
  border: 1px solid #4D88FF;
  background: #FFF;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.2);
  color: #4D88FF;
  cursor: pointer;
}
.dialog-wrap {
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}
.dialog-wrap .dialog {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  border-radius: 10px;
  overflow: hidden;
}
.dialog-wrap .dialog-title {
  font-size: 16px;
  color: #1d2129;
  height: 56px;
  line-height: 56px;
  border-bottom: 1px solid #e5e6eb;
  text-indent: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.dialog-wrap .dialog-title span {
  width: 20px;
  height: 20px;
  background: url('../images/close1.png') no-repeat center;
  background-size: 20px;
  margin: 17px 24px;
  cursor: pointer;
}
.dialog-wrap .dialog-btn {
  height: 70px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  border-top: 1px solid #E5E6EB;
  background: #FFF;
}
.dialog-wrap .dialog-btn button.sure {
  width: 88px;
  height: 36px;
  outline: none;
  border: none;
  border-radius: 18px;
  background: #4D88FF;
  box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
  color: #ffffff;
  margin-right: 30px;
  cursor: pointer;
}
.dialog-wrap .dialog-btn button.cancel {
  width: 88px;
  height: 36px;
  outline: none;
  border: none;
  border-radius: 18px;
  border: 1px solid #c9cdd4;
  color: #4e5969;
  margin-right: 16px;
  cursor: pointer;
  background-color: #fff;
}
.dialog-wrap.dialog-plan .dialog {
  width: 766px;
}
.dialog-wrap.dialog-plan .dialog .dialog-con {
  overflow: hidden;
  padding: 16px 80px;
  max-height: 500px;
  overflow-y: auto;
}
.dialog-wrap.dialog-plan .dialog .plan {
  padding: 24px 10px;
  border-bottom: 1px dashed #E5E6EB;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
.dialog-wrap.dialog-plan .dialog .plan.active {
  background-color: #E1EBFF;
  border-radius: 4px;
}
.dialog-wrap.dialog-plan .dialog .plan .text {
  flex: 1;
}
.dialog-wrap.dialog-plan .dialog .plan .state {
  width: 48px;
  height: 24px;
  text-align: center;
  line-height: 24px;
  font-size: 12px;
  color: #fff;
  border-radius: 4px;
}
.dialog-wrap.dialog-plan .dialog .plan .state.not-finished {
  background-color: #ffb026;
}
.dialog-wrap.dialog-plan .dialog .plan .state.finished {
  background-color: #f76560;
}
.dialog-wrap.dialog-plan .dialog .plan:last-child {
  border-bottom: unset;
}
.dialog-wrap.dialog-plan .dialog .plan h1 {
  font-size: 16px;
  color: #1D2129;
  margin-bottom: 4px;
}
.dialog-wrap.dialog-plan .dialog .plan p {
  font-size: 14px;
  color: #7A828E;
  margin-bottom: 4px;
  text-align: justify;
}
.dialog-wrap.dialog-course {
  display: none;
}
.dialog-wrap.dialog-course .dialog {
  width: 1661px;
}
.dialog-wrap.dialog-course .dialog .z-list {
  overflow: hidden;
  padding: 0 80px;
  max-height: calc(100vh - 560px);
  overflow-y: auto;
  cursor: pointer;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course {
  padding: 20px 48px;
  margin-bottom: 20px;
  border-radius: 4px;
  background: #F6F7FE;
  position: relative;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-review {
  background-color: #FFB026;
  padding: 0 8px;
  line-height: 20px;
  color: #fff;
  position: absolute;
  top: 20px;
  right: 48px;
  border-radius: 3px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-course-num {
  position: absolute;
  top: 16px;
  left: -5px;
  width: 32px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  border-radius: 0px 8px 8px 0px;
  background: #E1EBFF;
  font-size: 16px;
  color: #4D88FF;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-course-num::after {
  content: "";
  border-top: 3px solid #5b92ff;
  border-right: 3px solid #5b92ff;
  border-left: 3px solid transparent;
  border-bottom: 3px solid transparent;
  position: absolute;
  left: 0;
  bottom: -5px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-course-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-course-title h3 {
  font-size: 16px;
  color: #1d2129;
  font-weight: 600;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-course-title span {
  font-size: 14px;
  color: #4d88ff;
  padding-left: 8px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-mes {
  color: #1d2129;
  font-size: 14px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 8px;
  line-height: 20px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-mes p {
  margin-right: 24px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-mes p span {
  color: #4e5969;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-intro {
  display: flex;
  align-items: flex-start;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-intro h5 {
  flex-shrink: 0;
  font-size: 14px;
  color: #1D2129;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-intro p {
  font-size: 14px;
  color: #4E5969;
  text-align: justify;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course.active {
  background: #E1EBFF;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course.active::before {
  content: "";
  border-top: 4px solid #4D88FF;
  border-right: 4px solid #4D88FF;
  border-left: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-top-right-radius: 3px;
  position: absolute;
  right: 4px;
  top: 4px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course.active .z-course-num {
  background-color: #B8D3FF;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course.z-full {
  cursor: not-allowed;
}
.dialog-wrap.dialog-mes {
  display: none;
}
.dialog-wrap.dialog-mes .dialog {
  width: 408px;
}
.dialog-wrap.dialog-mes .dialog-con {
  padding: 40px 80px;
}
.dialog-wrap.dialog-mes .dialog-con p {
  font-size: 14px;
  color: #4E5969;
  display: flex;
  margin-bottom: 16px;
}
.dialog-wrap.dialog-mes .dialog-con p:last-child {
  margin-bottom: 0;
}
.dialog-wrap.dialog-mes .dialog-con p span {
  width: 98px;
  flex-shrink: 0;
  color: #1D2129;
  margin-right: 4px;
  font-weight: 500;
}
#tipsBox {
  position: fixed;
  left: 50%;
  top: 50%;
  z-index: 99999;
  transform: translateX(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  display: none;
}
#coursePage1 {
  border-top: 1px solid #E5E6EB;
}
#coursePage1 .layui-laypage {
  margin-bottom: 20px;
}
.total-sel-course {
  font-size: 14px;
  color: #86909C;
  margin: 16px 0;
  text-align: right;
}
.total-sel-course span {
  color: #4D88FF;
  padding: 0 4px;
}
@media screen and (max-width: 1700px) {
  .z-main {
    width: 1232px;
  }
  .z-main .main-con .z-search .z-item {
    margin-right: 24px;
  }
  .z-main .z-list {
    overflow: hidden;
    padding: 0 30px;
    cursor: pointer;
    max-height: calc(100vh - 410px);
    overflow-y: auto;
  }
  .z-search {
    align-items: center;
  }
  .dialog-wrap.dialog-course .dialog {
    width: 1323px;
  }
  .z-search .z-item-wrap .z-item:nth-child(1) .item-title,
  .z-search .z-item-wrap .z-item:nth-child(4) .item-title {
    width: 84px;
  }
  .z-search .z-item-wrap .z-item:nth-child(3) .item-title {
    width: auto;
  }
}
.j-search-con.single-box .j-select-year ul.select-course li.not-finished {
  background-image: url(../images/not-finished.png);
}
.j-search-con.single-box .j-select-year ul.select-course li.finished {
  background-image: url(../images/finished.png);
}
.j-search-con.single-box .j-select-year ul.select-course li:hover {
  background-color: #fff;
  color: #4e5969;
  font-weight: normal;
}
.j-search-con.single-box .j-select-year ul.select-course li.active {
  background-color: #E1EBFF;
  color: #4d88ff;
  font-weight: normal;
}
.z-title .z-item-wrap {
  padding-left: 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.z-title .z-item-wrap .z-item {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 57px;
}
.z-title .z-item-wrap .z-item .item-title {
  line-height: 34px;
  font-size: 14px;
  color: #1d2129;
  font-weight: 500;
}
.z-search .z-item:nth-child(3) .item-title {
  width: auto;
}
@media screen and (max-width: 1700px) {
  .z-main .main-con .z-search .z-item {
    margin-right: 38px !important;
  }
  .z-title .z-item-wrap .z-item {
    margin-right: 38px !important;
  }
  .z-search .z-item-wrap .z-item:nth-child(1) .item-title,
  .z-search .z-item-wrap .z-item:nth-child(4) .item-title {
    width: auto;
  }
}
