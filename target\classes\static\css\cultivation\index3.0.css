.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
.hide {
  display: none !important;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  font-size: 14px;
  color: #8f97a8;
}
.hide {
  display: none;
}
body {
  background-color: #f7f8fa;
}
/* .j-search-con {
  position: relative;
  cursor: pointer;
  flex: 1;

  .j-arrow {
    width: 10px;
    height: 10px;
    background: url(../images/drop-down-icon1.png) no-repeat center;
    position: absolute;
    right: 12px;
    top: 10px;
    pointer-events: none;

    &.j-arrow-slide {
      transform: rotate(180deg);
    }
  }

  input {
    width: 100%;
    height: 34px;
    border: 1px solid #d5d9e2;
    border-radius: 4px;
    padding: 0 24px 0 10px;
    box-sizing: border-box;
    font-size: 14px;
    color: #4E5969;
    cursor: pointer;

    &::placeholder {
      color: #8f97a8;
    }
  }

  .j-select-year {
    position: absolute;
    top: 36px;
    left: 0;
    z-index: 999;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #d5d9e2;
    border-radius: 4px;
    display: none;
    background-color: #fff;

    &.slideShow {
      display: block;
    }

    ul {
      li {
        line-height: 32px;
        overflow: hidden;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
        text-align: left;
        cursor: pointer;
        .borDer;
        padding-left: 15px;

        &.active,
        &:hover {
          background: #e1ebff;
        }
      }
    }
  }
} */
.classIpt {
  width: 100px;
  height: 30px;
  border: 1px solid #d5d9e2;
  border-radius: 4px;
  padding: 0 10px;
  box-sizing: border-box;
  font-size: 14px;
  color: #4E5969;
}
.classIpt::placeholder {
  color: #8f97a8;
}
.weekbox {
  display: flex;
  align-items: center;
}
.weekbox span {
  color: #4D88FF;
  font-size: 14px;
  margin-left: 10px;
  cursor: pointer;
}
.selStu {
  color: #4D88FF;
}
.main {
  margin: 20px auto;
  border-radius: 8px;
  min-height: calc(100vh - 40px);
  min-width: 1000px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .wrap-item {
  background-color: #ffffff;
  border-radius: 8px;
  margin-bottom: 10px;
  overflow: hidden;
}
.main .m-top {
  width: 100%;
  height: 57px;
  border-bottom: 1px solid #e8eaf1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top .back {
  width: 60px;
  height: 24px;
  background: url(../../images/cultivation/back-icon.png) no-repeat left center;
  cursor: pointer;
  margin-right: 10px;
}
.main .m-top .title {
  font-size: 16px;
  color: #1d2129;
  padding-left: 9px;
  position: relative;
}
.main .m-top .title::after {
  content: "";
  width: 3px;
  height: 16px;
  background-color: #4d88ff;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 3px;
}
.main .steps-box {
  width: 100%;
  margin: 32px 0;
  text-align: center;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  overflow: hidden;
}
.main .steps-box ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 40px;
  width: 744px;
  margin: 0 auto;
}
.main .steps-box ul li {
  position: relative;
  flex: 1;
}
.main .steps-box ul li em {
  display: block;
  width: 16px;
  height: 16px;
  border: 4px solid #dbeaff;
  border-radius: 50%;
  background-color: #4d88ff;
  color: #ffffff;
  margin: 0 auto 8px;
  position: relative;
}
.main .steps-box ul li em:after {
  content: "";
  position: absolute;
  left: 21px;
  top: -3px;
  width: 159px;
  height: 24px;
  background: url("../../images/cultivation/step-icon4.png") no-repeat center;
}
.main .steps-box ul li:last-child {
  padding-right: 0;
  margin-right: 0;
}
.main .steps-box ul li:last-child em::after {
  display: none;
}
.main .steps-box ul li span {
  font-size: 16px;
  line-height: 20px;
  color: #1d2129;
}
.main .step-box {
  width: 100%;
  height: 72px;
  padding: 24px 0;
  text-align: center;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .step-box ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
}
.main .step-box ul li {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-right: 116px;
  margin-right: 19px;
  position: relative;
  cursor: pointer;
}
.main .step-box ul li:last-child {
  padding-right: 0;
  margin-right: 0;
}
.main .step-box ul li:last-child:after {
  display: none;
}
.main .step-box ul li:after {
  content: "";
  position: absolute;
  right: 0;
  top: 11px;
  width: 100px;
  height: 2px;
  background: #dcdcdc;
}
.main .step-box ul li em {
  width: 24px;
  height: 24px;
  border: 1px solid #999999;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  text-align: center;
  line-height: 23px;
  font-size: 16px;
  color: #999999;
  margin-right: 16px;
  border-radius: 50%;
}
.main .step-box ul li span {
  font-size: 16px;
  line-height: 24px;
  color: #999999;
  font-weight: 400;
}
.main .step-box ul li.cur em {
  background: #4c88ff;
  border: 1px solid #4c88ff;
  color: rgba(255, 255, 255, 0.9);
}
.main .step-box ul li.cur span {
  color: #4c88ff;
  font-weight: 600;
}
.main .step-box ul li.completed:after {
  background: #4c88ff;
}
.main .step-box ul li.completed em {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  background: url(../../images/cultivation/stepper.png) no-repeat center;
  background-size: 24px;
}
.main .step-box ul li.completed em i {
  display: none;
}
.main .step-box ul li.completed span {
  color: #4c88ff;
  font-weight: 600;
}
.main .item {
  padding: 0 30px;
}
.main .item .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 32px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .item .i-top h3 {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #6581ba;
  position: relative;
  display: block;
  margin-right: 24px;
}
.main .item .i-top h3:after {
  content: "";
  position: absolute;
  left: 0;
  top: 1px;
  background: #6581ba;
  border-radius: 2px;
  width: 3px;
  height: 18px;
}
.main .item .i-top .arrow {
  position: relative;
  font-weight: 400;
  width: 12px;
  height: 12px;
  cursor: pointer;
  background: url(../../images/cultivation/arrow-icon.png) no-repeat right center;
  transform: rotate(-80deg);
}
.main .item .i-top .arrow.slide {
  transform: rotate(0);
}
.main .item .i-con {
  overflow: hidden;
  width: 100%;
}
.main .item .i-con .course-inform {
  margin-bottom: 16px;
}
.main .item .i-con .course-inform h4 {
  font-size: 16px;
  line-height: 20px;
  color: #1d2129;
  margin-bottom: 24px;
}
.main .item .i-con .course-inform ul {
  width: 100%;
}
.main .item .i-con .course-inform ul li {
  width: 220px;
  height: 20px;
  margin-bottom: 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  float: left;
  font-size: 16px;
}
.main .item .i-con .course-inform ul li .name {
  color: #1d2129;
  flex-shrink: 0;
}
.main .item .i-con .course-inform ul li .tit {
  color: #4e5969;
  flex: 1;
  margin-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .item .i-con .course-inform ul li.textbook {
  position: relative;
}
.main .item .i-con .course-inform ul li.textbook:hover .tit {
  color: #6581BA;
}
.main .item .i-con .course-inform ul li.textbook:hover #textbookTips {
  display: block;
}
.main .item .i-con .course-inform ul li #textbookTips {
  background-color: #4E5969;
  border-radius: 8px;
  padding: 16px 30px;
  width: 265px;
  box-sizing: border-box;
  color: #ffffff;
  position: absolute;
  left: 80px;
  top: 40px;
  display: none;
  z-index: 99;
  font-size: 14px;
}
.main .item .i-con .course-inform ul li #textbookTips::after {
  content: "";
  border-bottom: 8px solid #4E5969;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  position: absolute;
  top: -8px;
  left: 30px;
}
.main .item .i-con .course-inform ul li #textbookTips .textbook-item {
  display: flex;
  align-items: center;
  line-height: 20px;
}
.main .item .i-con .course-inform ul li #textbookTips .textbook-item:last-child {
  margin-bottom: 0;
}
.main .item .i-con .course-inform ul li #textbookTips .textbook-item h5 {
  width: 80px;
  flex-shrink: 0;
}
.main .item .i-con .course-inform ul li #textbookTips .textbook-con {
  margin-bottom: 16px;
}
.main .item .i-con .course-inform ul li #textbookTips .textbook-con:last-child {
  margin-bottom: 0;
}
.main .item .i-con .course-inform ul li #textbookTips.textbookBot::after {
  content: "";
  border-top: 8px solid #4E5969;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: unset;
  position: absolute;
  bottom: -8px;
  left: 30px;
  top: unset;
}
.main .item .i-con .mutate {
  width: 78px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  flex-wrap: wrap;
}
.main .item .i-con .mutate .up {
  width: 30px;
  height: 30px;
  background: url(../../images/cultivation/switchLeft1.png) no-repeat;
  background-size: 30px;
  margin-bottom: 16px;
  opacity: 0.6;
  cursor: pointer;
}
.main .item .i-con .mutate .down {
  width: 30px;
  height: 30px;
  background: url(../../images/cultivation/switchLeft1.png) no-repeat;
  background-size: 30px;
  transform: rotate(180deg);
  opacity: 0.6;
  cursor: pointer;
}
.main .item .i-con .mutate div:hover {
  opacity: 1;
}
.main .item .i-con .class-box {
  overflow: hidden;
  width: 100%;
}
.main .item .i-con .class-box .cb-top {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}
.main .item .i-con .class-box .cb-top .tit {
  font-size: 16px;
  color: #1d2129;
  font-weight: 600;
}
.main .item .i-con .class-box .cb-top .radio {
  overflow: hidden;
}
.main .item .i-con .class-box .cb-top .radio span {
  float: left;
  padding-left: 20px;
  background: url(../../images/cultivation/radio1.png) no-repeat left center;
  background-size: 14px;
  font-size: 14px;
  color: #717b91;
  margin-right: 36px;
  cursor: pointer;
}
.main .item .i-con .class-box .cb-top .radio span.cur {
  background: url(../../images/cultivation/radio2.png) no-repeat left center;
  background-size: 14px;
}
.main .item .i-con .class-box .cb-top .arrow {
  padding-right: 14px;
  position: relative;
  font-weight: 400;
  font-size: 14px;
  color: #4c88ff;
  cursor: pointer;
}
.main .item .i-con .class-box .cb-top .arrow:after {
  content: "";
  position: absolute;
  right: 0;
  top: 5px;
  width: 10px;
  height: 10px;
  background: url(../../images/cultivation/stow-icon.png) no-repeat right center;
}
.main .item .i-con .class-box .cb-top .switch {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #4e5969;
}
.main .item .i-con .class-box .cb-top .switch .switch-con {
  width: 28px;
  height: 14px;
  border-radius: 4px;
  background-color: #e5e6eb;
  margin-right: 10px;
  position: relative;
  cursor: pointer;
}
.main .item .i-con .class-box .cb-top .switch .switch-con i {
  width: 13px;
  height: 10px;
  margin: 2px;
  position: absolute;
  background-color: #ffffff;
  left: 0;
  transition: all linear 200ms;
  border-radius: 2px;
}
.main .item .i-con .class-box .cb-top .switch .switch-con.active {
  background-color: #4d88ff;
}
.main .item .i-con .class-box .cb-top .switch .switch-con.active i {
  left: 11px;
}
.main .item .i-con .class-box .button {
  border: 1px solid #4c88ff;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 4px;
  width: 88px;
  height: 34px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  color: #4c88ff;
  margin-bottom: 20px;
}
.main .item .i-con .class-box .j-search-item {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 24px;
  position: relative;
  float: left;
  margin-bottom: 20px;
}
.main .item .i-con .class-box .j-search-item .button {
  background: #4d88ff;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 6px;
  width: 92px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
}
.main .item .i-con .class-box .j-search-item .j-search-con {
  width: 240px;
}
.main .item .i-con .class-box .j-search-item.j-search-class input {
  width: 140px;
}
.main .item .i-con .class-box .j-search {
  position: relative;
  display: flex;
  align-items: center;
  /* input {
            width: 240px;
            height: 34px;
            border: 1px solid #d5d9e2;
            border-radius: 4px;
            padding: 0 12px;
            box-sizing: border-box;
            font-size: 14px;
            cursor: pointer;

            &::placeholder {
              color: #8f97a8;
            }
          } */
}
.main .item .i-con .class-box .j-search .j-search-item-wrap {
  flex: 1;
}
.main .item .i-con .class-box .j-search .radio {
  padding-left: 22px;
  background: url(../../images/cultivation/check-icon.png) no-repeat left center;
  background-size: 16px;
  cursor: pointer;
  line-height: 34px;
  font-size: 14px;
  color: #4e5969;
}
.main .item .i-con .class-box .j-search .radio.cur {
  background: url(../../images/cultivation/check-cur.png) no-repeat left center;
  background-size: 16px;
}
.main .item .i-con .class-box .j-search h5 {
  color: #1d2129;
  font-size: 14px;
  margin-right: 14px;
}
.main .item .i-con .class-box .j-search h5 span {
  color: #F76560;
  padding-right: 4px;
  position: relative;
  top: 2px;
}
.main .item .i-con .class-box .j-search button {
  background: #4d88ff;
  border: 1px solid #4d88ff;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 6px;
  margin-left: 24px;
  width: 90px;
  height: 30px;
  color: #ffffff;
  cursor: pointer;
  font-weight: normal;
  margin-bottom: 20px;
}
.main .item .i-con .class-box .j-search .clearSel {
  background: url("../../images/cultivation/icon_clear.png") no-repeat center;
  width: 34px;
  height: 34px;
  float: left;
  cursor: pointer;
  margin-left: 8px;
  display: none;
  margin-bottom: 20px;
}
.main .item .i-con .class-box .j-search .j-select {
  position: absolute;
  top: 36px;
  left: 94px;
  z-index: 9;
  width: 218px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #d5d9e2;
  border-radius: 4px;
  display: none;
}
.main .item .i-con .class-box .j-search .j-select ul {
  background-color: #fff;
}
.main .item .i-con .class-box .j-search .j-select ul li {
  display: block;
  padding: 0 20px;
  overflow: hidden;
  cursor: pointer;
}
.main .item .i-con .class-box .j-search .j-select ul li.active,
.main .item .i-con .class-box .j-search .j-select ul li:hover {
  background: #e1ebff;
}
.main .item .i-con .class-box .j-search .j-select ul li h3 {
  font-size: 14px;
  line-height: 20px;
  color: #4e5969;
  margin-top: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .item .i-con .class-box .j-search .j-select ul li h3 em {
  color: #4c85fa;
}
.main .item .i-con .class-box .j-search .j-select ul li p {
  color: #86909c;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .item .i-con .class-box .j-search .j-select ul li p span:first-child {
  padding-right: 16px;
}
.main .item .i-con .class-box .i-mes {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.main .item .i-con .class-box .i-mes h5 {
  color: #4E5969;
  font-size: 14px;
}
.main .item .i-con .class-box .i-mes h5 em {
  color: #4D88FF;
  padding-right: 4px;
}
.main .item .i-con .class-box .i-mes button {
  padding: 0 20px;
  height: 34px;
  border-radius: 4px;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  background-color: #4d88ff;
  color: #ffffff;
  border: 1px solid #4d88ff;
  cursor: pointer;
  margin-left: 16px;
}
.main .item .i-con .class-box .j-table {
  margin-bottom: 30px;
}
.main .item .i-con .class-box .j-table .stu {
  display: flex;
  align-items: center;
  justify-content: center;
}
.main .item .i-con .class-box .j-table .stu .stuPhoto {
  width: 22px;
  height: 22px;
  background: url("../../images/cultivation/stu-icon.png") no-repeat center;
  background-size: 22px;
  margin-left: 6px;
}
.layui-table-cell {
  padding: 0 17px;
}
.layui-table-cell .edit {
  display: inline-block;
  color: #4c88ff;
  margin-right: 17px;
  cursor: pointer;
}
.layui-table-cell .delet {
  display: inline-block;
  color: #ff5e5e;
  cursor: pointer;
}
div[lay-id=materialTable] .layui-table-cell {
  line-height: 35px;
}
.layui-layer {
  position: fixed;
  left: 50%!important;
  top: 50%!important;
  transform: translate(-50%, -50%) !important;
  border-radius: 10px;
}
.layui-layer-page .layui-layer-content {
  height: auto!important;
}
.layui-table-view .layui-table td[data-field="teacherList"] .layui-table-cell {
  overflow: unset;
}
.layui-table-cell {
  height: auto;
  padding: 0 16px;
}
.layui-table-view .layui-table th {
  font-weight: normal;
  color: #6581BA;
}
.layui-table-cell {
  height: auto;
}
.layui-table-view .layui-table tr th > div {
  margin: 0;
}
.layui-table-view .layui-table td {
  padding: 4px 0;
}
.layui-border {
  border-width: 1px;
  border-style: solid;
  color: #5f5f5f !important;
  width: 110px;
  height: 30px;
  display: inline-block;
  border-color: #E5E6EB;
  border-radius: 4px;
  padding: 0 6px;
}
.layui-border option {
  font-size: 14px;
  line-height: 30px !important;
}
.layui-border option:hover {
  background: #e1ebff;
}
.layui-border option:checked {
  background: #e1ebff;
}
.layui-laypage a,
.layui-laypage span {
  border: none;
}
.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  background: #f1f3f6;
  border-radius: 4px;
  color: #4E5969;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #4c85fa;
  border-radius: 4px;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #4c85fa !important;
}
.layui-laypage a:hover {
  color: #4c85fa;
}
.layui-laypage-prev {
  background-color: transparent !important;
}
.layui-laypage-next {
  background-color: transparent !important;
}
.layui-laypage-spr {
  background-color: transparent !important;
}
.layui-laypage-skip {
  background-color: transparent !important;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-laypage-skip input {
  border: 1px solid #e2e2e2;
  background-color: transparent;
}
.layui-laypage-skip input {
  background: #ffffff;
  border: 1px solid #e7eaf1;
  color: #4d4d4d;
  border-radius: 2px;
}
.layui-laypage button {
  background: #f5f8fb;
  border: 1px solid #e7eaf1;
  color: #595959;
  border-radius: 2px;
}
.layui-table-page > div {
  text-align: right;
}
.layui-table-edit {
  height: 100% !important;
  text-align: left;
}
.layui-table-edit:focus {
  border-color: #4c85fa !important;
}
.layui-table-view .layui-form-checkbox[lay-skin="primary"] i {
  width: 16px;
  height: 16px;
  border-radius: 3px;
}
.layui-table-page {
  text-align: center;
}
.layui-table-view .layui-table td[data-edit]:hover:after {
  border: 1px solid #4d88ff;
}
.layui-table-column {
  height: 46px;
}
.footer button{
  float: right;
  margin-bottom: 20px;
}
.footer button.sure{
  padding: 0 20px;
  height: 34px;
  border-radius: 4px;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  background-color: #4d88ff;
  color: #ffffff;
  border: 1px solid #4d88ff;
  cursor: pointer;
  margin-left: 16px;
}
.footer button.cancel{
  padding: 0 20px;
  height: 34px;
  border-radius: 4px;
  /*box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);*/
  background-color: #ffffff;
  color: #ff0000;
  border: 1px solid #ff0000;
  cursor: pointer;
  margin-left: 16px;
}
@media screen and (max-width: 1660px) {
  .main {
    margin: 20px;
  }
  .main .item .i-con .class-box .j-search-item .j-search-con {
    width: 200px;
  }
}
@media screen and (max-width: 1430px) {
  .main .item .i-con .class-box .j-search-item .j-search-con {
    width: 160px;
  }
}
@media screen and (max-width: 1280px) {
  .main .item .i-con .class-box .j-search-item .j-search-con {
    width: 140px;
  }
}
