@font-face {
  font-family: "iconfont"; /* Project id 4428075 */
  src: url('iconfont.woff2?t=1720513497620') format('woff2'),
       url('iconfont.woff?t=1720513497620') format('woff'),
       url('iconfont.ttf?t=1720513497620') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-biji:before {
  content: "\e636";
}

.icon-zixi:before {
  content: "\e635";
}

.icon-tzsz1:before {
  content: "\e634";
}

.icon-qxsz:before {
  content: "\e630";
}

.icon-tzsz:before {
  content: "\e631";
}

.icon-tkgz:before {
  content: "\e632";
}

.icon-dksz:before {
  content: "\e633";
}

