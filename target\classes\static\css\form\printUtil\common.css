.borDer {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.transforms {
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}

textarea::-webkit-input-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

textarea:-moz-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

textarea:-ms-input-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

.flex {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.clearfixs {
    zoom: 1;
}

.clearfixs:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

body {
    font-family: 'PingFang SC';
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#coursePage {
    text-align: center;
    border-top: 1px solid #C9CDD4;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.layui-laypage {
    margin: 16px 0;
}

.layui-laypage a,
.layui-laypage span,
.layui-laypage input,
.layui-laypage button,
.layui-laypage select {
    border: none;
}

.layui-laypage a,
.layui-laypage span {
    padding: 0 11px;
    margin: 0 5px;
    background: #F1F3F6;
    border-radius: 4px;
    color: #484F5D;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
    background: #4C85FA;
    border-radius: 4px;
}

.layui-laypage input:focus,
.layui-laypage select:focus {
    border-color: #4C85FA !important;
}

.layui-laypage a:hover {
    color: #4C85FA;
}

.layui-laypage-prev {
    background-color: transparent !important;
}

.layui-laypage-next {
    background-color: transparent !important;
}

.layui-laypage-spr {
    background-color: transparent !important;
}

.layui-laypage-skip {
    background-color: transparent !important;
}

.layui-laypage-count {
    background-color: transparent !important;
}

.layui-laypage-skip input {
    border: 1px solid #e2e2e2 !important;
    background-color: transparent !important;
}

.layui-laypage select {
    border: 1px solid #e2e2e2 !important;
}

.layui-laypage button:hover {
    color: #4A7CFE;
}

/* --清除谷歌浏览器下的 search 叉号  */
input::-webkit-search-cancel-button {
    display: none;
}

/* --清除IE下的 search 叉号  */
input[type="search"]::-ms-clear {
    display: none;
}

.layui-table-filter-box .layui-btn {
    float: none;
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    font-size: 12px;
    display: inline-block;
}

.layui-laydate table tbody tr:nth-child(even) td {
    border-right: none;
}

.crumbs-box {
    width: 100%;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    height: 54px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.crumbs-box .back {
    padding-left: 24px;
    background: url(../../../images/form/printUtil/back-icon.png) no-repeat left center;
    cursor: pointer;
    color: #4A7CFE;
    font-weight: 400;
    font-size: 14px;
    width: 64px;
    height: 24px;
    border-right: 1px solid #C9CDD4;
    margin-right: 14px;
    line-height: 24px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.crumbs-box .flow-path {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.crumbs-box .flow-path .name {
    color: #4E5969;
    font-weight: 400;
    font-size: 14px;
}

.crumbs-box .flow-path .name.cur {
    color: #1D2129;
    font-weight: 700;
}

.crumbs-box .flow-path .names {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: #1D2129;
}

.crumbs-box .flow-path .sign {
    margin: 0 9px;
    color: #C9CDD4;
}

.btnType {
    width: 108px;
    height: 32px;
    background: linear-gradient(0, #4A7CFE 7.45%, #769CFF 98.59%);
    background: -webkit-linear-gradient(0, #4A7CFE 7.45%, #769CFF 98.59%);
    border-radius: 24px;
    line-height: 32px;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    text-align: center;
    line-height: 30px;
}

.btnType:hover {
    background: linear-gradient(0, rgba(74, 124, 254, 0.8) 7.45%, rgba(118, 156, 255, 0.8) 98.59%);
    background: -webkit-linear-gradient(0, rgba(74, 124, 254, 0.8) 7.45%, rgba(118, 156, 255, 0.8) 98.59%);
}

.btnType:active {
    background: linear-gradient(0, #769CFF 7.45%, #4A7CFE 98.59%);
    background: -webkit-linear-gradient(0, #769CFF 7.45%, #4A7CFE 98.59%);
}

.btnType.disabled {
    background: #EEEFF2;
    color: #86909C;
}

.btnType.disabled span {
    color: #86909C;
}

.btnType1 {
    border: 1px solid #4A7CFE;
    border-radius: 4px;
}

.btnType1:hover {
    background: #E9F0FF;
    border: 1px solid #4A7CFE;
    border-radius: 4px;
}

.btnType1:active {
    background: #D3E0FF;
    border: 1px solid #4A7CFE;
    border-radius: 4px;
}

.btnType2 {
    background: #4A7CFE;
    border-radius: 30px;
}

.btnType2:hover {
    background: #4064E1;
    border-radius: 30px;
}

.btnType2:active {
    background: #2C56B8;
    border-radius: 30px;
}

.scrollBox::-webkit-scrollbar {
    width: 8px;
    height: 10px;
}

.scrollBox::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background: #DADFE6;
}

.scrollBox::-webkit-scrollbar-track {
    border-radius: 6px;
}

.layui-form-select dl::-webkit-scrollbar {
    width: 8px;
    height: 1px;
}

.layui-form-select dl::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background: #DADFE6;
}

.layui-form-select dl::-webkit-scrollbar-track {
    border-radius: 6px;
}

.layui-form-selected .layui-input {
    border: 1px solid #4A7CFE !important;
}

.layui-input:focus,
.layui-textarea:focus {
    border: 1px solid #4A7CFE !important;
}

.layui-form-select dl dd.layui-select-tips {
    display: none;
}

.mCSB_scrollTools {
    z-index: 999 !important;
}

.mCSB_inside > .mCSB_container {
    margin-right: 0px !important;
}

.mCSB_scrollTools .mCSB_draggerRail {
    background-color: transparent !important;
}

.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
    background: #C9CDD4 !important;
    border-radius: 18px !important;
    width: 6px !important;
}

.mCSB_scrollTools {
    right: 0px !important;
    width: 6px !important;
}

.popup {
    background: #FFFFFF;
    border-radius: 10px;
    display: none;
}

.popup .title {
    height: 60px;
    line-height: 60px;
    font-size: 16px;
    font-weight: bold;
    padding: 0 30px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #F2F2F2;
}

.popup .title .name {
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    color: #131B26;
    text-align: left;
}

.popup .title .close {
    width: 20px;
    height: 20px;
    background: url(../../../images/form/printUtil/close-icon.png) no-repeat center;
    cursor: pointer;
}

.popup .popup-con {
    padding: 30px;
}

.popup .bottom {
    border-top: 1px solid #F2F2F2;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 0;
    padding: 0 30px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.popup .bottom .layui-btn-primary {
    width: 92px;
    height: 36px;
    border-radius: 6px;
    border: 1px solid #8CBBFF;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
    font-size: 14px;
    color: #3A8BFF;
    margin-right: 30px;
}

.popup .bottom .exam-sure {
    width: 92px;
    height: 36px;
    border-radius: 6px;
    background: #3A8BFF;
    text-align: center;
    line-height: 36px;
    font-size: 14px;
    color: #FFFFFF;
    cursor: pointer;
    margin-left: 0;
}

.layui-laydate-footer span[lay-type="date"] {
    color: #3A8BFF !important;
}

.layui-layer {
    border-radius: 10px !important;
}

.no-flex {
    display: block !important;
}

/*6.28*/
.nodata {
    width: 100%;
    height: 200px;
    background: url(../../../images/form/printUtil/no-data.png) no-repeat center;
    position: relative;
}

.nodata p {
    position: absolute;
    left: 50%;
    top: 50%;
    color: #4E5969;
    font-weight: 400;
    font-size: 14px;
    margin-top: 90px;
    margin-left: -44px;
    width: 88px;
    text-align: center;
}

.selectUp .layui-form-select dl {
    top: auto;
    bottom: 36px;
}

.layui-table-view .layui-table {
    width: 100% !important;
}

.layui-table th {
    text-align: -webkit-center !important;
}

.layui-table-cell .correct {
    width: 45px;
    height: 20px;
    border-radius: 15px;
    background: #3EB35A;
    font-size: 14px;
    color: #FFFFFF;
    display: inline-block;
    line-height: 20px;
}

.layui-table-cell .deny {
    background-color: #FFB026;
    width: 45px;
    height: 20px;
    border-radius: 15px;
    font-size: 14px;
    color: #FFFFFF;
    display: inline-block;
    line-height: 20px;
}

.lable-list .lable {
    display: none;
}

.lable-list .lable.cur {
    display: block;
}

.layui-table-box {
    width: 100% !important;
}

.layui-table-view .layui-table th:last-child {
    border-right: none;
}

.layui-select-disabled .layui-disabled {
    border-color: #eee !important;
    background-color: #eee !important;
}

.sel {
    float: left;
    margin-right: 40px;
    margin-bottom: 20px;
    height: 32px;
    line-height: 32px;
}

.sel em {
    float: left;
    font-size: 14px;
    color: #474C59;
}

.sel .select-input {
    float: left;
    margin-left: 10px;
    margin-right: 10px;
    width: 90px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #D4D6D9;
    background-color: #fff;
    position: relative;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    cursor: pointer;
}

.sel .select-input i {
    position: absolute;
    top: 9px;
    right: 9px;
    width: 12px;
    height: 12px;
    background: url(../../../images/form/printUtil/down-icon.png) no-repeat center;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}

.sel .select-input .name {
    font-size: 14px;
    color: #b8bec7;
    padding-left: 10px;
    padding-right: 20px;
    line-height: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.sel .select-input .name.ckd {
    color: #1D2129;
}

.sel .select-input.clicked i {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
}

.sel .select-input.clicked em {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
}

.sel .select-input.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
}

.sel .select-input .select-dropdown {
    width: inherit;
    max-height: 320px;
    overflow: auto;
    left: -1px;
    margin: 5px 0;
    padding: 6px 0;
    background-color: #fff;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
    border-radius: 8px;
    position: absolute;
    transform: translate(0, -50px);
    -webkit-transform: translate(0, -50px);
    z-index: 900;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
    z-index: -1;
}

.sel .select-input .select-dropdown .search {
    width: 172px;
    height: 36px;
    background: #F5F7FA;
    border-radius: 18px;
    margin: 11px auto;
}

.sel .select-input .select-dropdown .search input {
    border: none;
    width: 142px;
    background: transparent;
    height: 36px;
    line-height: 36px;
    padding-left: 14px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    float: left;
}

.sel .select-input .select-dropdown .search span {
    cursor: pointer;
    float: left;
    width: 16px;
    height: 16px;
    background: url(../../../images/form/printUtil/search-icon.png) no-repeat center;
    margin-top: 10px;
}

.sel .select-input .select-dropdown .dropdown-lists {
    padding: 6px 0;
}

.sel .select-input .select-dropdown .dropdown-lists li {
    margin: 0;
    line-height: normal;
    line-height: 40px;
    padding: 0 20px;
    clear: both;
    color: #131B26;
    font-size: 14px !important;
    list-style: none;
    cursor: pointer;
    text-align: left;
}

.sel .select-input .select-dropdown .dropdown-lists li span {
    display: block;
    padding-left: 27px;
    background: url(../../../images/form/printUtil/check-icon.png) no-repeat left center;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
}

.sel .select-input .select-dropdown .dropdown-lists li:hover {
    background: #F5F7FA;
}

.sel .select-input .select-dropdown .dropdown-lists li.cur {
    color: #616EE6;
}

.sel .select-input .select-dropdown .dropdown-lists li.cur span {
    background: url(../../../images/form/printUtil/checked-icon.png) no-repeat left center;
}

.sel .select-input .select-dropdown .dropdown-list li {
    margin: 0;
    line-height: normal;
    padding: 0 16px;
    clear: both;
    color: #131B26;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
}

.sel .select-input .select-dropdown .dropdown-list li:hover {
    background-color: #F5F7FA;
}

.sel .select-input .select-dropdown .dropdown-list li.cur {
    color: #616EE6;
}

/*add*/
.sel .select-input .select-dropdown .all-selects {
    line-height: 17px;
    margin-bottom: 4px;
    height: 17px;
    padding: 0 14px;
    font-size: 12px;
    color: #6B89B3;
    cursor: pointer;
}

/*nadd*/
.sel .select-input .select-dropdown .dropdown-lists.person-drop {
    padding: 0;
}

.sel .select-input .select-dropdown .dropdown-lists.person-drop li {
    line-height: normal;
    padding-top: 10px;
    padding-bottom: 10px;
    position: relative;
}

.sel .select-input .select-dropdown .dropdown-lists.person-drop li span {
    line-height: 20px;
    font-size: 14px;
    color: #131B26;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    margin-bottom: 2px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.sel .select-input .select-dropdown .dropdown-lists.person-drop li p {
    position: absolute;
    top: 10px;
    right: 10px;
    height: 22px;
    line-height: 22px;
    padding: 0 10px;
    background: #E1EBFF;
    font-size: 12px;
    color: #4d88ff;
    font-weight: normal;
    border-radius: 4px;
}

.sel .select-input .select-dropdown .single_select li.cur span,
.sel .select-input .select-dropdown .single_select li span {
    background: none;
}
