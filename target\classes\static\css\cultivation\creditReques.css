#creditRequesDialog,
#courseScoreDialog {
  width: 990px;
  height: 737px;
}
#creditRequesDialog .major-con,
#courseScoreDialog .major-con {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 22px;
}
#creditRequesDialog .major-con ul,
#courseScoreDialog .major-con ul {
  display: flex;
  align-items: center;
}
#creditRequesDialog .major-con ul li,
#courseScoreDialog .major-con ul li {
  color: #1D2129;
  margin-right: 24px;
}
#creditRequesDialog .major-con ul li span,
#courseScoreDialog .major-con ul li span {
  color: #4E5969;
}
#creditRequesDialog .major-con .set-score,
#courseScoreDialog .major-con .set-score {
  color: #4D88FF;
  padding-left: 20px;
  background: url('../../images/cultivation/set.png') no-repeat left center;
  background-size: 16px;
  cursor: pointer;
}
#courseScoreDialog {
  display: none;
}
#oneCorrespondence {
  width: 96px;
  height: 34px;
  border-radius: 4px;
  background: #4D88FF;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  color: #fff;
  border: none;
  outline: none;
  cursor: pointer;
  margin-bottom: 20px;
}
.marker {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}
.dialog {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: #ffffff;
    border-radius: 10px;
}
.dialog .dialog-title {
    height: 54px;
    border-bottom: 1px solid #e5e6eb;
    color: #1d2129;
    font-size: 16px;
    line-height: 54px;
    text-indent: 30px;
    position: relative;
}
.dialog .dialog-title .close-btn {
    width: 20px;
    height: 20px;
    background: url('../../images/cultivation/close.png') no-repeat center;
    background-size: 20px;
    position: absolute;
    right: 24px;
    top: 17px;
    cursor: pointer;
}
.dialog .dialog-con {
    padding: 40px 80px;
}
.dialog .dialog-footer {
    height: 70px;
    border-top: 1px solid #e5e6eb;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.dialog .dialog-footer button {
    width: 88px;
    height: 34px;
    border: 1px solid #c9cdd4;
    border-radius: 18px;
    color: #4e5969;
    font-size: 14px;
    background-color: #ffffff;
    cursor: pointer;
}
.dialog .dialog-footer button:last-child {
    background: #4d88ff;
    border-color: #4d88ff;
    box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
    color: #ffffff;
    margin: 0 30px 0 16px;
}
.dialog .layui-table-view {
    border-right: 'none';
}
.dialog .layui-table-view .layui-table td:last-child,
.dialog .layui-table-view .layui-table th:last-child {
    border-right: 1px solid #ffffff;
}
.dialog .layui-table-total .layui-table {
    background: #F1F3F6;
}
.dialog .layui-table-total .layui-table tbody tr td:first-child {
    font-weight: bold;
}
.layui-table-edit {
    text-align: center !important;
}
.layui-table-edit:focus {
    border-color: #4d88ff !important;
}
.layui-table-view .layui-table td[data-edit]:hover:after {
    border: 1px solid #4d88ff;
}
.layui-table-hover {
    background-color: #E1EBFF !important;
}
.layui-table-body tr:nth-child(2n) {
    background: #fafbfc;
}