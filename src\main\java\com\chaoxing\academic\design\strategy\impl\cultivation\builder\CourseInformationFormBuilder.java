package com.chaoxing.academic.design.strategy.impl.cultivation.builder;

import cn.hutool.core.bean.BeanUtil;
import com.chaoxing.academic.design.strategy.bo.DataPushBO;
import com.chaoxing.academic.design.strategy.impl.cultivation.processor.WeekRangeProcessor;
import com.chaoxing.academic.entity.form.cultivation.CourseInformationForm;
import com.chaoxing.academic.entity.form.cultivation.CourseManageForm;
import com.chaoxing.academic.entity.form.cultivation.subform.Kkgl_jc;
import com.chaoxing.academic.entity.form.cultivation.subform.Kksz;
import com.chaoxing.academic.entity.form.cultivation.subform.Kkxxb_jcxx;
import com.chaoxing.academic.utils.MyUtils;
import com.chaoxing.academic.utils.StringUtils;
import com.chaoxing.academic.utils.form.FormUtils;
import com.chaoxing.form.pojo.IdName;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 课程信息表单构建器
 * 专门负责CourseInformationForm的创建和数据填充
 * 核心功能：按照List<Kksz> kksz的kkgl_zc按逗号分割填充到CourseInformationForm
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Component
@Slf4j
public class CourseInformationFormBuilder {
    
    @Autowired
    private WeekRangeProcessor weekRangeProcessor;
    
    /**
     * 构建课程信息表单
     * 核心方法：处理kkgl_zc按逗号分割的逻辑
     * 
     * @param courseManageForm 课程管理表单
     * @param kksz 开课设置
     * @param dataPushBO 数据推送对象
     * @param groupName 组名
     * @param groupNo 组号
     * @return 构建完成的课程信息表单
     */
    public CourseInformationForm buildCourseInformation(
            CourseManageForm courseManageForm, 
            Kksz kksz, 
            DataPushBO dataPushBO, 
            String groupName, 
            String groupNo) {
        
        CourseInformationForm form = new CourseInformationForm();
        
        // 设置基础信息
        setBasicInfo(form, courseManageForm, dataPushBO, groupName, groupNo);
        
        // 设置开课设置相关信息（包含周次处理）- 这是核心逻辑
        setKkszInfo(form, kksz, dataPushBO.getDeptId());
        
        // 设置教材信息
        setMaterialInfo(form, courseManageForm);
        
        // 设置其他信息
        setOtherInfo(form, courseManageForm);
        
        return form;
    }
    
    /**
     * 设置基础信息
     */
    private void setBasicInfo(CourseInformationForm form, CourseManageForm courseManageForm, 
                             DataPushBO dataPushBO, String groupName, String groupNo) {
        form.setKkxxb_kkxq(courseManageForm.getKkgl_kkxq())
            .setKkxxb_kkxqxq(courseManageForm.getKkgl_kkxqxq())
            .setKkxxb_kkbm(courseManageForm.getKkgl_kkyxyx())
            .setKkxxb_kkjys(courseManageForm.getKkgl_kkjys())
            .setKkxxb_zymc(courseManageForm.getKkgl_zymc())
            .setKkxxb_jxbrs(courseManageForm.getKkgl_jxbrs())
            .setKkxxb_zybh(courseManageForm.getKkgl_zybh())
            .setKkxxb_njnj(courseManageForm.getKkgl_nj())
            .setKkxxb_kcmc(courseManageForm.getKkgl_kcmc())
            .setKkxxb_kcbh(courseManageForm.getKkgl_kcbh())
            .setKkxxb_xf(courseManageForm.getKkgl_xf() != null ? Float.valueOf(courseManageForm.getKkgl_xf()) : null)
            .setKkxxb_kcxz(courseManageForm.getKkgl_kcxz())
            .setKkxxb_ksxs(courseManageForm.getKkgl_ksxs())
            .setKkxxb_jxbzclx(courseManageForm.getKkgl_jxbzclx())
            .setKkxxb_jxbzc(courseManageForm.getKkgl_jxbzc())
            .setKkxxb_jxbzcbh(courseManageForm.getKkgl_jxbzcbh())
            .setKkxxb_sfxzbcb(courseManageForm.getKkgl_sfxzbcb())
            .setKkxxb_jxbbzid(courseManageForm.getKkgl_jxbbh())
            .setKkxxb_sfcsjjxhj(courseManageForm.getKkgl_sfcsjjxhj())
            .setKkxxb_lilunxs(courseManageForm.getKkgl_lilunxs())
            .setKkxxb_shijixs(courseManageForm.getKkgl_shijixs())
            .setKkxxb_shangjixs(courseManageForm.getKkgl_shangjixs())
            .setKkxxb_shiyanxs(courseManageForm.getKkgl_shiyanxs())
            .setKkxxb_qtxs(courseManageForm.getKkgl_qtxs())
            .setKkxxb_zongxs(courseManageForm.getKkgl_zongxs())
            .setKkxxb_kkxs(courseManageForm.getKkgl_kkxs())
            .setKkxxb_sfxk(courseManageForm.getKkgl_sfxk())
            .setKkxxb_kxxb(courseManageForm.getKkgl_kxxb())
            .setKkxx_xxrs(courseManageForm.getKkgl_xxrs())
            .setKkxxb_kkgldataid(String.valueOf(dataPushBO.getIndexID()))
            .setKkxxb_jxbbzmc(groupName)
            .setKkxxb_jxbbzid(groupNo)
            .setKkxxb_kcxs(1)
            .setKkxxb_kkyxyx(courseManageForm.getKkgl_zyssxb())
            .setKkxxb_bz(courseManageForm.getKkgl_bz())
            .setKkxxb_xz(courseManageForm.getKkgl_xz())
            .setKkxxb_kkxqjc(courseManageForm.getKkgl_kkxqjc())
            .setJw_jxjhformUserId(courseManageForm.getKkgl_jxbbzid())
            .setJw_pyfaformUserId(courseManageForm.getJw_pyfaformUserId())
            .setKkxxb_sfpke(FormUtils.YES)
            .setKkxxb_zyfx(courseManageForm.getKkgl_zyfx());
    }
    
    /**
     * 设置开课设置相关信息，包含周次处理逻辑
     * 核心功能：按照kkgl_zc按逗号分割填充到CourseInformationForm
     */
    private void setKkszInfo(CourseInformationForm form, Kksz kksz, String deptId) {
        if (!BeanUtil.isNotEmpty(kksz, "")) {
            return;
        }
        
        // 处理周次信息 - 这是用户特别要求的核心逻辑
        // 按照List<Kksz> kksz的kkgl_zc按逗号分割填充到CourseInformationForm
        WeekRangeProcessor.WeekRangeInfo weekInfo = weekRangeProcessor.processWeekRange(kksz.getKkgl_zc());
        form.setKkxxb_zc(weekInfo.getWeekRange())
            .setKkxxb_jxzs(weekInfo.getTeachingWeeksStr());
        
        // 设置其他开课设置信息
        form.setKkxxb_zhouxs(kksz.getKkgl_zks() != null ? kksz.getKkgl_zks() : "0")
            .setKkxxb_lpjc(kksz.getKkgl_lpjc() != null ? String.valueOf(kksz.getKkgl_lpjc()) : "0")
            .setKkxxb_ltgz(kksz.getKkgl_lpgz())
            .setKkxxb_skjsxm(kksz.getKkgl_skjs())
            .setKkxxb_zjxm(kksz.getKkgl_zj())
            .setKkxxb_jslx(kksz.getKkgl_jslx())
            .setKkxxb_jsmc(kksz.getKkgl_jsmc())
            .setKkxxb_jsbh(kksz.getKkgl_jsbh())
            .setKkxxb_sflcj(kksz.getKkgl_sflcj())
            .setKkxxb_cjlrjs(kksz.getKkgl_cjlrjs());
        
        // 处理教师信息（简化版本）
        setSimpleTeacherInfo(form, kksz);
    }
    
    /**
     * 设置简化的教师信息
     */
    private void setSimpleTeacherInfo(CourseInformationForm form, Kksz kksz) {
        // 简化处理教师信息
        List<IdName> teacherList = kksz.getKkgl_skjs();
        if (MyUtils.isNotEmpty(teacherList)) {
            // 简单处理，提取教师名称
            List<String> teacherNames = new ArrayList<>();
            for (IdName teacher : teacherList) {
                if (StringUtils.isNotBlank(teacher.getName())) {
                    teacherNames.add(teacher.getName());
                }
            }
            form.setKkxxb_skjs(String.join(",", teacherNames));
        }
    }
    
    /**
     * 设置教材信息
     */
    private void setMaterialInfo(CourseInformationForm form, CourseManageForm courseManageForm) {
        if (MyUtils.isNotEmpty(courseManageForm.getKkgl_jc())) {
            List<Kkxxb_jcxx> materialList = new ArrayList<>();
            for (Kkgl_jc material : courseManageForm.getKkgl_jc()) {
                materialList.add(new Kkxxb_jcxx()
                    .setKkxxb_isbn(material.getKkgl_isbn())
                    .setKkxxb_jch(material.getKkgl_jch())
                    .setKkxxb_jcmc(material.getKkgl_jcmc()));
            }
            form.setKkxxb_jcxx(materialList);
        }
    }
    
    /**
     * 设置其他信息
     */
    private void setOtherInfo(CourseInformationForm form, CourseManageForm courseManageForm) {
        form.setKkxxb_ssxq(courseManageForm.getKkgl_ssxq())
            .setKkxxb_kcshx(courseManageForm.getKkgl_kcshx())
            .setKkxxb_kcfl(courseManageForm.getKkgl_kcfl())
            .setKkxxb_xklb(courseManageForm.getKkgl_xklb())
            .setKkxxb_sfzyhxk(courseManageForm.getKkgl_sfzyhxk())
            .setKkxxb_sfxskc(courseManageForm.getKkgl_sfxskc())
            .setKkxxb_xskcwz(courseManageForm.getKkgl_xskcwz())
            .setKkxxb_sfszsf(courseManageForm.getKkgl_sfszsf())
            .setKkxxb_kczy(courseManageForm.getKkgl_kczy())
            .setKkxxb_xnfzks(courseManageForm.getKkgl_xnfzks())
            .setKkxxb_xnfzxm(courseManageForm.getKkgl_xnfzxm())
            .setKkxxb_syxm(courseManageForm.getKkgl_syxm())
            .setKkxxb_sxxm(courseManageForm.getKkgl_sxxm())
            .setKkxxb_sxixm(courseManageForm.getKkgl_sxixm())
            .setKkxxb_sfkzrtkc(courseManageForm.getKkgl_xfkzrtkc())
            .setKkxxb_skfs(courseManageForm.getKkgl_skfs())
            .setKkxxb_zsh(courseManageForm.getKkgl_zsh())
            .setKkxxb_xgjsmc(courseManageForm.getKkgl_xgjsmc());
    }
}
