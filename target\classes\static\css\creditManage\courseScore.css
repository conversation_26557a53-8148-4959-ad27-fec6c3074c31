.main {
  min-width: 1200px;
}
.main .top {
  width: 100%;
  height: 60px;
  border-bottom: 1px solid #E8EBF1;
  position: relative;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  align-items: center;
}
.main .top .title {
  padding-left: 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 36px;
}
.main .top .title .back {
  padding-left: 22px;
  background: url(../../images/creditManage/back-icon.png) no-repeat left center;
  background-size: 16px;
  color: #7D92B3;
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
}
.main .top .title .levelone {
  padding-left: 9px;
  position: relative;
  color: #1D2129;
  font-weight: 700;
  font-size: 16px;
  margin-right: 6px;
}
.main .top .title .levelone:after {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
}
.main .top .title .icon {
  width: 12px;
  height: 12px;
  background: url(../../images/creditManage/arrow-right.png) no-repeat center;
  background-size: 12px;
  margin-right: 6px;
}
.main .top .title .leveltwo {
  color: #1D2129;
  font-weight: 700;
  font-size: 16px;
}
.main .top .btn {
  position: absolute;
  top: 17px;
  right: 28px;
  width: 116px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 14px;
  color: #FFFFFF;
  background: #4D88FF;
  box-shadow: 0px 0px 10px #4D88FF;
  border-radius: 4px;
}
.main .top h4 {
  position: relative;
  color: #1D2129;
  font-size: 16px;
  margin-left: 30px;
  padding-left: 9px;
  font-weight: bold;
}
.main .top h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}
.main .form-con {
  display: flex;
  font-size: 14px;
  padding: 20px 0 4px;
  position: relative;
}
.main .form-con::after {
  content: "";
  height: 1px;
  background-color: #E8EBF1;
  position: absolute;
  bottom: 0;
  left: 30px;
  right: 30px;
}
.main .form-con .form-btn {
  width: 166px;
  position: relative;
}
.main .form-con .form-btn::after {
  content: "";
  width: 1px;
  height: 84px;
  background: #E8EBF1;
  position: absolute;
  left: 0px;
  top: 0px;
}
.main .form-con .form-btn .btn {
  width: 104px;
  height: 34px;
  background: #ffffff;
  border-radius: 4px;
  margin: 0 auto;
  display: block;
  cursor: pointer;
}
.main .form-con .form-btn .btn.btn-search {
  background: #4D88FF;
  border: 1px solid #4D88FF;
  color: #ffffff;
  margin-bottom: 16px;
}
.main .form-con .form-btn .btn.btn-reset {
  border: 1px solid #4D88FF;
  color: #4D88FF;
}
.main .tab-con {
  overflow: hidden;
  margin: 0 30px;
}
.main .tab-con .no-data {
  width: 88px;
  height: 88px;
  margin: 215px auto 0;
}
.main .tab-con .tab-btn {
  display: flex;
  justify-content: flex-start;
  font-size: 14px;
  margin: 20px 0;
}
.main .tab-con .tab-btn .set {
  height: 34px;
  display: flex;
  align-items: center;
  color: #4D88FF;
  cursor: pointer;
}
.main .tab-con .tab-btn .set img {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.main .tab-con .tab-btn .opt-btn {
  overflow: hidden;
  display: flex;
}
.main .tab-con .tab-btn .opt-btn .line {
  width: 1px;
  height: 32px;
  background: #E5E6EB;
  margin: 0 16px;
}
.main .tab-con .tab-btn .opt-btn .btn {
  cursor: pointer;
  margin-right: 16px;
}
.main .tab-con .tab-btn .opt-btn .btn {
  padding: 0 20px;
  border: 1px solid #4d88ff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.2);
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  height: 34px;
  border-radius: 4px;
  line-height: 32px;
  font-size: 14px;
  color: #4d88ff;
  cursor: pointer;
  margin-right: 23px;
}
.main .tab-con .tab-btn .opt-btn .btn-import {
  width: 104px;
  height: 34px;
  background: #4D88FF;
  border-radius: 4px;
  color: #FFFFFF;
  text-align: center;
  line-height: 34px;
}
.main .tab-con .tab-btn .opt-btn .btn-score {
  width: 132px;
  height: 34px;
  background: #4D88FF;
  border-radius: 4px;
  color: #FFFFFF;
  text-align: center;
  line-height: 34px;
}
.main .tab-con .tab-btn .opt-btn .btn-class {
  width: 146px;
  height: 34px;
  border: 1px solid #4D88FF;
  border-radius: 4px;
  color: #4C88FF;
  text-align: center;
  line-height: 34px;
}
.main .tab-con .tab-btn .opt-btn .btn-export,
.main .tab-con .tab-btn .opt-btn .btn-del {
  width: 134px;
  height: 34px;
  border: 1px solid #4D88FF;
  border-radius: 4px;
  text-align: center;
  line-height: 34px;
  color: #4C88FF;
}
.oprate-table div,
.oprates-table div {
  margin: 0 8px;
  color: #4C88FF;
  font-size: 14px;
  cursor: pointer;
}
.main .tab-con .tab-btn .opt-btn .btn-del {
  margin-left: 16px;
}
.main .course-table {
  margin-bottom: 20px;
}
.layui-form {
  flex: 1;
}
.layui-form .layui-form-item {
  display: flex;
  flex-wrap: wrap;
}
.layui-form .layui-form-item .layui-inline {
  margin-bottom: 16px;
  width: 32%;
  display: flex;
}
.layui-form .layui-form-item .layui-input-inline {
  width: unset;
  min-width: 190px;
  flex: 1;
}
.layui-form .layui-form-label {
  color: #1D2129;
  padding: 7px 14px;
  width: 70px;
  text-align: right;
}
.layui-form .layui-input {
  height: 34px;
  border-radius: 4px;
  border-color: #E5E6EB;
}
.layui-form .layui-input::placeholder {
  color: #86909C;
}
.layui-form .layui-form-select dl {
  border: none;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.layui-form .layui-form-select dl dd {
  color: #4E5969;
}
.layui-form .layui-form-select dl dd.layui-this {
  background: #E1EBFF;
  color: #4D88FF;
}
.layui-form .layui-form-select dl dd.layui-select-tips {
  color: #999;
}
.layui-form .layui-form-select .layui-edge {
  background: url(../../images/creditManage/down-icon.png) no-repeat center;
  border: none;
  width: 10px;
  height: 10px;
  margin-top: -5px;
}
.layui-form.layui-form-stu .layui-form-item .layui-inline {
  width: 24%;
}
.layui-form.layui-form-stu .layui-form-item .layui-input-inline {
  width: unset;
  min-width: unset;
  flex: 1;
}
.dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}
.dialog .dialog-title {
  height: 56px;
  line-height: 56px;
  font-size: 16px;
  color: #1D2129;
  text-indent: 30px;
  border-bottom: 1px solid #E5E6EB;
}
.dialog .dialog-set {
  width: 601px;
  background-color: #FFFFFF;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 10px;
}
.dialog .dialog-set .dialog-con {
  padding: 0 70px 26px;
  overflow: hidden;
  max-height: 732px;
  height: auto;
  overflow-y: auto;
  box-sizing: border-box;
  /*复选框*/
}
.dialog .dialog-set .dialog-con .aa {
  overflow: visible;
  height: 732px;
}
.dialog .dialog-set .dialog-con .title {
  display: flex;
  align-items: center;
  margin-top: 32px;
  margin-bottom: 24px;
  position: relative;
}
.dialog .dialog-set .dialog-con .title h3 {
  color: #6581BA;
  font-size: 16px;
  margin-right: 16px;
}
.dialog .dialog-set .dialog-con .title .notes {
  width: 16px;
  height: 16px;
  background: url(../../images/creditManage/notes.png) no-repeat center;
}
.dialog .dialog-set .dialog-con .title .tips {
  height: 39px;
  background: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0px 0px 10px rgba(113, 119, 149, 0.3);
  padding: 0 24px 0 33px;
  position: fixed;
  top: 123px;
  left: 139px;
  white-space: nowrap;
  line-height: 39px;
  display: none;
}
.dialog .dialog-set .dialog-con .title .tips::after {
  content: "";
  width: 4px;
  height: 4px;
  background: #4080FF;
  position: absolute;
  left: 24px;
  top: 17.5px;
}
.dialog .dialog-set .dialog-con .title .tips::before {
  content: "";
  width: 12px;
  height: 6px;
  background: url(../../images/creditManage/icon-arrow.png) no-repeat center;
  position: absolute;
  top: -6px;
  left: 45px;
}
.dialog .dialog-set .dialog-con .title .tips h4 {
  font-size: 14px;
  color: #393751;
}
.dialog .dialog-set .dialog-con .secondary-title {
  color: #1D2129;
  line-height: 20px;
  margin-bottom: 14px;
}
.dialog .dialog-set .dialog-con .field {
  overflow: hidden;
}
.dialog .dialog-set .dialog-con .field .item-field {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
}
.dialog .dialog-set .dialog-con .field .item-field input {
  width: 240px;
  height: 34px;
  background: #FFFFFF;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  margin-right: 40px;
  padding: 0 10px;
  box-sizing: border-box;
  font-size: 14px;
}
.dialog .dialog-set .dialog-con .field .item-field input::placeholder {
  color: #86909C;
  font-weight: normal;
}
.dialog .dialog-set .dialog-con .field .item-field .item-del {
  width: 16px;
  height: 16px;
  background: url(../../images/creditManage/delet-icon.png) no-repeat center;
  background-size: 16px;
  margin-right: 20px;
  cursor: pointer;
}
.dialog .dialog-set .dialog-con .field .item-field .item-add {
  width: 16px;
  height: 16px;
  background: url(../../images/creditManage/add-icon.png) no-repeat center;
  background-size: 16px;
  margin-right: 20px;
  cursor: pointer;
  display: none;
}
.dialog .dialog-set .dialog-con .field .item-field:last-child .item-add {
  display: block;
}
.dialog .dialog-set .dialog-con .zero-add {
  overflow: hidden;
}
.dialog .dialog-set .dialog-con .zero-add .item-field {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  height: 34px;
}
.dialog .dialog-set .dialog-con .zero-add .item-field input {
  width: 240px;
  height: 34px;
  background: #FFFFFF;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  margin-right: 40px;
  padding: 0 10px;
  box-sizing: border-box;
  font-size: 14px;
}
.dialog .dialog-set .dialog-con .zero-add .item-field input::placeholder {
  color: #86909C;
  font-weight: normal;
}
.dialog .dialog-set .dialog-con .zero-add .item-field .item-del {
  width: 16px;
  height: 16px;
  background: url(../../images/creditManage/delet-icon.png) no-repeat center;
  background-size: 16px;
  margin-right: 20px;
  cursor: pointer;
}
.dialog .dialog-set .dialog-con .zero-add .item-field .item-add {
  width: 16px;
  height: 16px;
  background: url(../../images/creditManage/add-icon.png) no-repeat center;
  background-size: 16px;
  margin-right: 20px;
  cursor: pointer;
  display: none;
}
.dialog .dialog-set .dialog-con .zero-add .item-field:last-child .item-add {
  display: block;
}
.dialog .dialog-set .dialog-con .c-item-con {
  position: relative;
  height: 32px;
}
.dialog .dialog-set .dialog-con .c-item-con .item-input {
  width: 240px;
  height: 32px;
  padding: 0 26px 0 14px;
  background: url("../../images/creditManage/down-icon.png") no-repeat center right 12px;
  vertical-align: top;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  font-size: 14px;
  color: #4E5969;
  cursor: pointer;
  box-sizing: border-box;
  background-color: #ffffff;
  position: relative;
}
.dialog .dialog-set .dialog-con .c-item-con .item-input::placeholder {
  color: #999;
}
.dialog .dialog-set .dialog-con .gsel_search {
  z-index: 7;
  position: absolute;
  width: 240px;
  height: auto;
  top: 36px;
  background: #fff;
  box-shadow: 0px 0 8px 0px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: auto;
  display: none;
}
.dialog .dialog-set .dialog-con .gsel_search .gsel_per_uls {
  max-height: 150px;
  overflow: auto;
}
.dialog .dialog-set .dialog-con .gsel_search .gsel_per_uls .gsel_per_lis {
  line-height: 40px;
  padding: 0 20px;
  font-size: 14px;
  color: #242933;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}
.dialog .dialog-set .dialog-con .gsel_search .gsel_per_uls .gsel_per_lis:after {
  content: '';
  position: absolute;
  width: 18px;
  height: 18px;
  top: 11px;
  left: 196px;
  background: url('../../images/creditManage/check-icon.png') no-repeat center;
  background-size: contain;
  border-radius: 2px;
}
.dialog .dialog-set .dialog-con .gsel_search .gsel_per_uls .gsel_per_lis.cur:after {
  content: '';
  position: absolute;
  width: 18px;
  height: 18px;
  top: 11px;
  left: 196px;
  background: url('../../images/creditManage/check-cur.png') no-repeat center;
  background-size: contain;
  border-radius: 2px;
}
.dialog .dialog-set .dialog-con .gsel_search .gsel_per_uls .gsel_per_lis:hover {
  background: #F4F7FC;
}
.dialog .dialog-set .dialog-con .gsel_search .gsel_per_uls .gsel_per_lis.select {
  color: #4C88FF;
}
.dialog .dialog-set .dialog-con input[type='checkbox'] {
  position: relative;
  width: 14px;
  height: 14px;
  margin-right: 8px;
  vertical-align: middle;
  background-color: #fff;
  border: 1px solid transparent;
}
.dialog .dialog-set .dialog-con input[type='checkbox']:after {
  content: '';
  z-index: 1;
  position: absolute;
  width: 18px;
  height: 18px;
  top: -2px;
  left: -2px;
  background: url('../../images/creditManage/check-icon.png') no-repeat center;
  background-size: contain;
  border-radius: 2px;
}
.dialog .dialog-set .dialog-con input[type='checkbox']:checked:after {
  background: url('../../images/creditManage/check-cur.png') no-repeat center;
  background-size: contain;
}
.dialog .dialog-set .mapp .mapp-item {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
}
.dialog .dialog-set .mapp .mapp-item .item-score {
  width: 97px;
  height: 34px;
  padding-left: 5px;
  background: #F7F8FA;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  text-align: left;
  line-height: 34px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.dialog .dialog-set .mapp .mapp-item .item-txt {
  color: #4E5969;
  line-height: 34px;
  margin: 0 10px;
  text-align: center;
  width: 25%;
}
.dialog .dialog-set .mapp .mapp-item .layui-select-title {
  width: 236px;
}
.dialog .dialog-set .dialog-btn {
  height: 70px;
  background: #FFFFFF;
  border-top: 1px solid #E5E6EB;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  border-radius: 0 0 10px 10px;
}
.dialog .dialog-set .dialog-btn button {
  width: 88px;
  height: 36px;
  border-radius: 18px;
  cursor: pointer;
}
.dialog .dialog-set .dialog-btn button.btn-cancel {
  border: 1px solid #C9CDD4;
  background: #ffffff;
  color: #4E5969;
  margin-right: 16px;
}
.dialog .dialog-set .dialog-btn button.btn-sure {
  background: #4D88FF;
  border: 1px solid #4D88FF;
  color: #ffffff;
  margin-right: 30px;
}
.dialog .dialog-import {
  width: 860px;
  background-color: #FFFFFF;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 10px;
}
.dialog .dialog-import .dialog-con {
  overflow: hidden;
}
.dialog .dialog-import .dialog-con #uploadExcel {
  width: 800px;
  height: 184px;
  background: #F2F3F5;
  border: 1px dashed #E5E6EB;
  border-radius: 2px;
  margin: 30px;
  padding: 0;
}
.dialog .dialog-import .dialog-con #uploadExcel img {
  margin: 50px auto 24px;
}
.dialog .dialog-import .dialog-con #uploadExcel p {
  font-size: 14px;
  color: #1D2129;
  text-align: center;
  line-height: 22px;
  margin-bottom: 4px;
}
.dialog .dialog-import .dialog-con #uploadExcel p.uploadLimit {
  color: #86909C;
}
.dialog .dialog-import .dialog-con #uploadExcel #uploadView {
  color: #4D88FF;
}
.dialog .dialog-import .dialog-btn {
  height: 70px;
  background: #FFFFFF;
  border-top: 1px solid #E5E6EB;
  border-radius: 0px 0px 10px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.dialog .dialog-import .dialog-btn .download {
  width: 140px;
  height: 34px;
  background: #4D88FF;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  margin-left: 30px;
  overflow: hidden;
  cursor: pointer;
}
.dialog .dialog-import .dialog-btn .download img {
  margin-right: 8px;
}
.dialog .dialog-import .dialog-btn .btns button {
  width: 88px;
  height: 36px;
  border-radius: 18px;
  cursor: pointer;
}
.dialog .dialog-import .dialog-btn .btns button.btn-cancel {
  border: 1px solid #C9CDD4;
  background: #ffffff;
  color: #4E5969;
  margin-right: 16px;
}
.dialog .dialog-import .dialog-btn .btns button.btn-sure {
  background: #4D88FF;
  border: 1px solid #4D88FF;
  color: #ffffff;
  margin-right: 30px;
}
