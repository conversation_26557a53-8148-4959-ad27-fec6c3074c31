/* 教学班详情面板样式 */
.detail-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.detail-header h3 {
    margin: 0;
    color: #333;
    font-size: 20px;
    font-weight: 600;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-0 {
    background: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.status-1 {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.status-2 {
    background: #fff2f0;
    color: #f5222d;
    border: 1px solid #ffccc7;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.detail-item label {
    font-weight: 600;
    color: #666;
    margin-right: 10px;
    min-width: 80px;
}

.detail-item span {
    color: #333;
}

.detail-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.class-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.class-item {
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
    border-left: 4px solid #1890ff;
}

.class-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.class-info {
    color: #666;
    font-size: 14px;
}

.staff-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.staff-group h5 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

.staff-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.staff-item:last-child {
    border-bottom: none;
}

.staff-name {
    font-weight: 600;
    color: #333;
}

.staff-title {
    color: #666;
    font-size: 12px;
}

.staff-dept {
    color: #999;
    font-size: 12px;
}

.arrangement-list,
.classroom-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.arrangement-item,
.classroom-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.arrangement-item label,
.classroom-item label {
    font-weight: 600;
    color: #666;
    min-width: 100px;
}

.settings {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.settings span {
    padding: 2px 8px;
    background: #e6f7ff;
    border-radius: 4px;
    font-size: 12px;
    color: #1890ff;
}

.permission-notice {
    padding: 15px;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 4px;
    color: #fa8c16;
    display: flex;
    align-items: center;
    gap: 10px;
}

.permission-notice i {
    font-size: 18px;
}

.empty-data {
    text-align: center;
    color: #999;
    padding: 20px;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .detail-grid {
        grid-template-columns: 1fr;
    }
    
    .detail-section {
        padding: 15px;
    }
}