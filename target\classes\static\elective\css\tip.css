#jwTips {
    width: 560px;
    box-shadow: 0px 0px 20px 0px rgba(68, 104, 230, 0.15);
    overflow: hidden;
    font-family: PingFang SC;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    font-weight: 400;
    border-radius: 10px;
    background-color: #fff;
  }
  .jw-dialog {
    content: '';
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 9999;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
  }
  #jwTips .tip-con {
    margin: 40px 100px;
    font-size: 16px;
  }
  #jwTips .tip-con img {
    display: block;
    margin: 0 auto 24px;
  }
  #jwTips .tip-con h4 {
    color: #1D2129;
    text-align: center;
    margin-bottom: 4px;
    font-weight: 400;
    font-size: 16px;
  }
  #jwTips .tip-con p {
    color: rgba(29, 33, 41, 1);
    text-align: center;
    line-height: 1.5;
    font-size: 16px;
  }
  #jwTips .tip-con .inform{
    display: flex;
    display:-webkit-flex;
    align-items: flex-start;
    justify-content: center;
    font-size:16px;
    color:rgba(29, 33, 41, 1);
  }
#jwTips .tip-con .inform .inform-con{
  display: flex;
  display:-webkit-flex;
  align-items: flex-start;
  justify-content: center;
}
  #jwTips .tip-con .inform .name{
    font-weight:bold;
    white-space: nowrap;
    flex-shrink: 0;
  }
  #jwTips .tip-con .inform .text{
     flex:1;
  }
  #jwTips .tip-con .suc-box{
    text-align: center;
  }
  #jwTips .tip-con .suc-box span{
    display: inline-block;
    padding-left:32px;
    background: url(/elective/images/icon-success.png) no-repeat left center;
    background-size: 24px;
    font-size:16px;
    color:rgba(29, 33, 41, 1);
  }
  #jwTips .tip-con .warn-box{
    text-align: center;
  }
  #jwTips .tip-con .warn-box span{
    display: inline-block;
    padding-left:32px;
    background: url(/elective/images/icon-warning.png) no-repeat left center;
    background-size: 24px;
    font-size:16px;
    color:rgba(29, 33, 41, 1);
  }
  #jwTips .tip-con p i{
   color:rgba(101, 129, 186, 1)
  }
  #jwTips .tip-con  i.suc{
    color:rgba(62, 179, 90, 1);
    display: inline-block;
    margin:0 5px;
   }
   #jwTips .tip-con  i.error{
    color:rgba(255, 176, 38, 1);
    display: inline-block;
    margin:0 5px;
   }
  #jwTips .tip-con .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
    cursor: pointer;
  }
  #jwTips .tip-con .btn .btn-cancel {
    border: 1px solid #E5E6EB;
    padding: 0 30px;
    height: 36px;
    font-size: 14px;
    border-radius: 18px;
    color: #4E5969;
    margin-right: 16px;
    background-color: #fff;
  }
  #jwTips .tip-con .btn .btn-sure {
    color: #fff;
    background: #4d88ff;
    box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
    border: 1px solid #4d88ff;
    padding: 0 30px;
    height: 36px;
    font-size: 14px;
    border-radius: 18px;
    display: block;
  }
  #tipLoading {
    animation: rotate 1.5s linear infinite;
  }
  @keyframes rotate {
    0% {
      transform: rotate(0);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  