.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"] {
  -moz-appearance: textfield;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #86909C;
  font-size: 14px;
}
body {
  background-color: #F7F8FA;
  /*padding: 20px 20px 0;*/
  font-size: 14px;
  color: #4E5969;
}
.main {
  width: calc(100vw);
  min-height: calc(100vh);
  background: #FFFFFF;
  border-radius: 8px;
}
.j-search-con {
  display: flex;
  align-items: center;
  position: relative;
  width: 240px;
  cursor: pointer;
}
.j-search-con.multiple-box .allSelect {
  width: 100%;
  height: 40px;
  line-height: 40px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-left: 20px;
  padding-right: 36px;
  color: #4E5969;
  font-size: 14px;
  font-weight: 400;
  background: url(../../images/creditManage/check-icon.png) no-repeat 204px center;
  background-size: 16px;
}
.j-search-con.multiple-box .allSelect:hover {
  background: #E1EBFF url(../../images/creditManage/check-icon.png) no-repeat 204px center;
  background-size: 16px;
  color: #4D88FF;
  font-weight: 500;
}
.j-search-con.multiple-box .allSelect.cur {
  background: url(../../images/creditManage/check-cur.png) no-repeat 204px center;
  background-size: 16px;
  color: #4D88FF;
  font-weight: 500;
}
.j-search-con.multiple-box .allSelect.cur:hover {
  background: #E1EBFF url(../../images/creditManage/check-cur.png) no-repeat 204px center;
  background-size: 16px;
  color: #4D88FF;
  font-weight: 500;
}
.j-search-con.multiple-box .j-select-year ul li {
  padding-left: 20px;
  padding-right: 36px;
  background: url(../../images/creditManage/check-icon.png) no-repeat 204px center;
  background-size: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
}
.j-search-con.multiple-box .j-select-year ul li .tips {
  position: absolute;
  left: 20px;
  top: -40px;
  background: url(../../images/creditManage/tips-icons.png) no-repeat left center;
  width: 276px;
  height: 34px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-left: 36px;
  line-height: 34px;
}
.j-search-con.multiple-box .j-select-year ul li:hover {
  background: #E1EBFF url(../../images/creditManage/check-icon.png) no-repeat 204px center;
  background-size: 16px;
}
.j-search-con.multiple-box .j-select-year ul li.active {
  background: url(../../images/creditManage/check-cur.png) no-repeat 204px center;
  background-size: 16px;
}
.j-search-con.multiple-box .j-select-year ul li.active:hover {
  background: #E1EBFF url(../../images/creditManage/check-cur.png) no-repeat 204px center;
  background-size: 16px;
}
.j-search-con.single-box .j-select-year ul li {
  /*background: url("../../images/creditManage/radio-icon.png") no-repeat 90% center;*/
  background: url("") no-repeat 90% center;
  position: relative;
}
.j-search-con.single-box .j-select-year ul li:hover .tips {
  display: block;
  font-weight: normal;
}
.j-search-con.single-box .j-select-year ul li .tips {
  position: fixed;
  display: none;
  background: url(../../images/creditManage/tips-icons.png) no-repeat left center;
  background-size: 138px 17px;
  width: 138px;
  height: 17px;
  margin-top: -48px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-left: 36px;
  line-height: 15px;
  font-size: 20px;
  color: #737b86 !important;
}
.j-search-con.single-box .j-select-year ul li .tips em {
  display: block;
  transform: scale(0.7);
  transform: -webkit-scale(0.7);
  margin-left: -20px;
}
.j-search-con.single-box .j-select-year ul li.active {
  background: url("") no-repeat 90% center;
}
.j-search-con .j-select-year {
  left: 0;
}
.j-search-con input {
  width: 240px;
  height: 34px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  padding: 0 20px 0 10px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  cursor: pointer;
}
.j-search-con input::placeholder {
  color: #86909C;
}
.j-search-con .j-arrow {
  width: 10px;
  height: 10px;
  background: url(../../images/creditManage/down-icon.png) no-repeat center;
  position: absolute;
  right: 12px;
  top: 12px;
}
.j-search-con .j-arrow.j-arrow-slide {
  transform: rotate(180deg);
}
.j-search-con .j-select-year {
  position: absolute;
  top: 40px;
  left: -1px;
  z-index: 9;
  width: 240px;
  max-height: 300px;
  overflow-y: auto;
  display: none;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}
.j-search-con .j-select-year .search {
  height: 36px;
  background: #f5f7fa;
  border-radius: 18px;
  margin: 11px 10px;
}
.j-search-con .j-select-year .search input {
  border: none;
  width: 80% !important;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  float: left;
}
.j-search-con .j-select-year .search span {
  cursor: pointer;
  float: left;
  width: 16px;
  height: 16px;
  background: url(../../images/creditManage/search-icon.png) no-repeat center;
  margin-top: 10px;
}
.j-search-con .j-select-year.slideShow {
  display: block;
}
.j-search-con .j-select-year ul li {
  line-height: 40px;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: #4E5969;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-left: 20px;
}
.j-search-con .j-select-year ul li.active,
.j-search-con .j-select-year ul li:hover {
  background: #E1EBFF;
  color: #4D88FF;
  font-weight: 500;
}
.popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  display: none;
}
.popup .popup-box {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 752px;
  background-color: #FFFFFF;
  border-radius: 10px;
}
.popup .popup-box .pu-title {
  height: 56px;
  font-size: 16px;
  color: #1D2129;
  line-height: 56px;
  text-align: left;
  border-bottom: 1px solid #E5E6EB;
  padding-left: 30px;
}
.popup .popup-box .pu-con {
  padding: 32px 80px;
}
.popup .popup-box .pu-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 70px;
  border-top: 1px solid #E5E6EB;
  padding-right: 30px;
}
.popup .popup-box .pu-btn button {
  width: 88px;
  height: 36px;
  font-size: 14px;
  border-radius: 18px;
  cursor: pointer;
}
.popup .popup-box .pu-btn button.pu-cancel {
  border: 1px solid #C9CDD4;
  color: #4E5969;
  background-color: #fff;
  margin-right: 16px;
}
.popup .popup-box .pu-btn button.pu-sure {
  color: #fff;
  background: #4C85FA;
  border: 1px solid #4C85FA;
  box-shadow: 0px 0px 10px rgba(90, 177, 118, 0.3);
}
