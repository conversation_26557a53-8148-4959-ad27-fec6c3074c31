@charset "utf-8";
/* CSS Document */
*{margin:0px; padding:0px;}
body{font:12px/1.5 Microsoft YaHei,SimSun, Arial, Helvetica, sans-serif; color:#333; background:#FFF;}
img{border:0px;}
ul,ol{list-style-type:none;}
h1,h2,h3,h4,h5,h6{ font-weight:normal;}
table{border-collapse:collapse;border-spacing:0;}
input,select,textarea{vertical-align:middle; outline:none; resize:none; font-family:Microsoft YaHei;}
a{color:#333; text-decoration:none;}
.clearfix{zoom:1; overflow:hidden;}
.clear{ clear:both; font-size:0; height:0; line-height:0;}
.clearAfter:after{ content:''; display:block; clear:both; font-size:0; height:0; line-height:0; overflow:hidden;}
.fl{float:left;}
.fr{float:right;}
/*滚动条整体样式*/
::-webkit-scrollbar {
  /*高宽分别对应横竖滚动条的尺寸*/
  width: 8px;
  height: 8px; }

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 8px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #d9dde1; }

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  background: #ffffff; }

::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #d0d3d9; }

::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #d0d3d9; }

:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #d0d3d9; }
/********************************************************/

.bgColor{ background:#F2F3F8;}
.jwMain{ min-width:1300px;}
.jwMain{ margin:30px; min-height:600px; background:#FFFFFF; border-radius:8px; -webkit-box-shadow:0px 2px 10px 0px rgba(237, 238, 240, 0.5); box-shadow:0px 2px 10px 0px rgba(237, 238, 240, 0.5);}

.opt_home_top{ height:50px; line-height:50px; padding:0 20px; font-size:14px; color:#A8ABB3; border-bottom:1px solid #F2F2F2; background:#fff; box-shadow:0px -1px 0px 0px #f2f2f2;}
 
.opt_index{ padding:15px 40px 45px 30px; background:#fff;}

.opt_top_btns .opt_top_uls{ overflow:hidden;}
.opt_top_btns .opt_top_uls .opt_top_lis{ min-width:60px; height:34px; line-height:34px; padding:0 16px; margin-bottom:20px; margin-right:30px; border:1px solid #94B8FF; border-radius:10px; font-size:14px; color:#4C88FF; text-align:center; cursor:pointer;}
.opt_top_btns .opt_top_uls .opt_top_lis:hover{ background:#F5F8FF;}
.opt_top_btns .opt_top_uls .opt_top_lis.noClick{ opacity:0.4; background:#fff;}
.opt_top_btns .opt_top_uls .opt_top_exp{ position:relative;}
.opt_top_btns .opt_top_uls .opt_top_exp .exp_ols{ display:none; z-index:1; position:absolute; width:112px; padding:6px 0; top:35px; left:-10px; background:#FFFFFF; box-shadow:0px 2px 12px 0px rgba(175, 185, 204, 0.75); border-radius:6px;}
.opt_top_btns .opt_top_uls .opt_top_exp .exp_lis{ line-height:40px; font-size:14px; color:#242933; text-align:center; cursor:pointer;}
.opt_top_btns .opt_top_uls .opt_top_exp .exp_lis:hover{ color:#4C88FF;}
.opt_top_btns .opt_top_uls .opt_top_exp .exp_lis.active{ color:#4C88FF;}
.opt_top_btns .opt_top_uls .opt_top_exp:hover .exp_ols{ display:block;}
.opt_top_btns .opt_top_uls .opt_add_lis{ background:#3A8BFF; color:#fff; box-shadow:0px 2px 8px 0px rgba(39, 111, 255, 0.3);}
.opt_top_btns .opt_top_uls .opt_add_lis span{ display:inline-block; width:12px; height:12px; background:url(../../images/cultivation/icon_add_lis.png) no-repeat center; background-size:contain; vertical-align:top; margin-top:11px; margin-right:6px;}
.opt_top_btns .opt_top_uls .opt_add_lis:hover{ background:#3A8BFF; opacity:0.8;}

.opt_search .opt_search_right{ position:relative; padding-right:260px; width:calc(100% - 260px);}
.opt_search .opt_search_right .opt_search_temp{ height:34px; margin-right:40px; margin-bottom:20px; line-height:34px;}
.opt_search .opt_search_right .opt_search_temp .opt_search_name{ display:inline-block; width:100px; height:32px; line-height:32px; text-align:right; vertical-align:top; overflow:hidden;}
.opt_search .opt_search_right .opt_search_temp .opt_search_name .opt_search_table{ width:100px; height:32px; display:table;}
.opt_search .opt_search_right .opt_search_temp .opt_search_name .opt_search_cell{ display:table-cell; vertical-align:middle;}
.opt_search .opt_search_right .opt_search_temp .opt_search_name .opt_search_table span{ overflow:hidden;  text-overflow:ellipsis;  display:-webkit-box;  -webkit-line-clamp:2;  -webkit-box-orient:vertical;  line-height:16px;  margin-right:14px;  font-size:14px;  color:#242933;}
.opt_search .opt_search_right .opt_search_temp .opt_search_per{ display:inline-block; position:relative; width:178px; height:32px; border-radius:4px; vertical-align:top;}
.opt_search .opt_search_right .opt_search_temp .opt_search_per .opt_txt_input{width:calc(100% - 14px); height:100%; padding:0 6px 0 6px; vertical-align:top; border:1px solid #E1E2E5; border-radius:4px; font-size:14px; color:#242933;}
.opt_search .opt_search_right .opt_search_temp .opt_search_per .opt_txt_input:hover{border:1px solid #CCD9F3;}
.opt_search .opt_search_right .opt_search_temp .opt_search_per .opt_txt_input:focus{box-shadow:0px 0px 4px 0px #75a3ff; border:1px solid #CCD9F3;}

.opt_search .opt_search_right .opt_btns{ position:absolute; width:260px; height:34px; top:0; right:0;}
.opt_search .opt_search_right .opt_search_btn{ min-width:46px; height:32px; top:0; right:180px; line-height:32px; padding:0 16px; border:1px solid #94B8FF; border-radius:10px; font-size:14px; color:#fff; text-align:center; background:#3A8BFF; box-shadow:0 2px 10px 0px rgba(58, 139, 255, 0.3); cursor:pointer;}
.opt_search .opt_search_right .right0{ right:0;}
.opt_search .opt_search_right .opt_search_btn:hover{ background:#3A8BFF; opacity:0.8;}
.opt_search .opt_search_right .opt_clear_btn{ min-width:46px; height:32px; margin-left:20px; line-height:32px; padding:0 16px; border:1px solid #99BBFF; border-radius:20px; font-size:14px; color:#4C88FF; text-align:center; cursor:pointer;}
.opt_search .opt_search_right .opt_clear_btn:hover{ background:#F5F8FF;}
.opt_search .opt_search_right .opt_clear{ line-height:34px; margin-left:10px; font-size:14px; color:#4C88FF; text-align:right; cursor:pointer;}
.opt_search .opt_search_right .opt_clear:hover{ opacity:0.8;}
.opt_search .opt_search_right .opt_down{ line-height:34px; margin-left:40px; font-size:14px; color:#4C88FF; text-align:right; cursor:pointer;}
.opt_search .opt_search_right .opt_down label{ cursor:pointer;}
.opt_search .opt_search_right .opt_down span{ display:inline-block;   width:8px;   height:8px;   background:url(../../images/cultivation/opt_down.png) no-repeat center;   background-size:contain;   vertical-align:top;   margin-top:13px;   margin-left:3px;}
.opt_search .opt_search_right .opt_down:hover{ opacity:0.8;}
.opt_search .opt_search_right .opt_up{ line-height:34px; margin-left:40px; font-size:14px; color:#4C88FF; text-align:right; cursor:pointer;}
.opt_search .opt_search_right .opt_up span{ display:inline-block;   width:8px;   height:8px;   background:url(../../images/cultivation/opt_up.png) no-repeat center;   background-size:contain;   vertical-align:top;   margin-top:13px;   margin-left:3px;}
.opt_search .opt_search_right .opt_up:hover{ opacity:0.8;}

.opt_data .opt_data_top{ line-height:20px; margin-bottom:16px;}
.opt_data .opt_data_top .opt_data_num{ font-size:12px; color:#A8ACB3;}
.opt_data .opt_data_top .opt_data_num em{ margin:0 4px; color:#4C88FF; font-style:normal;}
.opt_data .opt_data_top .opt_data_btn{ margin-left:30px; font-size:14px; color:#7D8FB3; cursor:pointer;}
.opt_data .opt_data_top .opt_data_btn span{ display:inline-block; width:16px; height:16px; background-size:contain; vertical-align:top; margin-top:2px; margin-right:6px;}
.opt_data .opt_data_top .opt_data_print span{ background:url(../../images/cultivation/icon_print.png) no-repeat center; background-size:contain;}
.opt_data .opt_data_top .opt_data_btn:hover{ opacity:0.7;}

.opt_data_cont table{ min-width:100%;}

.layui-btn{ background:none!important; color:#4C88FF!important;}
.layui-btn:hover{ background:none!important; color:#4C88FF!important;}

.layui-form-checked[lay-skin=primary] i{ border-color:#4C88FF !important; background-color:#4C88FF !important;}
.layui-form-checkbox[lay-skin=primary]:hover i {border-color:#4C88FF !important;}

.tr_bj_color{ background-color:#f0f5ff !important;}


/*编辑*/
.xkb_cont{ padding:30px;}
.xkb_head{ padding:10px 0; background:#fafafa; font-size:14px; color:#656a73;}
.xkb_head li{ width:16.66%; line-height:38px; float:left; overflow:hidden; white-space:nowrap; text-overflow:ellipsis;}
.xkb_head li .titSpan{ min-width:100px; text-align:right; display: inline-block; vertical-align: top;}

.xkb_form{ padding:12px 0; border-bottom:solid #ebebeb 1px;}
.xkb_form:after{ content:''; display:block; clear:both; font-size:0; height:0; line-height:0; overflow:hidden;}
.xkb_form_item{ width:50%; min-height:40px; line-height:40px; font-size:14px; padding:12px 0;}
.xkb_form_item:after{ content:''; display:block; clear:both; font-size:0; height:0; line-height:0; overflow:hidden;}
.xkb_form_name{ width:140px; min-height:40px; padding-right:20px; color:#242933; text-align:right;}
.xkb_form_text{ color:#000000;}
.xkb_form_input,.fom_list .xkb_form_input_time{ width:380px; line-height:20px; padding:9px; vertical-align:top; border:1px solid #E1E2E5; border-radius:4px; font-size:14px; color:#242933;}
.xkb_form_input_time{ background:url(../../images/cultivation/icon_time_w.png) no-repeat center right 10px;}
.xkb_form_input:hover{border:1px solid #CCD9F3;}
.xkb_form_input:focus{box-shadow:0px 0px 4px 0px #75a3ff; border:1px solid #CCD9F3;}
.xkb_form_item .searchable-select{ width:400px;}
.xkb_form_item .searchable-select-holder{ height:38px; line-height:38px;}
.xkb_form_item .searchable-select-dropdown{ top:42px;}
.xkb_upload{ margin-left:24px;}
.xkb_upload{ width:118px; height:38px; line-height:38px; border:solid #99bbff 1px; font-size:14px; color:#4c88ff; border-radius:4px; display:block; overflow:hidden; position:relative; text-align:center;}
.xkb_upload input{ position:absolute; font-size:40px; right:0; top:0; opacity:0; filter:alpha(opacity=0); cursor:pointer}

.addTeac{ margin-top:30px;}
.addTeac_name{ width:140px; min-height:40px; line-height:40px; padding-right:20px; font-size:14px; color:#242933; text-align:right;}
.addTeac_con{ margin-left:335px; padding-top:4px;}
.addTeac_con li{ margin-right:20px; padding:0 15px; height:32px; line-height:32px; border-radius:32px; background:#f2f4f7; font-size:14px; color:#242933; float:left; position:relative; cursor:pointer;}
.addTeac_con li .dele{ display:none;}
.addTeac_con li:hover .dele{ display:block; width:16px; height:16px; background:url(../../images/cultivation/addTeac_del.png) no-repeat; position:absolute; top:-4px; right:-5px;}

.addTeac_item{ background:#fafafa; padding:12px 0; margin-top:24px; position:relative;}
.addTeac_item .addTeac_dele{ display:block; width:16px; height:16px; background:url(../../images/cultivation/addTeac_icoDle.png) no-repeat; position:absolute; top:20px; right:20px;}

.addTeac_bnt{ margin-top:30px; text-align:right;}
.addTeac_bnt .btnWhite,.addTeac_bnt .btnBlue{ margin-left:40px;}



/*弹窗*/
.maskDiv{width:100%;height:100%;position:fixed;top:0;left:0;background:rgba(16,26,41,0.76);z-index:10}
.wid840{width:840px;max-height:600px}
.wid100b{width:100%;max-height:600px}
.wid480{width:480px;max-height:400px}
.popDiv{min-height:100px;background:#FFFFFF;border-radius:10px;position:fixed;left:50%;overflow:hidden}
.popHead{line-height:60px;height:60px;font-size:18px;color:#242933;font-weight:bold;background:#FFFFFF;border-bottom:solid #F2F2F2 1px;padding:0 30px;background:#fff}
.popClose{display:inline-block;width:18px;height:18px; background:url(../../images/cultivation/popClose.png) no-repeat; margin-top:19px}
.het62{width:100%;height:61px;}
.popBottom{width:100%;height:53px;padding-top:17px;background:#fff;border-top:solid #F2F2F2 1px;}
.popBottom a{margin-right:30px;}
.het72{width:100%;height:71px;}
.popWord{padding:26px 30px;}
.btnWhite{ background:#FFFFFF; border:1px solid #94C1FF; border-radius:20px; display:inline-block; width:90px; height:34px; text-align:center; line-height:34px; font-size:14px; color:#4C88FF;}
.btnBlue{ background:#4C88FF; border:1px solid #4C88FF; border-radius:20px; display:inline-block; width:90px; height:34px; text-align:center; line-height:34px; font-size:14px; color:#FFFFFF;}
.popBody{ min-height:300px; max-height:468px; overflow:hidden;}

.popSearch{ padding:20px 0 0 0;}
.popSearch_row{ height:34px; margin-right:20px; margin-bottom:20px; line-height:34px;}
.popSearch_name{ display:inline-block; width:100px; height:32px; line-height:32px; text-align:right; vertical-align:top; overflow:hidden;}
.popSearch_name .popSearch_table{ width:100px; height:32px; display:table;}
.popSearch_name .popSearch_cell{ display:table-cell; vertical-align:middle;}
.popSearch_name .popSearch_table span{ overflow:hidden;  text-overflow:ellipsis;  display:-webkit-box;  -webkit-line-clamp:2;  -webkit-box-orient:vertical;  line-height:16px;  margin-right:8px;  font-size:14px;  color:#242933;}
.popSearch_per{ display:inline-block; position:relative; width:178px; height:32px; border-radius:4px; vertical-align:top;}
.popSearch_per .popSearch_input{width:calc(100% - 14px); height:100%; padding:0 6px 0 6px; vertical-align:top; border:1px solid #E1E2E5; border-radius:4px; font-size:14px; color:#242933;}
.popSearch_per .popSearch_input:hover{border:1px solid #CCD9F3;}
.popSearch_per .popSearch_input:focus{box-shadow:0px 0px 4px 0px #75a3ff; border:1px solid #CCD9F3;}
.popSearch_line{ line-height:34px; color:#ccc; padding:0 5px;}
.popSearch .popSearch_per{ width:152px;}
.popSearch .searchable-select{ width:152px;}
.popSearch_btns{ height:34px; padding:0 30px;}
.popSearch_search_btn,.submit-btn{ min-width:46px; height:32px; top:0; right:180px; line-height:32px; padding:0 16px; border:1px solid #94B8FF; border-radius:10px; font-size:14px; color:#fff; text-align:center; background:#3A8BFF; box-shadow:0 2px 10px 0px rgba(58, 139, 255, 0.3); cursor:pointer;}
.popSearch_clear{ line-height:34px; margin-left:10px; font-size:14px; color:#4C88FF; text-align:right; cursor:pointer;}
.popSearch_clear:hover{ opacity:0.8;}
.popSearch_cont{ padding:30px;}
.popSearch_cont .layui-table, .popSearch_cont .layui-table-view{ margin:0 !important;}
.submit-btn{margin-top: 30px;margin-right: 50px;}

.popTips{ width:100%; height:160px; line-height:24px; font-size:16px; display:table;}
.popTips_cell{ padding:0 30px; display:table-cell; vertical-align: middle; text-align:center;}

.popWeekly{ padding:30px;}
.popWeekly_tab{ height:36px;}
.popWeekly_tab li{ width:80px; height:34px; line-height:34px; background:#FFFFFF; font-size:14px; color:#4C88FF; border:solid #4C88FF 1px; border-radius:10px; float:left; text-align:center; cursor:pointer;}
.popWeekly_tab li+li{ margin-left:10px;}
.popWeekly_tab li.active{background:#4C88FF; color:#FFFFFF;}

.popWeekly_table{ margin-top:20px; width:100%; overflow:auto;}
.popWeekly_table table{ min-width:100%;}
.popWeekly_table th,.popWeekly_table td{ border:1px solid #e6e6e6; font-size:14px; color:#666666; white-space:nowrap;}
.popWeekly_table th{ padding:10px; line-height:24px; background:#f2f2f2; color:#656a73; font-weight:normal;}
.popWeekly_table td{ color:#656a73;}
.popWeekly_table td .tdNum{ min-width:40px; padding:0 10px;}
.popWeekly_table td .tdInput{ width:40px; height:48px; line-height:48px; padding:0 6px; vertical-align:top; border:1px solid #E1E2E5; border:none; border-radius:4px; font-size:14px; color:#242933; text-align:center;}

.popWeekly_table td.active,.popWeekly_table th.active{ background:#4C88FF; background:rgba(76,136,255,.3);}

.popWeekly_detect{ margin-top:10px; line-height:24px; font-size:14px; color:#666666; text-align:right;}
.popWeekly_detect input{ width:14px; height:14px; margin:0 0 3px 6px;}
.layui-form .layui-table-fixed-l td .layui-table-grid-down {
  display: none!important;
}


















