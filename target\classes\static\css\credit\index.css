.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #86909C;
  font-size: 14px;
  font-weight: 300;
}
body {
  background-color: #F7F8FA;
}
.j-material-wrap {
  max-width: 1700px;
  margin: 0 auto;
}
.j-material {
  margin: 20px;
  background-color: #fff;
  border-radius: 8px;
  padding-bottom: 20px;
  min-height: calc(100vh - 60px);
}
.j-material .j-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  border-bottom: 1px solid #E8EBF1;
  position: relative;
}
.j-material .j-title p {
  position: absolute;
  left: 119px;
  top: 0;
  height: 60px;
  line-height: 60px;
  color: #C9CDD4;
  font-weight: normal;
}
.j-material .j-title h4 {
  position: relative;
  font-size: 16px;
  color: #1D2129;
  margin-left: 30px;
  padding-left: 9px;
  font-weight: bold;
}
.j-material .j-title h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4C85FA;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}
.j-material .j-title .j-btns {
  margin-right: 30px;
}
.j-material .j-title .j-btns button {
  width: 92px;
  height: 36px;
  font-size: 14px;
  border-radius: 6px;
  outline: none;
  cursor: pointer;
}
.j-material .j-title .j-btns button.btn-cancel {
  border: 1px solid #4C88FF;
  box-shadow: 0px 0px 8px rgba(39, 111, 255, 0.31);
  color: #4C88FF;
  background-color: #fff;
  margin-right: 14px;
}
.j-material .j-title .j-btns button.btn-complate {
  background: #4C88FF;
  box-shadow: 0px 0px 8px rgba(39, 111, 255, 0.31);
  border: 1px solid #4C88FF;
  color: #fff;
}
.j-material .j-title-s {
  color: #1D2129;
  font-size: 16px;
  line-height: 60px;
  margin: 0 30px;
}
.j-material .j-title-s em {
  color: #4C85FA;
}
.j-material .j-material-lable {
  padding: 30px;
  padding-bottom: 10px;
}
.j-material .j-material-lable h4 {
  position: relative;
  color: #6581BA;
  font-size: 16px;
  padding-left: 9px;
  line-height: 20px;
  font-weight: bold;
  margin-bottom: 24px;
}
.j-material .j-material-lable h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #6581BA;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 2px;
}
.j-material .j-material-lable .j-switch {
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 30px;
}
.j-material .j-material-lable .j-switch .name {
  width: 154px;
  height: 34px;
  font-weight: 400;
  font-size: 14px;
  line-height: 34px;
  color: #1D2129;
}
.j-material .j-material-lable .j-switch .switch {
  background: #DADFE6;
  border-radius: 4px;
  width: 28px;
  height: 14px;
  position: relative;
  margin-top: 10px;
}
.j-material .j-material-lable .j-switch .switch span {
  position: absolute;
  left: 2px;
  top: 2px;
  display: block;
  width: 12px;
  height: 10px;
  border-radius: 2px;
  background: #ffffff;
  transition: all 200ms linear;
}
.j-material .j-material-lable .j-switch .switch.active {
  background: #4D88FF;
}
.j-material .j-material-lable .j-switch .switch.active span {
  left: 14px;
}
.j-material .j-material-lable .j-switch .tit {
  font-size: 14px;
  line-height: 34px;
  color: #C9CDD4;
  margin-left: 10px;
}
.j-material .j-material-lable .set-list .sl-lab {
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 30px;
}
.j-material .j-material-lable .set-list .sl-lab .name {
  width: 126px;
  height: 34px;
  font-weight: 400;
  font-size: 14px;
  line-height: 34px;
  color: #1D2129;
}
.j-material .j-material-lable .set-list .sl-lab .mult-choice ul {
  overflow: hidden;
  width: 670px;
}
.j-material .j-material-lable .set-list .sl-lab .mult-choice ul li {
  height: 34px;
  line-height: 34px;
  float: left;
  padding-left: 24px;
  color: #86909C;
  font-size: 14px;
  cursor: pointer;
  background: url(../../images/credit/check-icon.png) no-repeat left center;
  width: 134px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.j-material .j-material-lable .set-list .sl-lab .mult-choice ul li.cur {
  background: url(../../images/credit/check-cur.png) no-repeat left center;
}
.j-material .j-material-lable .set-list .sl-lab .select-input {
  width: 240px;
  height: 34px;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
}
.j-material .j-material-lable .set-list .sl-lab .select-input.score-sel {
  width: 120px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  margin-right: 10px;
  height: 34px;
}
.j-material .j-material-lable .set-list .sl-lab .select-input em {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 10px;
  height: 10px;
  background: url(../../images/credit/icon-arrow1.png) no-repeat center;
  background-size: 10px;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.j-material .j-material-lable .set-list .sl-lab .select-input .name {
  font-size: 14px;
  color: #86909C;
  padding-left: 10px;
  width: 210px;
  height: 32px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.j-material .j-material-lable .set-list .sl-lab .select-input .name.ckd {
  color: #1D2129;
}
.j-material .j-material-lable .set-list .sl-lab .select-input.clicked em {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  background: url(../../images/credit/icon-arrow1.png) no-repeat center;
  background-size: 10px;
}
.j-material .j-material-lable .set-list .sl-lab .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
}
.j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown {
  width: inherit;
  left: -1px;
  margin: 5px 0;
  padding: 6px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: absolute;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
.j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list {
  max-height: 320px;
  overflow: auto;
}
.j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li {
  margin: 0;
  line-height: normal;
  padding: 0 16px;
  clear: both;
  color: #131B26;
  font-size: 14px !important;
  white-space: nowrap;
  list-style: none;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
.j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li span {
  display: block;
  padding-left: 24px;
  background: url(../../images/credit/check-icon.png) no-repeat left center;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li:hover {
  background-color: #F5F7FA;
}
.j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li.cur {
  color: #616EE6;
}
.j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li.cur span {
  background: url(../../images/credit/check-cur.png) no-repeat left center;
}
.j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .confirm {
  line-height: 30px;
  text-align: right;
  cursor: pointer;
  padding: 0 16px;
  color: #4D88FF;
}
.j-material .j-material-lable .row {
  width: 100%;
  height: 34px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 20px;
}
.j-material .j-material-lable .row .tit {
  font-weight: 400;
  font-size: 14px;
  color: #1D2129;
  margin-right: 14px;
}
.j-material .j-material-lable .row .input {
  margin-right: 4px;
}
.j-material .j-material-lable .row .input input {
  height: 34px;
}
.j-material .j-material-lable .row .inform {
  font-weight: 400;
  font-size: 14px;
  color: #86909C;
  margin-right: 4px;
}
.j-material .j-material-lable .j-lable .lab {
  height: 34px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 24px;
}
.j-material .j-material-lable .j-lable .lab .name {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: #1D2129;
  margin-right: 14px;
  width: 140px;
}
.j-material .j-material-lable .j-lable .lab .time {
  background: url(../../images/credit/time-select.png) no-repeat 220px center;
  background-size: 12px;
}
.j-material .j-material-lable .j-lable .lab .time input {
  background-color: transparent;
}
.j-material .j-material-lable .add-oprate {
  height: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.j-material .j-material-lable .add-oprate h5 {
  font-weight: 700;
  font-size: 14px;
  color: #1D2129;
}
.j-material .j-material-lable .add-oprate .add {
  padding-left: 22px;
  background: url(../../images/credit/add-icon.png) no-repeat left center;
  background-size: 16px;
  font-weight: 400;
  font-size: 14px;
  color: #4C88FF;
  cursor: pointer;
}
.j-material .j-material-lable .j-table {
  border: 1px solid #E8EBF1;
  margin-bottom: 24px;
}
.j-material .j-material-lable .j-table .j-head {
  background: #F1F3F6;
  border-bottom: 1px solid #E8EBF1;
  height: 36px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.j-material .j-material-lable .j-table .j-head .j-th {
  border-right: 1px solid #E8EBF1;
  flex: 3;
  text-align: center;
  line-height: 35px;
  font-weight: 400;
  font-size: 14px;
  color: #6581BA;
}
.j-material .j-material-lable .j-table .j-head .j-th:last-child {
  flex: 2;
  flex-shrink: 0;
  border-right: none;
}
.j-material .j-material-lable .j-table .j-body .j-tr {
  background: #FFFFFF;
  border-bottom: 1px solid #E8EBF1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.j-material .j-material-lable .j-table .j-body .j-tr:nth-child(2n) {
  background: #FAFBFC;
}
.j-material .j-material-lable .j-table .j-body .j-tr:last-child {
  border-bottom: none;
}
.j-material .j-material-lable .j-table .j-body .j-tr.opafter .j-td .total {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  height: 80px;
  justify-content: center;
}
.j-material .j-material-lable .j-table .j-body .j-tr.opafter .j-td .oprate {
  display: none;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td {
  border-right: 1px solid #E8EBF1;
  flex: 3;
  height: 80px;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .oprate {
  display: flex;
  height: 80px;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .total {
  display: none;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .preservation {
  font-weight: 400;
  font-size: 14px;
  color: #4C88FF;
  margin: 0 10px;
  cursor: pointer;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .edit {
  font-weight: 400;
  font-size: 14px;
  color: #4C88FF;
  margin: 0 10px;
  cursor: pointer;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .delet {
  font-weight: 400;
  font-size: 14px;
  color: #F76560;
  margin: 0 10px;
  cursor: pointer;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .symbol {
  font-weight: 400;
  font-size: 14px;
  color: #86909C;
  margin: 0 4px;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .txt {
  font-weight: 400;
  font-size: 14px;
  color: #1D2129;
  margin-right: 14px;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inp-lay {
  width: 120px;
  position: relative;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inp-lay.errors .error {
  display: block;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inp-lay.errors input {
  border: 1px solid #F98981 !important;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inp-lay .error {
  position: absolute;
  display: none;
  left: 0;
  top: 34px;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  color: #F76560;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td:last-child {
  flex: 2;
  flex-shrink: 0;
  border-right: none;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input {
  width: 80px;
  height: 32px;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input.score-sel {
  width: 120px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  margin-right: 10px;
  height: 34px;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input.score-sel.error {
  border: 1px solid #F98981 !important;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input.score-sel .errorMsg {
  position: absolute;
  display: none;
  left: 0;
  top: 34px;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  color: #F76560;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input em {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 10px;
  height: 10px;
  background: url(../../images/credit/icon-arrow1.png) no-repeat center;
  background-size: 10px;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input .name {
  font-size: 14px;
  color: #86909C;
  padding-left: 10px;
  width: 100%;
  height: 32px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input .name.ckd {
  color: #1D2129;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input.clicked em {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  background: url(../../images/credit/icon-arrow1.png) no-repeat center;
  background-size: 10px;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input .select-dropdown {
  width: inherit;
  max-height: 320px;
  overflow: auto;
  left: -1px;
  margin: 5px 0;
  padding: 6px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: absolute;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input .select-dropdown .dropdown-list li {
  margin: 0;
  line-height: normal;
  padding: 0 16px;
  clear: both;
  color: #131B26;
  font-size: 14px !important;
  white-space: nowrap;
  list-style: none;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input .select-dropdown .dropdown-list li:hover {
  background-color: #F5F7FA;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .select-input .select-dropdown .dropdown-list li.cur {
  color: #616EE6;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inputs {
  margin-right: 10px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  width: 199px;
  height: 34px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inputs.errors {
  border: 1px solid #F98981;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inputs.errors .error {
  display: block;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inputs.errors .inp {
  border-right: 1px solid #F98981 !important;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inputs.right-inp .error {
  left: 80px;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inputs.right-inp input {
  border-right: none !important;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inputs.right-inp .select-input {
  border-right: 1px solid #E5E6EB;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inputs.right-inp.errors .error {
  display: block;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inputs.right-inp.errors .select-input {
  border-right: 1px solid #F98981 !important;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inputs input {
  width: 120px;
  height: 32px;
  background-color: transparent;
  border: none !important;
  border-right: 1px solid #E5E6EB !important;
  border-radius: 0!important;
}
.j-material .j-material-lable .j-table .j-body .j-tr .j-td .inputs .error {
  position: absolute;
  display: none;
  left: 0;
  top: 34px;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  color: #F76560;
}
.layui-input {
  border: 1px solid #E5E6EB !important;
  border-radius: 4px!important;
  height: 34px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"] {
  -moz-appearance: textfield;
}
.layui-laydate tr .layui-this {
  background-color: #4C88FF !important;
  color: #fff !important;
}
.layui-laydate-footer span:hover {
  color: #4C88FF !important;
}
