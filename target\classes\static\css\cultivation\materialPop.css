.dialog {
  width: 1104px;
  border-radius: 10px;
  background-color: #ffffff;
  overflow: hidden;
  margin: 0 auto;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
}
.dialog .dialog-title {
  height: 56px;
  border-bottom: 1px solid #e5e6eb;
  color: #1d2129;
  font-size: 16px;
  line-height: 56px;
  text-indent: 30px;
}
.dialog .dialog-con {
  padding: 0 100px;
  overflow: hidden;
  box-sizing: border-box;
}
.dialog .dialog-con .layui-form {
  margin: 30px 0 24px;
}
.dialog .dialog-con .layui-form .layui-form-label {
  width: 56px;
  padding: 0;
  line-height: 34px;
  padding-right: 8px;
}
.dialog .dialog-con .layui-form .layui-input-inline {
  margin-right: 17px;
}
.dialog .dialog-con .layui-form .layui-input-inline .layui-input {
  height: 34px;
  line-height: 34px;
  width: 120px;
}
.dialog .dialog-con .layui-form .layui-input-inline .layui-form-select {
  width: 100px;
}
.dialog .dialog-con .layui-form .layui-input-inline .layui-form-select .layui-input {
  width: 100px;
}
.dialog .dialog-con .layui-form .layui-btn {
  width: 64px;
  height: 34px;
  line-height: 32px;
  background: #4d88ff;
  border-radius: 6px;
}
.dialog .dialog-con .layui-form .layui-btn.layui-btn-primary {
  background-color: unset;
  border: 1px solid #4d88ff;
  color: #4d88ff;
}
.dialog .dialog-con .layui-form .layui-btn.layui-btn-primary:hover {
  border-color: #4d88ff;
}
.dialog .dialog-con .layui-table-page {
  text-align: right;
}
.dialog .dialog-con .layui-table-view select[lay-ignore] {
  height: 20px;
}
.dialog .dialog-con .layui-table-page .layui-laypage input:focus,
.dialog .dialog-con .layui-table-page .layui-laypage select:focus {
  border-color: #4d88ff !important;
}
.dialog .dialog-con .layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: #4d88ff !important;
}
.dialog .dialog-con .layui-table-page .layui-laypage a:hover,
.dialog .dialog-con .layui-table-page .layui-laypage span.layui-laypage-curr:hover {
  color: #4d88ff !important;
}
.dialog .dialog-con .layui-form-select dl dd.layui-this {
  color: #4d88ff !important;
}
.dialog .dialog-con .courseList {
  position: relative;
}
.dialog .dialog-con .courseList .selCourse {
  position: absolute;
  left: 10px;
  bottom: 0;
  color: #1d2129;
  font-size: 14px;
  line-height: 40px;
}
.dialog .dialog-con .courseList .selCourse em {
  color: #4d88ff;
  padding: 0 4px;
}
.dialog .dialog-footer {
  height: 70px;
  border-top: 1px solid #e5e6eb;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.dialog .dialog-footer button {
  width: 88px;
  height: 36px;
  border: 1px solid #c9cdd4;
  border-radius: 18px;
  color: #4e5969;
  font-size: 14px;
  background-color: #ffffff;
  cursor: pointer;
}
.dialog .dialog-footer button:last-child {
  background: #4d88ff;
  border-color: #4d88ff;
  box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
  color: #ffffff;
  margin: 0 30px 0 16px;
}
#selTextbook {
  width: 846px;
}
#selTextbook .tips {
  width: 100%;
  color: #6581BA;
  line-height: 50px;
  text-align: center;
  background: #E1EBFF;
}
#selTextbook .dialog-con {
  padding: 0 35px;
}
#selTextbook .dialog-con .layui-form .layui-input-inline .layui-input {
  width: 240px;
}
#selTextbook .dialog-con .z-check {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6581BA;
  position: absolute;
  left: 27px;
  bottom: 8px;
}
#selTextbook .dialog-con .z-check .check {
  width: 28px;
  height: 28px;
  cursor: pointer;
  margin-right: 6px;
  background: url(../../images/cultivation/mooc/check.png) no-repeat center;
  background-size: 28px;
}
#selTextbook .dialog-con .z-check .check.checked {
  background: url(../../images/cultivation/mooc/checked.png) no-repeat center;
}
#selTextbook .dialog-con .selCourse {
  left: 160px;
  color: #8F97A8;
  line-height: 43px;
}
.layui-form-checked.layui-checkbox-disabled:hover {
  border-color: #eee !important;
}
.dialog .dialog-con .layui-form-checked.layui-checkbox-disabled i {
  border-color: #eee !important;
}
.dialog .dialog-con .layui-form-checked.layui-checkbox-disabled i:hover {
  border-color: #eee !important;
}
.layui-form-checkbox[lay-skin="primary"] i:hover {
  border-color: #4d88ff !important;
}
.layui-form-checked[lay-skin="primary"] i {
  border-color: #4d88ff !important;
  background-color: #4d88ff;
}
#selTextbook .dialog-con .layui-form .layui-inline {
  margin-bottom: 18px;
}
