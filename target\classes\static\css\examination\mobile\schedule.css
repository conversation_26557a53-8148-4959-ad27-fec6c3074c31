@charset "UTF-8";

html,
body {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
}

::-webkit-scrollbar {
  width: 0px;
}

.bg0 {
  background: extract(#fa9191, #ff99b1, #ffa998, #ffb866, #ffd166, #8ed66b, #5fd6a6, #6ecbfa, #7ea4fc, #b3baff, #6bb5ff, #e5a1cf, 0);
}

.bg1 {
  background: #fa9191;
}

.bg2 {
  background: #ff99b1;
}

.bg3 {
  background: #ffa998;
}

.bg4 {
  background: #ffb866;
}

.bg5 {
  background: #ffd166;
}

.bg6 {
  background: #8ed66b;
}

.bg7 {
  background: #5fd6a6;
}

.bg8 {
  background: #6ecbfa;
}

.bg9 {
  background: #7ea4fc;
}

.bg10 {
  background: #b3baff;
}

.bg11 {
  background: #6bb5ff;
}


.tophead {
  width: 100%;
  height: auto;
  text-align: center;
  background-color: #FFFFFF;
  z-index: 1;
}

.tophead .head {
  position: relative;
}

.tophead .leftHead {
  position: absolute;
  left: .16rem;
  bottom: .2rem;
  width: .48rem;
  height: .48rem;
  z-index: 1;
}

.tophead .back {
  float: left;
  width: .48rem;
  height: .48rem;
  background: url(../../../images/examination/headback.png) center center/24px 24px no-repeat;
  background-size: .48rem;
}

.tophead .totalscheduel {
  display: none;
  float: left;
  line-height: 25px;
  padding-left: 13px;
  width: 50px;
  color: #0999FF;
  font-size: 0.3rem;
  box-sizing: border-box;
  margin-top: 10px;
  text-align: left;
}

.tophead .centerHead {
  position: relative;
}

.tophead .rightHead {
  position: absolute;
  right: .2rem;
  bottom: .2rem;
  z-index: 1;
  width: .48rem;
  height: .48rem;
}

.tophead .completeDelete {
  display: none;
  position: absolute;
  right: 0;
  bottom: 0;
  height: 45px;
  line-height: 45px;
  text-align: center;
  width: 1.1rem;
  font-size: 15px;
  color: #0999FF;
  z-index: 1;
}

.tophead .rightHead .scan {
  display: none;
  float: left;
  width: 24px;
  height: 24px;
  background: url(../../../images/examination/headback.png) center center/24px 24px no-repeat;
}

.tophead .rightHead .setting {
  float: left;
  width: 40px;
  height: 100%;
  background: url(../../../images/examination/setting.png) center center/24px 24px no-repeat;
}

.search {

  padding: .2rem .3rem .16rem;
  box-sizing: border-box;

}

.search .search-text {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: .28rem;
  height: .60rem;
  line-height: .6rem;
  color: #C9CDD4;
  background: #F5F6F8;
  border-radius: .30rem;
}

.search .search-text img {
  width: .36rem;
  margin-right: .1rem;
}

.search input {
  height: .60rem;
  background: #F5F6F8;
  border-radius: .30rem;
  font-size: .28rem;
  width: 100%;
  display: none;
  padding: 0 .3rem;
  box-sizing: border-box;
}

.batchSel {
  height: .88rem;
  font-size: .30rem;
  line-height: .88rem;
  background: #FFFFFF;
  box-shadow: 0 0.02rem .16rem rgba(13, 13, 14, 0.08);
  color: #595969;
  padding: 0 .45rem;
  box-sizing: border-box;
  position: relative;
  z-index: 99;

  display: flex;
  align-items: center;
}

.batchSel h3 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 600;
}

.batchSel h3 em {
  font-style: normal;
}

.batchSel span {
  width: 0.14rem;
  height: 0.08rem;
  flex-shrink: 0;
  background: url('../../../images/examination/tringle-icon.png') no-repeat center;
  background-size: .14rem;
  margin-left: 0.08rem;
}

.selectBox {
  height: .88rem;
  text-align: center;
}

.selectBox .selectWeek {
  font-size: .36rem;
  line-height: .88rem;
  color: #000000;
  height: 100%;
  width: 50%;
  margin: 0 auto;
}

.selectBox .selectWeek img {
  width: 12px;
  height: 12px;
  margin-left: 6px;
  transition: all 0.2s;
}

.selectBox .selectList {
  display: none;
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
}

.selectBox .selectList ul {
  width: 100%;
  padding: 0.22rem 0.2rem 0.12rem;
  position: relative;
  background-color: #FFFFFF;
  overflow: hidden;
}

.selectBox .selectList li {
  float: left;
  width: 25%;
  text-align: center;
  padding: 0.08rem;
}

.selectBox .selectList li p {
  background-color: #F5F6F8;
  border-radius: 0.08rem;
  height: 0.72rem;
  font-size: 0.26rem;
  line-height: 0.72rem;
  color: #333333;
}

.selectBox .selectList li p.active {
  color: #0099FF;
}

.selectBox.active .selectWeek img {
  transform: rotate(180deg);
}

.selectBox.active .selectList {
  display: block;
}

.settingMask {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1;
}

.tipsMask {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1;
}

.settingModal {
  position: absolute;
  right: 0.14rem;
  top: 45px;
  width: 2.1rem;
  background: #ffffff;
  box-shadow: 0px 0px 0.06rem 0px rgba(153, 153, 153, 0.6);
  border-radius: 0.08rem;
}

.settingModal .settingList {
  border-radius: 0.08rem;
  overflow: hidden;
  padding-left: 0;
}

.settingModal .listItem {
  text-align: center;
  height: 0.88rem;
  line-height: 0.88rem;
  font-size: 0.32rem;
  color: #333333;
}

.settingModal:after {
  position: absolute;
  content: '';
  top: -0.18rem;
  right: 0.14rem;
  width: 0.4rem;
  height: 0.2rem;
  background: url(../../../images/examination/trishadow.png) center center/0.4rem 0.2rem no-repeat;
}

.settingTips {
  z-index: 1;
  position: absolute;
  right: 0.14rem;
  top: 45px;
  text-align: center;
  height: 0.88rem;
  line-height: 0.88rem;
  font-size: 0.32rem;
  color: #EFEFEF;
  background: rgba(24, 30, 51, 0.8);
  border-radius: 4px;
  padding-left: 0.16rem;
  padding-right: 0.16rem;
}

.settingTips:after {
  position: absolute;
  content: '';
  top: -0.2rem;
  right: 0.14rem;
  width: 0.4rem;
  height: 0.2rem;
  background: url(../../../images/examination/Triangle.png) center center/0.4rem 0.2rem no-repeat;
}

/*拖动遮罩*/
.leftmaskdiv {
  position: absolute;
  left: 0;
  background: rgba(0, 153, 255, 0.15);
}

.topmaskdiv {
  position: absolute;
  top: 45px;
  background: rgba(0, 153, 255, 0.15);
}

.table {
  position: relative;
  /* top: 95px; */
}

.table:before {
  position: absolute;
  content: '';
  top: 5.59rem;
  left: 0;
  height: 0;
  width: 100%;
}

.table:after {
  position: absolute;
  content: '';
  top: 11.18rem;
  left: 0;
  height: 0;
  width: 100%;
}

table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  /*头部颜色*/
}

table thead tr {
  position: relative;

}

table thead tr::after {
  content: "";
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
  position: absolute;
  background-color: #E5E6EB;
  bottom: 0;
  left: 0;
  right: 0;
}

table thead tr th {
  width: 12.78%;
  height: 1.16rem;
  text-align: center;
  background-color: #FCFCFC;
  padding: 4px;
}

table td {
  width: 12.78%;
  height: 1.55rem;
  text-align: center;
  border-bottom: dashed 1px #F3F4F6;
  border-right: dashed 1px #F3F4F6;
  box-sizing: border-box;
}

table tbody tr {
  height: 1.55rem;
}

table tr th:first-child,
table tr td:first-child {
  width: 9.45%;
}

table tr th:last-child,
table tr td:last-child {
  width: 13.87%;
  border-right: none;
}

table tr th:last-child,
table .col7 {
  padding-right: 0.08rem;
}

table tr td:first-child {
  border-bottom: none;
  border-right: solid 1px #F2F2F2;
}

table td.border {
  border-bottom: solid 1px #e6e7eb !important;
}

table td.delete {
  display: none;
}

table .tddiv {
  position: relative;
  user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
}

table .tddiv p {
  position: relative;
  user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
}

table .tddiv i.delete {
  position: absolute;
  width: 0.6rem;
  height: 0.6rem;
  right: -0.08rem;
  bottom: -0.08rem;
  z-index: 0;
  background: url(../../../images/examination/delete.png) center center / 0.28rem 0.28rem no-repeat;
}

table th span:first-child {
  display: block;
  color: #475466;
  font-size: .30rem;
  line-height: .40rem;
  color: #4E5969;
  font-weight: 500;
}

table th span:last-child {
  display: block;
  color: #86909C;
  font-size: .24rem;
  line-height: .40rem;
  font-weight: normal;
}

table td>div {
  width: calc(100% - 0.08rem);
  height: calc(100% - 0.08rem);
  margin: 0.04rem;
  overflow: hidden;
  word-break: break-all;
  border-radius: 0.08rem;
}

.scheduleCon {
  border-radius: 0.08rem;
  box-sizing: border-box;
}

.scheduleCon h3 {
  color: #FFFFFF;
  font-size: .24rem;
  line-height: .42rem;
  padding: 0 0.1rem;
}

.scheduleCon h5 {
  color: #FFFFFF;
  opacity: 0.7;
  font-size: .18rem;
  line-height: .32rem;
  padding: 0 0.04rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  margin-bottom: 0.16rem;
}

table td div.firstClick {
  background: #E6ECF5;
}

table td img.add {
  display: block;
  width: 0.28rem;
  height: 0.28rem;
}

table .col0 {
  color: #475466;
  background-color: #FCFCFC;
  font-size: 0.24rem;
}

table .sIndex {
  font-size: 0.24rem;
  color: #4E5969;
  line-height: 0.40rem;
  margin-bottom: 0.02rem;
  font-weight: 500;
}

table .stime {
  color: #86909C;
  font-size: .24rem;
  line-height: .28rem;
}

table td[rowspan] {
  border-radius: 0.08rem;
  color: white;
}

table td[rowspan] p {
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
}

table .tddiv.color1 {
  background: #5fd6a9;
}

table .tddiv.color2 {
  background: #FFA998;
}

table .tddiv.color3 {
  background: #FFD166;
}

table .tddiv.color4 {
  background: #B3BAFF;
}

table .tddiv.color5 {
  background: #78C7FB;
}

table .tddiv.color6 {
  background: #FF99B0;
}

table .tddiv.color7 {
  background: #8ED66B;
}

table .tddiv.color8 {
  background: #E6A1CF;
}

table .tddiv.color9 {
  background: #6BB5FF;
}

table .tddiv.color10 {
  background: #FA9191;
}

table .tddiv.color11 {
  background: #FFB866;
}

table .tddiv.color12 {
  background: #7EA4FC;
}

table .courseName {
  transform: scale(0.85);
  transform-origin: left;
  width: 120%;
  font-size: 0.24rem;
  line-height: 0.34rem;
  padding: 0.04rem 0.04rem 0 0.04rem;
  box-sizing: content-box;
}

table .courseLoc,
table .teacher {
  transform: scale(0.85);
  transform-origin: left;
  width: 120%;
  font-size: 0.2rem;
  line-height: 0.28rem;
  margin: 0 0.04rem;
  opacity: 0.7;
  box-sizing: content-box;
}






/*iphone678状态栏*/
.ioswrapMax .tophead {
  padding-top: 44px !important;
  height: auto;
}

.ioswrapMax .settingModal {
  top: 65px;
}

.ioswrapMax .settingTips {
  top: 65px;
}



/*iphone678plus状态栏*/
.iospluswrapMax .tophead {
  padding-top: 44px !important;
  height: auto;
}

.iospluswrapMax .settingModal {
  top: 75px;
}

.iospluswrapMax .settingTips {
  top: 75px;
}