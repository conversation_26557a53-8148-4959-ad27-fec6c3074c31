.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.layui-laypage .layui-disabled {
  background-color: transparent !important;
  border: none !important;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #ACB4BF !important;
  font-size: 14px;
}
::-webkit-scrollbar {
  width: 6px;
  height: 10px;
}
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #d9d9d9;
}
::-webkit-scrollbar-track {
  border-radius: 6px;
}
.popups {
  background: #FFFFFF;
  border-radius: 10px;
  display: none;
}
.popups .title {
  height: 52px;
  line-height: 52px;
  font-size: 16px;
  font-weight: 400;
  padding: 0 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #F2F2F2;
}
.popups .title .name {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  color: #1d2129;
  text-align: left;
}
.popups .title .close {
  width: 20px;
  height: 20px;
  background: url(/css/examination/images/close-icon.png) no-repeat center;
  cursor: pointer;
}
.popups .popup-con {
  padding: 30px;
}
.popups .bottom {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 66px;
  border-top: 1px solid #E5E6EB;
  padding: 0 24px;
}
.popups .bottom div {
  width: 88px;
  height: 34px;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
  border-radius: 18px;
  cursor: pointer;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popups .bottom div.cancle {
  border: 1px solid #C9CDD4;
  color: #4E5969;
  background-color: #fff;
  margin-right: 16px;
}
.popups .bottom div.confirm {
  color: #fff;
  background: #4D88FF;
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  border: 1px solid #4D88FF;
}
.layui-layer-page .layui-layer-content {
  height: auto !important;
}
.exam-popup {
  width: 770px;
}
.exam-popup .popup-con {
  padding: 40px 80px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.exam-popup .popup-con .from-wrapper .lable {
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.exam-popup .popup-con .from-wrapper .lable .name {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 20px;
  width: 92px;
  flex-shrink: 0;
}
.exam-popup .popup-con .from-wrapper .lable .name span {
  font-size: 14px;
  color: #1D2129;
  margin-right: 6px;
}
.exam-popup .popup-con .from-wrapper .lable .name i {
  width: 16px;
  height: 16px;
  display: inline-block;
  background: url(/css/examination/images/feedback.png) no-repeat center;
  cursor: pointer;
  position: relative;
}
.exam-popup .popup-con .from-wrapper .lable .name i:hover em {
  display: block;
}
.exam-popup .popup-con .from-wrapper .lable .name i em {
  display: none;
  position: absolute;
  left: -14px;
  top: -49px;
  width: auto;
  height: 38px;
  border-radius: 6px;
  background: #4E5969;
  padding: 8px 12px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 22px;
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
  z-index: 999;
}
.exam-popup .popup-con .from-wrapper .lable .name i em:after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 16px;
  width: 12px;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  height: 6px;
  background: url(/css/examination/images/black-tringle-icon.png) no-repeat center;
}
.exam-popup .popup-con .from-wrapper .lable .checkbox {
  flex: 1;
}
.exam-popup .popup-con .from-wrapper .lable .checkbox ul {
  overflow: hidden;
}
.exam-popup .popup-con .from-wrapper .lable .checkbox ul li {
  float: left;
  color: #4e5969;
  width: 125px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 24px;
  padding-left: 24px;
  background: url(/css/examination/images/check1-icon.png) no-repeat left center;
  background-size: 16px;
  cursor: pointer;
}
.exam-popup .popup-con .from-wrapper .lable .checkbox ul li.cur {
  background: url(/css/examination/images/checked1-icon.png) no-repeat left center;
  background-size: 16px;
}
.exam-popup .popup-con .from-wrapper .lable .textarea {
  border: 1px solid #E5E6EB;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  border-radius: 4px;
  flex: 1;
  min-height: 132px;
  padding: 6px 9px;
}
.exam-popup .popup-con .from-wrapper .lable .textarea ul {
  overflow: hidden;
}
.exam-popup .popup-con .from-wrapper .lable .textarea ul li {
  float: left;
  background-color: #E1EBFF;
  height: 34px;
  padding: 0 12px;
  border-radius: 12px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-right: 12px;
  margin-bottom: 12px;
  cursor: move;
}
.exam-popup .popup-con .from-wrapper .lable .textarea ul li .delet {
  position: absolute;
  width: 14px;
  height: 15px;
  top: 0;
  right: -4px;
  background: url(/css/examination/images/icon-del.png) no-repeat left center;
  cursor: pointer;
}
.exam-popup .popup-con .from-wrapper .lable .textarea ul li i {
  font-size: 14px;
  color: #4D88FF;
  margin-right: 4px;
}
.exam-popup .popup-con .from-wrapper .lable .textarea ul li span {
  font-size: 14px;
  color: #4D88FF;
}
.layui-layer {
  border-radius: 10px !important;
}
