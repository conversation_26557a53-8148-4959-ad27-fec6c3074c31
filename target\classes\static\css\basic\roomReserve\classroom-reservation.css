body {
  background-color: #F7F8FA;
  font-size: 14px;
  color: #4E5969;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.layui-table-view .layui-table {
  width: auto;
}
.main {
  margin: 20px;
}
.main .top {
  width: 100%;
  height: 60px;
  background: #FFFFFF;
  position: relative;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E8EBF1;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .top .titles {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 22px;
  padding-left: 30px;
}
.main .top .titles .back {
  cursor: pointer;
  margin-right: 16px;
  padding-left: 22px;
  background: url(../../../images/basic/roomReserve/back.png) no-repeat left center;
  background-size: 16px;
  color: #7d92b2;
  font-size: 14px;
}
.main .top .titles ul {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 3px;
}
.main .top .titles ul::after {
  content: "";
  width: 3px;
  height: 16px;
  background-color: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.main .top .titles ul li {
  padding: 0 16px 0 6px;
  font-size: 16px;
  background: url(../../../images/basic/roomReserve/right-icon.png) no-repeat right 5px;
  background-size: 14px;
}
.main .top .titles ul li a {
  color: #4E5969;
}
.main .top .titles ul li:last-child {
  background: unset;
}
.main .top .titles ul li:last-child a {
  color: #1D2129;
  font-weight: 500;
}
.main .top h4 {
  position: relative;
  color: #1d2129;
  font-size: 16px;
  margin-left: 30px;
  padding-left: 9px;
}
.main .top h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}
.main .top .r-title {
  padding-right: 30px;
  display: flex;
  display: -wekit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .top .r-title .book-classroom {
  display: none;
  width: 96px;
  height: 34px;
  box-shadow: 0px 0px 10px 0px #4D88FF33;
  border: 1px solid #4D88FF;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  font-size: 14px;
  color: #4D88FF;
  border-radius: 4px;
  margin-right: 30px;
}
.main .top .r-title .set-appointment-rules {
  width: 127px;
  height: 34px;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  background-color: #4d88ff;
  text-align: center;
  line-height: 34px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  border-radius: 4px;
}
.main .form-con {
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
  font-size: 14px;
  padding: 20px 30px 20px;
  padding-bottom: 0;
  position: relative;
}
.main .form-con .sel-box {
  display: flex;
  display: -webkit-flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  flex: 1;
}
.main .form-con .sel-item {
  display: flex;
  align-items: center;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-right: 24px;
  margin-bottom: 24px;
}
.main .form-con .sel-item .btn {
  width: 104px;
  height: 34px;
  background: #ffffff;
  border-radius: 4px;
  display: block;
  cursor: pointer;
}
.main .form-con .sel-item .btn.btn-search {
  background: #4D88FF;
  border: 1px solid #4D88FF;
  color: #ffffff;
  margin-right: 16px;
  box-shadow: 0px 0px 10px 0px #4D88FF4D;
}
.main .form-con .sel-item .btn.btn-reset {
  border: 1px solid #4D88FF;
  color: #4D88FF;
  box-shadow: 0px 0px 10px 0px #4D88FF33;
}
.main .form-con .sel-item .sel-title {
  color: #1D2129;
  font-size: 14px;
  width: 56px;
  flex-shrink: 0;
  margin-right: 14px;
}
.main .form-con .sel-item .sel-title span {
  display: inline-block;
  width: 14px;
  color: #F76560;
}
.main .form-con .sel-item .sel {
  height: 34px;
  line-height: 34px;
  margin-bottom: 0;
}
.main .form-con .sel-item .sel.seat {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  width: 220px;
}
.main .form-con .sel-item .sel.seat .layui-input {
  width: 100px;
}
.main .form-con .sel-item .sel.seat span {
  margin: 0 14px;
}
.main .form-con .sel-item .sel em {
  float: left;
  font-size: 14px;
  color: #474C59;
}
.main .form-con .sel-item .sel .select-input {
  width: 220px;
  height: 34px;
  border-radius: 4px;
  border: 1px solid #D4D6D9;
  background-color: #fff;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
}
.main .form-con .sel-item .sel .select-input em {
  position: absolute;
  top: 11px;
  right: 11px;
  width: 12px;
  height: 12px;
  background: url(../../../images/basic/roomReserve/drop-down4.png) no-repeat center;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.main .form-con .sel-item .sel .select-input .name {
  font-size: 14px;
  color: #ACB4BF;
  padding-left: 13px;
  line-height: 32px;
  width: 86%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .form-con .sel-item .sel .select-input .name.ckd {
  color: #131B26;
}
.main .form-con .sel-item .sel .select-input.clicked em {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.main .form-con .sel-item .sel .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
  display: block;
}
.main .form-con .sel-item .sel .select-input .select-dropdown {
  width: 100%;
  display: none;
  left: -1px;
  margin: 5px 0;
  padding: 6px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: absolute;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .search {
  margin: 8px;
  height: 36px;
  box-sizing: border-box;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  display: flex;
  align-items: center;
  overflow: hidden;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .search input {
  border: none;
  flex: 1;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  font-size: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  width: 50px;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .search input::placeholder {
  color: #8F97A8;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .search span {
  cursor: pointer;
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  background: url(../../../images/basic/roomReserve/search-icon.png) no-repeat center;
  margin: 9px;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .all-selects {
  color: #4E5969;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  margin: 0 20px;
  padding-left: 24px;
  background: url(../../../images/basic/roomReserve/check-icon.png) no-repeat left center;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .all-selects.cur {
  background: url(../../../images/basic/roomReserve/checked-icon.png) no-repeat left center;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists {
  padding: 0 0 6px;
  max-height: 240px;
  overflow-y: auto;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists li {
  margin: 0;
  line-height: normal;
  line-height: 40px;
  padding: 0 20px;
  clear: both;
  color: #4E5969;
  font-size: 14px !important;
  list-style: none;
  cursor: pointer;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists li span {
  display: block;
  padding-left: 27px;
  background: url(../../../images/basic/roomReserve/check-icon.png) no-repeat left center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists li:hover {
  background: #F5F7FA;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #E1EBFF;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists li.cur span {
  background: url(../../../images/basic/roomReserve/checked-icon.png) no-repeat left center;
}
.main .selected-wrapper {
  padding: 0 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .selected-wrapper span {
  font-size: 14px;
  color: #707070;
  line-height: 18px;
  margin-right: 8px;
}
#classroomReservation {
  width: 1159px;
  height: 680px !important;
}
#classroomReservation .title .close {
  width: 20px;
  height: 20px;
  background: url(../../../images/basic/roomReserve/close-icon.png) no-repeat center;
  cursor: pointer;
}
#classroomReservation .popup-con {
  padding: 40px 80px;
  height: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
#classroomReservation .popup-con .layui-table-body.layui-table-main {
  max-height: 190px;
  overflow-y: auto;
  overflow-x: hidden;
}
#classroomReservation .popup-con .reservatio-inform {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#classroomReservation .popup-con .reservatio-inform .item {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 18px;
  line-height: 18px;
  margin-right: 8px;
}
#classroomReservation .popup-con .reservatio-inform .item .name {
  font-size: 14px;
  color: #1D2129;
  margin-right: 4px;
}
#classroomReservation .popup-con .reservatio-inform .item .texts {
  font-size: 14px;
  color: #4E5969;
}
#classroomReservation .popup-con .layui-form {
  margin-top: 24px;
}
#classroomReservation .popup-con .layui-form .layui-form-item {
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 24px;
}
#classroomReservation .popup-con .layui-form .layui-form-item:last-child {
  margin-bottom: 0;
}
#classroomReservation .popup-con .layui-form .layui-form-item .layui-form-label {
  font-size: 14px;
  color: #1D2129;
  width: 100px;
  margin-right: 0;
}
#classroomReservation .popup-con .layui-form .layui-form-item .layui-form-label em {
  color: #ef3e3e;
}
#classroomReservation .popup-con .layui-form .layui-form-item .borrow-item.hide {
  display: none !important;
}
#classroomReservation .popup-con .layui-form .layui-form-item .layui-input-block {
  width: 230px;
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
  height: 34px;
}
#classroomReservation .popup-con .layui-form .layui-form-item .layui-input-block.layui-textarea1 {
  height: auto;
}
#classroomReservation .popup-con .layui-form .layui-form-item .layui-input-block .select-borrower,
#classroomReservation .popup-con .layui-form .layui-form-item .layui-input-block .add-attachment {
  width: 100%;
  border: 1px dashed #4D88FF;
  border-radius: 4px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  background: #DBEAFF;
  padding: 0 20px;
  height: 34px;
  line-height: 32px;
  cursor: pointer;
}
#classroomReservation .popup-con .layui-form .layui-form-item .layui-input-block .select-borrower span,
#classroomReservation .popup-con .layui-form .layui-form-item .layui-input-block .add-attachment span {
  display: block;
  padding-left: 18px;
  background: url(../../../images/basic/roomReserve/add-icons.png) no-repeat left center;
  font-size: 14px;
  color: #4D88FF;
}
#classroomReservation .popup-con .layui-form .layui-form-item .layui-input-block .hide {
  display: none !important;
}
#classroomReservation .popup-con .layui-form .layui-form-item .layui-input-block .file-inform {
  width: 156px;
  height: 34px;
  border-radius: 4px;
  border: 1px dashed #4d88ff;
  background-color: #dbeaff;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
}
#classroomReservation .popup-con .layui-form .layui-form-item .layui-input-block .file-inform span {
  font-size: 14px;
  color: #4d88ff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: right;
}
#classroomReservation .popup-con .layui-form .layui-form-item .layui-input-block .file-inform em {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
  background: url(../../../images/basic/roomReserve/group-close.png) no-repeat center;
  cursor: pointer;
  margin: 0 4px;
}
#classroomReservationSet {
  width: 652px;
  height: 382px;
  display: none;
}
#classroomReservationSet .title .close {
  width: 20px;
  height: 20px;
  background: url(../../../images/basic/roomReserve/close-icon.png) no-repeat center;
  cursor: pointer;
}
#classroomReservationSet .popup-con {
  padding: 50px 53px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  height: 254px;
}
#classroomReservationSet .popup-con .set-wrapper .item {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 24px;
}
#classroomReservationSet .popup-con .set-wrapper .item .label {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 34px;
  padding-right: 6px;
}
#classroomReservationSet .popup-con .set-wrapper .item .label em {
  font-size: 14px;
  color: #1D2129;
  margin-right: 6px;
}
#classroomReservationSet .popup-con .set-wrapper .item .label i {
  width: 16px;
  height: 16px;
  display: inline-block;
  background: url(../../../images/basic/roomReserve/feedback.png) no-repeat center;
  cursor: pointer;
  position: relative;
}
#classroomReservationSet .popup-con .set-wrapper .item .label i:hover em {
  display: block;
}
#classroomReservationSet .popup-con .set-wrapper .item .label i em {
  display: none;
  position: absolute;
  left: -14px;
  top: -49px;
  width: auto;
  height: 38px;
  border-radius: 6px;
  background: #4E5969;
  padding: 8px 12px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 22px;
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
  z-index: 999;
}
#classroomReservationSet .popup-con .set-wrapper .item .label i em::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 16px;
  width: 12px;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  height: 6px;
  background: url(../../../images/basic/roomReserve/black-tringle-icon.png) no-repeat center;
}
#classroomReservationSet .popup-con .set-wrapper .item .limit-switch {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
#classroomReservationSet .popup-con .set-wrapper .item .limit-switch .layui-form-switch {
  border-radius: 3px;
  background: #D2D3D8;
  height: 14px;
  line-height: 14px;
  min-width: 28px;
  padding: 0 0;
  margin-top: 0;
  border: none;
}
#classroomReservationSet .popup-con .set-wrapper .item .limit-switch .layui-form-switch i {
  left: 2px;
  top: 2px;
  width: 12px;
  height: 10px;
  border-radius: 1px;
  background: #FFF;
  margin-left: 0;
}
#classroomReservationSet .popup-con .set-wrapper .item .limit-switch .layui-form-onswitch {
  border-radius: 3px;
  background: #537AF6;
}
#classroomReservationSet .popup-con .set-wrapper .item .limit-switch .layui-form-onswitch i {
  left: 100%;
  margin-left: -14px;
  background-color: #fff;
}
#classroomReservationSet .popup-con .set-wrapper .item .limit-switch .tit {
  margin-left: 10px;
  padding-top: 9px;
}
#classroomReservationSet .popup-con .set-wrapper .item .limit-switch .tit h4 {
  color: #131B26;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 17px;
  margin-bottom: 4px;
}
#classroomReservationSet .popup-con .set-wrapper .item .limit-switch .tit p {
  color: #8A8B99;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 17px;
}
#classroomReservationSet .popup-con .set-wrapper .item .select-block {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#classroomReservationSet .popup-con .set-wrapper .item .select-block .layui-form-select {
  width: 100px;
}
#classroomReservationSet .popup-con .set-wrapper .item .select-block .tips {
  font-size: 14px;
  color: #86909C;
  margin: 0 14px;
}
#appointmentPersonInformation {
  width: 1293px;
  height: 658px;
  display: none;
}
#appointmentPersonInformation .title .close {
  width: 20px;
  height: 20px;
  background: url(../../../images/basic/roomReserve/close-icon.png) no-repeat center;
  cursor: pointer;
}
#appointmentPersonInformation .popup-con {
  padding: 40px 80px;
}
#appointmentPersonInformation .popup-con .filter-wrapper {
  width: 100%;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#appointmentPersonInformation .popup-con .filter-wrapper .layui-form-item {
  margin-right: 20px;
}
#appointmentPersonInformation .popup-con .filter-wrapper .layui-form-item:last-child {
  margin-right: 0;
}
#appointmentPersonInformation .popup-con .filter-wrapper .layui-form-item .btn {
  width: 96px;
  height: 34px;
  background: #ffffff;
  border-radius: 4px;
  display: block;
  cursor: pointer;
  text-align: center;
  line-height: 32px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
#appointmentPersonInformation .popup-con .filter-wrapper .layui-form-item .btn.btn-search {
  background: #4D88FF;
  border: 1px solid #4D88FF;
  color: #ffffff;
  margin-right: 16px;
  box-shadow: 0px 0px 10px 0px #4D88FF4D;
}
#appointmentPersonInformation .popup-con .filter-wrapper .layui-form-item .btn.btn-reset {
  border: 1px solid #4D88FF;
  color: #4D88FF;
  box-shadow: 0px 0px 10px 0px #4D88FF33;
}
#appointmentPersonInformation .popup-con .filter-wrapper .layui-form-item .layui-form-label {
  width: auto;
  margin-right: 14px;
}
#appointmentPersonInformation .popup-con .filter-wrapper .layui-form-item .layui-input-block {
  width: 230px;
}
#appointmentPersonInformation .popup-con .layui-disabled {
  border-radius: 0;
  border: none !important;
  background: none !important;
}
#appointmentPersonInformation .popup-con .layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: #4D88FF;
}
#appointmentPersonInformation .popup-con .layui-table tr.layui-table-click {
  background: rgba(39, 125, 255, 0.3);
}
#appointmentPersonInformation .popup-con .layui-laypage span:hover {
  color: #999;
}
.layui-textarea {
  resize: none;
}
#appointmentPersonInformationStudent {
  width: 1539px;
  height: 658px;
  display: none;
}
#appointmentPersonInformationStudent .title .close {
  width: 20px;
  height: 20px;
  background: url(../../../images/basic/roomReserve/close-icon.png) no-repeat center;
  cursor: pointer;
}
#appointmentPersonInformationStudent .popup-con {
  padding: 40px 80px;
}
#appointmentPersonInformationStudent .popup-con .filter-wrapper {
  width: 100%;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#appointmentPersonInformationStudent .popup-con .filter-wrapper .layui-form-item {
  margin-right: 20px;
}
#appointmentPersonInformationStudent .popup-con .filter-wrapper .layui-form-item:last-child {
  margin-right: 0;
}
#appointmentPersonInformationStudent .popup-con .filter-wrapper .layui-form-item .btn {
  width: 96px;
  height: 34px;
  background: #ffffff;
  border-radius: 4px;
  display: block;
  cursor: pointer;
  text-align: center;
  line-height: 32px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
#appointmentPersonInformationStudent .popup-con .filter-wrapper .layui-form-item .btn.btn-search {
  background: #4D88FF;
  border: 1px solid #4D88FF;
  color: #ffffff;
  margin-right: 16px;
  box-shadow: 0px 0px 10px 0px #4D88FF4D;
}
#appointmentPersonInformationStudent .popup-con .filter-wrapper .layui-form-item .btn.btn-reset {
  border: 1px solid #4D88FF;
  color: #4D88FF;
  box-shadow: 0px 0px 10px 0px #4D88FF33;
}
#appointmentPersonInformationStudent .popup-con .filter-wrapper .layui-form-item .layui-form-label {
  width: auto;
  margin-right: 14px;
}
#appointmentPersonInformationStudent .popup-con .filter-wrapper .layui-form-item .layui-input-block {
  width: 230px;
}
#appointmentPersonInformationStudent .popup-con .layui-disabled {
  border-radius: 0;
  border: none !important;
  background: none !important;
}
#appointmentPersonInformationStudent .popup-con .layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: #4D88FF;
}
#appointmentPersonInformationStudent .popup-con .layui-table tr.layui-table-click {
  background: rgba(39, 125, 255, 0.3);
}
#appointmentPersonInformationStudent .popup-con .layui-laypage span:hover {
  color: #999;
}
#selectAdjustingClasses {
  width: 863px;
  height: auto;
  display: none;
}
#selectAdjustingClasses .layui-textarea {
  resize: none;
}
#selectAdjustingClasses .title .close {
  width: 20px;
  height: 20px;
  background: url(../../../images/basic/roomReserve/close-icon.png) no-repeat center;
  cursor: pointer;
}
#selectAdjustingClasses .popup-con {
  padding: 40px 54px;
}
#selectAdjustingClasses .popup-con .tips {
  height: 24px;
  line-height: 24px;
  padding-left: 32px;
  background: url(../../../images/basic/roomReserve/tips-blue.png) no-repeat left center;
  font-size: 14px;
  color: #1d2129;
  margin-bottom: 42px;
}
#selectAdjustingClasses .popup-con .week-wrapper .layui-form-item {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#selectAdjustingClasses .popup-con .week-wrapper .layui-form-item .layui-form-label {
  width: 100px;
  margin-right: 0;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
#selectAdjustingClasses .popup-con .week-wrapper .layui-form-item .layui-input-block {
  margin-left: 0;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#selectAdjustingClasses .popup-con .week-wrapper .layui-form-item .layui-input-block .symbol {
  margin: 0 24px;
}
textarea::placeholder {
  font-size: 14px;
}
.layui-form-radio > i {
  background: url(../../../images/basic/roomReserve/checkbox-icon.png) no-repeat center;
  background-size: 16px;
  color: transparent;
  box-shadow: none;
}
.layui-form-radioed > i {
  background: url(../../../images/basic/roomReserve/checkbox-cur-icon.png) no-repeat center;
  background-size: 16px;
  color: transparent;
  box-shadow: none;
}
.layui-table thead tr,
.layui-table-header {
  background-color: #4D88FF;
  color: #fff;
}
.layui-table-view .layui-table tr th > div {
  color: #fff;
}
.layui-form-radio:hover *,
.layui-form-radioed,
.layui-form-radioed > i {
  color: #4D88FF;
}
