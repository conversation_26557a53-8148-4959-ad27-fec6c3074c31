.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}
.flex {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}
.transforms {
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}
.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
body {
    background-color: #F7F8FA;
    padding: 20px;
    font-size: 14px;
    color: #4e5969;
    font-family: PingFang SC;
}
.hide {
    display: none !important;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: #4080FF !important;
}
.layui-laypage a:hover {
    color: #4080FF !important;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
    border-color: #4080FF !important;
}
.sel-item {
    display: flex;
    /* align-items: flex-start; */
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-right: 12px;
    /* padding-bottom: 18px; */
}
.sel-item .sel-title {
    color: #1D2129;
    font-size: 14px;
    width: 98px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}
.sel-item .sel-title span {
    display: inline-block;
    width: 14px;
    color: #F76560;
}
.sel-item .sel-title i {
    width: 16px;
    height: 16px;
    margin-left: 6px;
    background: url(../images/gloss-icon.png) no-repeat center;
    cursor: pointer;
    position: relative;
}
.sel-item .sel-title i.cur em {
    display: block;
}
.sel-item .sel-title i em {
    display: none;
    position: absolute;
    left: -86px;
    top: 27px;
    z-index: 999;
    border-radius: 8px;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    padding: 14px 24px;
    font-size: 14px;
    color: #4E5969;
    line-height: 20px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    height: 48px;
    white-space: nowrap;
    padding-left: 33px;
}
.sel-item .sel-title i em:before {
    content: '';
    position: absolute;
    left: 24px;
    top: 22px;
    width: 4px;
    height: 4px;
    background-color: #4D88FF;
}
.sel-item .sel-title i em:after {
    content: '';
    position: absolute;
    left: 88px;
    top: -5px;
    width: 12px;
    height: 6px;
    background: url(../images/tringle-icon.png) no-repeat center;
}
.sel {
    width: 200px;
    margin-right: 40px;
    height: 34px;
    line-height: 34px;
}
.sel em {
    float: left;
    font-size: 14px;
    color: #474C59;
}
.sel .radio-box {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
}
.sel .radio-box span {
    font-size: 14px;
    color: #4E5969;
    padding-left: 24px;
    background: url(../images/radio-icon.png) no-repeat left center;
    margin-right: 24px;
    cursor: pointer;
}
.sel .radio-box span.cur {
    background: url(../images/radio-cur-icon.png) no-repeat left center;
}
.sel .select-input {
    height: 34px;
    border-radius: 4px;
    border: 1px solid #D4D6D9;
    background-color: #fff;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    cursor: pointer;
}
.sel .select-input input {
    border: none;
    outline: none;
    background-color: transparent;
    width: 100%;
    height: 100%;
    font-size: 14px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-left: 13px;
    color: #1D2129;
    display: block;
}
.sel .select-input i {
    position: absolute;
    top: 11px;
    right: 11px;
    width: 12px;
    height: 12px;
    background: url(../../images/cultivation/icon-arrow1.png) no-repeat center;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}
.sel .select-input .name {
    font-size: 14px;
    color: #ACB4BF;
    padding-left: 13px;
    line-height: 32px;
    width: 86%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sel .select-input .name.ckd {
    color: #131B26;
}
.sel .select-input.clicked i {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
}
.sel .select-input.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
}
.sel .select-input .select-dropdown {
    width: 100%;
    left: -1px;
    margin: 5px 0;
    padding: 6px 0;
    background-color: #fff;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
    border-radius: 8px;
    position: absolute;
    transform: translate(0, -50px);
    -webkit-transform: translate(0, -50px);
    z-index: 900;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
    z-index: -1;
}
.sel .select-input .select-dropdown .search {
    margin: 8px;
    height: 36px;
    box-sizing: border-box;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    display: flex;
    align-items: center;
}
.sel .select-input .select-dropdown .search input {
    border: none;
    flex: 1;
    background: transparent;
    height: 36px;
    line-height: 36px;
    padding-left: 14px;
    font-size: 14px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    float: left;
}
.sel .select-input .select-dropdown .search input::placeholder {
    color: #8F97A8;
}
.sel .select-input .select-dropdown .search span {
    cursor: pointer;
    float: left;
    width: 16px;
    height: 16px;
    background: url(../../images/cultivation/search-icon.png) no-repeat center;
    margin: 9px;
}
.sel .select-input .select-dropdown .all-selects {
    color: #4E5969;
    font-size: 14px;
    height: 40px;
    line-height: 40px;
    margin: 0 20px;
    padding-left: 24px;
    background: url(../../images/cultivation/check-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .all-selects.cur {
    background: url(../../images/cultivation/checked-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .dropdown-lists {
    padding: 0 0 6px;
    max-height: 240px;
    overflow: auto;
}
.sel .select-input .select-dropdown .dropdown-lists li {
    margin: 0;
    line-height: normal;
    line-height: 40px;
    padding: 0 20px;
    clear: both;
    color: #4E5969;
    font-size: 14px !important;
    list-style: none;
    cursor: pointer;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sel .select-input .select-dropdown .dropdown-lists li span {
    display: block;
    padding-left: 27px;
    background: url(../../images/cultivation/check-icon.png) no-repeat left center;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
}
.sel .select-input .select-dropdown .dropdown-lists li:hover {
    background: #F5F7FA;
}
.sel .select-input .select-dropdown .dropdown-lists li.cur {
    background: #E1EBFF;
}
.sel .select-input .select-dropdown .dropdown-lists li.cur span {
    background: url(../../images/cultivation/checked-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .dropdown-list li {
    margin: 0;
    line-height: normal;
    padding: 0 16px;
    clear: both;
    color: #4E5969;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
}
.sel .select-input .select-dropdown .dropdown-list li:hover {
    background: #E1EBFF;
}
.sel.times .sel-time {
    width: 104px;
    height: 34px;
}
.sel.times .sel-time span {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 18px;
    height: 18px;
    background: url(../image/time-icon.png) no-repeat center;
}
#editPoups {
    width: auto;
}
#editPoups .popup-con {
    padding: 0;
}
#editPoups .popup-con .illustrate {
    width: 100%;
    height: 49px;
    background: #E1EBFF;
    text-align: center;
    line-height: 49px;
    font-size: 14px;
    color: #6581BA;
}
#editPoups .popup-con .illustrate ul li {
    display: none;
}
#editPoups .popup-con .illustrate ul li.cur {
    display: block;
}
#editPoups .popup-con .content {
    padding: 30px 19px;
    padding-bottom: 10px;
    padding-top: 10px;
}
#editPoups .popup-con .content .screen-box {
    margin-bottom: 24px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    justify-content: space-between;
}
#editPoups .popup-con .content .screen-box .sel-list {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
}
#editPoups .popup-con .content .screen-box .btns {
    display: flex;
    display: -webkit-flex;
    justify-content: flex-start;
    align-items: center;
    /* margin-bottom: 18px; */
}
#editPoups .popup-con .content .screen-box .btns .reset {
    border-radius: 4px;
    border: 1px solid #4D88FF;
    background: #FFF;
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
    width: 86px;
    height: 34px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
    margin-right: 16px;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    color: #4D88FF;
}
#editPoups .popup-con .content .screen-box .btns .screen {
    width: 86px;
    height: 34px;
    border-radius: 4px;
    background: var(---primary---color-primary, #4D88FF);
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
    text-align: center;
    line-height: 34px;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    color: #fff;
}
#editPoups .popup-con .content .oprate {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 20px;
    margin-bottom: 24px;
}
#editPoups .popup-con .content .oprate .t-name {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 14px;
    color: #1D2129;
}
#editPoups .popup-con .content .oprate .t-name span {
    display: inline-block;
    width: 14px;
    color: #F76560;
}
#editPoups .popup-con .content .oprate .invisible-switch {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 20px;
}
#editPoups .popup-con .content .oprate .invisible-switch .name {
    font-size: 14px;
    color: #1D2129;
    margin-right: 14px;
}
#editPoups .popup-con .content .oprate .invisible-switch .switc-con {
    display: flex;
    align-items: center;
    cursor: pointer;
}
#editPoups .popup-con .content .oprate .invisible-switch .switc-con .switch {
    position: relative;
    width: 28px;
    height: 14px;
    background: #cdcece;
    border-radius: 4px;
}
#editPoups .popup-con .content .oprate .invisible-switch .switc-con .switch span {
    width: 12px;
    height: 10px;
    position: absolute;
    top: 2px;
    left: 2px;
    background-color: #FFFFFF;
    border-radius: 2px;
}
#editPoups .popup-con .content .oprate .invisible-switch .switc-con .switch.switch-open {
    background: #4D88FF;
}
#editPoups .popup-con .content .oprate .invisible-switch .switc-con .switch.switch-open span {
    left: unset;
    right: 2px;
}
#editPoups .popup-con .content .oprate .invisible-switch .switc-con .switch-con {
    color: #C9CDD4;
    margin-left: 10px;
}
#editPoups .popup-con .content .c-table.noborder .thead ul li:hover {
    border-radius: 4px;
    border: 1px solid #E8EBF3;
}
#editPoups .popup-con .content .c-table.noborder .tbody ul li:first-child:hover {
    border: 1px solid #E8EBF3;
}
#editPoups .popup-con .content .c-table .thead {
    margin-bottom: 4px;
}
#editPoups .popup-con .content .c-table .thead ul {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}
#editPoups .popup-con .content .c-table .thead ul li {
    flex: 1;
    color: #4E5969;
    font-size: 14px;
    margin-right: 4px;
    border-radius: 4px;
    height: 36px;
    line-height: 34px;
    text-align: center;
    background-color: #E8EBF3;
    border: 1px solid #E8EBF3;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    cursor: pointer;
}
#editPoups .popup-con .content .c-table .thead ul li:first-child {
    flex: 0 0 30px;
    flex-shrink: 0;
}
#editPoups .popup-con .content .c-table .thead ul li:last-child {
    margin-right: 0;
}
#editPoups .popup-con .content .c-table .thead ul li:hover {
    border-radius: 4px;
    border: 1px solid #4D88FF;
}
#editPoups .popup-con .content .c-table .thead ul li.active {
    background-color: #E1EBFF;
}
#editPoups .popup-con .content .c-table .thead ul li.active span {
    color: #4D88FF;
}
#editPoups .popup-con .content .c-table .thead ul li.active:hover {
    border: 1px solid #E1EBFF;
}
#editPoups .popup-con .content .c-table .tbody ul {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 4px;
}
#editPoups .popup-con .content .c-table .tbody ul.carve-up {
    height: 4px;
    background: url(../images/line-bg.png) repeat-x;
}
#editPoups .popup-con .content .c-table .tbody ul li {
    flex: 1;
    border-radius: 4px;
    margin-right: 4px;
    color: #4E5969;
    font-size: 14px;
    font-weight: 400;
    height: 36px;
    width: 0;
    cursor: pointer;
}
#editPoups .popup-con .content .c-table .tbody ul li .lab-con {
    width: 100%;
    height: 36px;
    border-radius: 4px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    background: #F7F8FA;
    border: 1px solid #F7F8FA;
}
#editPoups .popup-con .content .c-table .tbody ul li.active .lab-con {
    background: #e1ebff url(../images/ring.png) no-repeat center;
    border: 1px solid #e1ebff;
}
#editPoups .popup-con .content .c-table .tbody ul li.active:hover .lab-con {
    border: 1px solid #e1ebff;
}
#editPoups .popup-con .content .c-table .tbody ul li:hover .lab-con {
    border-radius: 4px;
    border: 1px solid #4D88FF;
}
#editPoups .popup-con .content .c-table .tbody ul li:first-child {
    flex: 0 0 30px;
    flex-shrink: 0;
    text-align: center;
    display: flex;
    display: -webkit-flex;
    justify-content: flex-start;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: #E8EBF3;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}
#editPoups .popup-con .content .c-table .tbody ul li:first-child span {
    width: 30px;
    font-size: 14px;
    color: #4E5969;
}
#editPoups .popup-con .content .c-table .tbody ul li:first-child.active {
    background-color: #E1EBFF;
}
#editPoups .popup-con .content .c-table .tbody ul li:first-child.active span {
    color: #4D88FF;
}
#editPoups .popup-con .content .c-table .tbody ul li:first-child.active:hover {
    border: 1px solid #E1EBFF;
}
#editPoups .popup-con .content .c-table .tbody ul li:first-child:hover {
    border: 1px solid #4D88FF;
}
#editPoups .popup-con .content .c-table .tbody ul li:last-child {
    margin-right: 0;
}
.layui-table-view .layui-table {
    width: 100% !important;
}
.layui-table-view .layui-table th:last-child {
    border-right: none;
}
.layui-table-view .layui-table tr th > div span {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    cursor: default !important;
    justify-content: center;
}
.layui-table-view .layui-table td .layui-table-cell {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    justify-content: center;
}
.layui-table-view .layui-table td .icon-drag {
    width: 12px;
    display: inline-block;
    height: 12px;
    background: url(../images/drap-icon.png) no-repeat center;
    cursor: move;
}
.layui-table-view .layui-table th:first-child .layui-table-cell {
    padding: 6px;
    padding-left: 16px;
}
.fixed-bottom {
    position: relative;
    height: 1px;
}
.fixed-bottom .selectAll {
    position: absolute;
    left: 19px;
    top: -30px;
    display: block;
    font-size: 14px;
    color: #6581BA;
    background-size: 16px;
    cursor: pointer;
}
.fixed-bottom .selectAll span {
    position: relative;
    display: block;
    padding-left: 28px;
}
.fixed-bottom .selectAll span.cur:after {
    background: url(../../images/cultivation/checked-icon.png) no-repeat center;
    border: none;
    color: transparent;
}
.fixed-bottom .selectAll span:after {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    width: 16px;
    height: 16px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    border: 1px solid #E5E6EB;
    border-radius: 3px;
}
.main .con .table .layui-table-view .layui-table th:first-child .layui-table-cell {
    padding: 6px 15px;
}
.jw-dialog {
    z-index: 29999999;
}
.sel-item .sel-title {
    width: 56px;
    margin-right: 7px;
}
.main .con .table .layui-table-view .layui-table td:nth-child(2) .layui-table-cell {
    overflow: hidden;
}
.main .con .table .layui-table-view .layui-table td:nth-child(1) .layui-table-cell {
    overflow: hidden;
}
.layui-table-view .layui-table td .layui-table-cell .disabled {
    font-size: 14px;
    color: #C9CDD4;
}
.layui-table-view .layui-table th:first-child .layui-table-cell {
    padding: 6px 15px;
}
.layui-form-label {
    width: 115px;
    padding: 0;
    color: #83889D;
    text-align: left;
    height: 34px;
    color: #1D2129;
    font-weight: 400;
    font-size: 14px;
    line-height: 34px;
}
.layui-form-label.w70 {
    width: 70px;
}
.layui-input-block {
    min-height: 34px;
}
.layui-form-radio {
    margin-top: 3px;
}
.layui-form-item {
    padding-left: 0;
    margin-bottom: 24px;
}
.layui-form-item .layui-inline {
    margin-right: 0;
}
.layui-input,
.layui-textarea,
.layui-select {
    border-radius: 4px;
    border-color: #E5E6EB;
}
.layui-input {
    border-color: #E5E6EB;
    border-radius: 4px;
}
.layui-input::placeholder {
    color: #BCBCC5;
}
.layui-form-select dl dd.layui-this {
    background-color: #4C85FA;
}
.layui-btn {
    display: block;
    width: 100px;
    height: 34px;
    border-radius: 4px;
    text-align: center;
    line-height: 34px;
    font-size: 16px;
    cursor: pointer;
    float: left;
}
.layui-btn.layui-chaoxing-default-btn {
    border: 1px dashed #E5E6EB;
    border-radius: 4px;
    background: #EEEFF2;
    width: 110px;
    height: 34px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    text-align: center;
    line-height: 30px;
    padding: 0;
}
.layui-btn.layui-chaoxing-default-btn:hover {
    opacity: 1;
    background: #EFF1FF;
    border: 1px dashed #4A7CFE;
    border-radius: 4px;
}
.layui-btn.layui-chaoxing-default-btn:active {
    opacity: 1;
    background: #D3E0FF;
    border: 1px dashed #4A7CFE;
    border-radius: 4px;
}
.layui-btn.btn-cancel {
    background: #F5F6F8;
    border: 1px solid #4C85FA;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    color: #4C85FA;
}
.layui-btn.btn-related {
    border: 1px solid #525669;
    color: #525669;
    background: none;
}
.layui-btn.btn-search {
    background: #525669;
    color: #FFFFFF;
}
.layui-btn.btn-save {
    background: #4C85FA;
    color: #FFFFFF;
}
.layui-form-radio {
    margin: 3px 10px 0 0;
    padding-right: 12px;
}
.layui-form-radio * {
    color: #4E5969;
}
.layui-form-radio > i {
    margin-right: 10px;
}
.layui-form-radioed > i {
    color: #4C85FA;
}
.layui-form-radioed > i,
.layui-form-radio > i:hover {
    color: #4C85FA;
}
.layui-form-checkbox {
    margin: 0 10px 0 0;
}
.layui-form-checkbox[lay-skin="primary"] span {
    color: #4E5969;
    font-size: 14px;
    line-height: 16px;
}
.layui-form-checkbox[lay-skin="primary"] {
    padding-left: 24px;
    min-width: 16px;
    min-height: 16px;
}
.layui-form-checkbox[lay-skin="primary"] i {
    margin-right: 6px;
}
.layui-form-checkbox[lay-skin="primary"]:hover i {
    border-color: #4C85FA;
    color: #fff;
}
.layui-form-checked[lay-skin="primary"] i {
    border-color: #4C85FA !important;
    background-color: #4C85FA;
}
.layui-textarea::placeholder {
    color: #BCBCC5;
}
.layui-upload-drag {
    padding: 6px 15px;
    margin-right: 10px;
}
.layui-upload-drag .layui-icon {
    color: #4C85FA;
}
.uploadIntro p {
    color: #BCBCC5;
    margin: 5px 0px;
}
.layui-form-onswitch {
    border-color: #BCBCC5;
    background-color: #BCBCC5;
}
.layui-table {
    color: #4E5969;
    margin: 0;
}
.layui-table-view .layui-table th,
.layui-table-view .layui-table td {
    border-color: #E8EBF1;
}
.layui-table thead tr,
.layui-table-header,
.layui-table-tool,
.layui-table-total,
.layui-table-total tr,
.layui-table-patch,
.layui-table-mend,
.layui-table[lay-even] tr:nth-child(even),
.layui-table tbody tr:hover,
.layui-table-hover,
.layui-table-click {
    border-color: #E8EBF3;
}
.layui-table tr {
    height: 40px;
}
.layui-table-view .layui-table tr th > div {
    margin: -5px 0px;
    height: 40px;
    text-align: center;
}
.layui-table-view .layui-table tr th:nth-child(1) > div {
    border: none;
}
.layui-table thead tr,
.layui-table-header,
.layui-table-tool,
.layui-table-total,
.layui-table-total tr,
.layui-table-patch,
.layui-table-mend,
.layui-table[lay-even] tr:nth-child(even) {
    background: #F7F8FA;
}
.layui-table thead tr,
.layui-table-header {
    background: #F2F4F7;
    color: #8A8B99;
    font-size: 14px;
}
.layui-table-click {
    background: none;
}
.j-table table tbody tr {
    transition: none;
    -webkit-transition: none;
}
.j-table table tbody tr:nth-child(even) {
    background: #F7F8FA;
}
.j-table table tbody tr:nth-child(even) td {
    border-right: 1px solid #F7F8FA !important;
}
.j-table table tbody tr:nth-child(even) td:hover {
    border-right: 1px solid #DDE7FF !important;
}
.j-table table tbody tr:nth-child(even):hover {
    background: #DDE7FF;
}
.j-table table tbody tr:nth-child(even):hover td {
    border-right: 1px solid #DDE7FF !important;
}
.layui-table tr {
    transition: none !important;
    -webkit-transition: none !important;
}
.layui-table tr:nth-child(2n) {
    background: #FAFBFC;
}
.layui-table-view {
    margin: 0;
}
.layui-table-page {
    text-align: right;
}
.layui-table-view .layui-table tr th > div {
    color: #8A8B99;
    font-weight: 400;
    font-size: 14px;
}
.layui-table-view .layui-table tr th.icons > div {
    padding-left: 44px;
}
.layui-table-view .layui-table tr th.icons:first-child > div {
    padding-left: 0;
}
.layui-table-cell {
    font-size: 14px;
    color: #1D2129;
}
.layui-table-view .layui-table th .layui-table-cell {
    overflow: visible;
}
.layui-form-select dl {
    padding: 0;
    border: none;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
    top: 38px;
    border-radius: 4px;
}
.layui-form-select dl dt,
.layui-form-select dl dd {
    height: 34px;
    color: #4E5969;
    font-weight: 400;
    font-size: 14px;
    padding: 0 20px;
    line-height: 34px;
}
.layui-form-select dl dt:hover,
.layui-form-select dl dd:hover {
    background: #F7F8FA;
}
.layui-form-select dl dd.layui-this {
    background: #DDE7FF;
    color: #4A7CFE;
}
.layui-upload-choose {
    color: #86909C;
    font-weight: 400;
    font-size: 14px;
    height: 34px;
    line-height: 34px;
    margin-bottom: 0;
}
.layui-form-item .layui-inline {
    margin-bottom: 0;
}
.layui-form-item .layui-input-inline {
    width: 800px;
}
.layui-form-item .layui-input-inline.input-lab {
    width: 360px;
}
.layui-form-radio > i {
    font-size: 18px;
}
.layui-form-radioed > i {
    background: url(../images/selected-radio.png) no-repeat center;
    background-size: 18px;
    color: transparent;
    box-shadow: none;
}
.layui-form-select .layui-select-title {
    width: 100%;
}
.layui-form-checkbox[lay-skin="primary"] i {
    border: 1px solid #E5E6EB;
    border-radius: 3px;
    box-sizing: border-box;
    box-sizing: -webkit-border-box;
}
.layui-form-checked[lay-skin="primary"] i {
    background: url(../../images/cultivation/checked-icon.png) no-repeat center;
    border: none;
    color: transparent;
}
.layui-form-checked[lay-skin="primary"] i:before {
    display: none;
}
.layui-form-select {
    width: 100%;
}
.layui-layer-hui {
    border-radius: 4px !important;
}
.layui-layer-hui .layui-layer-content {
    padding: 10px 25px !important;
}
.layui-table-view .layui-form-checkbox[lay-skin="primary"] i {
    width: 16px;
    height: 16px;
}
.layui-laydate tr .layui-this {
    background: #4A7CFE !important;
}
.layui-laydate-content tr td.laydate-selected {
    background: #E9F0FF !important;
}
.layui-laydate-footer span:hover {
    color: #4A7CFE !important;
}
.layui-laydate-header i:hover,
.layui-laydate-header span:hover {
    color: #4A7CFE !important;
}
.layui-btn-normal {
    background: #4A7CFE !important;
}
.layui-laydate .layui-laydate-list .layui-this {
    background: #4A7CFE !important;
}
.layui-btn-primary:hover {
    border-color: #4A7CFE !important;
    color: #4A7CFE;
}
.layui-layer-tips {
    margin-top: -12px !important;
}
.showImgEdit .layui-btn {
    width: auto;
}
.layui-fluid {
    padding: 15px;
}
.laydate-footer-btns span {
    border: none !important;
}
.layui-laydate .laydate-disabled {
    border: none !important;
}
.layui-layer-tips i.layui-layer-TipsR {
    left: -16px !important;
    transform: rotate(-90deg) !important;
    top: 12px !important;
}
.layui-btn {
    background-color: #4A7CFE;
}
.layui-btn-primary {
    background-color: #fff;
}
.layui-layer-page .layui-layer-content {
    overflow: visible !important;
    height: auto !important;
}
.layui-laypage button {
    margin-left: 10px;
    padding: 0 10px;
    cursor: pointer;
    background: #F1F3F6;
    border-radius: 4px;
    color: #4D88FF;
}
.layui-laydate,
.layui-laydate-hint {
    border: none !important;
}
.fixed-bottom .selected {
    position: absolute;
    left: 145px;
    top: -30px;
    color: #8F97A8;
    font-family: "PingFang SC";
    font-size: 14px;
}
.fixed-bottom .selected i {
    color: #6AA1FF;
}