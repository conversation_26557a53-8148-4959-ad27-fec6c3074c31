.layui-form-label{
    width: auto!important;
    line-height: 34px;
    padding: 0 16px 0 0;
}
.layui-form-label i{
  color: #fc342d;
  padding-right: 3px;
  position: relative;
  top: 3px;
}
.layui-form .layui-form-label, .layui-form .layui-form-label {
    line-height: 34px;
}
.layui-form-checkbox[lay-skin="primary"]:hover i {
    border-color: #4c85fa;
    color: #fff;
  }
  .layui-form-checked[lay-skin="primary"] i {
    border-color: #4c85fa !important;
    background-color: #4c85fa;
  }
.layui-btn {
    padding: 0 16px;
    height: 34px;
    line-height: 32px;
    background: #4d88ff;
    border-radius: 4px;
}
.layui-btn.layui-btn-primary {
    background-color: unset;
    border: 1px solid #4d88ff;
    color: #4d88ff;
}
.layui-layer{
    position: fixed;
    left: 50%!important;
    top: 50%!important;
    transform: translate(-50%,-50%)!important;
    border-radius: 10px;
  }
  .layui-layer-page .layui-layer-content{
    height: auto!important;
  }
  .layui-table-view .layui-table td[data-field="teacherList"] .layui-table-cell {
    overflow: unset;
  }
  
  .layui-table-cell {
    height: auto;
    padding: 0 16px;
  }
  .layui-table-view .layui-table th{
    font-weight: normal;
    color: #6581BA;
  }
  .layui-table-cell {
    height: auto;
  }
  .layui-table-view .layui-table tr th > div{
    margin: 0;
  }
  .layui-table-view .layui-table td{
    padding: 4px 0;
  }
  
  .layui-border {
    border-width: 1px;
    border-style: solid;
    color: #5f5f5f !important;
    width: 110px;
    height: 30px;
    display: inline-block;
    border-color: #E5E6EB;
    border-radius: 4px;
    padding: 0 6px;
  }
  
  .layui-border option {
    font-size: 14px;
    line-height: 30px !important;
  }
  
  .layui-border option:hover {
    background: #e1ebff;
  }
  
  .layui-border option:checked {
    background: #e1ebff;
  }
  
  .layui-laypage a,
  .layui-laypage span {
    border: none;
  }
  
  .layui-laypage a,
  .layui-laypage span {
    padding: 0 11px;
    margin: 0 5px;
    background: #f1f3f6;
    border-radius: 4px;
    color: #4E5969;
  }
  
  .layui-laypage .layui-laypage-curr .layui-laypage-em {
    background: #4c85fa;
    border-radius: 4px;
  }
  
  .layui-laypage input:focus,
  .layui-laypage select:focus {
    border-color: #4c85fa !important;
  }
  
  .layui-laypage a:hover {
    color: #4c85fa;
  }
  
  .layui-laypage-prev {
    background-color: transparent !important;
  }
  
  .layui-laypage-next {
    background-color: transparent !important;
  }
  
  .layui-laypage-spr {
    background-color: transparent !important;
  }
  
  .layui-laypage-skip {
    background-color: transparent !important;
  }
  
  .layui-laypage-count {
    background-color: transparent !important;
  }
  
  .layui-laypage-skip input {
    border: 1px solid #e2e2e2;
    background-color: transparent;
  }
  
  .layui-laypage-skip input {
    background: #ffffff;
    border: 1px solid #e7eaf1;
    color: #4d4d4d;
    border-radius: 2px;
  }
  
  .layui-laypage button {
    background: #f5f8fb;
    border: 1px solid #e7eaf1;
    color: #595959;
    border-radius: 2px;
  }
  
  .layui-table-page>div {
    text-align: right;
  }
  
  .layui-table-edit {
    height: 100% !important;
    text-align: left;
  }
  
  .layui-table-edit:focus {
    border-color: #4c85fa !important;
  }
  
  .layui-table-view .layui-form-checkbox[lay-skin="primary"] i {
    width: 16px;
    height: 16px;
    border-radius: 3px;
  }
  .layui-table-page{
    text-align: center;
  }
  .layui-table-view .layui-table td[data-edit]:hover:after{
    border: 1px solid #4d88ff;
  }
  .layui-table-column{
    height: 46px;
  }
  .layui-table-view .layui-table th,
.layui-table-view .layui-table td {
  border-color: #e8ebf1;
  height: 36px;
}

.layui-table thead tr,
.layui-table-header,
.layui-table-tool,
.layui-table-total,
.layui-table-total tr,
.layui-table-patch,
.layui-table-mend,
.layui-table[lay-even] tr:nth-child(even),
.layui-table tbody tr:hover,
.layui-table-hover,
.layui-table-click {
  
  border-color: #e8ebf1;
}

.layui-table-view .layui-table tr th > div {
  margin: -5px 0px;
  height: 36px;
  line-height: 36px;
}

.layui-table-view .layui-table tr th:nth-child(1) > div {
  border: none;
}

.layui-table thead tr,
.layui-table-header,
.layui-table-tool,
.layui-table-total,
.layui-table-total tr,
.layui-table-patch,
.layui-table-mend,
.layui-table[lay-even] tr:nth-child(even) {
  background-color: #fafbfc;
}

.layui-table thead tr,
.layui-table-header {
  background-color: #f1f3f6;
}

.layui-table-click {
  background: none;
}

table tbody tr:nth-child(even) {
  background-color: #f8f8fa;
}

.layui-table tbody tr:hover,
.layui-table-hover {
  background-color: #edf2fd;
}

.layui-table-view .layui-table td {
  padding: 4px 0;
}

.layui-table-view {
  margin: 0;
}