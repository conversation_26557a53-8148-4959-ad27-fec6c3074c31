.borDer {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #bcbcc5;
  font-size: 12px;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
body {
  font-family: 'PingFang SC';
  background: #F6F7FE;
}
.yz-header {
  width: 100%;
  height: 60px;
  background: linear-gradient(180deg, #5562C6 0%, #202864 100%);
  background: -webkit-linear-gradient(180deg, #5562C6 0%, #202864 100%);
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding: 0 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.yz-header img {
  margin-right: 16px;
}
.yz-header .name {
  font-weight: 900;
  font-size: 20px;
  color: #FFFFFF;
}
.yz-header.titpic img {
  display: block;
}
.yz-header.titpic .name {
  display: block;
}
.yz-header.singlepic .name {
  display: none;
}
.yz-header.singlepic img {
  display: block;
}
.yz-header.singletit .name {
  display: block;
}
.yz-header.singletit img {
  display: none;
}
.yz-con {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 20px;
  padding-top: 20px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
}
.yz-con .yz-scroll {
  width: 220px;
  background-color: #293996;
  border-radius: 8px;
  flex-shrink: 0;
  padding-top: 13px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
}
.yz-con .yz-menu {
  width: 220px;
  background-size: cover;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
}
.yz-con .yz-menu .menu-list {
  padding: 4px 8px;
  padding-bottom: 15px;
}
.yz-con .yz-menu .menu-list ul li {
  width: 100%;
  height: auto;

  position: relative;
  border: 1px solid transparent;
  cursor: pointer;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;


  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  line-height: 22px;
  color: #FFFFFF;
  /*padding-left: 16px;*/
  /*padding-right: 16px;*/
}
.yz-con .yz-menu .menu-list ul li:hover {
  border-radius: 2px;
  background: rgba(18, 29, 109, 0.4);
}
.yz-con .yz-menu .menu-list ul li.cur {
  border-radius: 2px;
  background: linear-gradient(296deg, #6A82FF 0%, #4C73FF 100%);
  background: -webkit-linear-gradient(296deg, #6A82FF 0%, #4C73FF 100%);
}
.yz-con .yz-menu .menu-list ul li .copy {
  font-weight: normal;
  font-size: 12px;
  display: none;
}
.yz-con .yz-menu .menu-list ul li:hover .copy {
  display: block;
}
.yz-con .yz-menu .menu-list ul li .label-top {
  width: 100%;
  display: flex;
  height: 40px;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  border: 1px solid transparent;
  cursor: pointer;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
}
.yz-con .yz-menu .menu-list ul li .label-top:hover {
  border-radius: 2px;
  background: rgba(18, 29, 109, 0.4);
}
.yz-con .yz-menu .menu-list ul li .label-top.cur {
  border-radius: 2px;
  background: linear-gradient(296deg, #6A82FF 0%, #4C73FF 100%);
  background: -webkit-linear-gradient(296deg, #6A82FF 0%, #4C73FF 100%);
}
.yz-con .yz-menu .menu-list ul li .label-top.active .slide-arrow {
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
}
.yz-con .yz-menu .menu-list ul li .label-top .icon {
  margin-left: 12px;
  width: 16px;
  height: 16px;
}
.yz-con .yz-menu .menu-list ul li .label-top .icon.zygl-icon {
  background: url(../../images/form/zygl-icon.png) no-repeat center;
  background-size: contain;
}
.yz-con .yz-menu .menu-list ul li .label-top .icon.gjgl-icon {
  background: url(../../images/form/gjsz-icon.png) no-repeat center;
  background-size: contain;
}
.yz-con .yz-menu .menu-list ul li .label-top .icon.qxgl-icon {
  background: url(../../images/form/qxgl-icon.png) no-repeat center;
  background-size: contain;
}
.yz-con .yz-menu .menu-list ul li .label-top .icon.tjfx-icon {
  background: url(../../images/form/thfx-icon.png) no-repeat center;
  background-size: contain;
}
.yz-con .yz-menu .menu-list ul li .label-top .icon.xtsz-icon {
  background: url(../../images/form/xtsz-icon.png) no-repeat center;
  background-size: contain;
}
.yz-con .yz-menu .menu-list ul li .label-top .icon.mhpz-icon {
  background: url(../../images/form/mhpz-icon.png) no-repeat center;
  background-size: contain;
}
.yz-con .yz-menu .menu-list ul li .label-top .name {
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  line-height: 22px;
  color: #FFFFFF;
  margin-left: 8px;
}
.yz-con .yz-menu .menu-list ul li .label-top{
  position: relative;
}
.yz-con .yz-menu .menu-list ul li .label-top .copy{
  position: absolute;
  top:0;
  right:0;
  line-height: 40px;
}
.yz-con .yz-menu .menu-list ul li{
  position: relative;
}
.yz-con .yz-menu .menu-list ul li .label-con .item-list .item{
  position: relative;
}
.yz-con .yz-menu .menu-list ul li .copy{
  position: absolute;
  top:0;
  right:0;
  line-height: 40px;
}

.yz-con .yz-menu .menu-list ul li .label-top .slide-arrow {
  position: absolute;
  top: 13px;
  right: 12px;
  width: 14px;
  height: 14px;
  background: url(../../images/form/arrow-icon.png) no-repeat center;
  transform: rotate(-90deg);
  -webkit-transform: rotate(-90deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.yz-con .yz-menu .menu-list ul li .label-con {
  display: none;
}
.yz-con .yz-menu .menu-list ul li .label-con .item-list .item {
  width: 100%;
  height: 40px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  padding-left: 26px;
  color: #FFFFFF;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  text-align: left;
  line-height: 38px;
  border: 1px solid transparent;
  cursor: pointer;
}
.yz-con .yz-menu .menu-list ul li .label-con .item-list .item:hover {
  border-radius: 2px;
  background: rgba(18, 29, 109, 0.4);
}
.yz-con .yz-menu .menu-list ul li .label-con .item-list .item.cur {
  border-radius: 2px;
  background: linear-gradient(296deg, #6A82FF 0%, #4C73FF 100%);
  background: -webkit-linear-gradient(296deg, #6A82FF 0%, #4C73FF 100%);
}
.yz-con .yz-main {
  flex: 1;
  padding: 0 20px;
}
.yz-con .yz-main .yz-cons {
  background-color: #fff;
  height: calc(100vh - 100px);
  border-radius: 8px;
  overflow-y: auto;
}
.cons {
  border: 1px solid #C9CDD4;
  width: auto;
  margin: 0 50px 40px;
  height: 500px;
  border-radius: 15px;
}
.submit {
  float: right;
  width: 108px;
  height: 32px;
  margin: 0 auto;
  background: linear-gradient(0, #4A7CFE 7.45%, #769CFF 98.59%);
  background: -webkit-linear-gradient(0, #4A7CFE 7.45%, #769CFF 98.59%);
  border-radius: 24px;
  line-height: 32px;
  font-size: 14px;
  color: #fff;
  cursor: pointer;
  text-align: center;
  line-height: 30px;
  margin-right: 50px;
}
.layui-form-item {
  padding-top: 100px;
  display: flex;
  display: -webkit-flex;
  align-content: center;
  justify-content: center;
  margin-bottom: 50px;
}
.layui-form-item .select-file {
  display: block;
  color: #4E5969;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  margin: 4px auto;
  width: 56px;
  padding: 0 10px;
}
.layui-form-item .layui-btn.layui-chaoxing-default-btn {
  border: 1px dashed #E5E6EB;
  flex-shrink: 0;
  border-radius: 4px;
  background: #EEEFF2;
  width: 110px;
  height: 32px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  text-align: center;
  line-height: 30px;
  padding: 0;
}
.layui-form-item .layui-input-inline {
  min-height: 32px;
  width: auto;
}
.layui-form-item .layui-input-inline .upload-files {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.layui-form-item .layui-input-inline .data-shows.hide {
  display: none;
}
.layui-form-item .layui-input-inline .data-shows ul {
  overflow: hidden;
}
.layui-form-item .layui-input-inline .data-shows ul li {
  margin-right: 8px;
  margin-bottom: 8px;
  padding: 0 8px;
  background: #E9F0FF;
  border-radius: 15px;
  height: 28px;
  line-height: 28px;
  margin-right: 16px;
  margin-bottom: 2px;
  margin-top: 2px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  float: left;
  margin-top: 8px;
}
.layui-form-item .layui-input-inline .data-shows ul li span {
  font-size: 14px;
  color: #4E5969;
  margin-right: 8px;
}
.layui-form-item .layui-input-inline .data-shows ul li .layui-icon {
  font-size: 16px;
  color: #4E5969;
  cursor: pointer;
}
.layui-form-item .layui-input-inline .texts {
  padding: 0 10px;
  color: #86909C;
  font-weight: 400;
  font-size: 14px;
  height: 32px;
  line-height: 32px;
  white-space: nowrap;
}
.layui-form-item .layui-input-inline .download {
  color: #4A7CFE;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  cursor: pointer;
  margin-left: 10px;
}
.layui-form-item .layui-input-inline .tips {
  color: #86909C;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  margin-left: 10px;
}
.layui-form-item .layui-input-inline .unregistereddata {
  width: 108px;
  height: 32px;
  background: #FFFFFF;
  border: 1px solid #4A7CFE;
  border-radius: 30px;
  font-size: 14px;
  color: #4A7CFE;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  cursor: pointer;
  margin-right: 16px;
  text-align: center;
  line-height: 30px;
}
.layui-form-item .layui-input-inline .unregistereddata:hover {
  background: #E9F0FF;
}
.layui-form-item .layui-input-inline .unregistereddata:active {
  background: #D3E0FF;
}
.layui-form-item .layui-input-inline .upload {
  width: 108px;
  height: 32px;
  background: linear-gradient(0, #4A7CFE 7.45%, #769CFF 98.59%);
  background: -webkit-linear-gradient(0, #4A7CFE 7.45%, #769CFF 98.59%);
  border-radius: 24px;
  line-height: 32px;
  font-size: 14px;
  color: #fff;
  cursor: pointer;
  text-align: center;
  line-height: 30px;
}
.layui-form-item .layui-input-inline .upload:hover {
  background: linear-gradient(0, rgba(74, 124, 254, 0.8) 7.45%, rgba(118, 156, 255, 0.8) 98.59%);
  background: -webkit-linear-gradient(0, rgba(74, 124, 254, 0.8) 7.45%, rgba(118, 156, 255, 0.8) 98.59%);
}
.layui-form-item .layui-input-inline .upload:active {
  background: linear-gradient(0, #769CFF 7.45%, #4A7CFE 98.59%);
  background: -webkit-linear-gradient(0, #769CFF 7.45%, #4A7CFE 98.59%);
}
.layui-form-item .layui-input-inline .name {
  margin-right: 10px;
  font-size: 14px;
  color: #C9CDD4;
}
.layui-form-item .layui-input-inline .introIcon {
  width: 16px;
  height: 16px;
  background: url(../../images/form/gloss.png) no-repeat center;
  cursor: pointer;
}
.layui-form-select .layui-edge {
  border: none;
  width: 12px;
  height: 12px;
  margin-top: -6px;
  background: url(../../images/form/down-icon.png) no-repeat center;
}
.layui-form-select dl {
  padding: 0;
  border: none;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
  top: 38px;
  border-radius: 4px;
}
.layui-form-select dl dt,
.layui-form-select dl dd {
  height: 32px;
  color: #4E5969;
  font-weight: 400;
  font-size: 14px;
  padding: 0 20px;
  line-height: 32px;
}
.layui-form-select dl dt:hover,
.layui-form-select dl dd:hover {
  background: #F7F8FA;
}
.layui-form-select dl dd.layui-this {
  background: #DDE7FF;
  color: #4A7CFE;
}
.layui-input,
.layui-textarea,
.layui-select {
  height: 32px;
  border-radius: 4px;
  border-color: #C9CDD4;
}
