.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.j-search-con {
    display: flex;
    align-items: center;
    position: relative;
    width: 240px;
    cursor: pointer;

    &.slideShow {
        display: block;

        .j-arrow {
            transform: rotate(180deg);
            background: url(/css/examination/images/down-icon1.png) no-repeat center;
        }

        .j-select-year {
            display: block;
        }


        .schoolSel {
            border: 1px solid #4D88FF;
        }

        .fuzzy-query-input {
            border: 1px solid #4D88FF;
        }

        .j-select-search {
            display: block;
        }
    }

    .j-select-year {
        left: 0;
    }

    input {
        width: 100%;
        height: 34px;
        border: 1px solid #e5e6eb;
        border-radius: 4px;
        padding: 0 20px 0 10px;
        .borDer;
        font-size: 14px;
        cursor: pointer;

        &::placeholder {
            color: #86909c;
        }

        &:focus {
            border: 1px solid #4D88FF;
        }

        &.error {
            border-color: #F76560;
        }
    }

    .j-arrow {
        width: 10px;
        height: 10px;
        background: url(/css/examination/images/down-icon.png) no-repeat center;
        position: absolute;
        right: 12px;
        top: 12px;
        pointer-events: none;

        &.j-arrow-slide {
            transform: rotate(180deg);
        }
    }

    .j-select-year {
        position: absolute;
        top: 40px;
        left: -1px;
        z-index: 999;
        width: 100%;
        max-height: 300px;
        overflow-y: auto;
        border-radius: 4px;
        display: none;
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);

        /*  &.slideShow {
            display: block;
        } */

        &.slideShowTop {
            display: block;
            top: unset;
            bottom: 40px;
        }

        .search {
            height: 36px;
            background: #f5f7fa;
            border-radius: 18px;
            margin: 11px 10px;

            input {
                border: none;
                width: 84%;
                background: transparent;
                height: 36px;
                line-height: 36px;
                padding-left: 14px;
                box-sizing: borDer-box;
                -webkit-box-sizing: borDer-box;
                -moz-box-sizing: borDer-box;
                -ms-box-sizing: borDer-box;
                -o-box-sizing: borDer-box;
                float: left;
            }

            span {
                cursor: pointer;
                float: left;
                width: 16px;
                height: 16px;
                background: url(/css/examination/images/search-icon.png) no-repeat center;
                margin-top: 10px;
            }
        }

        .all-selects {
            line-height: 17px;
            margin-bottom: 4px;
            height: 17px;
            padding: 0 14px;
            font-size: 12px;
            color: #6b89b3;
            cursor: pointer;
            user-select: none;
        }

        ul {
            overflow: hidden;
            max-height: 200px;
            overflow-y: auto;

            li {
                line-height: 40px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-align: left;
                text-indent: 16px;
                cursor: pointer;
                font-size: 14px;
                color: #4e5969;
                .textEls;
                .borDer;
                padding-right: 30px;
                background-color: #ffffff;
                background-image: url("/css/examination/images/check-icon.png");
                background-repeat: no-repeat;
                background-position: 96% center;

                &:hover {
                    background-color: #e1ebff;
                    color: #4D88FF;
                }

                &.active {
                    background-image: url("/css/examination/images/check-cur.png");
                    // font-weight: 500;
                }
            }
        }
    }

    .j-select-search {
        position: absolute;
        top: 40px;
        left: -1px;
        z-index: 999;
        width: 100%;
        max-height: 300px;
        overflow-y: auto;
        border-radius: 4px;
        display: none;
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);



        ul {
            overflow: hidden;
            max-height: 200px;
            overflow-y: auto;

            li {
                line-height: 40px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-align: left;
                text-indent: 16px;
                cursor: pointer;
                font-size: 14px;
                color: #4e5969;
                .textEls;
                .borDer;
                padding-right: 30px;
                background-color: #ffffff;


                &:hover {
                    background-color: #e1ebff;
                    color: #4d88ff;
                }

                &.active {

                    color: #4d88ff;

                }
            }
        }
    }

    &.single-box {
        .j-select-year {
            ul {
                li {
                    background-image: unset;

                    &.active {
                        background-color: #E1EBFF;
                        color: #4D88FF;

                    }
                }
            }
        }
    }
}