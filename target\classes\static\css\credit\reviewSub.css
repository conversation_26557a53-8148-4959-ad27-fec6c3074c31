.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #B5B9BF;
  font-size: 14px;
}
body {
  background-color: #F7F8FA;
}
.j-material-wrap {
  max-width: 1700px;
  margin: 0 auto;
}
.j-material {
  margin: 20px 20px 10px 20px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  min-height: calc(100vh - 40px);
}
.j-material .j-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  border-bottom: 1px solid #E8EBF1;
  position: relative;
}
.j-material .j-title p {
  position: absolute;
  left: 119px;
  top: 0;
  height: 60px;
  line-height: 60px;
  color: #C9CDD4;
  font-weight: normal;
}
.j-material .j-title h4 {
  position: relative;
  color: #6581BA;
  font-size: 16px;
  margin-left: 30px;
  padding-left: 9px;
  font-weight: bold;
}
.j-material .j-title h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4C85FA;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}
.j-material .j-title .j-btns {
  margin-right: 30px;
}
.j-material .j-title .j-btns button {
  width: 92px;
  height: 36px;
  font-size: 14px;
  border-radius: 6px;
  outline: none;
  cursor: pointer;
}
.j-material .j-title .j-btns button.btn-cancel {
  border: 1px solid #4C88FF;
  box-shadow: 0px 0px 8px rgba(39, 111, 255, 0.31);
  color: #4C88FF;
  background-color: #fff;
  margin-right: 14px;
}
.j-material .j-title .j-btns button.btn-complate {
  background: #4C88FF;
  box-shadow: 0px 0px 8px rgba(39, 111, 255, 0.31);
  border: 1px solid #4C88FF;
  color: #fff;
}
.j-material .j-title-s {
  color: #1D2129;
  font-size: 16px;
  line-height: 60px;
  margin: 0 30px;
}
.j-material .j-title-s em {
  color: #4C85FA;
}
.j-material .j-table {
  margin: 0 30px 40px;
}
.j-material .j-table table tbody tr.trSel {
  background-color: #E1EBFF !important;
}
.j-material .j-table table tbody tr.layui-table-click {
  background-color: #E1EBFF !important;
}
.j-material .j-search-item {
  display: flex;
  align-items: center;
  margin-right: 40px;
  position: relative;
}
.j-material .j-search-item:nth-child(2) {
  margin-right: 0;
}
.j-material .j-search-item .button {
  background: #4D88FF;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 6px;
  width: 92px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  font-size: 14px;
  color: #FFFFFF;
}
.j-material .j-search-item input {
  width: 220px;
  height: 30px;
  border: 1px solid #D5D9E2;
  border-radius: 4px;
  padding: 0 14px;
  box-sizing: border-box;
  font-size: 14px;
}
.j-material .j-search-item input::placeholder {
  color: #8F97A8;
}
.j-material .j-search-item .j-arrow {
  width: 10px;
  height: 10px;
  background: url(../../images/credit/icon-arrow.png) no-repeat center;
  position: absolute;
  right: 10px;
  top: 10px;
}
.j-material .j-search-item .j-arrow.j-arrow-slide {
  transform: rotate(180deg);
}
.j-material .j-search-item .j-select-year {
  position: absolute;
  top: 36px;
  left: 80px;
  z-index: 9;
  width: 218px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #D5D9E2;
  border-radius: 4px;
  display: none;
  background-color: #fff;
}
.j-material .j-search-item .j-select-year.slideShow {
  display: block;
}
.j-material .j-search-item .j-select-year ul li {
  line-height: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  text-align: left;
  cursor: pointer;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-left: 15px;
}
.j-material .j-search-item .j-select-year ul li.active,
.j-material .j-search-item .j-select-year ul li:hover {
  background: #E1EBFF;
}
.j-material .j-search-item .j-search-con {
  position: relative;
  margin-right: 10px;
  cursor: pointer;
}
.j-material .j-search-item .j-search-con .j-select-year {
  left: 0;
}
.j-material .j-search {
  display: flex;
  align-items: center;
  position: relative;
  justify-content: space-between;
  margin-bottom: 20px;
}
.j-material .j-search .radio {
  padding-left: 22px;
  background: url(../../images/credit/check-icon.png) no-repeat left center;
  background-size: 16px;
  cursor: pointer;
  line-height: 34px;
  font-size: 14px;
  color: #4E5969;
}
.j-material .j-search .radio.cur {
  background: url(../../images/credit/check-cur.png) no-repeat left center;
  background-size: 16px;
}
.j-material .j-search h5 {
  color: #15171C;
  font-size: 14px;
  line-height: 30px;
  margin-right: 10px;
}
.j-material .j-search input {
  width: 220px;
  height: 30px;
  border: 1px solid #D5D9E2;
  border-radius: 4px;
  padding: 0 14px;
  box-sizing: border-box;
  font-size: 14px;
}
.j-material .j-search input::placeholder {
  color: #8F97A8;
}
.j-material .j-search button {
  background: #4D88FF;
  border: 1px solid #4D88FF;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 6px;
  margin-left: 24px;
  width: 90px;
  height: 30px;
  color: #FFFFFF;
  cursor: pointer;
  font-weight: normal;
}
.j-material .j-search .j-select {
  position: absolute;
  top: 36px;
  left: 94px;
  z-index: 9;
  width: 218px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #D5D9E2;
  border-radius: 4px;
  display: none;
}
.j-material .j-search .j-select ul {
  background-color: #fff;
}
.j-material .j-search .j-select ul li {
  display: block;
  padding: 0 20px;
  overflow: hidden;
  cursor: pointer;
}
.j-material .j-search .j-select ul li.active,
.j-material .j-search .j-select ul li:hover {
  background: #E1EBFF;
}
.j-material .j-search .j-select ul li h3 {
  font-size: 14px;
  line-height: 20px;
  color: #4E5969;
  margin-top: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.j-material .j-search .j-select ul li h3 em {
  color: #4C85FA;
}
.j-material .j-search .j-select ul li p {
  color: #86909C;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.j-material .j-search .j-select ul li p span:first-child {
  padding-right: 16px;
}
.j-material .j-opt {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 30px 20px;
  font-size: 14px;
}
.j-material .j-opt .j-add {
  background: #4D88FF;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 4px;
  width: 120px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
}
.j-material .j-opt .j-add img {
  width: 12px;
  margin-right: 6px;
}
.j-material .j-review-con {
  padding: 30px;
}
.j-material .j-review-con .j-top {
  width: 100%;
  height: 40px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.j-material .j-review-con .j-top .name {
  display: flex;
  display: -webkit-flex;
  align-items: center;
}
.j-material .j-review-con .j-top .name span {
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;
  color: #1D2129;
  margin-right: 20px;
}
.j-material .j-review-con .j-top .status {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  margin-top: 10px;
}
.j-material .j-review-con .j-top .status span {
  font-weight: 400;
  font-size: 14px;
  margin-left: 32px;
  line-height: 20px;
  color: #86909C;
  padding-left: 26px;
  background: url(../../images/credit/grant-icon.png) no-repeat left center;
}
.j-material .j-review-con .j-top .status span.ungrant {
  background: url(../../images/credit/ungrant-icon.png) no-repeat left center;
}
.j-material .j-top-inform {
  width: 100%;
  height: auto;
  margin-bottom: 30px;
}
.j-material .j-top-inform .semester {
  float: left;
  font-size: 16px;
  color: #1D2129;
  line-height: 20px;
  font-weight: 700;
  margin-right: 40px;
}
.j-material .j-top-inform .semester .name {
  float: left;
}
.j-material .j-top-inform .semester .txt {
  float: left;
}
.j-material .j-top-inform .total {
  float: right;
  font-size: 16px;
  color: #1D2129;
  font-weight: 700;
  line-height: 20px;
}
.j-material .j-top-inform .total .name {
  float: left;
}
.j-material .j-top-inform .total .principal {
  float: left;
  color: #FF7D00;
}
.j-material .j-table {
  border: 1px solid #E8EBF1;
}
.j-material .j-table .j-table-thead {
  display: flex;
  align-items: center;
}
.j-material .j-table .j-table-thead .cell {
  flex: 1;
  height: 36px;
  line-height: 36px;
  font-size: 14px;
  color: #86909C;
  background: #F1F3F6;
  border-right: 1px solid #E8EBF3;
  border-bottom: 1px solid #E8EBF3;
  text-align: center;
}
.j-material .j-table .j-table-thead .cell.checked {
  flex: 0 0 100px;
}
.j-material .j-table .j-table-thead .cell:last-child {
  border-right: 1px solid #F1F3F6;
}
.j-material .j-table .j-table-thead .cell span {
  display: inline-block;
  padding-left: 22px;
  background: url(../../images/credit/check-icon.png) no-repeat left center;
  background-size: 16px;
  cursor: pointer;
}
.j-material .j-table .j-table-thead .cell span.cur {
  background: url(../../images/credit/check-cur.png) no-repeat left center;
}
.j-material .j-table .j-table-tr {
  height: auto;
}
.j-material .j-table .j-table-tr .cell {
  display: flex;
  align-items: center;
}
.j-material .j-table .j-table-tr .cell:nth-child(2n) {
  background: #F7F8FA;
}
.j-material .j-table .j-table-tr .cell:last-child .lable {
  border-bottom: none;
}
.j-material .j-table .j-table-tr .cell:last-child .c-main {
  border-bottom: none;
}
.j-material .j-table .j-table-tr .cell .lable {
  flex: 0 0 75px;
  height: 85px;
  border-right: 1px solid #E8EBF1;
  border-bottom: 1px solid #E8EBF1;
  padding-left: 25px;
  line-height: 85px;
}
.j-material .j-table .j-table-tr .cell .lable span {
  width: 16px;
  height: 16px;
  display: inline-block;
  padding-left: 22px;
  background: url(../../images/credit/check-icon.png) no-repeat left center;
  background-size: contain;
  cursor: pointer;
}
.j-material .j-table .j-table-tr .cell .lable span.cur {
  background: url(../../images/credit/check-cur.png) no-repeat left center;
}
.j-material .j-table .j-table-tr .cell .c-main {
  flex: 1;
  border-bottom: 1px solid #E8EBF1;
}
.j-material .j-table .j-table-tr .cell .c-main .c-inform {
  display: flex;
  align-items: center;
}
.j-material .j-table .j-table-tr .cell .c-main .c-inform .item {
  flex: 1;
  border-right: 1px solid #E8EBF1;
  border-bottom: 1px solid #E8EBF1;
  height: 36px;
  font-size: 14px;
  color: #4E5969;
  text-align: center;
  line-height: 36px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.j-material .j-table .j-table-tr .cell .c-main .c-inform .item:last-child {
  border-right: 1px solid #fff;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 48px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .txt {
  font-size: 14px;
  color: #86909C;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .housNus {
  display: flex;
  align-items: center;
  margin-right: 30px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .housNus .name {
  line-height: 48px;
  font-size: 14px;
  color: #1D2129;
  margin-right: 14px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .housNus .layui-input {
  width: 80px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .number {
  display: flex;
  align-items: center;
  margin-right: 40px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .number .name {
  line-height: 48px;
  font-size: 14px;
  color: #1D2129;
  margin-right: 14px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .number .layui-input {
  width: 80px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .j-search-item {
  margin-right: 40px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .j-search-item h5 {
  margin-right: 14px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .j-search-item .j-search-con {
  margin-right: 0;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .discount {
  line-height: 48px;
  font-size: 14px;
  color: #1D2129;
  display: flex;
  align-items: center;
  margin-right: 24px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .discount p {
  color: #4C85FA;
  margin-left: 8px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .tatol {
  line-height: 48px;
  font-size: 14px;
  display: flex;
  align-items: center;
  color: #1D2129;
  margin-right: 24px;
}
.j-material .j-table .j-table-tr .cell .c-main .c-opt .tatol p {
  color: #FF7D00;
  margin-left: 8px;
}
.j-con {
  overflow: hidden;
  min-height: calc(100vh - 100px);
}
.j-con .j-left {
  float: left;
  width: 61.7%;
  min-height: calc(100vh - 100px);
}
.j-con .j-left .j-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
}
.j-con .j-left .j-top .name {
  overflow: hidden;
}
.j-con .j-left .j-top .name h4 {
  font-size: 16px;
  color: #1D2129;
  font-weight: 700;
  line-height: 20px;
  float: left;
  margin-right: 16px;
}
.j-con .j-left .j-top .name p {
  font-size: 14px;
  color: #86909C;
  line-height: 20px;
  float: left;
}
.j-con .j-left .j-top .search .layui-input {
  float: left;
  margin-right: 24px;
  width: 220px;
}
.j-con .j-left .j-top .search .btn {
  float: left;
  width: 92px;
  height: 30px;
  background: #4D88FF;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 6px;
  cursor: pointer;
  text-align: center;
  line-height: 30px;
  font-size: 14px;
  color: #FFFFFF;
}
.j-con .j-left .j-table {
  border: none;
}
.j-con .j-left .j-table .layui-table {
  width: 100%;
}
.j-con .j-left .j-table .layui-body {
  overflow-x: scroll;
}
.j-con .j-left .j-table .layui-table-cell .select {
  cursor: pointer;
  color: #4C85FA;
}
.j-con .j-left .j-table .layui-table-cell .cancle {
  cursor: pointer;
  color: #4C85FA;
}
.j-con .j-right {
  float: left;
  width: 38.3%;
  height: 100%;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  border-left: 1px solid #E8EBF1;
  min-height: calc(100vh - 100px);
}
#coursePage {
  text-align: center;
  margin-bottom: 30px;
}
.layui-laypage a,
.layui-laypage span,
.layui-laypage input,
.layui-laypage button,
.layui-laypage select {
  border: none;
}
.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  background: #F1F3F6;
  border-radius: 4px;
  color: #484F5D;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #4C85FA;
  border-radius: 4px;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #4C85FA !important;
}
.layui-laypage a:hover {
  color: #4C85FA;
}
.layui-laypage-prev {
  background-color: transparent !important;
}
.layui-laypage-next {
  background-color: transparent !important;
}
.layui-laypage-spr {
  background-color: transparent !important;
}
.layui-laypage-skip {
  background-color: transparent !important;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-laypage-skip input {
  border: 1px solid #e2e2e2 !important;
  background-color: transparent !important;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"] {
  -moz-appearance: textfield;
}
.j-material .j-search-item input {
  cursor: pointer;
}
.layui-body {
  overflow-x: scroll;
}
.layui-table tbody tr:hover,
.layui-table-hover {
  background-color: #edf2fd !important;
}
.layui-table-cell .warehouse {
  font-size: 14px;
  color: #4C85FA;
  cursor: pointer;
}
.layui-table-cell .already {
  display: inline-block;
  background: #5AB176;
  border-radius: 15px;
  width: 58px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 14px;
  color: #FFFFFF;
}
.layui-table-cell .wait {
  display: inline-block;
  background: #4080FF;
  border-radius: 15px;
  width: 58px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 14px;
  color: #FFFFFF;
}
.layui-table-cell {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
}
.layui-table-cell .grant {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../../images/credit/grant-icon.png) no-repeat center;
}
.layui-table-cell .ungrant {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../../images/credit/ungrant-icon.png) no-repeat center;
}
.layui-laypage button {
  margin-left: 10px;
  padding: 0 10px;
  cursor: pointer;
  background: #F1F3F6;
  border-radius: 4px;
  color: #4D88FF;
}
