body {
    background-color: #F7F8FA;
}

.j-material-wrap {
    max-width: 1700px;
    margin: 0 auto;
}

.j-material {
    margin: 20px 20px 10px 20px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;

    .j-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 60px;
        border-bottom: 1px solid #E8EBF1;

        h4 {
            position: relative;
            color: #6581BA;
            font-size: 16px;
            margin-left: 30px;
            padding-left: 9px;
            font-weight: bold;

            &::after {
                content: "";
                width: 3px;
                height: 16px;
                background: #4C85FA;
                border-radius: 2px;
                position: absolute;
                left: 0;
                top: 4px;
            }
        }

        .j-btns {
            margin-right: 30px;

            button {
                width: 92px;
                height: 36px;
                font-size: 14px;
                border-radius: 6px;
                outline: none;
                cursor: pointer;

                &.btn-cancel {
                    border: 1px solid #4C88FF;
                    box-shadow: 0px 0px 8px rgba(39, 111, 255, 0.31);
                    color: #4C88FF;
                    background-color: #fff;
                    margin-right: 14px;
                }

                &.btn-complate {
                    background: #4C88FF;
                    box-shadow: 0px 0px 8px rgba(39, 111, 255, 0.31);
                    border: 1px solid #4C88FF;
                    color: #fff;
                }
            }
        }
    }

    .j-title-s {
        color: #1D2129;
        font-size: 16px;
        line-height: 60px;
        margin: 0 30px;

        em {
            color: #4C85FA;
        }
    }

    .j-title-wrap {
        display: flex;
        align-items: center;

        .j-checkbox {
            overflow: hidden;

            ul {
                display: flex;
                align-items: center;

                li {
                    margin-right: 24px;
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    cursor: pointer;

                    i {
                        color: #d2d2d2;
                        font-size: 18px;
                        margin-right: 4px;

                        &.layui-icon-radio {
                            color: #4D88FF;
                        }

                        &.layui-icon-square {
                            border: 1px solid #d2d2d2;
                            width: 16px;
                            height: 16px;
                        }

                        &.layui-icon-ok {
                            background-color: #4D88FF;
                            border: 1px solid #4D88FF;
                            color: #fff;
                            font-size: 12px;
                            text-align: center;
                            line-height: 16px;
                        }
                    }
                }
            }
        }

    }

    .j-table {
        margin: 0 30px 30px;

        table tbody tr.trSel {
            background-color: #E1EBFF !important;
        }
    }

    .j-opt {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 0 30px 20px;
        font-size: 14px;

        .j-search {
            display: flex;
            align-items: center;
            position: relative;

            h5 {
                color: #15171C;
                font-size: 14px;
                line-height: 30px;
                margin-right: 10px;
            }



            input {
                width: 220px;
                height: 30px;
                border: 1px solid #D5D9E2;
                border-radius: 4px;
                padding: 0 14px;
                box-sizing: border-box;
                font-size: 14px;

                &::placeholder {
                    color: #8F97A8;
                }
            }

            button {
                background: #4D88FF;
                border: 1px solid #4D88FF;
                box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
                border-radius: 6px;
                margin-left: 24px;
                width: 90px;
                height: 30px;
                color: #FFFFFF;
                cursor: pointer;
                font-weight: normal;
            }

            .j-select {
                position: absolute;
                top: 36px;
                left: 94px;
                z-index: 9;
                width: 218px;
                max-height: 300px;
                overflow-y: auto;
                border: 1px solid #D5D9E2;
                border-radius: 4px;
                display: none;

                ul {
                    background-color: #fff;

                    li {
                        display: block;

                        padding: 0 20px;
                        overflow: hidden;
                        cursor: pointer;

                        &.active,
                        &:hover {
                            background: #E1EBFF;
                        }


                        h3 {
                            font-size: 14px;
                            line-height: 20px;
                            color: #4E5969;
                            margin-top: 10px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;


                            em {
                                color: #4C85FA;
                            }
                        }

                        p {
                            color: #86909C;
                            margin-bottom: 10px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;

                            span {
                                &:first-child {
                                    padding-right: 16px;
                                }
                            }
                        }

                    }
                }
            }

            .j-search-item {
                display: flex;
                align-items: center;
                margin-right: 40px;
                position: relative;

                &:nth-child(2) {
                    margin-right: 0;
                }

                .j-arrow {
                    width: 10px;
                    height: 10px;
                    background: url(../images/icon-arrow.png) no-repeat center;
                    position: absolute;
                    right: 10px;
                    top: 10px;

                    &.j-arrow-slide {
                        transform: rotate(180deg);
                    }
                }

                .j-select-year {
                    position: absolute;
                    top: 36px;
                    left: 80px;
                    z-index: 9;
                    width: 218px;
                    max-height: 300px;
                    overflow-y: auto;
                    border: 1px solid #D5D9E2;
                    border-radius: 4px;
                    display: none;
                    background-color: #fff;

                    &.slideShow {
                        display: block;
                    }

                    ul {
                        li {
                            line-height: 32px;
                            overflow: hidden;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            text-align: center;
                            cursor: pointer;

                            &.active,
                            &:hover {
                                background: #E1EBFF;
                            }



                        }
                    }
                }

                .j-search-con {
                    position: relative;
                    margin-right: 10px;

                    .j-select-year {
                        left: 0;
                    }
                }
            }


        }



        .j-add {
            background: #4D88FF;
            box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
            border-radius: 4px;
            width: 120px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;


            img {
                width: 12px;
                margin-right: 6px;
            }
        }
    }

}