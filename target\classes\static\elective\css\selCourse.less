body {
  background-color: #f7f8fa;
}
.z-main {
  margin: 8px 20px 0;
  background-color: #ffffff;
  overflow: hidden;
  min-height: calc(~"100vh - 8px");

  .z-title {
    padding: 29px 0 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e8ebf1;
    justify-content: space-between;

    h3 {
      font-size: 16px;
      line-height: 22px;
      color: #1d2129;
      padding-left: 9px;
      position: relative;
      margin-left: 30px;

      &::after {
        content: "";
        width: 3px;
        height: 16px;
        background: #4d88ff;
        border-radius: 2px;
        position: absolute;
        left: 0;
        top: 3px;
      }
    }

    span {
      font-size: 14px;
      color: #86909c;
      font-size: 14px;
      margin-left: 16px;
      margin-top: 2px;
    }
    #saveBth {
      width: 96px;
      height: 36px;
      border-radius: 4px;
      background: #4d88ff;
      box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
      margin-right: 20px;
      color: #fff;
      text-align: center;
      line-height: 36px;
      cursor: pointer;
    }
  }
  .z-search {
    margin: 30px 30px 0;

    .layui-form {
      display: flex;
      flex-wrap: wrap;

      .layui-form-item {
        margin-right: 32px;
      }

      .layui-form-label {
        width: 60px;
      }

      .layui-input-block {
        margin-left: 75px;
      }
    }
    .z-btn {
      width: 68px;
      height: 36px;
      border-radius: 4px;
      background: #4d88ff;
      box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
      outline: none;
      border: none;
      color: #ffffff;
      font-size: 14px;
      cursor: pointer;
    }
    .clear {
      background: url("../images/icon_clear.png") no-repeat center;
      width: 36px;
      height: 36px;
      cursor: pointer;
      margin-left: 16px;
    }
  }
  .z-tab-search {
    overflow: hidden;
    margin: 0 30px 30px;
    position: relative;
    input {
      display: block;
      float: right;
      width: 220px;
      height: 32px;
      border-radius: 4px;
      border: 1px solid #c9cdd4;
      padding: 0 28px 0 12px;
      box-sizing: border-box;
      &::placeholder {
        font-size: 14px;
        color: #86909c;
      }
    }
    img {
      display: block;
      position: absolute;
      right: 12px;
      top: 9px;
    }
  }
  .z-table {
    margin: 0 30px;
    position: relative;
    .z-check {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #666768;
      position: absolute;
      left: 24px;
      bottom: 8px;
      .check {
        width: 28px;
        height: 28px;
        cursor: pointer;
        margin-right: 6px;
        background: url(../images/check.png) no-repeat center;
        background-size: 28px;
        &.checked {
          background: url(../images/check1.png) no-repeat center;
        }
      }
    }
  }
}
.dialog {
  // overflow: hidden;
  border-radius: 10px;
  background-color: #ffffff;

  .dialog-title {
    border-bottom: 1px solid #e5e6eb;
    height: 56px;
    line-height: 56px;
    color: #1d2129;
    font-size: 16px;
    text-indent: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      width: 24px;
      height: 24px;
      background: url("../images/close.png") no-repeat center;
      background-size: 24px;
      margin-right: 23px;
      cursor: pointer;
    }
  }

  .dialog-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 70px;
    border-top: 1px solid #e5e6eb;
    padding-right: 30px;

    button {
      width: 88px;
      height: 36px;
      font-size: 14px;
      border-radius: 18px;
      cursor: pointer;

      &.pu-cancel {
        border: 1px solid #c9cdd4;
        color: #4e5969;
        background-color: #fff;
        margin-right: 16px;
      }

      &.pu-sure {
        color: #fff;
        background: #4d88ff;
        box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
        border: 1px solid #4d88ff;
      }
    }
  }

  .dialog-con {
    padding: 0 30px;
    box-sizing: border-box;
    .z-table {
      margin-bottom: 30px;
      position: relative;
    }
  }
}
#selCourse {
  width: 1098px;
  display: none;
  height: 756px;
  .j-search-con {
    width: 190px;
  }
}
