body {
  background-color: #F7F8FA;
}
.main {
  margin: 20px;
  background-color: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 40px);
  min-width: 1000px;
}
.main .m-top {
  width: 100%;
  height: 57px;
  border-bottom: 1px solid #E8EAF1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: border-box;
}
.main .m-top .back {
  width: 60px;
  height: 24px;
  background: url(../../images/cultivation/back-icon.png) no-repeat left center;
  cursor: pointer;
  margin-right: 10px;
}
.main .m-top .title {
  font-size: 16px;
  color: #A5AFC4;
}
.main .m-top span {
  width: 12px;
  height: 12px;
  background: url(../../images/cultivation/icon-right.png) no-repeat center;
  background-size: 12px;
  margin: 0 4px;
}
.main .m-top h3 {
  font-weight: 600;
  font-size: 16px;
  color: #6581BA;
}
.main .item {
  padding: 0 30px;
}
.main .item-teacher .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 20px;
  margin-top: 30px;
}
.main .item-teacher .i-top span {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #484F5D;
  position: relative;
  display: block;
}
.main .item-teacher .i-top span:after {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  background: #4D88FF;
  border-radius: 1px;
  width: 3px;
  height: 14px;
}
.main .item-teacher .i-search {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.main .item-teacher .i-search .i-add {
  width: 110px;
  height: 34px;
  border: 1px solid #4C88FF;
  border-radius: 4px;
  font-size: 14px;
  color: #4C88FF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 40px;
  box-sizing: border-box;
  cursor: pointer;
}
.main .item-teacher .i-search .i-add span {
  margin-right: 6px;
  font-size: 24px;
}
.main .item-teacher .i-search .layui-inline {
  margin-bottom: 0;
}
.main .item-teacher .i-search .layui-form-label {
  width: 57px;
  text-align: right;
  font-size: 14px;
  line-height: 34px;
  color: #717B91;
  padding: 0;
  margin-right: 12px;
}
.main .item-teacher .i-search .layui-input {
  border-color: #D5D9E2;
  border-radius: 4px;
  width: 240px;
  height: 34px;
  font-size: 14px;
}
.main .item-teacher .i-search .layui-input::placeholder {
  color: #8F97A8 !important;
}
.main .item-teacher .i-search .btnSubmit {
  width: 88px;
  height: 34px;
  line-height: 34px;
  padding: 0;
  border: 1px solid #4C88FF;
  border-radius: 4px;
  margin-left: 40px;
  background-color: #fff;
  font-size: 14px;
  color: #4C88FF;
}
.main .item-teacher .layui-table tbody tr:nth-child(even) {
  background-color: #FFFFFF;
}
.main .item-teacher .layui-table tbody tr:nth-child(even):hover {
  background-color: #edf2fd;
}
.layui-table-cell {
  padding: 0 17px;
  height: 30px;
}
.layui-table-cell .tmplOpt {
  display: flex;
  align-items: center;
}
.layui-table-cell .tmplOpt h5 {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.layui-table-cell .tmplOpt span.set {
  color: #4C88FF;
  margin-left: 16px;
  cursor: pointer;
}
.layui-table-cell .layui-input {
  width: 220px;
  background: #FFFFFF;
  border: 1px solid #D5D9E2;
  border-radius: 4px;
}
.layui-table-cell .layui-input:focus {
  border: 1px solid #4C88FF !important;
}
.layui-table-cell .edit {
  display: inline-block;
  color: #4C88FF;
  margin-right: 17px;
  cursor: pointer;
}
.layui-table-cell .delet {
  display: inline-block;
  color: #FF5E5E;
  cursor: pointer;
}
.layui-table-view .layui-table th {
  font-size: 14px;
  color: #6581BA;
  height: 44px;
}
.layui-table-view .layui-table td {
  color: #484F5D;
  font-size: 14px;
  height: 44px;
}
.layui-laypage a,
.layui-laypage span {
  border: none;
}
.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  background: #F1F3F6;
  border-radius: 4px;
  color: #484F5D;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #4C85FA;
  border-radius: 4px;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #4C85FA !important;
}
.layui-laypage a:hover {
  color: #4C85FA;
}
.layui-laypage-prev {
  background-color: transparent !important;
}
.layui-laypage-next {
  background-color: transparent !important;
}
.layui-laypage-spr {
  background-color: transparent !important;
}
.layui-laypage-skip {
  background-color: transparent !important;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-laypage-skip input {
  border: 1px solid #e2e2e2;
  background-color: transparent;
}
.layui-laypage-skip input {
  background: #FFFFFF;
  border: 1px solid #E7EAF1;
  color: #4D4D4D;
  border-radius: 2px;
}
.layui-laypage button {
  background: #F5F8FB;
  border: 1px solid #E7EAF1;
  color: #595959;
  border-radius: 2px;
}
.layui-table-page > div {
  text-align: right;
}
.main .item-teacher .i-search{
  position: relative;
}
.main .item-teacher .i-search .submit{
  position: absolute;
  top:0;
  right:0;
  background: linear-gradient(0deg, #4C88FF, #4C88FF), #D9D9D9;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 4px;
  width: 88px;
  height:34px;
  text-align: center;
  line-height: 34px;
  font-size:14px;
  color: #FFFFFF;
  cursor: pointer;
}
