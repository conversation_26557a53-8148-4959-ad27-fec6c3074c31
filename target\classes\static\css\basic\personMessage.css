.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.flex {
  display: flex;
  align-items: center;
}
body {
  background-color: #f7f8fa;
}
.main {
  margin: 0 auto;
  border-radius: 8px;
  min-height: calc(100vh);
  min-width: 1000px;
  max-width: 1660px;
  background-color: #ffffff;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  overflow: hidden;
}
.main .m-top {
  width: 100%;
  height: 57px;
  border-bottom: 1px solid #e8eaf1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top .back {
  width: 60px;
  height: 24px;
  background: url(../../images/basic/back-icon.png) no-repeat left center;
  cursor: pointer;
  margin-right: 10px;
}
.main .m-top .title {
  font-size: 16px;
  color: #1d2129;
  padding-left: 9px;
  position: relative;
}
.main .m-top .title::after {
  content: "";
  width: 3px;
  height: 16px;
  background-color: #4d88ff;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 3px;
}
.main .i-top {
  width: 100%;
  height: 22px;
  margin: 20px 32px 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .i-top h3 {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #6581BA;
  position: relative;
  display: block;
  margin-right: 24px;
}
.main .i-top h3:after {
  content: "";
  position: absolute;
  left: 0;
  top: 1px;
  background: #6581BA;
  border-radius: 2px;
  width: 3px;
  height: 18px;
}
.main .person-mes {
  display: flex;
  align-items: center;
  background: url('../../images/basic/bg.png') no-repeat center;
  background-size: cover;
  padding: 24px 32px;
  border-top: 4px solid #4D88FF;
  border-radius: 8px;
  overflow: hidden;
  margin: 32px 30px 38px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .person-mes .photo {
  border-radius: 4px;
  background: #FFF;
  width: 144px;
  height: 184px;
  margin-right: 32px;
}
.main .person-mes .photo img {
  display: block;
  width: 120px;
  margin: 11px;
  border-radius: 4px;
}
.main .person-mes .stu-info {
  flex: 1;
}
.main .person-mes .stu-info dl {
  font-size: 16px;
  line-height: 32px;
}
.main .person-mes .stu-info dl span {
  font-weight: 500;
}
.main .person-mes button {
  background-color: #4D88FF ;
  color: #ffffff;
  padding: 6px 14px;
  outline: none;
  border: 1px solid #4D88FF;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 40px;
}
.main .person-nav {
  margin: 0 32px 40px;
  border: 1px solid #ddd;
  display: inline-flex;
  border-radius: 4px;
  line-height: 36px;
  font-size: 14px;
  overflow: hidden;
}
.main .person-nav li {
  padding: 0 16px;
  border-right: 1px solid #ddd;
  cursor: pointer;
}
.main .person-nav li.active {
  background-color: #4D88FF;
  color: #fff;
}
.main .person-nav li:last-child {
  border-right: none;
}
.main .person-box-wrap {
  margin: 0 32px;
}
.main .person-box-wrap .person-box {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  display: none;
}
.main .person-box-wrap .person-box:first-child {
  display: flex;
}
.main .person-box-wrap .person-box dl {
  width: 25%;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.main .person-box-wrap .person-box dl:nth-child(3n) {
  margin-right: 0;
}
.main .person-box-wrap .person-box dl dt {
  color: #1d2129;
  flex-shrink: 0;
}
.main .person-box-wrap .person-box dl dd {
  color: #4e5969;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 24px;
}
