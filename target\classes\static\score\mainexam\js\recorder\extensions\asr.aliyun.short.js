/*
录音
https://github.com/xiangyuecn/Recorder
src: extensions/asr.aliyun.short.js
*/
!function(){"use strict";var e=function(e){return new t(e)},s="ASR_Aliyun_Short",t=function(e){var t=this,n={tokenApi:"",apiArgs:{action:"token",lang:"普通话"},apiRequest:null,log:o,fileSpeed:6};for(var s in e)n[s]=e[s];t.set=e=n,t.state=0,t.started=0,t.sampleRate=16e3,t.pcmBuffers=[],t.pcmTotal=0,t.pcmOffset=0,t.pcmSend=0,t.joinBuffers=[],t.joinSize=0,t.joinSend=0,t.joinOffset=-1,t.joinIsOpen=0,t.joinSendTotal=0,t.sendCurSize=0,t.sendTotal=0,t.resTxts=[],e.asrProcess||t.log("未绑定asrProcess回调无法感知到abort事件",3)},w=function(e,t){var n=new Date;return e="["+(("0"+n.getMinutes()).substr(-2)+":"+("0"+n.getSeconds()).substr(-2)+"."+("00"+n.getMilliseconds()).substr(-3))+" "+s+"]"+e,console[1==t?"error":3==t?"warn":"log"](e),e};function o(){}t.prototype=e.prototype={log:function(e,t){e=w(e,t),this.set.log(e,3==t?"#f60":t)},inputDuration:function(){return Math.round(this.pcmTotal/this.sampleRate*1e3)},sendDuration:function(e){var t=this.sendTotal;return t+=e||0,Math.round(t/this.sampleRate*1e3)},asrDuration:function(){return this.sendDuration(-this.joinSendTotal)},audioToText:function(t,r,i){var f=this,n=function(e){f.log(e,1),i&&i(e)};if(Recorder.GetContext()){var e=new FileReader;e.onloadend=function(){Recorder.Ctx.decodeAudioData(e.result,function(e){for(var t=e.getChannelData(0),n=e.sampleRate,s=new Int16Array(t.length),o=0;o<t.length;o++){var a=Math.max(-1,Math.min(1,t[o]));a=a<0?32768*a:32767*a,s[o]=a}f.pcmToText(s,n,r,i)},function(e){n("音频解码失败["+t.type+"]:"+e.message)})},e.readAsArrayBuffer(t)}else n("浏览器不支持音频解码")},pcmToText:function(e,t,n,s){var o=this;o.start(function(){o.log("单个文件"+Math.round(e.length/t*1e3)+"ms转文字"),o.sendSpeed=o.set.fileSpeed,o.input([e],t),o.stop(n,s)},s)},start:function(e,t){var n=this,s=function(e){n.sendAbortMsg=e,t&&t(e)};if(window.WebSocket)if(0==n.state){n.state=1;var o=function(){n.log("ASR start被stop中断",1),n._send()};n._token(function(){1!=n.state?o():(n.log("OK start",2),n.started=1,e&&e(),n._send())},function(e){e="语音识别token接口出错："+e,n.log(e,1),1!=n.state?o():(s(e),n._send())})}else s("ASR对象不可重复start");else s("当前浏览器不支持语音识别")},stop:function(t,n){t=t||o,n=n||o;var e,s=this;if(2==s.state)return e="语音识别stop出错："+(e="ASR对象不可重复stop"),s.log(e,1),void n(e);s.state=2,s.stopWait=function(){if(s.stopWait=null,s.started){var e=s.getText();!e&&s.sendAbortMsg?n(s.sendAbortMsg):t(e,s.sendAbortMsg||"")}else n(s.sendAbortMsg||"未开始语音识别")},s._send()},input:function(e,t,n){var s=this;if(2!=s.state){var o="input输入的采样率低于"+s.sampleRate;if(t<s.sampleRate)return w(o+"，数据已丢弃",3),s.pcmTotal||(s.sendAbortMsg=o),void s._send();if(s.sendAbortMsg==o&&(s.sendAbortMsg=""),n){for(var a=[],r=n;r<e.length;r++)a.push(e[r]);e=a}var i=Recorder.SampleData(e,t,s.sampleRate).data;s.pcmTotal+=i.length,s.pcmBuffers.push(i),s._send()}else s._send()},_send:function(){var s=this,o=s.set;if(!s.sendWait){var e=function(){s.stopWait&&s.stopWait()};if(2!=s.state||s.started&&s.stopWait)if(s.sendAbort)e();else{var t=function(e){s.sendAbort||(s.sendAbort=1,s.sendAbortMsg=e||"-",n(0,1)),s._send()},n=function(e,t){if(!t&&s.sendAbort)return!1;if(e=e||0,!o.asrProcess)return s.sendTotal+e<=r;var n=o.asrProcess(s.getText(),s.sendDuration(e),s.sendAbort?s.sendAbortMsg:"");return s._prsw||"boolean"==typeof n||w("asrProcess返回值必须是boolean类型，true才能继续识别，否则立即超时",1),s._prsw=1,n},a=5*s.sampleRate,r=60*s.sampleRate,i=s.wsCur;if(i){if(!s.wsLock&&2==i._s&&!i.isStop)if(s.pcmSend>=s.pcmTotal){if(1==s.state)return;i.stopWs(function(){e()},function(e){t(e)})}else{var f=s.sampleRate/1e3*50,u=s.sampleRate;if(i.bufferedAmount/2>3*u)s.sendWait=setTimeout(function(){s.sendWait=0,s._send()},100);else{if(s.sendSpeed){var l=(Date.now()-i.okTime)*s.sendSpeed,p=(s.sendCurSize+u/3)/s.sampleRate*1e3,d=Math.floor((p-l)/s.sendSpeed);if(0<d)return w("[ASR]延迟"+d+"ms发送"),void(s.sendWait=setTimeout(function(){s.sendWait=0,s._send()},d))}var c=1,m=function(e,t,n){for(var s=n.length,o=0;o<s&&0<t.length;){var a=t[0];if(!(a.length-e<=s-o)){n.set(a.subarray(e,e+(s-o)),o),e+=s-o;break}n.set(0==e?a:a.subarray(e),o),o+=a.length-e,e=0,t.splice(0,1)}return e};if(s.joinIsOpen){if(-1==s.joinOffset){s.joinSend=0,s.joinOffset=0,s.log("发送上1分钟结尾5秒数据...");for(var g=0,h=s.joinBuffers.length-1;0<=h;h--)if(a<=(g+=s.joinBuffers[h].length)){s.joinBuffers.splice(0,h),s.joinSize=g,s.joinOffset=g-a;break}}var v=s.joinSize-s.joinOffset;if((k=Math.min(u,v))<=0)return s.log("发送新1分钟数据(重叠"+Math.round(s.joinSend/s.sampleRate*1e3)+"ms)..."),s.joinBuffers=[],s.joinSize=0,s.joinOffset=-1,s.joinIsOpen=0,void s._send();var _=new Int16Array(k);s.joinSend+=k,s.joinSendTotal+=k,s.joinOffset=m(s.joinOffset,s.joinBuffers,_);for(h=s.joinSize=0;h<s.joinBuffers.length;h++)s.joinSize+=s.joinBuffers[h].length}else{v=s.pcmTotal-s.pcmSend;var S=Math.round(v/s.sampleRate*1e3),T=r-s.sendCurSize,R=Math.min(u,v),k=Math.min(R,T);if(1==s.state&&k<Math.min(f,T))return;var j=0;if(T<=0&&(2==s.state&&v<1.2*s.sampleRate?(k=v,s.log("丢弃结尾"+S+"ms数据","#999"),c=0):j=!0),c&&!n(R)){var A=Math.round(s.asrDuration()/1e3);return s.log("已主动超时，共识别"+A+"秒，丢弃缓冲"+S+"ms，正在终止..."),s.wsLock=1,void i.stopWs(function(){t("已主动超时，共识别"+A+"秒，终止识别")},function(e){t(e)})}if(j)return w("[ASR]新1分钟接续，当前缓冲"+S+"ms..."),s.wsLock=1,void i.stopWs(function(){s._token(function(){s.log("新1分钟接续OK，当前缓冲"+S+"ms",2),s.wsLock=0,s.wsCur=0,s.sendCurSize=0,s.joinIsOpen=1,s.joinOffset=-1,s._send()},function(e){t("语音识别新1分钟token接口出错："+e)})},function(e){t(e)});_=new Int16Array(k);s.pcmOffset=m(s.pcmOffset,s.pcmBuffers,_),s.pcmSend+=k,s.joinBuffers.push(_),s.joinSize+=k}if(s.sendCurSize+=_.length,s.sendTotal+=_.length,c)try{i.send(_.buffer)}catch(e){console.error(e)}s.sendWait=setTimeout(function(){s.sendWait=0,s._send()})}}}else if(s.started){var b={};s.resTxts.push(b),i=s.wsCur=s._wsNew(s.tokenData,"ws:"+s.resTxts.length,b,function(){n()},function(){s._send()},function(e){i==s.wsCur&&t(e)})}}else e()}},getText:function(){for(var e=this.resTxts,t="",n=0;n<e.length;n++){var s=e[n];if(s.fullTxt)t=s.fullTxt;else{var o=s.tempTxt||"";if(s.okTxt&&(o=s.okTxt),t){for(var a=t.substr(-20),r=[],i=0,f=Math.min(17,o.length-3);i<=f;i++)for(var u=0;u<17;u++)if(a[u]==o[i]){for(var l=1;l<17&&a[u+l]==o[i+l];l++);3<=l&&r.push({x:i,i0:u,n:l})}r.sort(function(e,t){var n=t.n-e.n;return 0!=n?n:t.i0-e.i0});var p=r[0];p?(t=t.substr(0,t.length-a.length+p.i0),t+=o.substr(p.x)):t+=o}else t=o;null!=s.okTxt&&o==s.okTxt&&(s.fullTxt=t)}}return t},_wsNew:function(e,l,p,d,c,t){var o=function(){for(var e,t=[],n=0;n<32;n++)e=Math.floor(16*Math.random()),t.push(String.fromCharCode(e<10?e+48:e-10+97));return t.join("")},m=this;w("[ASR "+l+"]正在连接...");var g=new WebSocket("wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1?token="+e.token);return g.onclose=function(){var e=4!=g._s;g._s=-1,m.log("["+l+"]close"),e&&t(g._err||"连接"+l+"已关闭")},g.onopen=function(){g._s=1,w("[ASR "+l+"]open"),g._task=o(),g.send(JSON.stringify({header:{message_id:o(),task_id:g._task,appkey:e.appkey,namespace:"SpeechRecognizer",name:"StartRecognition"},payload:{format:"pcm",sample_rate:m.sampleRate,enable_intermediate_result:!0,enable_punctuation_prediction:!0,enable_inverse_text_normalization:!0},context:{}}))},g.onerror=function(e){m.log("["+l+"]连接出错",1)},g.onmessage=function(e){var t=e.data,n=!0;if("string"==typeof t&&"{"==t[0]){var s=(t=JSON.parse(t)).header||{},o=t.payload||{},a=s.name||"",r=s.status||0,i="TaskFailed"==a,f="";if(1!=g._s||"RecognitionStarted"!=a&&!i||(i?f="连接"+l+"失败["+r+"]"+s.status_text:(g._s=2,m.log("["+l+"]连接OK"),g.okTime=Date.now(),c())),2!=g._s||"RecognitionResultChanged"!=a&&!i||(i?f="识别出现错误["+r+"]"+s.status_text:(n=!g._clmsg,g._clmsg=1,p.tempTxt=o.result||"",d())),3==g._s&&("RecognitionCompleted"==a||i)){var u="";i?f="停止识别出现错误["+r+"]"+s.status_text:(u=o.result||"",m.log("["+l+"]最终识别结果："+u)),g.stopCall&&g.stopCall(u,f)}f&&(m.log("["+l+"]"+f,1),g._err||(g._err=f))}n&&w("[ASR "+l+"]msg",t)},g.stopWs=function(n,s){2==g._s?(g._s=3,g.isStop=1,g.stopCall=function(e,t){clearTimeout(g.stopInt),g.stopCall=0,g._s=4,g.close(),p.okTxt=e,d(),t?s(t):n()},g.stopInt=setTimeout(function(){g.stopCall&&g.stopCall("","停止识别返回结果超时")},1e4),w("[ASR "+l+"]send stop"),g.send(JSON.stringify({header:{message_id:o(),task_id:g._task,appkey:e.appkey,namespace:"SpeechRecognizer",name:"StopRecognition"}}))):s(l+"状态不正确["+g._s+"]")},g},_token:function(t,n){var s=this,e=s.set;e.tokenApi?(e.apiRequest||function(e,t,n,s){var o=new XMLHttpRequest;o.timeout=2e4,o.open("POST",e),o.onreadystatechange=function(){if(4==o.readyState)if(200==o.status){try{var e=JSON.parse(o.responseText)}catch(e){}if(0!==e.c||!e.v)return void s(e.m||"接口返回非预定义json数据");n(e.v)}else s("请求失败["+o.status+"]")};var a=[];for(var r in t)a.push(r+"="+encodeURIComponent(t[r]));o.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),o.send(a.join("&"))})(e.tokenApi,e.apiArgs||{},function(e){e&&e.appkey&&e.token?(s.tokenData=e,t()):n("apiRequest回调的数据格式不正确")},n):n("未配置tokenApi")}},Recorder[s]=e}();