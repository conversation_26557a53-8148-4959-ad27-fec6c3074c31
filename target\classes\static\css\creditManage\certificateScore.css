.dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}
.dialog .dialog-title {
  height: 56px;
  line-height: 56px;
  font-size: 16px;
  color: #1D2129;
  text-indent: 30px;
  border-bottom: 1px solid #E5E6EB;
}
.dialog .dialog-btn {
  height: 70px;
  background: #FFFFFF;
  border-top: 1px solid #E5E6EB;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  border-radius: 0 0 10px 10px;
}
.dialog .dialog-btn button {
  width: 88px;
  height: 36px;
  border-radius: 18px;
  cursor: pointer;
}
.dialog .dialog-btn button.btn-cancel {
  border: 1px solid #C9CDD4;
  background: #ffffff;
  color: #4E5969;
  margin-right: 16px;
}
.dialog .dialog-btn button.btn-sure {
  background: #4D88FF;
  border: 1px solid #4D88FF;
  color: #ffffff;
  margin-right: 30px;
}
.dialog .dialog-rule {
  width: 860px;
  background-color: #FFFFFF;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 10px;
}
.dialog .dialog-rule .dialog-con {
  overflow: hidden;
}
.dialog .dialog-rule .dialog-con form {
  margin: 32px 30px 0;
  font-size: 14px;
}
.dialog .dialog-rule .dialog-con form .form-item-type {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
}
.dialog .dialog-rule .dialog-con form .form-item-type .item-title {
  width: 70px;
  color: #1D2129;
  line-height: 34px;
}
.dialog .dialog-rule .dialog-con form .form-item-type input {
  width: 240px;
  height: 34px;
  background: #F8F8FA;
  border: 1px solid #D4D6D9;
  border-radius: 4px;
  padding: 0 14px;
  box-sizing: border-box;
  color: #ACB4BF;
}
.dialog .dialog-rule .dialog-con form .form-item-score h3 {
  display: flex;
  color: #1D2129;
  line-height: 20px;
  margin-bottom: 10px;
}
.dialog .dialog-rule .dialog-con form .form-item-score h3 span {
  color: #F76560;
  line-height: 20px;
  margin-right: 4px;
}
.dialog .dialog-rule .dialog-con form table {
  margin-bottom: 40px;
}
.dialog .dialog-rule .dialog-con form table thead {
  background: #F1F3F6;
}
.dialog .dialog-rule .dialog-con form table thead tr td {
  color: #86909C;
}
.dialog .dialog-rule .dialog-con form table tr {
  height: 36px;
}
.dialog .dialog-rule .dialog-con form table tr:nth-child(2n) {
  background: #FAFBFC;
}
.dialog .dialog-rule .dialog-con form table tr td {
  text-align: center;
  border: 1px solid #E8EBF1;
  color: #4E5969;
}
.dialog .dialog-rule .dialog-con form table tr td:nth-child(2) input,
.dialog .dialog-rule .dialog-con form table tr td:nth-child(3) input {
  display: none;
  width: 100%;
  height: 34px;
  text-align: center;
  border: none;
  box-sizing: border-box;
}
.dialog .dialog-rule .dialog-con form table tr td:nth-child(2) input:focus,
.dialog .dialog-rule .dialog-con form table tr td:nth-child(3) input:focus {
  border: 1px solid #4D88FF;
  box-shadow: 0px 0px 4px rgba(77, 136, 255, 0.2);
}
.dialog .dialog-rule .dialog-con form table tr td:nth-child(2) input.focusCla,
.dialog .dialog-rule .dialog-con form table tr td:nth-child(3) input.focusCla {
  border: 1px solid #4D88FF;
  box-shadow: 0px 0px 4px rgba(77, 136, 255, 0.2);
}
.dialog .dialog-rule .dialog-con form table tr td:last-child .edit {
  color: #4C88FF;
  cursor: pointer;
}
.dialog .dialog-rule .dialog-con form table tr td:last-child .opt {
  display: none;
}
.dialog .dialog-rule .dialog-con form table tr td:last-child .opt span {
  color: #4C88FF;
  cursor: pointer;
}
.dialog .dialog-rule .dialog-con form table tr td:last-child .opt span:first-child {
  margin-right: 16px;
}
