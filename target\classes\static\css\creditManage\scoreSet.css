.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.container {
  height: calc(100vh);
  margin: 20px;
  overflow-y: auto;
}
.set-form .stu-apply {
  display: flex;
  align-items: center;
}
.set-form .stu-apply .layui-form-switch {
  background-color: #d2d2d2;
  border-color: #d2d2d2;
}
.set-form .stu-apply .layui-form-switch i {
  background-color: #fff;
}
.set-form .stu-apply .layui-form-onswitch {
  background-color: #0084ff;
  border-color: #0084ff;
}
.set-form .stu-apply .layui-form-checkbox[lay-skin="primary"] i {
  border-radius: 4px;
}
.set-form .btn-submit {
  height: 36px;
  width: 96px;
  border-radius: 6px;
  background: #3a8bff;
  margin: 0 auto;
  position: absolute;
  bottom: 99px;
}
.set-form .tips {
  color: #999;
  font-size: 14px;
  line-height: 24px;
  display: inline-block;
  margin-left: 11px;
  padding-top: 8px;
}
.j-material-lable {
  padding-bottom: 10px;
  width: 925px;
  min-height: calc(100vh - 400px);
}
.j-material-lable h4 {
  position: relative;
  color: #6581ba;
  font-size: 16px;
  padding-left: 9px;
  line-height: 20px;
  font-weight: bold;
  margin-bottom: 24px;
}
.j-material-lable h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #6581ba;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 2px;
}
.j-material-lable .j-switch {
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 30px;
}
.j-material-lable .j-switch .name {
  width: 154px;
  height: 34px;
  font-weight: 400;
  font-size: 14px;
  line-height: 34px;
  color: #1d2129;
}
.j-material-lable .j-switch .switch {
  background: #dadfe6;
  border-radius: 4px;
  width: 28px;
  height: 14px;
  position: relative;
  margin-top: 10px;
}
.j-material-lable .j-switch .switch span {
  position: absolute;
  left: 2px;
  top: 2px;
  display: block;
  width: 12px;
  height: 10px;
  border-radius: 2px;
  background: #ffffff;
  transition: all 200ms linear;
}
.j-material-lable .j-switch .switch.active {
  background: #4d88ff;
}
.j-material-lable .j-switch .switch.active span {
  left: 14px;
}
.j-material-lable .j-switch .tit {
  font-size: 14px;
  line-height: 34px;
  color: #c9cdd4;
  margin-left: 10px;
}
.j-material-lable .set-list .sl-lab {
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 30px;
}
.j-material-lable .set-list .sl-lab .name {
  width: 126px;
  height: 34px;
  font-weight: 400;
  font-size: 14px;
  line-height: 34px;
  color: #1d2129;
}
.j-material-lable .set-list .sl-lab .mult-choice ul {
  overflow: hidden;
  width: 536px;
}
.j-material-lable .set-list .sl-lab .mult-choice ul li {
  height: 34px;
  line-height: 34px;
  float: left;
  padding-left: 24px;
  color: #86909c;
  font-size: 14px;
  cursor: pointer;
  background: url(../../images/creditManage/check-icon.png) no-repeat left center;
  width: 134px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.j-material-lable .set-list .sl-lab .mult-choice ul li.cur {
  background: url(../../images/creditManage/check-cur.png) no-repeat left center;
}
.j-material-lable .set-list .sl-lab .select-input {
  width: 240px;
  height: 34px;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
}
.j-material-lable .set-list .sl-lab .select-input.score-sel {
  width: 136px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  margin-right: 10px;
  height: 34px;
}
.j-material-lable .set-list .sl-lab .select-input em {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 10px;
  height: 10px;
  background: url(../../images/creditManage/icon-arrow1.png) no-repeat center;
  background-size: 10px;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.j-material-lable .set-list .sl-lab .select-input .name {
  font-size: 14px;
  color: #86909c;
  padding-left: 10px;
  width: 210px;
  height: 32px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.j-material-lable .set-list .sl-lab .select-input .name.ckd {
  color: #1d2129;
}
.j-material-lable .set-list .sl-lab .select-input.clicked em {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  background: url(../../images/creditManage/icon-arrow1.png) no-repeat center;
  background-size: 10px;
}
.j-material-lable .set-list .sl-lab .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
}
.j-material-lable .set-list .sl-lab .select-input .select-dropdown {
  width: inherit;
  left: -1px;
  margin: 5px 0;
  padding: 6px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: absolute;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
.j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list {
  max-height: 320px;
  overflow: auto;
}
.j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li {
  margin: 0;
  line-height: normal;
  padding: 0 16px;
  clear: both;
  color: #131b26;
  font-size: 14px !important;
  white-space: nowrap;
  list-style: none;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
.j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li span {
  display: block;
  padding-left: 24px;
  background: url(../../images/creditManage/check-icon.png) no-repeat left center;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li:hover {
  background-color: #f5f7fa;
}
.j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li.cur {
  color: #616ee6;
}
.j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li.cur span {
  background: url(../../images/creditManage/check-cur.png) no-repeat left center;
}
.j-material-lable .set-list .sl-lab .select-input .select-dropdown .confirm {
  line-height: 30px;
  text-align: right;
  cursor: pointer;
  padding: 0 16px;
  color: #4d88ff;
}
.j-material-lable .row {
  width: 100%;
  height: 34px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.j-material-lable .row .tit {
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  margin-right: 14px;
}
.j-material-lable .row .input {
  margin-right: 4px;
}
.j-material-lable .row .input input {
  height: 34px;
}
.j-material-lable .row .inform {
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  margin-right: 4px;
}
.j-material-lable .j-lable .lab {
  height: 34px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 24px;
}
.j-material-lable .j-lable .lab .name {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: #1d2129;
  margin-right: 14px;
  width: 140px;
}
.j-material-lable .j-lable .lab .time {
  background: url(../../images/creditManage/time-select.png) no-repeat 220px center;
  background-size: 12px;
}
.j-material-lable .j-lable .lab .time input {
  background-color: transparent;
}
.j-material-lable .add-oprate {
  height: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.j-material-lable .add-oprate h5 {
  font-weight: 700;
  font-size: 14px;
  color: #1d2129;
}
.j-material-lable .add-oprate .add {
  padding-left: 22px;
  background: url(../../images/creditManage/add-icon.png) no-repeat left center;
  background-size: 16px;
  font-weight: 400;
  font-size: 14px;
  color: #4c88ff;
  cursor: pointer;
}
.j-material-lable .j-table {
  border: 1px solid #e8ebf1;
  margin-bottom: 24px;
}
.j-material-lable .j-table .j-head {
  background: #f1f3f6;
  border-bottom: 1px solid #e8ebf1;
  height: 36px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.j-material-lable .j-table .j-head .j-th {
  border-right: 1px solid #e8ebf1;
  text-align: center;
  line-height: 35px;
  font-weight: 400;
  font-size: 14px;
  color: #6581ba;
}
.j-material-lable .j-table .j-head .j-th:first-child {
  width: 300px;
}
.j-material-lable .j-table .j-head .j-th:nth-child(2) {
  width: 160px;
}
.j-material-lable .j-table .j-head .j-th:nth-child(3) {
  width: 360px;
}
.j-material-lable .j-table .j-head .j-th:last-child {
  width: 80px;
  text-align: center;
  flex-shrink: 0;
  border-right: none;
}
.j-material-lable .j-table .j-body .j-tr {
  background: #ffffff;
  border-bottom: 1px solid #e8ebf1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.j-material-lable .j-table .j-body .j-tr:nth-child(2n) {
  background: #fafbfc;
}
.j-material-lable .j-table .j-body .j-tr:last-child {
  border-bottom: none;
}
.j-material-lable .j-table .j-body .j-tr.opafter .j-td .total {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  height: 80px;
  justify-content: center;
}
.j-material-lable .j-table .j-body .j-tr.opafter .j-td .oprate {
  display: none;
}
.j-material-lable .j-table .j-body .j-tr .j-td {
  border-right: 1px solid #e8ebf1;
  height: 80px;
}
.j-material-lable .j-table .j-body .j-tr .j-td:first-child {
  width: 300px;
}
.j-material-lable .j-table .j-body .j-tr .j-td:nth-child(2) {
  width: 160px;
}
.j-material-lable .j-table .j-body .j-tr .j-td:nth-child(3) {
  width: 360px;
}
.j-material-lable .j-table .j-body .j-tr .j-td:last-child {
  width: 80px;
}
.j-material-lable .j-table .j-body .j-tr .j-td .oprate {
  display: flex;
  height: 80px;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
}
.j-material-lable .j-table .j-body .j-tr .j-td .splite {
  margin-right: 10px;
}
.j-material-lable .j-table .j-body .j-tr .j-td .total {
  display: none;
}
.j-material-lable .j-table .j-body .j-tr .j-td .preservation {
  font-weight: 400;
  font-size: 14px;
  color: #4c88ff;
  margin: 0 10px;
  cursor: pointer;
}
.j-material-lable .j-table .j-body .j-tr .j-td .edit {
  font-weight: 400;
  font-size: 14px;
  color: #4c88ff;
  margin: 0 10px;
  cursor: pointer;
}
.j-material-lable .j-table .j-body .j-tr .j-td .delet {
  font-weight: 400;
  font-size: 14px;
  color: #f76560;
  margin: 0 10px;
  cursor: pointer;
}
.j-material-lable .j-table .j-body .j-tr .j-td .symbol {
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  margin: 0 4px;
}
.j-material-lable .j-table .j-body .j-tr .j-td .txt {
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  margin-right: 14px;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inp-lay {
  width: 120px;
  position: relative;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inp-lay.errors .error {
  display: block;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inp-lay.errors input {
  border: 1px solid #f98981 !important;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inp-lay .error {
  position: absolute;
  display: none;
  left: 0;
  top: 34px;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  color: #f76560;
}
.j-material-lable .j-table .j-body .j-tr .j-td:last-child {
  flex: 2;
  flex-shrink: 0;
  border-right: none;
}
.j-material-lable .j-table .j-body .j-tr .j-td .select-input {
  width: 80px;
  height: 32px;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
}
.j-material-lable .j-table .j-body .j-tr .j-td .select-input.score-sel {
  width: 120px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  margin-right: 10px;
  height: 34px;
}
.j-material-lable .j-table .j-body .j-tr .j-td .select-input em {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 10px;
  height: 10px;
  background: url(../../images/creditManage/icon-arrow1.png) no-repeat center;
  background-size: 10px;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.j-material-lable .j-table .j-body .j-tr .j-td .select-input .name {
  font-size: 14px;
  color: #86909c;
  padding-left: 10px;
  width: 100%;
  height: 32px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.j-material-lable .j-table .j-body .j-tr .j-td .select-input .name.ckd {
  color: #1d2129;
}
.j-material-lable .j-table .j-body .j-tr .j-td .select-input.clicked em {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  background: url(../../images/creditManage/icon-arrow1.png) no-repeat center;
  background-size: 10px;
}
.j-material-lable .j-table .j-body .j-tr .j-td .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
}
.j-material-lable .j-table .j-body .j-tr .j-td .select-input .select-dropdown {
  width: inherit;
  max-height: 320px;
  overflow: auto;
  left: -1px;
  margin: 5px 0;
  padding: 6px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: absolute;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
.j-material-lable .j-table .j-body .j-tr .j-td .select-input .select-dropdown .dropdown-list li {
  margin: 0;
  line-height: normal;
  padding: 0 16px;
  clear: both;
  color: #131b26;
  font-size: 14px !important;
  white-space: nowrap;
  list-style: none;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
.j-material-lable .j-table .j-body .j-tr .j-td .select-input .select-dropdown .dropdown-list li:hover {
  background-color: #f5f7fa;
}
.j-material-lable .j-table .j-body .j-tr .j-td .select-input .select-dropdown .dropdown-list li.cur {
  color: #616ee6;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inputs {
  margin-right: 10px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  width: 199px;
  height: 34px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inputs.errors {
  border: 1px solid #f98981;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inputs.errors .error {
  display: block;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inputs.errors .inp {
  border-right: 1px solid #f98981 !important;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inputs.right-inp .error {
  left: 80px;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inputs.right-inp input {
  border-right: none !important;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inputs.right-inp .select-input {
  border-right: 1px solid #e5e6eb;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inputs.right-inp.errors .error {
  display: block;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inputs.right-inp.errors .select-input {
  border-right: 1px solid #f98981 !important;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inputs input {
  width: 120px;
  height: 32px;
  background-color: transparent;
  border: none !important;
  border-right: 1px solid #e5e6eb !important;
  border-radius: 0 !important;
}
.j-material-lable .j-table .j-body .j-tr .j-td .inputs .error {
  position: absolute;
  display: none;
  left: 0;
  top: 34px;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  color: #f76560;
}
.save-set {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}
.save-set .btn-submit {
  height: 36px;
  width: 96px;
  border-radius: 6px;
  background: #3a8bff;
  margin: 0 auto;
}
.dialog-wrap {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
}
.dialog-wrap .dialog {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  width: 560px;
  padding: 40px 0;
  border-radius: 10px;
  overflow: hidden;
}
.dialog-wrap .dialog img {
  display: block;
  width: 48px;
  height: 48px;
  margin: 0 auto;
}
.dialog-wrap .dialog p {
  text-align: center;
  font-size: 16px;
  color: #1d2129;
  margin: 24px 0;
}
.dialog-wrap .dialog button {
  border-radius: 18px;
  background: var(--unnamed, #4d88ff);
  box-shadow: 0px 0px 10px 0px rgba(0, 108, 226, 0.4);
  width: 88px;
  height: 36px;
  margin: 0 auto;
  float: unset;
}

.set-form .stu-apply .layui-form-switch {
  background-color: #d2d2d2;
  border-color: #d2d2d2;
  width: 28px;
  height: 14px;
  border-radius: 3px;
  min-width: 0;
  padding: 0;
  box-sizing: border-box;
}
.set-form .stu-apply .layui-form-switch i {
  background-color: #fff;
  width: 12px;
  height: 10px;
  border-radius: 1px;
  margin-left: -4px;
  top: 1px;
}
.set-form .stu-apply .layui-form-onswitch {
  background-color: #4D88FE;
  border-color: #4D88FE;
  width: 28px;
  height: 14px;
  padding: 0;
  border-radius: 3px;
  min-width: 0;
}
.set-form .stu-apply .layui-form-onswitch i{
  margin-left: -13px;
}

