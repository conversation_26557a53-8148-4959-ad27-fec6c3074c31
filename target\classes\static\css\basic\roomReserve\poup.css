.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.layui-laypage .layui-disabled {
  background-color: transparent !important;
  border: none !important;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #ACB4BF !important;
  font-size: 14px;
}
::-webkit-scrollbar {
  width: 6px;
  height: 10px;
}
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #d9d9d9;
}
::-webkit-scrollbar-track {
  border-radius: 6px;
}
.popups {
  background: #FFFFFF;
  border-radius: 10px;
  display: none;
}
.popups .title {
  height: 52px;
  line-height: 52px;
  font-size: 16px;
  font-weight: 400;
  padding: 0 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #F2F2F2;
}
.popups .title .name {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  color: #1d2129;
  text-align: left;
}
.popups .title .close {
  width: 20px;
  height: 20px;
  background: url(../images/close-icon.png) no-repeat center;
  cursor: pointer;
}
.popups .popup-con {
  padding: 30px;
}
.popups .bottom {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 66px;
  border-top: 1px solid #E5E6EB;
  padding: 0 24px;
}
.popups .bottom div {
  width: 88px;
  height: 34px;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
  border-radius: 18px;
  cursor: pointer;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popups .bottom div.cancle {
  border: 1px solid #C9CDD4;
  color: #4E5969;
  background-color: #fff;
  margin-right: 16px;
}
.popups .bottom div.confirm {
  color: #fff;
  background: #4D88FF;
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  border: 1px solid #4D88FF;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: #4D88FF !important;
}
.layui-laypage a:hover {
  color: #4D88FF !important;
}
.layui-laypage .layui-disabled,
.layui-laypage .layui-disabled:hover {
  color: #d2d2d2 !important;
  cursor: not-allowed !important;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #4D88FF !important;
}
.tips-popup {
  width: 520px;
}
.tips-popup .popup-con {
  padding: 40px 80px;
}
.tips-popup .popup-con p {
  color: #1d2129;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 20px;
}
.tips-popup .popup-con .checkbox {
  padding-left: 24px;
  font-size: 14px;
  color: #4e5969;
  line-height: 20px;
  cursor: pointer;
  background: url(../images/check-icon.png) no-repeat left center;
}
.tips-popup .popup-con .checkbox.selected {
  background: url(../images/checked-icon.png) no-repeat left center;
}
.layui-layer {
  border-radius: 10px !important;
}
.style1 .tips-popup .popup-con .checkbox.selected {
  background: url(../images/checked-hy-icon.png) no-repeat left center;
}
.style1 .popups .bottom div.confirm {
  background-color: #3eb35a;
  box-shadow: 0px 0px 8px 0px rgba(62, 179, 90, 0.3);
  border-color: #3eb35a;
}
.tips-popup * {
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* IE/Edge */
  user-select: none;
  /* 标准语法 */
}
/*调整记录新增2024.8.19*/
#adjustingRecords {
  width: 719px;
}
#adjustingRecords .popup-con .texts-box {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 32px;
}
#adjustingRecords .popup-con .texts-box .lable {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 20px;
  margin-right: 24px;
}
#adjustingRecords .popup-con .texts-box .lable .name {
  font-size: 14px;
  color: #1d2129;
}
#adjustingRecords .popup-con .texts-box .lable .text {
  font-size: 14px;
  color: #4e5969;
}
/* 2024.9.12 新增样式 */
#notifyTeachers {
  width: 840px;
  height: 600px;
}
#notifyTeachers .popup-con {
  width: 100%;
  height: 480px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
#notifyTeachers .popup-con .affair-list .lable {
  overflow: hidden;
  margin-bottom: 30px;
}
#notifyTeachers .popup-con .affair-list .lable .name {
  width: auto;
  min-width: 70px;
  text-align: right;
  margin-right: 20px;
  float: left;
  height: 34px;
  line-height: 34px;
}
#notifyTeachers .popup-con .affair-list .lable .radio {
  float: left;
  height: 34px;
  line-height: 34px;
  overflow: hidden;
}
#notifyTeachers .popup-con .affair-list .lable .radio span {
  float: left;
  padding-left: 24px;
  background: url(../images/radio-cur.png) no-repeat left center;
  font-size: 14px;
  color: #474C59;
  margin-right: 30px;
  cursor: pointer;
}
#notifyTeachers .popup-con .affair-list .lable .radio span.cur {
  background: url(../images/radioed-icon.png) no-repeat left center;
}
#notifyTeachers .popup-con .affair-list .lable .kalamu-area {
  float: left;
  width: 614px;
  min-height: 200px;
  border: 1px solid #D4D6D9;
  border-radius: 4px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 10px 15px;
  overflow-y: auto;
  font-size: 14px;
  outline: none;
  letter-spacing: 2px;
  color: #666;
  position: relative;
}
#notifyTeachers .popup-con .affair-list .lable .mul-choice {
  float: left;
  width: 614px;
  height: 34px;
  border: 1px solid #D4D6D9;
  border-radius: 4px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  position: relative;
  padding: 3px 10px;
}
#notifyTeachers .popup-con .affair-list .lable .mul-choice ul li {
  float: left;
  height: 26px;
  line-height: 26px;
  padding: 0 10px;
  background-color: #F2F5FA;
  margin-right: 16px;
  position: relative;
  font-size: 14px;
  color: #131B26;
}
#notifyTeachers .popup-con .affair-list .lable .mul-choice ul li em {
  position: absolute;
  width: 12px;
  height: 12px;
  top: -2px;
  right: -6px;
  cursor: pointer;
  background: url(../images/group-close.png) no-repeat center;
}
#notifyTeachers .popup-con .affair-list .lable .mul-choice .place {
  position: absolute;
  left: 14px;
  top: 6px;
  font-size: 14px;
  color: #ACB4BF;
}
#notifyTeachers .popup-con .affair-list .lable .mul-choice.clicked .place {
  display: none;
}
#notifyTeachers .popup-con .affair-list .lable .mul-choice .add {
  position: absolute;
  right: -30px;
  top: 6px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  background: url(../images/group-icon.png) no-repeat center;
}
#notifyTeachers .popup-con .affair-list .lable .mul-choice .add:hover {
  background: url(../images/groupcur-icon.png) no-repeat center;
}
#notifyTeachers .popup-con .affair-list .lable .sel {
  float: left;
  margin-bottom: 0;
}
#notifyTeachers .popup-con .affair-list .lable .sel .select-input {
  margin-left: 0;
  margin-right: 0;
  width: 200px;
}
#notifyTeachers .popup-con .affair-list .lable .sel .select-input .select-dropdown {
  position: absolute;
  left: -1px;
  top: 33px;
}
#notifyTeachers .popup-con .affair-list .lable .sel .select-input .name {
  text-align: left;
  width: 170px;
}
#notifyTeachers .popup-con .affair-list .lable .input {
  width: 644px;
  height: 34px;
  border: 1px solid #D4D6D9;
  border-radius: 4px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  float: left;
}
#notifyTeachers .popup-con .affair-list .lable .input input {
  border: none;
  background: transparent;
  outline: none;
  width: 100%;
  height: 32px;
  line-height: 32px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-left: 14px;
}
#notifyTeachers .popup-con .affair-list .lable .timetable {
  float: left;
  width: auto;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row {
  overflow: hidden;
  margin-bottom: 3px;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row.bigMb {
  margin-bottom: 10px;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row.thead {
  background: #193B67;
  border-radius: 4px;
  height: 36px;
  line-height: 36px;
  font-size: 14px;
  color: #FFFFFF;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row.thead .lab {
  float: left;
  width: 84px;
  text-align: center;
  margin-right: 4px;
  background-color: transparent;
  line-height: 36px;
  color: #FFFFFF;
  cursor: default;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row.thead .lab:first-child {
  width: 66px;
  height: 36px;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row.thead .lab:last-child {
  margin-right: 0;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row.thead .lab:hover {
  border: none;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row .lab {
  float: left;
  margin-right: 4px;
  width: 84px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  cursor: pointer;
  background-color: #F2F5FA;
  font-size: 14px;
  color: #131B26;
  border-radius: 4px;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row .lab.has:hover {
  border: 1px solid #616EE6;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row .lab:first-child {
  width: 66px;
  height: 50px;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row .lab:last-child {
  margin-right: 0;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row .lab.disabled {
  cursor: default;
}
#notifyTeachers .popup-con .affair-list .lable .timetable .row .lab.cur {
  background: #E5E8FF url(../images/duihao-icon.png) no-repeat center;
}
#selectPersonDialog {
  width: 640px;
  height: 610px;
}
#selectPersonDialog .popup-con {
  padding: 0;
  height: 490px;
  overflow: hidden;
}
#selectPersonDialog .popup-con .framework {
  float: left;
  width: 380px;
  height: 490px;
  border-right: 1px solid #F2F2F2;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
#selectPersonDialog .popup-con .framework .orgSel {
  margin: 14px 30px 20px;
  width: 320px;
  height: 36px;
  border: 1px solid #E1E1E5;
  border-radius: 4px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
#selectPersonDialog .popup-con .framework .orgSel input {
  width: 280px;
  height: 34px;
  padding-left: 14px;
  border: none;
  background-color: transparent;
  outline: none;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  float: left;
}
#selectPersonDialog .popup-con .framework .orgSel span {
  width: 16px;
  height: 34px;
  float: right;
  background: url(../images/search-icons.png) no-repeat center;
  margin-right: 14px;
  cursor: pointer;
}
#selectPersonDialog .popup-con .framework .fra-box {
  margin-left: 30px;
  height: 420px;
  overflow: hidden;
  width: 350px;
  overflow-y: auto;
}
#selectPersonDialog .popup-con .framework .fra-box ul li {
  width: 100%;
  clear: both;
  position: relative;
  height: 40px;
  cursor: pointer;
}
#selectPersonDialog .popup-con .framework .fra-box ul li:nth-child(2n) {
  background-color: #F7FAFC;
}
#selectPersonDialog .popup-con .framework .fra-box ul li.selected .handle {
  display: block;
  background: url(../images/checked-icon.png) no-repeat center;
}
#selectPersonDialog .popup-con .framework .fra-box ul li .name {
  float: left;
  line-height: 40px;
  font-size: 14px;
  color: #131B26;
  margin-left: 20px;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#selectPersonDialog .popup-con .framework .fra-box ul li .handle {
  display: block;
  float: right;
  margin-right: 20px;
  height: 16px;
  width: 16px;
  margin-top: 12px;
  background: url(../images/check-icon.png) no-repeat center;
  background-size: 100%;
}
#selectPersonDialog .popup-con .seleced-list {
  float: right;
  width: 259px;
  height: 470px;
  background: #ffffff;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
#selectPersonDialog .popup-con .seleced-list h3 {
  padding: 24px 24px 10px;
  font-size: 14px;
  color: #8A8B99;
  line-height: 20px;
}
#selectPersonDialog .popup-con .seleced-list h3 i {
  display: inline-block;
  margin: 0 4px;
}
#selectPersonDialog .popup-con .seleced-list .data-scroll {
  width: 100%;
  height: 436px;
  overflow-y: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
#selectPersonDialog .popup-con .seleced-list .data-scroll ul {
  padding: 0 14px 0 13px;
}
#selectPersonDialog .popup-con .seleced-list .data-scroll ul li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-left: 20px;
}
#selectPersonDialog .popup-con .seleced-list .data-scroll ul li .name {
  float: left;
  line-height: 40px;
  color: #131B26;
  font-size: 14px;
  max-width: 156px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#selectPersonDialog .popup-con .seleced-list .data-scroll ul li .cancl {
  float: right;
  width: 14px;
  height: 14px;
  background: url(../images/sm-close-icon.png) no-repeat center;
  cursor: pointer;
  margin-right: 20px;
  margin-top: 13px;
  display: none;
}
#selectPersonDialog .popup-con .seleced-list .data-scroll ul li:hover {
  background-color: #F7FAFC;
}
#selectPersonDialog .popup-con .seleced-list .data-scroll ul li:hover .cancl {
  display: block;
}
.layui-laypage .layui-disabled,
.layui-laypage .layui-disabled:hover {
  color: #d2d2d2 !important;
  cursor: not-allowed !important;
}
#pushRecords {
  width: 1029px;
}
#pushRecords .popup-con .table {
  margin-bottom: 20px;
}
#pushRecords .popup-con .table .progress-content {
  width: 96%;
  margin: 0 auto;
  display: flex;
  align-items: center;
}
#pushRecords .popup-con .table .progress-content .progress-bar {
  flex: 1;
  border-radius: 3px;
  background: #e5e6eb;
  height: 3px;
  text-align: left;
}
#pushRecords .popup-con .table .progress-content .progress-bar .progress {
  background-color: #00b42a;
  border-radius: 3px;
  display: block;
  height: 3px;
}
#pushRecords .popup-con .table .progress-content .progress-bar .progress.success {
  background-color: #00B42A;
}
#pushRecords .popup-con .table .progress-content .progress-bar .progress.error {
  background-color: #f76560;
}
#pushRecords .popup-con .table .progress-content .progress-text {
  font-size: 12px;
  color: #4e5969;
  margin-left: 8px;
}
#pushRecords .popup-con .table .progress-content .progress-tip {
  display: none;
  width: 12px;
  height: 12px;
}
#pushRecords .popup-con .table .progress-content .progress-tip.error {
  display: block;
  background: url('../images/ts-error-icons.png') no-repeat center;
  background-size: 12px;
  margin-left: 4px;
}
#pushRecords .popup-con .tab-mes {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#pushRecords .popup-con .tab-mes .total {
  font-size: 14px;
  color: #86909c;
}
#pushRecords .popup-con .tab-mes .total em {
  color: #4D88FF;
  display: inline-block;
  margin: 0 4px;
}
#pushRecords .popup-con .tab-mes .refresh {
  padding-left: 20px;
  color: #4D88FF;
  font-size: 14px;
  background: url(../images/refresh.png) no-repeat left center;
  cursor: pointer;
  background-size: 16px;
}
#pushRecords .layui-laypage a,
#pushRecords .layui-laypage span {
  margin-bottom: 0;
}
#pushRecords .layui-laypage {
  margin: 0;
}
.layui-table,
.layui-table-view {
  margin: 0;
}
#pushRecords .layui-table thead tr {
  background-color: #4D88FF;
}
#pushRecords .layui-table th {
  color: #fff;
}
#pushRecords .layui-table tr:nth-child(2n) {
  background-color: #fafbfc;
}
#pushRecords .layui-table tr:nth-child(2n):hover {
  background-color: #bedaff;
}
#pushRecords .layui-table tr:nth-child(2n):hover .layui-table-cell {
  color: #4D88FF;
}
#pushRecords .layui-table tr:hover {
  background-color: #bedaff;
}
#pushRecords .layui-table tr:hover .layui-table-cell {
  color: #4D88FF;
}
.style1 #pushRecords .layui-table thead tr {
  background-color: #3eb35a;
}
.style1 #pushRecords .layui-table thead tr:hover {
  background-color: #3eb35a;
}
.style1 #pushRecords .layui-table tr:hover .layui-table-cell {
  color: #3eb35a;
}
.style1 #pushRecords .layui-table tr:hover {
  background-color: #e8ffea;
}
.style1 #pushRecords .layui-table tr:nth-child(2n):hover {
  background-color: #e8ffea;
}
.style1 #pushRecords .layui-table tr:hover th .layui-table-cell {
  color: #fff;
}
.style1 #pushRecords .popup-con .tab-mes .total em {
  color: #3eb35a;
}
.style1 .layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: #3eb35a !important;
}
.style1 .layui-laypage a:hover {
  color: #3eb35a !important;
}
.style1 .layui-laypage a.layui-disabled,
.layui-laypage a.layui-disabled:hover {
  color: #d2d2d2 !important;
}
.style1 .layui-laypage input:focus,
.style1 .layui-laypage select:focus {
  border-color: #3eb35a !important;
}
.style1 #pushRecords .popup-con .tab-mes .refresh {
  color: #3eb35a;
  font-size: 14px;
  background: url(../images/hy-refresh.png) no-repeat left center;
  background-size: 16px;
}
.style1 #notifyTeachers .popup-con .affair-list .lable .mul-choice ul li em {
  background: url(../images/hy-group-close.png) no-repeat center;
}
.style1 #notifyTeachers .popup-con .affair-list .lable .mul-choice .add:hover {
  background: url(../images/hy-groupcur-icon.png) no-repeat center;
}
.style1 #selectPersonDialog .popup-con .framework .fra-box ul li.selected .handle {
  display: block;
  background: url(../images/checked-hy-icon.png) no-repeat center;
}
.layui-layer-shade {
  z-index: 99999;
}
/* 2024.9.12 新增样式 */
