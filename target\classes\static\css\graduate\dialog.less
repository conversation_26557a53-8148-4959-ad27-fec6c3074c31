.masker {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
}

.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.j-search-con {
    display: flex;
    align-items: center;
    position: relative;
    width: 240px;
    cursor: pointer;



    .j-select-year {
        left: 0;
    }

    input {
        width: 100%;
        height: 34px;
        border: 1px solid #E5E6EB;
        border-radius: 4px;
        padding: 0 20px 0 10px;
        .borDer;
        font-size: 14px;
        cursor: pointer;

        &::placeholder {
            color: #86909C;
        }
    }

    .j-arrow {
        width: 10px;
        height: 10px;
        background: url(../images/down-icon.png) no-repeat center;
        position: absolute;
        right: 12px;
        top: 12px;

        &.j-arrow-slide {
            transform: rotate(180deg);
        }
    }

    .j-select-year {
        position: absolute;
        top: 40px;
        left: -1px;
        z-index: 9;
        width: 100%;
        max-height: 300px;
        overflow-y: auto;
        border-radius: 4px;
        display: none;
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);

        &.slideShow {
            display: block;
        }

        .search {
            height: 36px;
            background: #F5F7FA;
            border-radius: 18px;
            margin: 11px 10px;


            input {
                border: none;
                width: 176px;
                background: transparent;
                height: 36px;
                line-height: 36px;
                padding-left: 14px;
                box-sizing: borDer-box;
                -webkit-box-sizing: borDer-box;
                -moz-box-sizing: borDer-box;
                -ms-box-sizing: borDer-box;
                -o-box-sizing: borDer-box;
                float: left;
            }

            span {
                cursor: pointer;
                float: left;
                width: 16px;
                height: 16px;
                background: url(../images/search-icon.png) no-repeat center;
                margin-top: 10px;
            }
        }

        .all-selects {
            line-height: 17px;
            margin-bottom: 4px;
            height: 17px;
            padding: 0 14px;
            font-size: 12px;
            color: #6B89B3;
            cursor: pointer;
            user-select: none;
        }

        ul {
            overflow: hidden;
            max-height: 200px;
            overflow-y: auto;

            li {
                line-height: 40px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-align: left;
                text-indent: 16px;
                cursor: pointer;
                font-size: 14px;
                color: #4E5969;
                .textEls;
                .borDer;
                padding-right: 30px;
                background-color: #ffffff;
                background-image: url("../images/check-icon.png");
                background-repeat: no-repeat;
                background-position: 96% center;

                &:hover {
                    background-color: #E1EBFF;
                    color: #4D88FF;
                    font-weight: 500;
                }

                &.active {
                    background-color: #E1EBFF;
                    background-image: url("../images/check-cur.png");
                    color: #4D88FF;
                    font-weight: 500;
                }



            }
        }
    }

    &.single-box {
        .j-select-year {
            ul {
                li {
                    background-image: url("../images/radio-icon.png");

                    &.active {
                        background-image: url("../images/radio-cur-icon.png");
                    }
                }
            }
        }
    }


}

.dialog {
    // overflow: hidden;
    border-radius: 10px;
    background-color: #ffffff;

    .dialog-title {
        border-bottom: 1px solid #E5E6EB;
        height: 56px;
        line-height: 56px;
        color: #1D2129;
        font-size: 16px;
        text-indent: 30px;
    }

    .dialog-btn {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 70px;
        border-top: 1px solid #E5E6EB;
        padding-right: 30px;

        button {
            width: 88px;
            height: 36px;
            font-size: 14px;
            border-radius: 18px;
            cursor: pointer;

            &.pu-cancel {
                border: 1px solid #C9CDD4;
                color: #4E5969;
                background-color: #fff;
                margin-right: 16px;
            }

            &.pu-sure {
                width: 116px;
                color: #fff;
                background: #4D88FF;
                box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
                border: 1px solid #4D88FF;
            }
        }
    }

    .dialog-con {
        margin: 30px 60px;

        .item {
            display: flex;
            align-items: center;
            margin-bottom: 24px;

            .label {
                color: #1D2129;
                font-size: 14px;
                margin-right: 14px;
            }
        }
    }

}

#invigilateMax {
    width: 488px;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

#exportRecord {
    width: 858px;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    .dialog-title {
        position: relative;

        span {
            display: block;
            width: 30px;
            height: 30px;
            background: url('../images/ungrant-icon.png') no-repeat center;
            background-size: 26px;
            position: absolute;
            right: 14px;
            top: 14px;
            cursor: pointer;
        }
    }

    .dialog-con {
        margin: 30px;

        .download {
            color: #4D88FF;
            margin-right: 10px;
            cursor: pointer;
        }

        .delete {
            color: #f14848;
            cursor: pointer;
        }

        .tab-mes {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 40px;
            line-height: 40px;

            .total {
                color: #86909C;
            }

            .refresh {
                color: #4D88FF;
                background: url('../images/fresh.png') no-repeat left center;
                background-size: 16px;
                padding-left: 20px;
                cursor: pointer;
            }
        }
    }
}
