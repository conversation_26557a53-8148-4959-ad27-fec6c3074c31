.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
.hide {
  display: none !important;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #999999;
  font-size: 0.3rem;
}
.hide {
  display: none;
}
.school-diag .window {
  width: 840px;
  height: 786px;
}
.school-diag .window .p-content {
  padding: 0;
}
.school-diag .window .p-content .st-top {
  padding: 30px 0 30px 0;
  text-align: center;
  position: relative;
}
.school-diag .window .p-content .st-top p {
  font-size: 14px;
  color: #474C59;
  line-height: 20px;
}
.school-diag .window .p-content .st-top .btns {
  position: absolute;
  cursor: pointer;
  top: 26px;
  right: 40px;
  line-height: 20px;
  font-size: 14px;
  color: #6B89B3;
  padding-left: 22px;
  background: url(../../images/cultivation/export-icon.png) no-repeat left center;
  z-index: 999;
}
.school-diag .window .p-content .st-top .print {
  position: absolute;
  cursor: pointer;
  top: 26px;
  right: 120px;
  line-height: 20px;
  font-size: 14px;
  color: #6B89B3;
  padding-left: 22px;
  background: url(../../images/cultivation/print-icon.png) no-repeat left center;
  z-index: 999;
}
.school-diag .window .p-content .st-scroll {
  width: 100%;
  height: 650px;
}
.school-diag .window .p-content .st-scroll .print-box {
  margin-bottom: 40px;
}
.school-diag .window .p-content h2 {
  font-weight: 500;
  font-size: 24px;
  line-height: 34px;
  color: #131B26;
  text-align: center;
  margin-bottom: 30px;
  margin-top: 44px;
}
.school-diag .window .p-content .sa-table {
  overflow: hidden;
  width: 760px;
  margin: 0 auto;
}
.school-diag .window .p-content .sa-table table {
  table-layout: fixed;
  border: 1px solid #ACB4BF;
}
.school-diag .window .p-content .sa-table table thead tr {
  height: 40px;
  background: #F2F4F7;
}
.school-diag .window .p-content .sa-table table thead tr th {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #131B26;
  border-right: 1px solid #ACB4BF;
  border-bottom: 1px solid #ACB4BF;
}
.school-diag .window .p-content .sa-table table thead tr th:first-child {
  width: 100px;
  border-bottom: 1px solid #ACB4BF;
  border-radius: 0;
  color: #ACB4BF;
}
.school-diag .window .p-content .sa-table table thead tr th:last-child {
  border-right: none;
  border-radius: 0;
}
.school-diag .window .p-content .sa-table table tbody tr:last-child td {
  background-color: #fff;
}
.school-diag .window .p-content .sa-table table tbody tr:last-child td:first-child {
  border-radius: 0;
  border-bottom: none;
  border-right: 1px solid #ACB4BF;
}
.school-diag .window .p-content .sa-table table tbody tr:last-child td:last-child {
  border-radius: 0;
}
.school-diag .window .p-content .sa-table table tbody tr td:first-child {
  width: 100px;
  background: #F2F4F7;
  border-right: 1px solid #ACB4BF;
  border-bottom: 1px solid #ACB4BF;
}
.school-diag .window .p-content .sa-table table tbody tr td:first-child .period {
  padding: 0;
  width: auto;
  margin: 0 auto;
  font-size: 14px;
  font-weight: 400;
  color: #131B26;
  text-align: center;
  position: relative;
}
.school-diag .window .p-content .sa-table table tbody tr td {
  border-right: 1px solid #ACB4BF;
  border-bottom: 1px solid #ACB4BF;
  padding-top: 1px;
  height: 60px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.school-diag .window .p-content .sa-table table tbody tr td .select-area {
  background: transparent;
  padding: 10px 10px 0;
  width: 100%;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  text-align: left;
}
.school-diag .window .p-content .sa-table table tbody tr td .select-area.nodata {
  background: transparent;
}
.school-diag .window .p-content .sa-table table tbody tr td .select-area .state {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  border-radius: 2px;
  background: #F1F3F5 url(../image/frema-icon.png) no-repeat center;
  display: none;
}
.school-diag .window .p-content .sa-table table tbody tr td .select-area .sel-week {
  display: none;
  position: absolute;
  top: 8px;
  right: 7px;
  width: 16px;
  height: 16px;
  background: url(../image/kz-icon.png) no-repeat center;
  background-size: 16px;
}
.school-diag .window .p-content .sa-table table tbody tr td .select-area .tag {
  display: none;
  position: absolute;
  top: 8px;
  right: 5px;
  padding: 0 4px;
  height: 16px;
  line-height: 16px;
  font-size: 10px;
  color: #FF6B6B;
  background: rgba(255, 107, 107, 0.2);
  border-radius: 4px;
}
.school-diag .window .p-content .sa-table table tbody tr td .select-area .tag em {
  display: block;
  transform: scale(0.9);
  -webkit-transform: scale(0.9);
}
.school-diag .window .p-content .sa-table table tbody tr td .select-area .class,
.school-diag .window .p-content .sa-table table tbody tr td .select-area .teacher {
  font-size: 12px;
  font-weight: 400;
  color: rgba(25, 59, 104, 0.8);
}
.school-diag .window .p-content .sa-table table tbody tr td .select-area .course {
  font-size: 14px;
  line-height: 20px;
  color: #131B26;
  margin-bottom: 2px;
}
.school-diag .window .p-content .sa-table table tbody tr td .select-area .weeks {
  line-height: 17px;
  font-size: 12px;
  color: #8A8B99;
  margin-bottom: 2px;
}
.school-diag .window .p-content .sa-table table tbody tr td .select-area .address {
  font-size: 12px;
  color: #8A8B99;
  line-height: 17px;
}
.school-diag .window .p-content .sa-table table tbody tr td .select-area .weeks,
.school-diag .window .p-content .sa-table table tbody tr td .select-area .address {
  width: 100%;
}
.school-diag .window .p-content .sa-table table tbody.operable tr td.select .select-area.current {
  background: #616EE6 !important;
  border: 2px solid #616EE6 !important;
}
.school-diag .window .p-content .sa-table table tbody.operable tr td .select-area {
  cursor: pointer;
}
.school-diag .window .p-content .sa-table table tbody.operable tr td .select-area .state {
  display: block;
}
.school-diag .window .p-content .sa-table table tbody.operable tr td .select-area .sel-week {
  display: block;
}
.school-diag .window .p-content .sa-table table tbody.operable tr td .select-area .tag {
  display: block;
}
.school-diag .window .p-content .sa-table table tbody.operable tr td .select-area:hover {
  border: 2px solid #616EE6;
}
.school-diag .window .p-content .sa-table table tbody.operable tr td .select-area.nodata {
  cursor: pointer;
}
.school-diag .window .p-content .sa-table table tbody.operable tr td .select-area.nodata:hover {
  border: 2px solid #616EE6;
}






@media print {
  .Noprint {
    display: none;
  }
  .st-scroll {
    width: 100%;
    height: auto;
    overflow: visible;
    position: absolute;
    left: 0;
    top: 0;
    background-color: red;
  }
  .st-scroll .print-box {
    margin-bottom: 40px;
    min-height:980px;
  }
  .st-scroll h2 {
    font-weight: 500;
    font-size: 24px;
    line-height: 34px;
    color: #131B26;
    text-align: center;
    margin-bottom: 30px;
  }
  .st-scroll .sa-table {
    overflow: hidden;
    width: 760px;
    margin: 0 auto;
  }
  .st-scroll .sa-table table {
    table-layout: fixed;
    border: 1px solid #ACB4BF;
  }
  .st-scroll .sa-table table thead tr {
    height: 40px;
    background: #F2F4F7;
  }
  .st-scroll .sa-table table thead tr th {
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    color: #131B26;
    border-right: 1px solid #ACB4BF;
    border-bottom: 1px solid #ACB4BF;
  }
  .st-scroll .sa-table table thead tr th:first-child {
    width: 100px;
    border-bottom: 1px solid #ACB4BF;
    border-radius: 0;
    color: #ACB4BF;
  }
  .st-scroll .sa-table table thead tr th:last-child {
    border-right: none;
    border-radius: 0;
  }
  .st-scroll .sa-table table tbody tr:last-child td {
    background-color: #fff;
  }
  .st-scroll .sa-table table tbody tr:last-child td:first-child {
    border-radius: 0;
    border-bottom: none;
    border-right: 1px solid #ACB4BF;
  }
  .st-scroll .sa-table table tbody tr:last-child td:last-child {
    border-radius: 0;
  }
  .st-scroll .sa-table table tbody tr td:first-child {
    width: 100px;
    background: #F2F4F7;
    border-right: 1px solid #ACB4BF;
    border-bottom: 1px solid #ACB4BF;
  }
  .st-scroll .sa-table table tbody tr td:first-child .period {
    padding: 0;
    width: auto;
    margin: 0 auto;
    font-size: 14px;
    font-weight: 400;
    color: #131B26;
    text-align: center;
    position: relative;
  }
  .st-scroll .sa-table table tbody tr td {
    border-right: 1px solid #ACB4BF;
    border-bottom: 1px solid #ACB4BF;
    padding-top: 1px;
    height: 60px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
  }
  .st-scroll .sa-table table tbody tr td .select-area {
    background: transparent;
    padding: 10px 10px 0;
    width: 100%;
    height: 100%;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
  }
  .st-scroll .sa-table table tbody tr td .select-area.nodata {
    background: transparent;
  }
  .st-scroll .sa-table table tbody tr td .select-area .state {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 20px;
    height: 20px;
    border-radius: 2px;
    background: #F1F3F5 url(../image/frema-icon.png) no-repeat center;
    display: none;
  }
  .st-scroll .sa-table table tbody tr td .select-area .sel-week {
    display: none;
    position: absolute;
    top: 8px;
    right: 7px;
    width: 16px;
    height: 16px;
    background: url(../image/kz-icon.png) no-repeat center;
    background-size: 16px;
  }
  .st-scroll .sa-table table tbody tr td .select-area .tag {
    display: none;
    position: absolute;
    top: 8px;
    right: 5px;
    padding: 0 4px;
    height: 16px;
    line-height: 16px;
    font-size: 10px;
    color: #FF6B6B;
    background: rgba(255, 107, 107, 0.2);
    border-radius: 4px;
  }
  .st-scroll .sa-table table tbody tr td .select-area .tag em {
    display: block;
    transform: scale(0.9);
    -webkit-transform: scale(0.9);
  }
  .st-scroll .sa-table table tbody tr td .select-area .class,
  .st-scroll .sa-table table tbody tr td .select-area .teacher {
    font-size: 12px;
    font-weight: 400;
    color: rgba(25, 59, 104, 0.8);
  }
  .st-scroll .sa-table table tbody tr td .select-area .course {
    font-size: 14px;
    line-height: 20px;
    color: #131B26;
    margin-bottom: 2px;
  }
  .st-scroll .sa-table table tbody tr td .select-area .weeks {
    line-height: 17px;
    font-size: 12px;
    color: #8A8B99;
    margin-bottom: 2px;
  }
  .st-scroll .sa-table table tbody tr td .select-area .address {
    font-size: 12px;
    color: #8A8B99;
    line-height: 17px;
  }
  .st-scroll .sa-table table tbody tr td .select-area .weeks,
  .st-scroll .sa-table table tbody tr td .select-area .address {
    width: 100%;
  }
  .st-scroll .sa-table table tbody.operable tr td.select .select-area.current {
    background: #616EE6 !important;
    border: 2px solid #616EE6 !important;
  }
  .st-scroll .sa-table table tbody.operable tr td .select-area {
    cursor: pointer;
  }
  .st-scroll .sa-table table tbody.operable tr td .select-area .state {
    display: block;
  }
  .st-scroll .sa-table table tbody.operable tr td .select-area .sel-week {
    display: block;
  }
  .st-scroll .sa-table table tbody.operable tr td .select-area .tag {
    display: block;
  }
  .st-scroll .sa-table table tbody.operable tr td .select-area:hover {
    border: 2px solid #616EE6;
  }
  .st-scroll .sa-table table tbody.operable tr td .select-area.nodata {
    cursor: pointer;
  }
  .st-scroll .sa-table table tbody.operable tr td .select-area.nodata:hover {
    border: 2px solid #616EE6;
  }
}
@media print {
  .Noprint {
    display: none;
  }

  .pageBreak {
    page-break-after: always;
  }
}

