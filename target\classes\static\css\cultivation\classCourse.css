.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
body {
  background-color: #f7f8fa;
}
.j-search-con {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  cursor: pointer;
}
.j-search-con .j-select-year {
  left: 0;
}
.j-search-con input {
  width: 100%;
  height: 34px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  padding: 0 20px 0 10px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  cursor: pointer;
}
.j-search-con input::placeholder {
  color: #86909c;
}
.j-search-con .j-arrow {
  width: 10px;
  height: 10px;
  background: url(../../images/cultivation/down-icon.png) no-repeat center;
  position: absolute;
  right: 12px;
  top: 12px;
}
.j-search-con .j-arrow.j-arrow-slide {
  transform: rotate(180deg);
}
.j-search-con .j-select-year {
  position: absolute;
  top: 40px;
  left: -1px;
  z-index: 999;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  display: none;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}
.j-search-con .j-select-year.slideShow {
  display: block;
}
.j-search-con .j-select-year .search {
  height: 36px;
  background: #f5f7fa;
  border-radius: 18px;
  margin: 11px 10px;
}
.j-search-con .j-select-year .search input {
  border: none;
  width: 84%;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  float: left;
}
.j-search-con .j-select-year .search span {
  cursor: pointer;
  float: left;
  width: 16px;
  height: 16px;
  background: url(../../images/cultivation/search-icon.png) no-repeat center;
  margin-top: 10px;
}
.j-search-con .j-select-year .all-selects {
  line-height: 17px;
  margin-bottom: 4px;
  height: 17px;
  padding: 0 14px;
  font-size: 12px;
  color: #6b89b3;
  cursor: pointer;
  user-select: none;
}
.j-search-con .j-select-year ul {
  overflow: hidden;
  max-height: 200px;
  overflow-y: auto;
}
.j-search-con .j-select-year ul li {
  line-height: 40px;
  text-align: left;
  text-indent: 16px;
  cursor: pointer;
  font-size: 14px;
  color: #4e5969;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-right: 30px;
  background-color: #ffffff;
  background-image: url("../../images/cultivation/check-icon.png");
  background-repeat: no-repeat;
  background-position: 96% center;
}
.j-search-con .j-select-year ul li:hover {
  background-color: #e1ebff;
  color: #4d88ff;
  font-weight: 500;
}
.j-search-con .j-select-year ul li.active {
  background-color: #e1ebff;
  background-image: url("../../images/cultivation/check-cur.png");
  color: #4d88ff;
  font-weight: 500;
}
.j-search-con.single-box .j-select-year ul li {
  background-image: url("../../images/cultivation/r-icon.png");
}
.j-search-con.single-box .j-select-year ul li.active {
  background-image: url("../../images/cultivation/radio-cur-icon.png");
}
.main {
  margin: 20px 20px 10px 20px;
  background-color: #fff;
  border-radius: 8px;
  min-width: 1000px;
  overflow: hidden;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top {
  width: 100%;
  height: 57px;
  border-bottom: 1px solid #e8eaf1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: border-box;
}
.main .m-top .back {
  cursor: pointer;
  margin-right: 16px;
  padding-left: 22px;
  background: url(../../images/cultivation/back.png) no-repeat left center;
  background-size: 16px;
  color: #7d92b2;
  font-size: 14px;
}
.main .m-top .title {
  font-size: 16px;
  color: #1d2129;
  padding-left: 6px;
  position: relative;
}
.main .m-top .title::after {
  content: "";
  width: 3px;
  height: 16px;
  border-radius: 2px;
  background-color: #4d88ff;
  position: absolute;
  left: 0;
  top: 3px;
}
.main .m-top span {
  width: 12px;
  height: 12px;
  background: url(../../images/cultivation/icon-right.png) no-repeat center;
  background-size: 12px;
  margin: 0 4px;
}
.main .m-top h3 {
  font-size: 16px;
  color: #1d2129;
}
.main .m-top .btns {
  flex: 1;
}
.main .m-top .btns span {
  float: right;
  width: 86px;
  height: 34px;
  text-align: center;
  line-height: 34px;
  border-radius: 4px;
  background: #4d88ff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  cursor: pointer;
  color: #ffffff;
  margin-right: 30px;
  display: block;
}
.main .form1 {
  margin: 20px 0 40px 30px;
}
.main .form1 .layui-form-label {
  color: #1d2129;
  width: 56px;
}
.main .form1 .layui-form-select {
  width: 240px;
}
.main .form1 .layui-form-select .layui-edge {
  background: url(../../images/cultivation/down-icon.png) no-repeat center;
  border: none;
  width: 10px;
  height: 10px;
  margin-top: -5px;
}
.main .item {
  margin: 0 30px;
}
.main .item .i-top {
  width: 100%;
  height: 22px;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}
.main .item .i-top span {
  padding-left: 8px;
  font-size: 16px;
  line-height: 22px;
  color: #6581ba;
  position: relative;
  display: block;
}
.main .item .i-top span:after {
  content: "";
  position: absolute;
  left: 0;
  top: 4px;
  background: #6581ba;
  border-radius: 1px;
  width: 3px;
  height: 14px;
}
.main .item .i-top .i-submit {
  width: 96px;
  height: 34px;
  background-color: #4d88ff;
  border-radius: 4px;
  border: 1px solid #4d88ff;
  color: #ffffff;
  font-size: 14px;
  overflow: hidden;
  cursor: pointer;
}
.main .item .form-con {
  display: flex;
  font-size: 14px;
  padding: 20px 0 4px;
}
.main .item .form-con .form-btn {
  width: 166px;
  position: relative;
}
.main .item .form-con .form-btn::after {
  content: "";
  width: 1px;
  height: 84px;
  background: #e8ebf1;
  position: absolute;
  left: 0px;
  top: 0px;
}
.main .item .form-con .form-btn .btn {
  width: 86px;
  height: 34px;
  background: #ffffff;
  border-radius: 4px;
  margin: 0 auto;
  display: block;
  cursor: pointer;
}
.main .item .form-con .form-btn .btn.btn-search {
  background: #4d88ff;
  border: 1px solid #4d88ff;
  color: #ffffff;
}
.main .item .form-con .form-btn .btn.btn-reset {
  border: 1px solid #4d88ff;
  color: #4d88ff;
  margin-bottom: 16px;
}
.main .item .form-con .form-btn.form-btn-course {
  display: flex;
}
.main .item .form-con .form-btn.form-btn-course::after {
  content: "";
  background: none;
}
.main .item .form-con .form-btn.form-btn-course .btn-reset {
  margin-right: 16px;
}
.main .item .form-con .layui-form {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}
.main .item .form-con .layui-form .layui-form-item {
  display: flex;
  flex-wrap: wrap;
}
.main .item .form-con .layui-form .layui-inline {
  margin-bottom: 16px;
  margin-right: 2%;
  width: 31%;
  display: flex;
}
.main .item .form-con .layui-form .layui-input-inline {
  width: unset;
  min-width: 190px;
  flex: 1;
}
.main .item .form-con .layui-form .layui-form-label {
  color: #1d2129;
  padding: 7px 14px 7px 0;
  width: 70px;
  text-align: left;
}
.main .item .form-con .layui-form .layui-input {
  height: 34px;
  border-radius: 4px;
  border-color: #e5e6eb;
}
.main .item .form-con .layui-form .layui-input::placeholder {
  color: #86909c;
}
.main .item .form-con .layui-form .layui-form-select dl {
  border: none;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.main .item .form-con .layui-form .layui-form-select dl dd {
  color: #4e5969;
}
.main .item .form-con .layui-form .layui-form-select dl dd.layui-this {
  color: #ffffff;
}
.main .item .form-con .layui-form .layui-form-select dl dd.layui-this.layui-select-tips {
  color: #ffffff;
}
.main .item .form-con .layui-form .layui-form-select .layui-edge {
  background: url(../../images/cultivation/down-icon.png) no-repeat center;
  border: none;
  width: 10px;
  height: 10px;
  margin-top: -5px;
}
.main .item .form-con .layui-form.layui-form-course {
  flex: 1;
}
.main .item .form-con .layui-form.layui-form-course .layui-inline {
  width: 23%;
}
.main .item .form-con .layui-form.layui-form-course .layui-form-label {
  width: 56px;
}
.main .item .form-con .layui-form.layui-form-course .layui-input-inline {
  width: unset;
  min-width: unset;
  flex: 1;
}
.main1 {
  margin: 0 20px 20px;
  background-color: #fff;
  border-radius: 8px;
  min-width: 1000px;
  overflow: hidden;
  padding: 0 30px;
  box-sizing: border-box;
}
.main1 .title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
}
.main1 .title h1 {
  font-size: 16px;
}
.main1 .title h1 span {
  color: #4d88ff;
}
.main1 .title .clear {
  cursor: pointer;
  width: 86px;
  height: 34px;
  text-align: center;
  line-height: 32px;
  box-sizing: border-box;
  border-radius: 4px;
  border: 1px solid #4d88ff;
  background: #fff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  font-size: 14px;
  color: #4d88ff;
}
.main1 .delet {
  color: #f76560;
}
.course-table {
  overflow: hidden;
  margin-bottom: 24px;
}