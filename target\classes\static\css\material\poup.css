.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.popup {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(16, 26, 41, 0.76);
  width: 100%;
  height: 100%;
  z-index: 100;
}
.popup .window {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  background: #FFFFFF;
  border-radius: 10px;
  width: 840px;
  height: 600px;
  padding-bottom: 70px;
}
.popup .window .p-title {
  line-height: 60px;
  border-bottom: 1px solid #F2F2F2;
  height: 60px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popup .window .p-title span {
  font-size: 18px;
  font-weight: 500;
  color: #131B26;
  margin-left: 30px;
}
.popup .window .p-title .close {
  width: 18px;
  height: 18px;
  float: right;
  margin-top: 21px;
  margin-right: 30px;
  cursor: pointer;
}
.popup .window .p-content {
  padding: 30px;
  width: 100%;
  height: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popup .window .p-btns {
  width: 100%;
  position: absolute;
  bottom: 0;
  height: 70px;
  border-top: 1px solid #F2F2F2;
  text-align: right;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popup .window .p-btns .btn {
  display: inline-block;
  width: 92px;
  height: 36px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #3A8BFF;
  text-align: center;
  border: 1px solid #8CBCFF;
  box-sizing: border-box;
  border-radius: 20px;
  line-height: 34px;
  margin-right: 30px;
  margin-top: 17px;
  cursor: pointer;
}
.popup .window .p-btns .sure {
  background: #3A8BFF;
  border-radius: 50px;
  color: #FFFFFF;
  margin-right: 30px;
  font-size: 14px;
  border: none;
}
.date-edit .window {
  width: auto;
  height: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.date-edit .window .p-content {
  padding: 30px 60px;
}
.date-edit .window .p-content .table table tbody td span {
  padding-right: 0;
  margin: 0 8px;
}
.date-edit .window .p-content .total {
  line-height: 20px;
  font-size: 14px;
  color: #86909C;
  margin-bottom: 20px;
}
.date-edit .window .p-content .total i {
  color: #4D88FF;
  display: inline-block;
  margin: 0 5px;
}
.date-edit .window .p-content .table {
  margin: 0;
}
.date-edit .table {
  width: 714px;
}
.date-edit .layui-table-view .layui-table td,
.date-edit .layui-table-view .layui-table th {
  height: 36px;
}
.date-edit .layui-table tbody tr:hover,
.date-edit .layui-table-hover {
  background-color: #edf2fd !important;
}
.date-edit .layui-table-view .layui-table {
  width: 712px !important;
}
.date-edit .layui-table-cell {
  padding: 0 5px;
}
.date-edit .layui-table,
.date-edit .layui-table-view {
  margin: 0;
}
.date-edit .layui-table td,
.date-edit .layui-table th,
.date-edit .layui-table-col-set,
.date-edit .layui-table-fixed-r,
.date-edit .layui-table-grid-down,
.date-edit .layui-table-header,
.date-edit .layui-table-page,
.date-edit .layui-table-tips-main,
.date-edit .layui-table-tool,
.date-edit .layui-table-total,
.date-edit .layui-table-view,
.date-edit .layui-table[lay-skin=line],
.date-edit .layui-table[lay-skin=row] {
  border-color: #E8EBF3;
}
.date-edit .layui-table tbody tr:hover,
.date-edit .layui-table thead tr,
.date-edit .layui-table-click,
.date-edit .layui-table-header,
.date-edit .layui-table-hover,
.date-edit .layui-table-mend,
.date-edit .layui-table-patch,
.date-edit .layui-table-tool,
.date-edit .layui-table-total,
.date-edit .layui-table-total tr,
.date-edit .layui-table[lay-even] tr:nth-child(even) {
  background: #F1F3F6;
}
.date-edit .layui-table-cell {
  font-size: 14px;
  color: #86909C;
  font-weight: normal;
  height: 18px;
  line-height: 18px;
}
.date-edit .layui-table tr:nth-child(2n) {
  background: #FAFBFC;
}
.date-edit .layui-table td {
  font-size: 14px;
  color: #4E5969;
}
.date-edit .layui-table-cell .download {
  font-size: 14px;
  color: #4080FF;
  cursor: pointer;
}
.date-edit .layui-table-cell .delete {
  color: #F76560;
  font-size: 14px;
  cursor: pointer;
}
.date-edit .layui-table tr {
  height: 36px;
}
.date-edit .table table tr {
  height: 36px;
}
.no-data {
  width: 100%;
  height: 145px;
  background: url(../image/no-data.png) no-repeat center;
}
