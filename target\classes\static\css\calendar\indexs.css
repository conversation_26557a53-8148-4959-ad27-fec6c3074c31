.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.transforms {
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}

textarea::-webkit-input-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

textarea:-moz-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

input[type=search]::-webkit-search-cancel-button {
    -webkit-appearance: none;
}

textarea:-ms-input-placeholder {
    font-size: 12px;
}

.clearfixs {
    zoom: 1;
}

.clearfixs:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.hide {
    display: none !important;
}

input::-webkit-input-placeholder {
    color: #86909C;
    font-size: 14px;
    font-weight: 300;
}

body {
    background-color: #F7F8FA;
}

.select-input {
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    cursor: pointer;
    width: 221px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    margin-right: 24px;
    height: 34px;
}

.select-input.w78 {
    width: 78px !important;
}

.select-input em {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 10px;
    height: 10px;
    background: url(../../images/calendar/icon-arrow1.png) no-repeat center;
    background-size: 10px;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}

.select-input .name {
    font-size: 14px;
    color: #86909C;
    padding-left: 10px;
    width: 100%;
    height: 32px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    line-height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.select-input .name.ckd {
    color: #1D2129;
}

.select-input.clicked em {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    background: url(../../images/calendar/icon-arrow1.png) no-repeat center;
    background-size: 10px;
}

.select-input.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
}

.select-input .select-dropdown {
    width: inherit;
    max-height: 132px;
    overflow: auto;
    left: -1px;
    margin: 5px 0;
    padding: 6px 0;
    background-color: #fff;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
    border-radius: 8px;
    position: absolute;
    transform: translate(0, -50px);
    -webkit-transform: translate(0, -50px);
    z-index: 900;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
    z-index: -1;
}

.select-input .select-dropdown .dropdown-list li {
    margin: 0;
    line-height: normal;
    padding: 0 16px;
    clear: both;
    color: #131B26;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
}

.select-input .select-dropdown .dropdown-list li:hover {
    background: #E1EBFF;
}

.select-input .select-dropdown .dropdown-list li.cur {
    color: #4D88FF;
}

.main {
    background-color: #fff;
    border-radius: 8px;
    min-height: calc(100vh);
    min-width: 1300px;
}

.main .j-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    border-bottom: 1px solid #E8EBF1;
    position: relative;
}

.main .j-title p {
    position: absolute;
    left: 119px;
    top: 0;
    height: 60px;
    line-height: 60px;
    color: #C9CDD4;
    font-weight: normal;
}

.main .j-title h4 {
    position: relative;
    font-size: 16px;
    color: #6581BA;
    margin-left: 30px;
    padding-left: 9px;
    font-weight: 400;
}

.main .j-title h4::after {
    content: "";
    width: 3px;
    height: 16px;
    background: #4C85FA;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 4px;
}

.main .j-title .j-btns {
    margin-right: 30px;
}

.main .j-title .j-btns button {
    width: 92px;
    height: 36px;
    font-size: 14px;
    border-radius: 6px;
    outline: none;
    cursor: pointer;
}

.main .j-title .j-btns button.btn-cancel {
    border: 1px solid #4C88FF;
    box-shadow: 0px 0px 8px rgba(39, 111, 255, 0.31);
    color: #4C88FF;
    background-color: #fff;
    margin-right: 14px;
}

.main .j-title .j-btns button.btn-complate {
    background: #4C88FF;
    box-shadow: 0px 0px 8px rgba(39, 111, 255, 0.31);
    border: 1px solid #4C88FF;
    color: #fff;
}

.main .cons {
    padding: 20px 30px;
}

.main .cons .c-top {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 20px;
}

.main .cons .c-top .generate {
    background: #FFFFFF;
    border: 1px solid #4D88FF;
    box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
    border-radius: 6px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    width: 92px;
    height: 30px;
    text-align: center;
    line-height: 28px;
    font-size: 14px;
    color: #4D88FF;
    cursor: pointer;
    margin-right: 24px;
}

.main .cons .c-top .export {
    width: 92px;
    height: 30px;
    background: #4D88FF;
    box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
    border-radius: 6px;
    font-size: 14px;
    color: #FFFFFF;
    cursor: pointer;
    text-align: center;
    line-height: 28px;
}

.main .cons p {
    font-size: 14px;
    line-height: 20px;
    color: #4E5969;
}

.main .cons .j-table {
    border: 1px solid #E8EBF3;
    margin-bottom: 10px;
}

.main .cons .j-table .j-head {
    background: #F1F3F6;
    border-bottom: 1px solid #E8EBF3;
    height: 36px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.main .cons .j-table .j-head .j-th {
    border-right: 1px solid #E8EBF1;
    text-align: center;
    line-height: 35px;
    font-weight: 400;
    font-size: 14px;
    color: #86909C;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.main .cons .j-table .j-head .j-th .time {
    width: 100%;
    height: 35px;
    position: relative;
}

.main .cons .j-table .j-head .j-th .time:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    height: 77px;
    background-color: #E8EBF1;
    transform-origin: 0 0;
    -webkit-transform-origin: 0 0;
    transform: rotate(-62deg);
    -webkit-transform: rotate(-62deg);
}

.main .cons .j-table .j-head .j-th .time:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    height: 143px;
    background-color: #E8EBF1;
    transform-origin: 0 0;
    -webkit-transform-origin: 0 0;
    transform: rotate(-78deg);
    -webkit-transform: rotate(-78deg);
}

.main .cons .j-table .j-head .j-th .time .ym {
    position: absolute;
    left: 5px;
    top: 16px;
    line-height: 20px;
}

.main .cons .j-table .j-head .j-th .time .data {
    position: absolute;
    left: 71px;
    top: 16px;
    line-height: 20px;
}

.main .cons .j-table .j-head .j-th .time .week {
    position: absolute;
    left: 106px;
    top: 0px;
    line-height: 20px;
}

.main .cons .j-table .j-head .j-th:last-child {
    border-right: none;
}

.main .cons .j-table .j-head .j-th.th-sort {
    width: 80px;
    flex-shrink: 0;
}

.main .cons .j-table .j-head .j-th.th-time {
    width: 140px;
    flex-shrink: 0;
}

.main .cons .j-table .j-head .j-th.th-week {
    width: 100px;
    flex-shrink: 0;
}

.main .cons .j-table .j-head .j-th.th-remarks {
    flex: 30;
    min-width: 100px;
}

.main .cons .j-table .j-head .j-th.th-struc {
    flex: 37;
    min-width: 170px;
}

.main .cons .j-table .j-head .j-th.col {
    color: #4080FF;
}

.main .cons .j-table .j-body {
    color: #4E5969;
    font-size: 14px;
}

.main .cons .j-table .j-body .j-tr {
    background: #FFFFFF;
    border-bottom: 1px solid #E8EBF1;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.main .cons .j-table .j-body .j-tr:last-child {
    border-bottom: none;
}

.main .cons .j-table .j-body .j-tr .j-td {
    border-right: 1px solid #E8EBF1;
    height: 100%;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.main .cons .j-table .j-body .j-tr .j-td:last-child {
    border-right: none;
}

.main .cons .j-table .j-body .j-tr .j-td ul li {
    width: 100%;
    height: 36px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    border-bottom: 1px solid #E8EBF1;
    text-align: center;
    line-height: 35px;
    font-size: 14px;
    color: #4E5969;
}

.main .cons .j-table .j-body .j-tr .j-td ul li:last-child {
    border-bottom: none;
}

.main .cons .j-table .j-body .j-tr .j-td ul li:nth-child(2n) {
    background: #FAFBFC;
}

.main .cons .j-table .j-body .j-tr .j-td.td-sort {
    width: 80px;
    flex-shrink: 0;
}

.main .cons .j-table .j-body .j-tr .j-td.td-time {
    width: 139px;
    flex-shrink: 0;
    text-align: center;
    border-right: none;
}

.main .cons .j-table .j-body .j-tr .j-td.td-table {
    width: 701px;
}

.main .cons .j-table .j-body .j-tr .j-td.td-remarks {
    flex: 30;
    min-width: 100px;
    border-right: 0;
    overflow: hidden;
    cursor: pointer;
    position: relative;
}

.main .cons .j-table .j-body .j-tr .j-td.td-remarks .marks {
    width: 300px;
    display: flex;
    display: -webkit-flex;
    align-items: flex;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding: 10px;
    padding-left: 30px;
    justify-content: center;
    flex-flow: column;
    overflow: hidden;
}

.main .cons .j-table .j-body .j-tr .j-td.td-struc {
    flex: 37;
    min-width: 170px;
    border-left: 1px solid #E8EBF1;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.main .cons .j-table .j-body .j-tr .j-td.col {
    color: #4080FF;
}

.main .cons .j-table .j-body .j-tr .j-td .m-table {
    width: 100%;
    height: auto;
    border-collapse: collapse;
    border-left: 1px solid #E8EBF1;
}

.main .cons .j-table .j-body .j-tr .j-td .m-table tbody tr {
    height: 36px;
    border-bottom: 1px solid #E8EBF1;
}

.main .cons .j-table .j-body .j-tr .j-td .m-table tbody tr:last-child {
    border-bottom: none;
}

.main .cons .j-table .j-body .j-tr .j-td .m-table tbody tr td {
    width: 100px;
    height: 36px;
    text-align: center;
    border-right: 1px solid #E8EBF1;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    position: relative;
}

.main .cons .j-table .j-body .j-tr .j-td .m-table tbody tr td span {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
    cursor: pointer;
}

.main .cons .j-table .j-body .j-tr .j-td .m-table tbody tr td span em {
    width: 100%;
    line-height: 14px;
    font-style: normal;
}

.main .cons .j-table .j-body .j-tr .j-td .m-table tbody tr td span p {
    line-height: 14px;
    width: 100%;
    font-size: 12px;
    color: #4080FF;
}

.main .cons .j-table .j-body .j-tr .j-td .m-table tbody tr td span:hover {
    box-shadow: 0px 0px 8px rgba(39, 111, 255, 0.25) !important;
}

.main .cons .j-table .j-body .j-tr .j-td .m-table tbody tr td:last-child {
    border-right: none;
}

.main .cons .j-table .j-body .j-tr .j-td .m-table tbody tr:nth-child(2n) {
    background: #FAFBFC;
}

.layui-input {
    border: 1px solid #E5E6EB !important;
    border-radius: 4px !important;
    height: 34px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

input[type="number"] {
    -moz-appearance: textfield;
}

.layui-laydate tr .layui-this {
    background-color: #4C88FF !important;
    color: #fff !important;
}

.layui-laydate-footer span:hover {
    color: #4C88FF !important;
}

.add-schedule .window {
    width: 747px;
    height: auto;
}

.add-schedule .window .p-content {
    padding: 30px 100px 70px 100px;
}

.add-schedule .window .p-content .con .lable {
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin-bottom: 24px;
}

.add-schedule .window .p-content .con .lable .inputs {
    width: 160px;
    margin-right: 14px;
    display: none;
}

.add-schedule .window .p-content .con .lable .inputs input {
    width: 160px;
}

.add-schedule .window .p-content .con .lable .kalamu-area {
    width: 477px;
    height: 120px;
}

.add-schedule .window .p-content .con .lable .names {
    width: 71px;
    height: 34px;
    line-height: 34px;
    font-size: 14px;
    color: #1D2129;
    white-space: nowrap;
}

.add-schedule .window .p-content .con .lable .input {
    width: 476px;
}

.add-schedule .window .p-content .con .lable .input input {
    width: 476px;
}

.add-schedule .window .p-content .con .lable .w476 {
    width: 476px;
    margin-right: 0;
}

.add-schedule .window .p-content .con .lable .switch {
    background: #DADFE6;
    border-radius: 4px;
    width: 28px;
    height: 14px;
    position: relative;
    margin-top: 10px;
    cursor: pointer;
}

.add-schedule .window .p-content .con .lable .switch span {
    position: absolute;
    left: 2px;
    top: 2px;
    display: block;
    width: 12px;
    height: 10px;
    border-radius: 2px;
    background: #ffffff;
    transition: all 200ms linear;
}

.add-schedule .window .p-content .con .lable .switch.active {
    background: #4D88FF;
}

.add-schedule .window .p-content .con .lable .switch.active span {
    left: 14px;
}

.add-schedule .window .p-content .con .lable .tit {
    font-size: 14px;
    line-height: 34px;
    color: #86909C;
    margin-left: 10px;
}

.add-schedule .window .p-content .con .lable .item {
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
}

.add-schedule .window .p-content .con .lable .item:last-child .select-input {
    margin-right: 0;
}

.add-schedule .window .p-content .con .lable .item:last-child .inputs {
    margin-right: 0;
}

.add-schedule .window .p-content .con .lable .item .names {
    width: 71px;
    font-size: 14px;
    color: #1D2129;
    line-height: 34px;
}

.add-schedule .window .p-content .con .lable .item .select-input {
    width: 160px;
    margin-right: 14px;
}

.date-edit .window {
    height: auto;
    width: 1158px;
}

.date-edit .window .p-content {
    padding: 30px 100px 70px 100px;
}

.date-edit .window .p-content .con {
    position: relative;
}

.date-edit .window .p-content .con .all-select {
    position: absolute;
    left: 0;
    bottom: 0px;
    padding-left: 26px;
    background: url(../../images/calendar/radio-icon.png) no-repeat left center;
    font-size: 14px;
    color: #4E5969;
    cursor: pointer;
    line-height: 50px;
}

.date-edit .window .p-content .con .all-select.cur {
    background: url(../../images/calendar/radio-cur-icon.png) no-repeat left center;
}

.date-edit .window .p-content .con .btn-list {
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
    height: 30px;
    margin-bottom: 24px;
}

.date-edit .window .p-content .con .btn-list .btn {
    width: 64px;
    height: 30px;
    background: #FFFFFF;
    border: 1px solid #4D88FF;
    border-radius: 6px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    text-align: center;
    line-height: 28px;
    cursor: pointer;
    font-size: 14px;
    color: #4D88FF;
    margin-right: 24px;
}

.date-edit .window .p-content .con .btn-list .delet {
    width: 64px;
    height: 30px;
    background: #FFFFFF;
    border: 1px solid #4D88FF;
    border-radius: 6px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    text-align: center;
    line-height: 28px;
    cursor: pointer;
    font-size: 14px;
    color: #4D88FF;
    margin-right: 24px;
}

.date-edit .window .p-content .con .btn-list .search {
    width: 64px;
    height: 30px;
    background: #4D88FF;
    border-radius: 6px;
    cursor: pointer;
    text-align: center;
    line-height: 30px;
    font-size: 14px;
    color: #FFFFFF;
}

.date-edit .window .p-content .con .scalp {
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
    height: 34px;
    margin-bottom: 24px;
}

.date-edit .window .p-content .con .scalp .item {
    margin-right: 46px;
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
}

.date-edit .window .p-content .con .scalp .item:last-child {
    margin-right: 0;
}

.date-edit .window .p-content .con .scalp .item .names {
    font-size: 14px;
    color: #1D2129;
    line-height: 34px;
    margin-right: 14px;
    white-space: nowrap;
}

.date-edit .window .p-content .con .scalp .item .select-input {
    width: 100px;
    margin-right: 0;
}

.date-edit .window .p-content .con .scalp .item .input {
    width: 192px;
}

.date-edit .window .p-content .con .scalp .item .input input {
    width: 192px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-left: 10px;
}

.date-edit .window .p-content .con .table {
    width: 100%;
    height: auto;
    margin-bottom: 20px;
}

.date-edit .window .p-content .con .table .no-data {
    width: 100%;
    height: 145px;
    background: url(../../images/calendar/no-data.png) no-repeat center;
}

.create-cal .window {
    height: 244px;
    width: 354px;
}

.create-cal .window .p-content {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 114px;
}

.create-cal .window .p-content p {
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    color: #4E5969;
}

.edit-schedule .window {
    width: 932px;
    height: 642px;
}

.edit-schedule .window .select-input .select-dropdown {
    max-height: 132px;
}

.edit-schedule .window .p-content {
    padding: 30px 100px;
}

.edit-schedule .window .p-content .schedule {
    margin-bottom: 60px;
}

.edit-schedule .window .p-content .schedule h2 {
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    color: #4080FF;
    margin-bottom: 24px;
}

.edit-schedule .window .p-content .schedule .lable {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 24px;
}

.edit-schedule .window .p-content .schedule .lable.wn-lab .name {
    white-space: nowrap;
}

.edit-schedule .window .p-content .schedule .lable .item {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
    margin-right: 50px;
}

.edit-schedule .window .p-content .schedule .lable .item:last-child {
    margin-right: 0;
}

.edit-schedule .window .p-content .schedule .lable .item .names {
    margin-right: 14px;
    line-height: 34px;
    white-space: nowrap;
    font-size: 14px;
    color: #1D2129;
}

.edit-schedule .window .p-content .schedule .lable .item .names.w98 {
    width: 84px;
}

.edit-schedule .window .p-content .schedule .lable .item .select-input {
    width: 100px;
    margin-right: 0;
}

.edit-schedule .window .p-content .schedule .lable .item .select-input.w150 {
    width: 150px;
}

.edit-schedule .window .p-content .schedule .lable .item .select-input.w80 {
    width: 80px;
}

.edit-schedule .window .p-content .schedule .lable .item .tit {
    margin-left: 14px;
    line-height: 34px;
    font-size: 14px;
    color: #1D2129;
    white-space: nowrap;
}

.genschool-calendar .window {
    height: 426px;
    width: 912px;
}

.genschool-calendar .window .p-content {
    padding: 30px 100px;
}

.genschool-calendar .window .p-content .school-calendar .sc-lable {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
}

.genschool-calendar .window .p-content .school-calendar .sc-lable .item {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
}

.genschool-calendar .window .p-content .school-calendar .sc-lable .item .names {
    width: 84px;
    line-height: 34px;
    font-size: 14px;
    color: #1D2129;
}

.genschool-calendar .window .p-content .school-calendar .sc-lable .item:last-child .names {
    width: 98px;
}

.genschool-calendar .window .p-content .school-calendar .bz-con {
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
}

.genschool-calendar .window .p-content .school-calendar .bz-con .name {
    font-size: 14px;
    line-height: 20px;
    color: #1D2129;
    width: 84px;
    text-align: left;
}

.genschool-calendar .window .p-content .school-calendar .bz-con .kalamu-area {
    width: 628px;
}

.tips .window {
    height: 240px;
    width: 354px;
}

.tips .window .p-content {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 170px;
}

.tips .window .p-content p {
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    color: #4E5969;
}

.tips .window .p-btns {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
}

.tips .window .p-btns .btn {
    margin-right: 0;
    margin-top: 0;
}

.edit-notes .window {
    width: 676px;
    height: 368px;
}

.edit-notes .window .p-content {
    padding: 30px 100px;
}

.edit-notes .window .p-content .top {
    width: 100%;
    height: 34px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    margin-bottom: 24px;
}

.edit-notes .window .p-content .top .lable {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    margin-right: 13px;
}

.edit-notes .window .p-content .top .lable .name {
    font-size: 14px;
    color: #1D2129;
    margin-right: 14px;
}

.edit-notes .window .p-content .top .lable .btn {
    width: 150px;
    height: 34px;
    background: #FFFFFF;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-left: 10px;
    line-height: 32px;
    color: #4E5969;
    font-size: 14px;
}

.edit-notes .window .p-content .bz-con {
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
}

.edit-notes .window .p-content .bz-con .name {
    font-size: 14px;
    line-height: 20px;
    color: #1D2129;
    width: 84px;
    text-align: left;
}

.kalamu-area {
    width: 392px;
    height: 120px;
    background: #FFFFFF;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding: 7px 10px;
    min-height: 120px;
    font-size: 14px;
    overflow-y: auto;
    outline: none;
    position: relative;
}

.kalamu-area:empty:before {
    content: attr(placeholder);
    font-size: 14px;
    color: #C9CDD4;
    line-height: normal;
}

.kalamu-area:focus:before {
    content: none;
}

.layui-table,
.layui-table-view {
    margin: 0;
}

.layui-table td,
.layui-table th,
.layui-table-col-set,
.layui-table-fixed-r,
.layui-table-grid-down,
.layui-table-header,
.layui-table-page,
.layui-table-tips-main,
.layui-table-tool,
.layui-table-total,
.layui-table-view,
.layui-table[lay-skin=line],
.layui-table[lay-skin=row] {
    border-color: #E8EBF3;
}

.layui-table tbody tr:hover,
.layui-table thead tr,
.layui-table-click,
.layui-table-header,
.layui-table-hover,
.layui-table-mend,
.layui-table-patch,
.layui-table-tool,
.layui-table-total,
.layui-table-total tr,
.layui-table[lay-even] tr:nth-child(even) {
    background: #F1F3F6;
}

.layui-table-cell {
    font-size: 14px;
    color: #86909C;
    font-weight: normal;
    height: 18px;
    line-height: 18px;
}

.layui-table tr:nth-child(2n) {
    background: #FAFBFC;
}

.layui-table td {
    font-size: 14px;
    color: #4E5969;
}

.layui-table-view .layui-form-checkbox[lay-skin=primary] i {
    width: 16px;
    height: 16px;
}

.layui-form-checked[lay-skin=primary] i {
    border-color: #4D88FF !important;
    background-color: #4D88FF;
    color: #fff;
}

.layui-form-checkbox[lay-skin=primary] {
    min-width: 16px;
    min-height: 16px;
}

.layui-table-cell .edit {
    font-size: 14px;
    color: #4080FF;
    margin: 0 10px;
    cursor: pointer;
}

.layui-table-cell .delete {
    color: #F76560;
    font-size: 14px;
    margin: 0 10px;
    cursor: pointer;
}
