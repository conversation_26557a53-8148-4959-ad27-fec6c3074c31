.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}
.flex {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}
.transforms {
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
    font-size: 14px;
    color: #86909c;
}
textarea:-moz-placeholder {
    font-size: 14px;
    color: #86909c;
}
input[type=search]::-webkit-search-cancel-button {
    -webkit-appearance: none;
}
textarea:-ms-input-placeholder {
    font-size: 14px;
    color: #86909c;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
.clearfixs {
    zoom: 1;
}
.clearfixs:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}
.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
input::-webkit-input-placeholder {
    color: #ACB4BF !important;
    font-size: 14px;
}
.layui-layer {
    border-radius: 10px !important;
}
.popups {
    background: #FFFFFF;
    border-radius: 10px;
}
.popups .title {
    height: 52px;
    line-height: 52px;
    font-size: 16px;
    font-weight: 400;
    padding: 0 24px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #F2F2F2;
}
.popups .title .name {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    color: #1d2129;
    text-align: left;
}
.popups .title .close {
    width: 20px;
    height: 20px;
    background: url(../../images/material/close-icon.png) no-repeat center;
    cursor: pointer;
}
.popups .popup-con {
    padding: 30px;
}
.popups .bottom {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 66px;
    border-top: 1px solid #E5E6EB;
    padding: 0 24px;
}
.popups .bottom div {
    width: 88px;
    height: 34px;
    text-align: center;
    line-height: 32px;
    font-size: 14px;
    border-radius: 18px;
    cursor: pointer;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}
.popups .bottom div.cancle {
    border: 1px solid #C9CDD4;
    color: #4E5969;
    background-color: #fff;
    margin-right: 16px;
}
.popups .bottom div.confirm {
    color: #fff;
    background: #4D88FF;
    box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
    border: 1px solid #4D88FF;
}
#textbookChanges {
    width: 812px;
}
#textbookChanges .illustrate ul {
    padding: 0 30px;
    background-color: #e1ebff;
}
#textbookChanges .illustrate ul li {
    width: 100%;
    height: 50px;
    line-height: 50px;
    font-size: 14px;
    color: #1d2129;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-left: 28px;
    background: url(../../images/material/gloss-icons.png) no-repeat left center;
}
#textbookChanges .popup-con {
    padding: 40px 76px;
}
#textbookChanges .popup-con .filter-options {
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-wrap: wrap;
}
#textbookChanges .popup-con .filter-options .lable {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    margin-right: 24px;
    margin-bottom: 24px;
}
#textbookChanges .popup-con .filter-options .lable:nth-child(2n) {
    margin-right: 0;
}
#textbookChanges .popup-con .filter-options .lable .name {
    width: 70px;
    font-size: 14px;
    height: 34px;
    line-height: 34px;
    color: #1D2129;
}
#textbookChanges .popup-con .filter-options .lable .layui-input {
    width: 240px;
    height: 34px;
    border-radius: 4px;
    border: 1px solid #D4D6D9;
    background-color: #fff;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    color: #86909C;
}
#textbookChanges .popup-con .filter-options .lable .select-input {
    width: 240px;
    height: 34px;
    border-radius: 4px;
    border: 1px solid #D4D6D9;
    background-color: #fff;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    cursor: pointer;
}
#textbookChanges .popup-con .filter-options .lable .select-input em {
    position: absolute;
    top: 11px;
    right: 11px;
    width: 12px;
    height: 12px;
    background: url(../../images/material/arrow-icon.png) no-repeat center;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}
#textbookChanges .popup-con .filter-options .lable .select-input .name {
    font-size: 14px;
    color: #ACB4BF;
    padding-left: 13px;
    line-height: 32px;
    width: 86%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
#textbookChanges .popup-con .filter-options .lable .select-input .name.ckd {
    color: #131B26;
}
#textbookChanges .popup-con .filter-options .lable .select-input.clicked em {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
}
#textbookChanges .popup-con .filter-options .lable .select-input.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
}
#textbookChanges .popup-con .filter-options .lable .select-input .select-dropdown {
    width: 100%;
    left: -1px;
    margin: 5px 0;
    padding: 6px 0;
    background-color: #fff;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
    border-radius: 8px;
    position: absolute;
    transform: translate(0, -50px);
    -webkit-transform: translate(0, -50px);
    z-index: 900;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
    z-index: -1;
    max-height: 200px;
    overflow-y: auto;
}
#textbookChanges .popup-con .filter-options .lable .select-input .select-dropdown .dropdown-lists li {
    margin: 0;
    line-height: normal;
    padding: 0 16px;
    clear: both;
    color: #4E5969;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
}
#textbookChanges .popup-con .filter-options .lable .select-input .select-dropdown .dropdown-lists li.cur {
    background: #E1EBFF;
    color: #4d88ff;
}
#textbookChanges .popup-con .change-remarks {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 34px;
    margin-bottom: 14px;
}
#textbookChanges .popup-con .change-remarks .name {
    font-size: 14px;
    color: #1D2129;
    margin-right: 6px;
}
#textbookChanges .popup-con .change-remarks em {
    width: 16px;
    height: 16px;
    background: url(../../images/material/question-circle.png) no-repeat center;
}
.layui-textarea {
    border-radius: 4px;
    border-color: #E5E6EB;
}
#textbookChanges .popup-con .filter-options .lable .select-input .search {
    margin: 8px;
    height: 36px;
    box-sizing: border-box;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    display: flex;
    align-items: center;
}
#textbookChanges .popup-con .filter-options .lable .select-input .search input {
    border: none;
    flex: 1;
    background: transparent;
    height: 36px;
    line-height: 36px;
    padding-left: 14px;
    font-size: 14px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    width: 50px;
}
#textbookChanges .popup-con .filter-options .lable .select-input .search input::placeholder {
    color: #8F97A8;
}
#textbookChanges .popup-con .filter-options .lable .select-input .search span {
    cursor: pointer;
    float: left;
    width: 16px;
    height: 16px;
    background: url(../../images/material/search-icon.png) no-repeat center;
    margin: 9px;
}
