.dialog {
    width: 1104px;
    border-radius: 10px;
    background-color: #ffffff;
    overflow: hidden;
    margin: 0 auto;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
}

.dialog .dialog-title {
    height: 56px;
    border-bottom: 1px solid #e5e6eb;
    color: #1d2129;
    font-size: 16px;
    line-height: 56px;
    text-indent: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dialog .dialog-title span {
    background: url('../../images/cultivation/close1.png') no-repeat center;
    background-size: 18px;
    width: 18px;
    height: 18px;
    margin-right: 16px;
    cursor: pointer;
}

.dialog .dialog-con {
    padding: 0 100px;
    overflow: hidden;
    box-sizing: border-box;
}

.dialog .dialog-con .layui-form .layui-input {
    width: 260px;
    border-radius: 4px;
    height: 34px;
}

.dialog .dialog-con .layui-form .layui-input::placeholder {
    color: #bcbdbd;
}

.dialog .dialog-con .layui-table-page .layui-laypage input {
    width: 40px;
    height: 28px;
}

.dialog .dialog-con .layui-disabled {
    background-color: #f5f5f5;
}

.dialog .dialog-con .layui-form .layui-form-label {
    width: 56px;
    padding: 0;
    line-height: 34px;
    padding-right: 8px;
    color: #1D2129;
}

.dialog .dialog-con .layui-form .layui-input-inline {
    margin-right: 17px;
}

.dialog .dialog-con .layui-form .layui-input-inline .layui-input {
    height: 34px;
    line-height: 34px;
    width: 120px;
}

.dialog .dialog-con .layui-form .layui-input-inline .layui-form-select {
    width: 100px;
}

.dialog .dialog-con .layui-form .layui-input-inline .layui-form-select .layui-input {
    width: 100px;
}

.dialog .dialog-con .layui-form .layui-btn {
    width: 90px;
    height: 30px;
    line-height: 30px;
    background: #4d88ff;
    border-radius: 4px;
}

.dialog .dialog-con .layui-form .layui-btn.layui-btn-primary {
    background-color: unset;
    border: 1px solid #4d88ff;
    color: #4d88ff;
}

.dialog .dialog-con .layui-form .layui-btn.layui-btn-primary:hover {
    border-color: #4d88ff;
}

.dialog .dialog-footer {
    height: 70px;
    border-top: 1px solid #e5e6eb;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.dialog .dialog-footer button {
    width: 88px;
    height: 36px;
    border: 1px solid #c9cdd4;
    border-radius: 18px;
    color: #4e5969;
    font-size: 14px;
    background-color: #ffffff;
    cursor: pointer;
}

.dialog .dialog-footer button:last-child {
    background: #4d88ff;
    border-color: #4d88ff;
    box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
    color: #ffffff;
    margin: 0 30px 0 16px;
}

#addType {
    width: 600px;
    display: none;
}

#addType .dialog-con {
    padding: 0 60px;
}

#addType .dialog-con .item-title {
    padding-left: 8px;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: #6581ba;
    position: relative;
    display: block;
    margin-right: 24px;
    margin: 24px 0;
}

#addType .dialog-con .item-title:after {
    content: "";
    position: absolute;
    left: 0;
    top: 1px;
    background: #6581ba;
    border-radius: 2px;
    width: 3px;
    height: 18px;
}

#addType .dialog-con #addLevelBtn {
    width: 90px;
    margin-bottom: 24px;
}

#addType .dialog-con .table-box {
    margin-bottom: 24px;
}

#addLevel {
    width: 500px;
    display: none;
}

#addLevel .dialog-con {
    padding: 0 60px;
}

#addLevel .form-level {
    margin: 40px 0;
}

#addLevel .switch {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #4e5969;
    height: 34px;
}

#addLevel .switch .switch-con {
    width: 28px;
    height: 14px;
    border-radius: 4px;
    background-color: #e5e6eb;
    margin-right: 10px;
    position: relative;
    cursor: pointer;
}

#addLevel .switch .switch-con i {
    width: 13px;
    height: 10px;
    margin: 2px;
    position: absolute;
    background-color: #ffffff;
    left: 0;
    transition: all linear 200ms;
    border-radius: 2px;
}

#addLevel .switch .switch-con.active {
    background-color: #4d88ff;
}

#addLevel .switch .switch-con.active i {
    left: 11px;
}

.layui-layer-page .layui-layer-content {
    overflow: unset;
}

#addChildType, #addPlanCorrespond {
    width: 532px;
    display: none;
    overflow: unset;
}

#addChildType .dialog, #addPlanCorrespond .dialog {
    overflow: unset;
}

#addChildType .dialog-con, #addPlanCorrespond .dialog-con {
    padding: 32px 90px;
    overflow: unset;
}

#courseSet {
    width: 1000px;
    display: none;
}

#courseSet .dialog-con {
    padding: 0 32px 20px;
    overflow: unset;
}

#courseSet .tab-nav {
    border-bottom: 1px solid #ddd;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
}

#courseSet .tab-nav li {
    line-height: 60px;
    font-size: 15px;
    margin-right: 40px;
    color: #272727;
    cursor: pointer;
}

#courseSet .tab-nav li:hover {
    color: #4c85fa;
}

#courseSet .tab-nav li.active {
    color: #4c85fa;
    font-weight: bold;
    position: relative;
}

#courseSet .tab-nav li.active::after {
    content: "";
    width: 100%;
    height: 2px;
    background-color: #4c85fa;
    position: absolute;
    bottom: 0;
    left: 0;
}

#courseSet .tab-box .layui-inline {
    margin-bottom: 16px;
}

#courseSet .tab-box .j-search-con {
    width: 200px;
}

#courseSet .tab-box .courseName {
    width: 100%;
    height: 34px;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    padding: 0 20px 0 10px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    font-size: 14px;
    cursor: pointer;
}

#courseSet .tab-box .tab-box-con {
    display: none;
}

#courseSet .tab-box .tab-box-con:first-child {
    display: block;
}

#addClassTimeDialog {
    width: 900px;
    display: none;
}

#addClassTimeDialog .dialog-con {
    padding: 24px 32px;
}

#addClassTimeDialog .dialog-con .table-opt {
    margin-bottom: 24px;
}

#dialogTip {
    width: 488px;
    display: none;
}

#dialogTip .dialog-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#dialogTip .dialog-title span {
    background: url('../images/close1.png') no-repeat center;
    background-size: 18px;
    width: 18px;
    height: 18px;
    margin-right: 16px;
    cursor: pointer;
}

#dialogTip .dialog-con {
    padding: 32px 0;
}

#dialogTip .dialog-con img {
    display: block;
    width: 56px;
    margin: 0 auto 24px;
}

#dialogTip .dialog-con h5 {
    color: #1D2129;
    text-align: center;
    font-size: 16px;
    margin-bottom: 4px;
}

#dialogTip .dialog-con p {
    text-align: center;
}

#dialogTip .dialog-con #tipSuccessBtn {
    width: 88px;
    height: 34px;
    border-radius: 18px;
    background: #4D88FF;
    box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
    color: #ffffff;
    outline: none;
    display: block;
    margin: 32px auto 0;
    border: none;
    cursor: pointer;
}
