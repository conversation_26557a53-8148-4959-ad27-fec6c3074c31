body {
    background-color: #fff;
}

.z-main {
    background-color: #ffffff;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    padding: 30px 30px 30px 30px;
}

.z-main.z-main-course {
    display: block;
    font-size: 14px;
    padding: 0;
}

.z-main.z-main-course .z-nav {
    height: 60px;
    border-bottom: 1px solid #E8EBF1;
    overflow: hidden;
}

.z-main.z-main-course .z-nav ul {
    display: flex;
    align-items: center;
    margin-top: 3px;
    line-height: 57px;
    color: #86909C;
    margin-left: 30px;
    overflow: hidden;
}

.z-main.z-main-course .z-nav ul li {
    width: 60px;
    margin-right: 60px;
    text-align: center;
    cursor: pointer;
}

.z-main.z-main-course .z-nav ul li.active {
    color: #1D2129;
    position: relative;
}

.z-main.z-main-course .z-nav ul li.active::after {
    content: "";
    width: 60px;
    height: 3px;
    background: #4D88FF;
    position: absolute;
    bottom: 0;
    left: 0;
    border-radius: 3px 3px 0 0;
}

.z-main.z-main-course .z-box-wrap .z-box {
    display: none;
    height: calc(100vh - 70px);
    overflow-y: auto;
    padding-left: 30px;
}

.z-main.z-main-course .z-box-wrap .z-box:first-child {
    display: block;
}

.z-main.z-main-course .z-box-wrap .z-box:last-child .course-list {
    margin-top: 24px;
}

.z-main.z-main-course .z-box-wrap .z-search {
    float: right;
    margin: 20px 30px;
    position: relative;
}

.z-main.z-main-course .z-box-wrap .z-search span {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 16px;
    height: 16px;
    background: url('../../../images/cultivation/mooc/icon-search.png') no-repeat center;
    cursor: pointer;
}

.z-main.z-main-course .z-box-wrap .z-search input {
    width: 290px;
    border-radius: 4px;
    border: 1px solid #E5E6EB;
    height: 34px;
    padding: 0 30px 0 10px;
    font-size: 14px;
}

.z-main.z-main-course .z-box-wrap .z-search input::placeholder {
    color: #8F97A8;
    font-size: 14px;
}

.z-main.z-main-course .z-box-wrap .course-list {
    display: flex;
    flex-wrap: wrap;
    clear: both;
}

.z-main .course {
    width: 380px;
    border-radius: 12.6px;
    overflow: hidden;
    box-shadow: 0px 0px 20px 0px rgba(68, 104, 230, 0.15);
    position: relative;
    margin-right: 26px;
    margin-bottom: 26px;
}

.z-main .course .course-cover {
    width: 100%;
    height: 150px;
    position: relative;
    cursor: pointer;
}

.z-main .course .course-cover img {
    display: block;
    width: 100%;
    height: 150px;
}

.z-main .course .course-cover .marsk {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 150px;
    background: rgba(0, 0, 0, 0.2);
}

.z-main .course .course-cover .clock-wrap {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    overflow: hidden;
    width: 80%;
}

.z-main .course .course-cover .clock-wrap .clock {
    width: 56px;
    height: 56px;
    background: url("../../../images/cultivation/mooc/clock.png") no-repeat center;
    margin: 0 auto;
    display: block;
}

.z-main .course .course-cover .clock-wrap p {
    margin-top: 8px;
    text-align: center;
    color: #FFF;
    font-size: 14px;
}


.z-main .course .course-mes {
    padding: 10px 16px;
}

.z-main .course .course-mes h3 {
    color: #1d2129;
    line-height: 22px;
    margin-bottom: 4px;
    font-size: 16px;
}

.z-main .course .course-mes .mes {
    display: flex;
    align-items: center;
    line-height: 20px;
    font-size: 14px;
    color: #4e5969;
    margin-bottom: 4px;
}

.z-main .course .course-mes .mes:last-child {
    margin-bottom: 0;
}

.z-main .course .course-mes .mes span {
    padding-right: 8px;
}

.z-main .course .course-mes .mes h4 {
    color: #86909c;
    width: 70px;
    text-align: justify;
    text-align-last: justify;
}

.z-main .course .couraeActivation {
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 16px;
    height: 32px;
    line-height: 32px;
    border-radius: 0px 16px;
    background: linear-gradient(84deg, #4d88ff 0%, #8dadff 100%);
    font-size: 14px;
    color: #ffffff;
    cursor: pointer;
}

.z-main .course.hasActivation .course-cover .marsk,
.z-main .course.hasActivation .course-cover .clock {
    display: none;
}

.z-main .course.hasActivation .couraeActivation {
    display: none;
}

#tips {
    position: fixed;
    z-index: 99;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: #ffffff;
    padding: 16px 24px;
    border-radius: 4px;
    font-size: 14px;
    display: none;
}

.j-search-con {
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
    cursor: pointer;
}

.j-search-con .j-select-year {
    left: 0;
}

.j-search-con input {
    width: 100%;
    height: 34px;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    padding: 0 20px 0 10px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    font-size: 14px;
    cursor: pointer;
}

.j-search-con input::placeholder {
    color: #86909c;
}

.j-search-con .j-arrow {
    width: 10px;
    height: 10px;
    background: url(../../../images/cultivation/mooc/down-icon.png) no-repeat center;
    position: absolute;
    right: 12px;
    top: 12px;
    pointer-events: none;
}

.j-search-con .j-arrow.j-arrow-slide {
    transform: rotate(180deg);
}

.j-search-con .j-select-year {
    position: absolute;
    top: 40px;
    left: -1px;
    z-index: 999;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}

.j-search-con .j-select-year.slideShow {
    display: block;
}

.j-search-con .j-select-year .search {
    height: 36px;
    background: #f5f7fa;
    border-radius: 18px;
    margin: 11px 10px;
}

.j-search-con .j-select-year .search input {
    border: none;
    width: 84%;
    background: transparent;
    height: 36px;
    line-height: 36px;
    padding-left: 14px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    float: left;
}

.j-search-con .j-select-year .search span {
    cursor: pointer;
    float: left;
    width: 16px;
    height: 16px;
    background: url(../../../images/cultivation/mooc/icon-search.png) no-repeat center;
    margin-top: 10px;
}

.j-search-con .j-select-year .all-selects {
    line-height: 17px;
    margin-bottom: 4px;
    height: 17px;
    padding: 0 14px;
    font-size: 12px;
    color: #6b89b3;
    cursor: pointer;
    user-select: none;
}

.j-search-con .j-select-year ul {
    overflow: hidden;
    max-height: 200px;
    overflow-y: auto;
}

.j-search-con .j-select-year ul li {
    line-height: 40px;
    text-align: left;
    text-indent: 16px;
    cursor: pointer;
    font-size: 14px;
    color: #4e5969;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-right: 30px;
    background-color: #ffffff;
    background-image: url("../images/check-icon.png");
    background-repeat: no-repeat;
    background-position: 96% center;
}

.j-search-con .j-select-year ul li:hover {
    background-color: #e1ebff;
    color: #4d88ff;
    font-weight: 500;
}

.j-search-con .j-select-year ul li.active {
    background-color: #e1ebff;
    background-image: url("../../../images/cultivation/mooc/check-cur.png");
    color: #4d88ff;
    font-weight: 500;
}

.j-search-con.single-box .j-select-year ul li {
    background-image: url("../../../images/cultivation/mooc/radio-icon.png");
}

.j-search-con.single-box .j-select-year ul li.active {
    background-image: url("../../../images/cultivation/mooc/radio-cur-icon.png");
}

.opt-btn span {
    display: inline-block;
}

.dialog {
    border-radius: 10px;
    background-color: #ffffff;
    display: none;
}

.dialog .dialog-title {
    border-bottom: 1px solid #e5e6eb;
    height: 56px;
    line-height: 56px;
    color: #1d2129;
    font-size: 16px;
    text-indent: 30px;
}

.dialog .dialog-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 70px;
    border-top: 1px solid #e5e6eb;
    padding-right: 30px;
}

.dialog .dialog-btn button {
    padding: 0 30px;
    height: 36px;
    font-size: 14px;
    border-radius: 18px;
    cursor: pointer;
}

.dialog .dialog-btn button.pu-cancel {
    border: 1px solid #c9cdd4;
    color: #4e5969;
    background-color: #fff;
    margin-right: 16px;
}

.dialog .dialog-btn button.pu-sure {
    color: #fff;
    background: #4d88ff;
    box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
    border: 1px solid #4d88ff;
}

.dialog .dialog-con {
    margin: 40px 80px;
}

.dialog .dialog-con .box-title {
    color: #4080FF;
    font-size: 16px;
    display: flex;
    align-items: center;
    margin-bottom: 32px;
}

.dialog .dialog-con .box-title span {
    width: 16px;
    height: 16px;
    background: url('../../../images/cultivation/mooc/arrow-icon1.png') no-repeat center;
    margin-left: 8px;
    cursor: pointer;
}

.dialog .dialog-con .box-title span.active {
    transform: rotate(-90deg);
}

.dialog .dialog-con .class-mes {
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    overflow: hidden;
}

.dialog .dialog-con .class-mes li {
    font-size: 16px;
    margin-right: 40px;
}

.dialog .dialog-con .class-mes li span {
    color: #4E5969;
}

.dialog .dialog-con .table-con {
    width: 100%;
    overflow: hidden;
}

.dialog .layui-form.form-course {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
}

.dialog .layui-form.form-course .layui-form-item {
    display: flex;
    flex-wrap: wrap;
}

.dialog .layui-form.form-course .layui-inline {
    margin-bottom: 16px;
    margin-right: 2%;
    width: 31%;
    display: flex;
}

.dialog .layui-form.form-course .layui-input-inline {
    width: unset;
    min-width: 190px;
    flex: 1;
}

.dialog .layui-form.form-course .layui-form-label {
    color: #1d2129;
    padding: 7px 14px 7px 0;
    width: 70px;
    text-align: left;
}

.dialog .layui-form.form-course .layui-input {
    height: 34px;
    border-radius: 4px;
    border-color: #e5e6eb;
}

.dialog .layui-form.form-course .layui-input::placeholder {
    color: #86909c;
    font-size:14px;
}

.dialog .form-con {
    display: flex;
    font-size: 14px;
    padding: 0 0 4px;
}

.dialog .form-btn {
    width: 118px;
    position: relative;
    padding-left: 32px;
    box-sizing: border-box;
}

.dialog .form-btn::after {
    content: "";
    width: 1px;
    height: 84px;
    background: #e8ebf1;
    position: absolute;
    left: 0px;
    top: 0px;
}

.dialog .form-btn .btn {
    width: 86px;
    height: 34px;
    background: #ffffff;
    border-radius: 4px;
    margin: 0 auto;
    display: block;
    cursor: pointer;
}

.dialog .form-btn .btn.btn-search {
    background: #4d88ff;
    border: 1px solid #4d88ff;
    color: #ffffff;
}

.dialog .form-btn .btn.btn-reset {
    border: 1px solid #4d88ff;
    color: #4d88ff;
    margin-bottom: 16px;
}

.dialog .form-btn.form-btn-course {
    display: flex;
}

.dialog .form-btn.form-btn-course::after {
    content: "";
    background: none;
}

.dialog .form-btn.form-btn-course .btn-reset {
    margin-right: 16px;
}

#activateCourse {
    width: 630px;
    box-shadow: 0px 0px 20px 0px rgba(68, 104, 230, 0.15);
    color: #1D2129;
}

#activateCourse .dialog-con {
    font-size: 16px;
}

#activateCourse .dialog-con .connext-type {
    margin-bottom: 24px;
    font-weight: 500;
}

#activateCourse .dialog-con .type {
    margin-bottom: 24px;
}

#activateCourse .dialog-con .type span {
    color: #4E5969;
}

#activateCourse .dialog-con .btn-course {
    display: flex;
    align-items: center;
    justify-content: center;
}

#activateCourse .dialog-con .btn-course .btn {
    border-radius: 4px;
    background: #4D88FF;
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
    padding: 0 20px;
    line-height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFF;
    font-size: 14px;
    margin-right: 16px;
    cursor: pointer;
}

#activateCourse .dialog-con .btn-course .btn:last-child {
    margin-right: 0;
}

#activateCourse .dialog-con .btn-course .btn img {
    margin-right: 6px;
}

#activateCourse .context-type {
    font-size: 16px;
}

#addCourse,
#selTeachCourse {
    width: 1360px;
    box-shadow: 0px 0px 20px 0px rgba(68, 104, 230, 0.15);
    color: #1D2129;
}

#checkStu {
    width: 1352px;
    overflow: hidden;
    box-shadow: 0px 0px 20px 0px rgba(68, 104, 230, 0.15);
}

#checkStu .dialog-con .layui-form.form-course .layui-form-label {
    width: 28px;
}

#createSuccess,#checkTips {
    width: 560px;
    box-shadow: 0px 0px 20px 0px rgba(68, 104, 230, 0.15);
    overflow: hidden;
}
#checkTips .dialog-con,
#createSuccess .dialog-con {
    margin: 40px 100px;
    font-size: 16px;
}
#checkTips .dialog-con img,
#createSuccess .dialog-con img {
    display: block;
    margin: 0 auto 24px;
}
#checkTips .dialog-con h4,
#createSuccess .dialog-con h4 {
    color: #1D2129;
    text-align: center;
    margin-bottom: 4px;
}
#checkTips .dialog-con p,
#createSuccess .dialog-con p {
    color: #4E5969;
    text-align: center;
    line-height: 1.5;
}
#createSuccess .dialog-con .btn-sure {
    margin: 24px auto 0;
    color: #fff;
    background: #4d88ff;
    box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
    border: 1px solid #4d88ff;
    padding: 0 30px;
    height: 36px;
    font-size: 14px;
    border-radius: 18px;
    cursor: pointer;
    display: block;
}

#createTips {
    width: 560px;
    box-shadow: 0px 0px 20px 0px rgba(68, 104, 230, 0.15);
    overflow: hidden;
}

#createTips .dialog-con {
    margin: 40px 100px;
    font-size: 16px;
}

#createTips .dialog-con img {
    display: block;
    margin: 0 auto 24px;
}

#createTips .dialog-con p {
    color: #1D2129;
    text-align: center;
    line-height: 1.5;
}
#checkTips .dialog-con .btn,
#createTips .dialog-con .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
}
#checkTips .dialog-con .btn .btn-cancel,
#createTips .dialog-con .btn .btn-cancel {
    border: 1px solid #E5E6EB;
    padding: 0 30px;
    height: 36px;
    font-size: 14px;
    border-radius: 18px;
    color: #4E5969;
    margin-right: 16px;
    background-color: #fff;
}
#checkTips .dialog-con .btn-sure,
#createTips .dialog-con .btn .btn-sure {
    color: #fff;
    background: #4d88ff;
    box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
    border: 1px solid #4d88ff;
    padding: 0 30px;
    height: 36px;
    font-size: 14px;
    border-radius: 18px;
    cursor: pointer;
    display: block;
}

#cloneCourse,
#joinCourse {
    width: 1372px;
    box-shadow: 0px 0px 20px 0px rgba(68, 104, 230, 0.15);
    color: #1D2129;
    display: none;
}

#cloneCourse .number-nav,
#joinCourse .number-nav {
    display: flex;
    align-items: center;
    line-height: 58px;
    border-bottom: 1px solid #E8EBF1;
    margin-bottom: 24px;
    margin-top: 5px;
}

#cloneCourse .number-nav li,
#joinCourse .number-nav li {
    color: #86909C;
    font-size: 14px;
    margin-right: 60px;
    cursor: pointer;
}

#cloneCourse .number-nav li.active,
#joinCourse .number-nav li.active {
    font-size: 16px;
    color: #1D2129;
    position: relative;
}

#cloneCourse .number-nav li.active::after,
#joinCourse .number-nav li.active::after {
    content: "";
    width: 60px;
    height: 3px;
    background-color: #4D88FF;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 3px 3px 0 0;
}

#cloneCourse .layui-form.form-course .layui-inline,
#joinCourse .layui-form.form-course .layui-inline {
    margin-bottom: 24px;
}

#cloneCourse .form-btn,
#joinCourse .form-btn {
    width: auto;
    display: flex;
    align-items: center;
    padding-left: 0;
}

#cloneCourse .form-btn::after,
#joinCourse .form-btn::after {
    content: "";
    background: none;
}

#cloneCourse .form-btn .btn,
#joinCourse .form-btn .btn {
    margin: 0;
    margin-bottom: 24px;
}

#cloneCourse .form-btn .btn:last-child,
#joinCourse .form-btn .btn:last-child {
    margin-left: 16px;
}

#cloneCourse .member-box-wrap .member-box,
#joinCourse .member-box-wrap .member-box {
    display: none;
}

#cloneCourse .member-box-wrap .member-box:first-child,
#joinCourse .member-box-wrap .member-box:first-child {
    display: block;
}

#cloneCourse .member-course,
#joinCourse .member-course {
    display: flex;
    flex-wrap: wrap;
}

#cloneCourse .member-course .course-con,
#joinCourse .member-course .course-con {
    border-radius: 4px;
    box-shadow: 0 0 10px 0 rgba(68, 104, 230, 0.26);
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 20px 32px;
    width: 594px;
    box-sizing: border-box;
}

#joinCourse .member-course .course-con:nth-child(2n) {
    margin-left: 24px;
}

#cloneCourse .member-course .course-con .course-check,
#joinCourse .member-course .course-con .course-check {
    width: 16px;
    height: 16px;
    background: url('../../../images/cultivation/mooc/check-icon.png') no-repeat center;
    background-size: 16px;
    cursor: pointer;
    border-radius: 50%;
    background: unset;
    border: 1px solid #C9CDD4;
    box-sizing: border-box;
}

#cloneCourse .member-course .course-con .course-check.course-checked,
#joinCourse .member-course .course-con .course-check.course-checked {
    background: url('../../../images/cultivation/mooc/check-cur.png') no-repeat center;
    border: none;
}

#cloneCourse .member-course .course-con .course-wrap,
#joinCourse .member-course .course-con .course-wrap {
    display: flex;
    align-items: center;
    cursor: pointer;
    flex: 1;
}

#cloneCourse .member-course .course-con .course-wrap .course-cover,
#joinCourse .member-course .course-con .course-wrap .course-cover {
    width: 80px;
    height: 80px;
    margin: 0 24px;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

#cloneCourse .member-course .course-con .course-wrap .course-cover .course-review,
#joinCourse .member-course .course-con .course-wrap .course-cover .course-review {
    width: 80px;
    height: 80px;
    text-align: center;
    line-height: 80px;
    border-radius: 4px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 100%, rgba(0, 0, 0, 0) 100%);
    position: absolute;
    left: 0;
    top: 0;
    color: #fff;
    display: none;
}

#cloneCourse .member-course .course-con .course-wrap .course-cover img,
#joinCourse .member-course .course-con .course-wrap .course-cover img {
    width: 80px;
    height: 80px;
}

#cloneCourse .member-course .course-con .course-wrap .course-mes,
#joinCourse .member-course .course-con .course-wrap .course-mes {
    flex: 1;
}

#cloneCourse .member-course .course-con .course-wrap .course-mes h4,
#joinCourse .member-course .course-con .course-wrap .course-mes h4 {
    font-size: 16px;
    margin-bottom: 10px;
}

#cloneCourse .member-course .course-con .course-wrap .course-mes p,
#joinCourse .member-course .course-con .course-wrap .course-mes p {
    font-size: 14px;
    color: #4E5969;
    margin-bottom: 4px;
}

#cloneCourse .member-course .course-con .course-wrap .course-mes p span,
#joinCourse .member-course .course-con .course-wrap .course-mes p span {
    padding-left: 8px;
}

#cloneCourse .member-course .course-con .course-wrap:hover,
#joinCourse .member-course .course-con .course-wrap:hover {
    /* .course-btn {
                    display: block;
                  } */
}

#cloneCourse .member-course .course-con .course-wrap:hover .course-cover .course-review,
#joinCourse .member-course .course-con .course-wrap:hover .course-cover .course-review {
    display: block;
}

#cloneCourse .member-course .course-con .course-btn,
#joinCourse .member-course .course-con .course-btn {
    margin-left: 24px;
}

#cloneCourse .member-course .course-con .course-btn .apply-clone,
#joinCourse .member-course .course-con .course-btn .apply-clone {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border: 1px dashed #4D88FF;
    box-sizing: border-box;
    width: 100px;
    height: 34px;
    font-size: 14px;
    color: #4D88FF;
    cursor: pointer;
}

#cloneCourse .member-course .course-con .course-btn .apply-clone img,
#joinCourse .member-course .course-con .course-btn .apply-clone img {
    margin-right: 4px;
}

#cloneCourse .member-course .course-con .course-btn .apply-through,
#joinCourse .member-course .course-con .course-btn .apply-through {
    width: 100px;
    height: 34px;
    border: 1px dashed #C9CDD4;
    box-sizing: border-box;
    text-align: center;
    line-height: 32px;
    border-radius: 4px;
    color: #86909C;
    cursor: pointer;
}

#cloneCourse .member-course .course-con .course-btn .hide,
#joinCourse .member-course .course-con .course-btn .hide {
    display: none;
}

#fyClass {
    width: 1358px;
    box-shadow: 0px 0px 20px 0px rgba(68, 104, 230, 0.15);
}

#fyClass .layui-form.form-course .layui-inline {
    margin-bottom: 24px;
}

#fyClass .form-btn {
    width: auto;
    display: flex;
    align-items: center;
    padding-left: 0;
}

#fyClass .form-btn::after {
    content: "";
    background: none;
}

#fyClass .form-btn .btn {
    margin: 0;
    margin-bottom: 24px;
}

#fyClass .form-btn .btn:last-child {
    margin-left: 16px;
}

#fyStuClass {
    width: 1163px;
    box-shadow: 0px 0px 20px 0px rgba(68, 104, 230, 0.15);
}

#fyStuClass .layui-form.form-course .layui-form-label {
    width: 30px;
}

@media screen and (max-width: 2668px) {
    .z-main .course {
        width: 14.7%;
        margin-right: 1%;
        margin-bottom: 1%;
    }
}
@media screen and (max-width: 2068px) {
    .z-main .course {
        width: 18%;
        margin-right: 2%;
        margin-bottom: 2%;
    }
}
@media screen and (max-width: 1800px) {
    .z-main.z-main-course .z-box-wrap .z-box {
        padding-left: 1.66%;
    }
    .z-main .course {
        width: 18.4%;
        margin-right: 1.54%;
        margin-bottom: 1.54%;
        /*  &:nth-child(5n) {
          margin-right: 0;
        } */
    }
}
@media screen and (max-width: 1200px) {
    .z-main.z-main-course .z-box-wrap .z-box {
        padding-left: 2.5%;
    }
    .z-main .course {
        width: 22.86%;
        margin-right: 2%;
        margin-bottom: 2%;
        /* &:nth-child(4n) {
          margin-right: 0;
        }

        &:nth-child(5n) {
          margin-right: 2%;
        } */
    }
}
@media screen and (max-width: 1000px) {
    .z-main.z-main-course .z-box-wrap .z-box {
        padding-left: 3%;
    }
    .z-main .course {
        width: 31%;
        margin-right: 2%;
        margin-bottom: 2%;
        /*  &:nth-child(4n) {
          margin-right: 2%;
        } */
    }
}
@media screen and (max-width: 750px) {
    .z-main.z-main-course .z-box-wrap .z-box {
        padding-left: 4%;
    }
    .z-main .course {
        width: 47%;
        margin-right: 2%;
        margin-bottom: 2%;
    }
}
.dialog .dialog-btn button.pu-sure.btn-disabled {
    cursor: not-allowed;
    opacity: 0.5;
}
.z-main.z-main-course .z-box-wrap .no-data {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
.z-main.z-main-course .z-box-wrap .no-data img {
    display: block;
    width: 270px;
    margin-bottom: 8px;
}
.z-main.z-main-course .z-box-wrap .no-data p {
    color: #898989;
    text-align: center;
}