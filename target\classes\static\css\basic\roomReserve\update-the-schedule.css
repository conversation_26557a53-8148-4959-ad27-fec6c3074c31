body {
  background-color: #F7F8FA;
  font-size: 14px;
  color: #4E5969;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
::selection {
  background: #dde7ff;
  /* 粉红色背景 */
}
::-webkit-scrollbar {
  width: 6px;
  height: 10px;
}
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #d9d9d9;
}
::-webkit-scrollbar-track {
  border-radius: 6px;
}
.main .top {
  width: 100%;
  height: 60px;
  background: #FFFFFF;
  position: relative;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E8EBF1;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .top .titles {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 22px;
  padding-left: 30px;
}
.main .top .titles .back {
  cursor: pointer;
  margin-right: 16px;
  padding-left: 22px;
  background: url(../images/back.png) no-repeat left center;
  background-size: 16px;
  color: #7d92b2;
  font-size: 14px;
}
.main .top .titles ul {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 3px;
}
.main .top .titles ul::after {
  content: "";
  width: 3px;
  height: 16px;
  background-color: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.main .top .titles ul li {
  padding: 0 16px 0 6px;
  font-size: 16px;
  background: url(../images/right-icon.png) no-repeat right 5px;
  background-size: 14px;
}
.main .top .titles ul li a {
  color: #4E5969;
}
.main .top .titles ul li:last-child {
  background: unset;
}
.main .top .titles ul li:last-child a {
  color: #1D2129;
  font-weight: 500;
}
.main .top h4 {
  position: relative;
  color: #1d2129;
  font-size: 16px;
  margin-left: 30px;
  padding-left: 9px;
}
.main .top h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}
.main .top .r-title {
  padding-right: 30px;
  display: flex;
  display: -wekit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .top .r-title .update {
  width: 124px;
  height: 34px;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  background-color: #4d88ff;
  text-align: center;
  line-height: 34px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  border-radius: 4px;
}
.main .form-con {
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
  font-size: 14px;
  padding: 20px 30px 20px;
  padding-bottom: 0;
  position: relative;
}
.main .form-con .form-btn {
  width: auto;
  flex-shrink: 0;
  margin-bottom: 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .form-con .form-btn::after {
  display: none;
}
.main .form-con .form-btn .btn {
  width: 104px;
  height: 34px;
  background: #ffffff;
  border-radius: 4px;
  display: block;
  cursor: pointer;
}
.main .form-con .form-btn .btn.btn-search {
  background: #4D88FF;
  border: 1px solid #4D88FF;
  color: #ffffff;
  box-shadow: 0px 0px 10px 0px #4D88FF4D;
}
.main .form-con .form-btn .btn.btn-reset {
  border: 1px solid #4D88FF;
  color: #4D88FF;
  margin-right: 16px;
  box-shadow: 0px 0px 10px 0px #4D88FF33;
}
.main .form-con .btns {
  padding-left: 25px;
}
.main .form-con .btns .see-more {
  padding-left: 20px;
  color: #4d88ff;
  font-size: 14px;
  height: 32px;
  line-height: 32px;
  background: url(../images/see-more.png) no-repeat left center;
  margin-bottom: 16px;
  cursor: pointer;
  display: block;
}
.main .form-con .btns .collapse-content {
  padding-left: 20px;
  color: #4d88ff;
  font-size: 14px;
  height: 32px;
  line-height: 32px;
  background: url(../images/collapse-content.png) no-repeat left center;
  cursor: pointer;
  display: none;
}
.main .form-con .btns.active .see-more {
  display: none;
}
.main .form-con .btns.active .collapse-content {
  display: block;
}
.main .form-con .sel-box {
  display: flex;
  display: -webkit-flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  flex: 1;
}
.main .form-con .sel-item {
  display: flex;
  align-items: center;
  width: 307px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-right: 24px;
  margin-bottom: 24px;
}
.main .form-con .sel-item .sel-title {
  color: #1D2129;
  font-size: 14px;
  width: auto;
  margin-right: 14px;
}
.main .form-con .sel-item .sel-title span {
  display: inline-block;
  width: 14px;
  color: #F76560;
}
.main .form-con .sel-item .sel {
  flex: 1;
  width: 0;
  height: 34px;
  line-height: 34px;
  margin-bottom: 0;
}
.main .form-con .sel-item .sel em {
  float: left;
  font-size: 14px;
  color: #474C59;
}
.main .form-con .sel-item .sel .select-input {
  height: 34px;
  border-radius: 4px;
  border: 1px solid #D4D6D9;
  background-color: #fff;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
}
.main .form-con .sel-item .sel .select-input em {
  position: absolute;
  top: 11px;
  right: 11px;
  width: 12px;
  height: 12px;
  background: url(../images/drop-down4.png) no-repeat center;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.main .form-con .sel-item .sel .select-input .name {
  font-size: 14px;
  color: #ACB4BF;
  padding-left: 13px;
  line-height: 32px;
  width: 86%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .form-con .sel-item .sel .select-input .name.ckd {
  color: #131B26;
}
.main .form-con .sel-item .sel .select-input.clicked em {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.main .form-con .sel-item .sel .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
  display: block;
}
.main .form-con .sel-item .sel .select-input .select-dropdown {
  width: 100%;
  display: none;
  left: -1px;
  margin: 5px 0;
  padding: 6px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: absolute;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .search {
  margin: 8px;
  height: 36px;
  box-sizing: border-box;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  display: flex;
  align-items: center;
  overflow: hidden;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .search input {
  border: none;
  flex: 1;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  font-size: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  width: 50px;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .search input::placeholder {
  color: #8F97A8;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .search span {
  cursor: pointer;
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  background: url(../images/search-icon.png) no-repeat center;
  margin: 9px;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .all-selects {
  color: #4E5969;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  margin: 0 20px;
  padding-left: 24px;
  background: url(../images/check-icon.png) no-repeat left center;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .all-selects.cur {
  background: url(../images/checked-icon.png) no-repeat left center;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists {
  padding: 0 0 6px;
  max-height: 240px;
  overflow-y: auto;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists li {
  margin: 0;
  line-height: normal;
  line-height: 40px;
  padding: 0 20px;
  clear: both;
  color: #4E5969;
  font-size: 14px !important;
  list-style: none;
  cursor: pointer;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists li span {
  display: block;
  padding-left: 27px;
  background: url(../images/check-icon.png) no-repeat left center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists li:hover {
  background: #F5F7FA;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #E1EBFF;
}
.main .form-con .sel-item .sel .select-input .select-dropdown .dropdown-lists li.cur span {
  background: url(../images/checked-icon.png) no-repeat left center;
}
.main .tips-wrapper {
  width: 100%;
  height: 22px;
  line-height: 22px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0 30px;
}
.main .tips-wrapper .tips {
  padding-left: 28px;
  background: url(../images/tip1-icon.png) no-repeat left;
  font-size: 14px;
  color: #1D2129;
  line-height: 22px;
}
.main .jpPage .count-total span {
  color: #4D88FF;
}
#updateSchedule {
  width: 608px;
  height: 446px;
  display: none;
}
#updateSchedule .title .close {
  width: 20px;
  height: 20px;
  background: url(../images/close-icon.png) no-repeat center;
  cursor: pointer;
}
#updateSchedule .popup-con {
  padding: 0;
}
#updateSchedule .popup-con .tips {
  width: 100%;
  height: 50px;
  background-color: #e1ebff;
  box-sizing: border-box;
  padding: 0 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#updateSchedule .popup-con .tips span {
  background: url(../images/tips-blue.png) no-repeat left center;
  padding-left: 28px;
  font-size: 14px;
  color: #1d2129;
}
#updateSchedule .popup-con .radio-box {
  padding: 40px 80px;
}
#updateSchedule .popup-con .radio-box ul {
  margin-bottom: 6px;
}
#updateSchedule .popup-con .radio-box ul li {
  cursor: pointer;
}
#updateSchedule .popup-con .radio-box ul li:first-child {
  margin-bottom: 24px;
}
#updateSchedule .popup-con .radio-box ul li.cur h4:before {
  content: '';
  position: absolute;
  left: 3px;
  top: 5px;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  background-color: #4d88ff;
}
#updateSchedule .popup-con .radio-box ul li h4 {
  padding-left: 24px;
  font-size: 14px;
  color: #4e5969;
  line-height: 20px;
  margin-bottom: 6px;
  position: relative;
}
#updateSchedule .popup-con .radio-box ul li h4:after {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  border-radius: 50%;
  border: 1px solid #c9cdd4;
  width: 16px;
  height: 16px;
  box-sizing: border-box;
}
#updateSchedule .popup-con .radio-box ul li p {
  color: #86909c;
  font-size: 12px;
  line-height: 21px;
}
#updateSchedule .popup-con .attention {
  font-size: 12px;
  color: #F76560;
  line-height: 29px;
}
