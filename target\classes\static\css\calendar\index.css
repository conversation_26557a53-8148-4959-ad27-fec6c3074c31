.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
input[type=search] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
  font-family: inherit;
  font-size: 100%;
}
input::-webkit-search-decoration,
input::-webkit-search-cancel-button {
  display: none;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #86909C;
  font-size: 0.14rem;
}
.hide {
  display: none;
}
body {
  background-color: #fff;
}
.hide {
  display: none;
}
.main {
  overflow: hidden;
  margin-top: 0.64rem;
  overflow-y: auto;
  z-index: 10;
  padding-bottom: 1.04rem;
}
.main .change-box {
  width: 100%;
  height: 0.44rem;
  background: #FFFFFF;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0 0.16rem;
}
.main .change-box .current-semester {
  overflow: hidden;
}
.main .change-box .current-semester .name {
  float: left;
  font-size: 0.14rem;
  color: #595969;
}
.main .change-box .current-semester .time {
  float: left;
  font-size: 0.14rem;
  color: #595969;
  padding-right: 0.11rem;
  position: relative;
}
.main .change-box .current-semester .time:after {
  content: '';
  position: absolute;
  top: 0.08rem;
  right: 0;
  width: 0.07rem;
  height: 0.04rem;
  background: url(../images/arrows-icons.png) no-repeat left center;
  background-size: contain;
}
.main .change-box .types {
  width: 1.02rem;
  height: 0.24rem;
  background: #F1F3F6;
  border-radius: 0.04rem;
  padding: 0.03rem;
  overflow: hidden;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .change-box .types span {
  float: left;
  width: 50%;
  height: 0.18rem;
  text-align: center;
  line-height: 0.18rem;
  font-size: 0.12rem;
  color: #4E5969;
}
.main .change-box .types span.cur {
  color: #4D88FF;
  background: #FFFFFF;
  border-radius: 0.04rem;
}
.main .calendar-box {
  padding: 0 0.16rem;
}
.main .calendar-box .cal-top {
  width: 100%;
  height: 0.32rem;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0.05rem 0;
}
.main .calendar-box .cal-top span {
  flex: 1;
  margin-right: 0.04rem;
  font-size: 0.14rem;
  color: #4E5969;
  text-align: center;
  line-height: 0.32rem;
}
.main .calendar-box .cal-top span:last-child {
  margin-right: 0;
}
.main .calendar-box .cla-con .lable {
  width: 100%;
  padding-bottom: 0.12rem;
  margin-bottom: 0.12rem;
  border-bottom: 0.01rem solid #E8EBF1;
}
.main .calendar-box .cla-con .lable:last-child {
  border-bottom: none;
}
.main .calendar-box .cla-con .lable .row {
  padding: 0.05rem 0;
  width: 100%;
  height: 0.32rem;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .calendar-box .cla-con .lable .row .week {
  border-right: 1px dashed #E5E6EB;
  flex: 1;
  margin-right: 0.04rem;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.14rem;
  color: #6581BA;
}
.main .calendar-box .cla-con .lable .row .unit {
  flex: 1;
  margin-right: 0.04rem;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  height: 0.32rem;
}
.main .calendar-box .cla-con .lable .row .unit span {
  font-size: 0.14rem;
  color: #4E5969;
}
.main .calendar-box .cla-con .lable .row .unit:last-child {
  margin-right: 0;
}
.main .calendar-box .cla-con .lable .row .unit.disable span {
  font-size: 0.14rem;
  color: #C9CDD4;
}
.main .calendar-box .cla-con .lable .row .unit.m-start span {
  color: #4D88FF;
  font-size: 0.14rem;
  width: 100%;
  text-align: center;
}
.main .calendar-box .cla-con .lable .row .unit.m-start p {
  width: 100%;
  text-align: center;
  color: #6581BA;
  font-size: 0.1rem;
  line-height: 0.14rem;
  transform: scale(0.9);
  -webkit-transform: scale(0.9);
}
.main .calendar-box .cla-con .lable .row .unit.current span {
  width: 0.24rem;
  height: 0.24rem;
  background: #4D88FF;
  border-radius: 0.12rem;
  font-size: 0.14rem;
  color: #FFFFFF;
  text-align: center;
  line-height: 0.24rem;
}
.main .calendar-box .cla-con .lable .row .unit.events {
  position: relative;
}
.main .calendar-box .cla-con .lable .row .unit.events:after {
  content: '';
  position: absolute;
  left: 50%;
  margin-left: -0.02rem;
  bottom: -0.04rem;
  width: 0.04rem;
  height: 0.04rem;
  background: #4D88FF;
  border-radius: 50%;
}
.main .calendar-box .notes-list {
  margin-bottom: 0.16rem;
}
.main .calendar-box .notes-list .lable {
  display: none;
}
.main .calendar-box .notes-list .lable.active {
  display: block;
  width: 100%;
  height: 3.17rem;
  padding: 0.16rem;
  background: #F1F3F6;
  border-radius: 0.04rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  position: relative;
}
.main .calendar-box .notes-list .lable.active h4 {
  font-size: 0.16rem;
  line-height: 0.21rem;
  color: #4E5969;
  margin-bottom: 0.08rem;
}
.main .calendar-box .notes-list .lable.active .tts {
  font-size: 0.14rem;
  line-height: 0.21rem;
  color: #4E5969;
}
.main .calendar-box .notes-list .lable.active .no-data {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  text-align: center;
}
.main .calendar-box .notes-list .lable.active span {
  font-size: 0.16rem;
  color: #86909C;
}
.school-bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1.04rem;
  background-color: #fff;
  box-shadow: 0px -0.08rem 0.12rem -0.04rem rgba(184, 184, 210, 0.2);
}
.school-bottom .handle {
  width: 100%;
  height: 0.7rem;
  padding: 0.15rem 0.16rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
}
.school-bottom .handle .btn {
  flex: 1;
  height: 0.4rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.school-bottom .handle .btn.generate {
  margin-right: 0.2rem;
  height: 0.4rem;
  background: #FFFFFF;
  border: 0.01rem solid #C9CDD4;
  border-radius: 0.2rem;
  font-size: 0.14rem;
  color: #4E5969;
  text-align: center;
  line-height: 0.38rem;
}
.school-bottom .handle .btn.export {
  background: #4D88FF;
  border: 0.01rem solid #4D88FF;
  border-radius: 0.2rem;
  font-size: 0.14rem;
  color: #FFFFFF;
  text-align: center;
  line-height: 0.38rem;
}
.school-bottom .handle .btn.disable {
  background: #F7F8FA;
  border: 0.01rem solid #C9CDD4;
  border-radius: 0.2rem;
  color: #C9CDD4;
}
.marker {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
}
.editNotes {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.2rem 0.2rem 0px 0px;
  background-color: #fff;
  overflow: hidden;
  z-index: 1001;
  transform: translate(0, 120%);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.editNotes.move {
  transform: translate(0, 0);
}
.editNotes .en-top {
  width: 100%;
  height: 0.45rem;
  text-align: center;
  position: relative;
  font-size: 0.16rem;
  color: #333333;
  line-height: 0.45rem;
  border-bottom: 0.01rem solid #F2F2F2;
}
.editNotes .en-con {
  padding: 0.12rem 0.15rem;
}
.editNotes .en-con .lable {
  margin-bottom: 0.24rem;
}
.editNotes .en-con .lable:last-child {
  margin-bottom: 0;
}
.editNotes .en-con .lable .title {
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #1D2129;
  margin-bottom: 0.14rem;
}
.editNotes .en-con .lable .inputs {
  width: 100%;
  height: 0.34rem;
  padding: 0 0.1rem;
  background: #F7F8FA;
  border: 0.01rem solid #E5E6EB;
  border-radius: 0.04rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #86909C;
  line-height: 0.32rem;
}
.editNotes .en-con .lable .kalamu-area {
  width: 100%;
  min-height: 1.2rem;
  background: #FFFFFF;
  border: 0.01rem solid #E5E6EB;
  border-radius: 0.04rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0.07rem 0.1rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  font-size: 0.14rem;
  outline: none;
  letter-spacing: 0.02rem;
  color: #4E5969;
  position: relative;
}
.editNotes .en-con .lable .kalamu-area:empty:before {
  content: attr(placeholder);
  font-size: 0.14rem;
  color: #86909C;
  line-height: normal;
}
.editNotes .en-con .lable .kalamu-area:focus:before {
  content: none;
}
.editNotes .btns {
  width: 100%;
  height: 1.04rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0 0.16rem;
}
.editNotes .btns .handle {
  width: 100%;
  height: 0.7rem;
  padding: 0.15rem 0.16rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
}
.editNotes .btns .handle .btn {
  flex: 1;
  height: 0.4rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.editNotes .btns .handle .btn.generate {
  margin-right: 0.2rem;
  height: 0.4rem;
  background: #FFFFFF;
  border: 0.01rem solid #C9CDD4;
  border-radius: 0.2rem;
  font-size: 0.14rem;
  color: #4E5969;
  text-align: center;
  line-height: 0.38rem;
}
.editNotes .btns .handle .btn.export {
  background: #4D88FF;
  border: 0.01rem solid #4D88FF;
  border-radius: 0.2rem;
  font-size: 0.14rem;
  color: #FFFFFF;
  text-align: center;
  line-height: 0.38rem;
}
.markers {
  display: none;
  position: fixed;
  z-index: 1005;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(10, 10, 10, 0.5);
}
.bot-window {
  z-index: 1006;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-top: 0.15rem;
  background: #ffffff;
  border-radius: 0.16rem 0.16rem 0px 0px;
  transform: translate(0, 120%);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.bot-window.move {
  transform: translate(0, 0);
}
.bot-window.category .bot-list {
  height: 1.5rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.bot-window .bot-list {
  height: auto;
  height: 1.5rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.bot-window .bot-list ul li {
  width: 100%;
  height: 0.5rem;
  line-height: 0.5rem;
  text-align: center;
  font-size: 0.14rem;
  color: #0a0a0a;
  border-bottom: 0.01rem solid rgba(10, 10, 10, 0.05);
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.bot-window .bot-list ul li.cur {
  color: #2962ff;
}
.bot-window .bot-close {
  width: 100%;
  height: 0.56rem;
  line-height: 0.56rem;
  background-color: #fafbfc;
  text-align: center;
  font-size: 0.14rem;
  color: #2962ff;
}
.geschool-calendar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.2rem 0.2rem 0px 0px;
  background-color: #fff;
  overflow: hidden;
  z-index: 1001;
  transform: translate(0, 120%);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.geschool-calendar.move {
  transform: translate(0, 0);
}
.geschool-calendar .en-top {
  width: 100%;
  height: 0.45rem;
  text-align: center;
  position: relative;
  font-size: 0.16rem;
  color: #333333;
  line-height: 0.45rem;
  border-bottom: 0.01rem solid #F2F2F2;
}
.geschool-calendar .en-con {
  padding: 0.12rem 0.15rem;
  max-height: 4rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.geschool-calendar .en-con .lable {
  margin-bottom: 0.24rem;
}
.geschool-calendar .en-con .lable:last-child {
  margin-bottom: 0;
}
.geschool-calendar .en-con .lable .title {
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #1D2129;
  margin-bottom: 0.14rem;
}
.geschool-calendar .en-con .lable .inputs {
  width: 100%;
  height: 0.34rem;
  padding: 0 0.1rem;
  border: 0.01rem solid #E5E6EB;
  border-radius: 0.04rem;
  background-color: #fff;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
}
.geschool-calendar .en-con .lable .inputs span {
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #86909C;
}
.geschool-calendar .en-con .lable .inputs span.deepColors {
  color: #4E5969;
}
.geschool-calendar .en-con .lable .inputs em {
  width: 0.1rem;
  height: 0.1rem;
  background: url(../images/slide-icons.png) no-repeat center;
  background-size: 0.1rem;
}
.geschool-calendar .en-con .lable .kalamu-area {
  width: 100%;
  min-height: 1.2rem;
  background: #FFFFFF;
  border: 0.01rem solid #E5E6EB;
  border-radius: 0.04rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0.07rem 0.1rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  font-size: 0.14rem;
  outline: none;
  letter-spacing: 0.02rem;
  color: #4E5969;
  position: relative;
}
.geschool-calendar .en-con .lable .kalamu-area:empty:before {
  content: attr(placeholder);
  font-size: 0.14rem;
  color: #86909C;
  line-height: normal;
}
.geschool-calendar .en-con .lable .kalamu-area:focus:before {
  content: none;
}
.geschool-calendar .btns {
  width: 100%;
  height: 1.04rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.geschool-calendar .btns .handle {
  width: 100%;
  height: 0.7rem;
  padding: 0.15rem 0.16rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
}
.geschool-calendar .btns .handle .btn {
  flex: 1;
  height: 0.4rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.geschool-calendar .btns .handle .btn.generate {
  margin-right: 0.2rem;
  height: 0.4rem;
  background: #FFFFFF;
  border: 0.01rem solid #C9CDD4;
  border-radius: 0.2rem;
  font-size: 0.14rem;
  color: #4E5969;
  text-align: center;
  line-height: 0.38rem;
}
.geschool-calendar .btns .handle .btn.export {
  background: #4D88FF;
  border: 0.01rem solid #4D88FF;
  border-radius: 0.2rem;
  font-size: 0.14rem;
  color: #FFFFFF;
  text-align: center;
  line-height: 0.38rem;
}
.poup {
  display: none;
  position: fixed;
  z-index: 1002;
  width: 72%;
  height: auto;
  left: 14%;
  top: 50%;
  transform: translate(0, -50%);
  -webkit-transform: translate(0, -50%);
  background: #FFFFFF;
  border-radius: 0.16rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.poup .p-botm {
  width: 100%;
  height: 0.44rem;
  position: relative;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
}
.poup .p-botm:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border-bottom: 1px solid #EBEBEB;
  transform: scaleY(0.5);
  -webkit-transform: scaleY(0.5);
  -moz-transform: scaleY(0.5);
  -o-transform: scaleY(0.5);
}
.poup .p-botm div {
  flex: 1;
}
.poup .p-botm div.cancle {
  font-size: 0.32rem;
  height: 0.88rem;
  line-height: 0.88rem;
  color: #86909C;
  text-align: center;
  position: relative;
}
.poup .p-botm div.cancle:after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  border-right: 1px solid #EBEBEB;
  transform: scaleX(0.5);
  -webkit-transform: scaleX(0.5);
  -moz-transform: scaleX(0.5);
  -o-transform: scaleX(0.5);
}
.poup .p-botm div.confirm {
  font-size: 0.16rem;
  color: #4D88FF;
  text-align: center;
}
.poup.save-dialog .p-con {
  padding: 0.6rem 0.3rem 0.16rem;
  color: #4E5969;
  font-size: 0.16rem;
  line-height: 0.22rem;
  background: url(../images/success-icons.png) no-repeat center 0.19rem;
  background-size: 0.32rem;
  text-align: center;
}
.ios-select-widget-box.olay {
  z-index: 1500;
}
.ios-select-widget-box header.iosselect-header a {
  color: #4D88FF;
}
