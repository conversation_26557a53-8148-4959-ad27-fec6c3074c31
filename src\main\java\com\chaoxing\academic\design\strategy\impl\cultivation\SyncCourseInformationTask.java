package com.chaoxing.academic.design.strategy.impl.cultivation;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chaoxing.academic.design.strategy.DataPushHandler;
import com.chaoxing.academic.design.strategy.bo.DataPushBO;
import com.chaoxing.academic.design.template.bo.TargetPoint;
import com.chaoxing.academic.entity.form.basic.MajorInfoForm;
import com.chaoxing.academic.entity.form.basic.TeacherInfoForm;
import com.chaoxing.academic.entity.form.cultivation.*;
import com.chaoxing.academic.entity.form.cultivation.subform.Kkgl_jc;
import com.chaoxing.academic.entity.form.cultivation.subform.Kksz;
import com.chaoxing.academic.entity.form.cultivation.subform.Kkxxb_jcxx;
import com.chaoxing.academic.entity.po.cultivation.CultivationClassStartsInfo;
import com.chaoxing.academic.entity.po.cultivation.CultivationClassStartsInfoDetail;
import com.chaoxing.academic.entity.po.cultivation.CultivationClassStartsStudent;
import com.chaoxing.academic.entity.po.cultivation.CultivationMajorCourseSet;
import com.chaoxing.academic.enums.ErrorType;
import com.chaoxing.academic.enums.SearchStrBodyType;
import com.chaoxing.academic.service.cultivation.ClassStartsInfoDetailService;
import com.chaoxing.academic.service.cultivation.ClassStartsInfoService;
import com.chaoxing.academic.service.cultivation.CultivationClassStartsStudentService;
import com.chaoxing.academic.service.cultivation.MajorCourseSetService;
import com.chaoxing.academic.utils.MyUtils;
import com.chaoxing.academic.utils.StringUtils;
import com.chaoxing.academic.utils.form.FormUtils;
import com.chaoxing.academic.utils.redis.RedisUtils;
import com.chaoxing.form.FormTemplate;
import com.chaoxing.form.constant.FormComponentConstants;
import com.chaoxing.form.param.SearchParam;
import com.chaoxing.form.pojo.IdName;
import com.chaoxing.form.pojo.SearchStrBody;
import com.chaoxing.form.util.FormUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.function.Supplier;

@Component
@Slf4j
public class SyncCourseInformationTask implements DataPushHandler<DataPushBO> {

    @Autowired
    ClassStartsInfoDetailService classStartsInfoDetailService;

    @Autowired
    CultivationClassStartsStudentService cultivationClassStartsStudentService;

    @Autowired
    ClassStartsInfoService classStartsInfoService;

    @Autowired
    MajorCourseSetService majorCourseSetService;

    private final Supplier<List<TargetPoint>> allPoints =
            () -> Arrays.asList(TeachClassGroupForm.POINT, CourseInformationForm.POINT, TeachPlanForm.POINT, new TargetPoint("cultivation_class_starts_info", "开班记录表"), new TargetPoint("cultivation_class_starts_info_detail", "开班详情表"));

    @Override
    public void update(DataPushBO args) {
        updateLogic(args);
    }

    @Override
    public void recover(DataPushBO args) {
        recoverLogic(args);
    }

    @Override
    public void remove(DataPushBO args) {
        removeLogic(args);
    }

    public void updateLogic(DataPushBO dataPushBO) {
        if (ObjectUtil.hasEmpty(dataPushBO.getData(), dataPushBO.getDeptId(), dataPushBO.getOp(), dataPushBO.getIndexID())) {
            recordError(allPoints, dataPushBO, null, ErrorType._2B00001);
            return;
        }
        CourseManageForm courseManageForm = FormUtil.parseFormData(JSON.parseArray(dataPushBO.getData()), CourseManageForm.class);
        List<Kksz> list = MyUtils.isNotEmpty(courseManageForm.getKksz()) ? courseManageForm.getKksz() : Collections.singletonList(new Kksz());
        getOtherCourseManageInfo(dataPushBO, courseManageForm);
        addCourseInformation(dataPushBO, courseManageForm, list);
    }

    public void recoverLogic(DataPushBO dataPushBO) {
        if (ObjectUtil.hasEmpty(dataPushBO.getData(), dataPushBO.getDeptId(), dataPushBO.getOp(), dataPushBO.getIndexID())) {
            recordError(allPoints, dataPushBO, null, ErrorType._2B00001);
            return;
        }
        CourseManageForm courseManageForm = FormUtil.parseFormData(JSON.parseArray(dataPushBO.getData()), CourseManageForm.class);
        LambdaQueryWrapper<CultivationClassStartsInfoDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CultivationClassStartsInfoDetail::getCsFormUserId, dataPushBO.getFormUserId()).last("limit 1");
        CultivationClassStartsInfoDetail detail = classStartsInfoDetailService.getOne(wrapper);
        if (detail == null) {
            return;
        }
        detail.setStatus(0);
        classStartsInfoDetailService.updateById(detail);
        modifyDistributeNum(dataPushBO, detail);
        modifyStatus(dataPushBO, courseManageForm);
    }

    public void removeLogic(DataPushBO dataPushBO) {
        if (ObjectUtil.hasEmpty(dataPushBO.getData(), dataPushBO.getDeptId(), dataPushBO.getOp(), dataPushBO.getIndexID())) {
            recordError(allPoints, dataPushBO, null, ErrorType._2B00001);
            return;
        }
        CourseManageForm courseManageForm = FormUtil.parseFormData(JSON.parseArray(dataPushBO.getData()), CourseManageForm.class);
        LambdaQueryWrapper<CultivationClassStartsInfoDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CultivationClassStartsInfoDetail::getCsFormUserId, dataPushBO.getFormUserId()).last("limit 1");
        CultivationClassStartsInfoDetail detail = classStartsInfoDetailService.getOne(wrapper);
        if (detail == null || StringUtils.isBlank(detail.getClassFormNum())) {
            return;
        }
        detail.setStatus(1);
        classStartsInfoDetailService.updateById(detail);
        //同步删除分配学生
        LambdaQueryWrapper<CultivationClassStartsStudent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CultivationClassStartsStudent::getCourseManageDataId, detail.getCsFormUserId());
        cultivationClassStartsStudentService.remove(queryWrapper);
        modifyDistributeNum(dataPushBO, detail);
        modifyStatus(dataPushBO, courseManageForm);
        //删除开课信息表
        removeCourseInformation(dataPushBO);
    }

    private CourseInformationForm getCourseInformationForm(CourseManageForm courseManageForm, Kksz kksz, DataPushBO dataPushBO, Integer no, String groupName, String groupNo) {
        CourseInformationForm courseInformationForm = new CourseInformationForm();
        courseInformationForm.setKkxxb_kkxq(courseManageForm.getKkgl_kkxq()).setKkxxb_kkxqxq(courseManageForm.getKkgl_kkxqxq());
        courseInformationForm.setKkxxb_kkbm(courseManageForm.getKkgl_kkyxyx()).setKkxxb_kkjys(courseManageForm.getKkgl_kkjys());
        courseInformationForm.setKkxxb_zymc(courseManageForm.getKkgl_zymc()).setKkxxb_jxbrs(courseManageForm.getKkgl_jxbrs());
        courseInformationForm.setKkxxb_zybh(courseManageForm.getKkgl_zybh()).setKkxxb_njnj(courseManageForm.getKkgl_nj());
        courseInformationForm.setKkxxb_kcmc(courseManageForm.getKkgl_kcmc()).setKkxxb_kcbh(courseManageForm.getKkgl_kcbh());
        courseInformationForm.setKkxxb_xf(courseManageForm.getKkgl_xf() != null ? Float.valueOf(courseManageForm.getKkgl_xf()) : null);
        courseInformationForm.setKkxxb_kcxz(courseManageForm.getKkgl_kcxz()).setKkxxb_ksxs(courseManageForm.getKkgl_ksxs());
        courseInformationForm.setKkxxb_jxbzclx(courseManageForm.getKkgl_jxbzclx()).setKkxxb_jxbzc(courseManageForm.getKkgl_jxbzc());
        courseInformationForm.setKkxxb_jxbzcbh(courseManageForm.getKkgl_jxbzcbh()).setKkxxb_sfxzbcb(courseManageForm.getKkgl_sfxzbcb());
        courseInformationForm.setKkxxb_jxbbzid(courseManageForm.getKkgl_jxbbh()).setKkxxb_sfcsjjxhj(courseManageForm.getKkgl_sfcsjjxhj());
        courseInformationForm.setKkxxb_lilunxs(courseManageForm.getKkgl_lilunxs()).setKkxxb_shijixs(courseManageForm.getKkgl_shijixs());
        courseInformationForm.setKkxxb_shangjixs(courseManageForm.getKkgl_shangjixs()).setKkxxb_shiyanxs(courseManageForm.getKkgl_shiyanxs());
        courseInformationForm.setKkxxb_qtxs(courseManageForm.getKkgl_qtxs()).setKkxxb_zongxs(courseManageForm.getKkgl_zongxs());
        courseInformationForm.setKkxxb_kkxs(courseManageForm.getKkgl_kkxs()).setKkxxb_sfxk(courseManageForm.getKkgl_sfxk());
        courseInformationForm.setKkxxb_kxxb(courseManageForm.getKkgl_kxxb()).setKkxx_xxrs(courseManageForm.getKkgl_xxrs());
        courseInformationForm.setKkxxb_kkgldataid(String.valueOf(dataPushBO.getIndexID())).setKkxxb_jxbbzmc(groupName);
        courseInformationForm.setKkxxb_jxbbzid(groupNo).setKkxxb_kcxs(1).setKkxxb_kkyxyx(courseManageForm.getKkgl_zyssxb());
        courseInformationForm.setKkxxb_bz(courseManageForm.getKkgl_bz()).setKkxxb_xz(courseManageForm.getKkgl_xz());
        courseInformationForm.setKkxxb_kkxqjc(courseManageForm.getKkgl_kkxqjc()).setJw_jxjhformUserId(courseManageForm.getKkgl_jxbbzid());
        courseInformationForm.setJw_pyfaformUserId(courseManageForm.getJw_pyfaformUserId()).setKkxxb_sfpke(FormUtils.YES);
        courseInformationForm.setKkxxb_zyfx(courseManageForm.getKkgl_zyfx());
        if (BeanUtil.isNotEmpty(kksz, "")) {
            List<IdName> teacherList = kksz.getKkgl_skjs();
            List<IdName> assistantList = kksz.getKkgl_zj();
            List<IdName> scoreList = kksz.getKkgl_cjlrjs();
            courseInformationForm.setKkxxb_zc(kksz.getKkgl_zc());
            courseInformationForm.setKkxxb_zhouxs(kksz.getKkgl_zks() != null ? kksz.getKkgl_zks() : "0");
            courseInformationForm.setKkxxb_lpjc(kksz.getKkgl_lpjc() != null ? String.valueOf(kksz.getKkgl_lpjc()) : "0");
            courseInformationForm.setKkxxb_ltgz(kksz.getKkgl_lpgz());
            courseInformationForm.setKkxxb_skjsxm(kksz.getKkgl_skjs());
            courseInformationForm.setKkxxb_zjxm(kksz.getKkgl_zj());
            courseInformationForm.setKkxxb_jslx(kksz.getKkgl_jslx());
            courseInformationForm.setKkxxb_jsmc(kksz.getKkgl_jsmc());
            courseInformationForm.setKkxxb_jsbh(kksz.getKkgl_jsbh());
            courseInformationForm.setKkxxb_sflcj(kksz.getKkgl_sflcj());
            courseInformationForm.setKkxxb_cjlrjs(kksz.getKkgl_cjlrjs());
            if (StringUtils.isNotBlank(kksz.getKkgl_zc())) {
                String weekStr = kksz.getKkgl_zc().replaceAll("\\s+", "");
                int teachingWeeks = Arrays.stream(weekStr.split("[,，]")).mapToInt(range -> {
                    String[] rangeParts = range.split("-");
                    return Integer.parseInt(rangeParts[rangeParts.length - 1]) - Integer.parseInt(rangeParts[0]) + 1;
                }).sum();
                courseInformationForm.setKkxxb_jxzs(String.valueOf(teachingWeeks));
            }
            if (MyUtils.isNotEmpty(teacherList)) {
                List<String> skjsghList = new ArrayList<>();
                List<String> skjsList = new ArrayList<>();
                for (IdName idName : teacherList) {
                    TeacherInfoForm teacherInfoForm = FormUtils.getOne(SearchStrBodyType.AND, dataPushBO.getDeptId(), TeacherInfoForm.ALIAS,
                            searchStrBody -> searchStrBody.createAndAdd(TeacherInfoForm::getJsjbxx_xmlxr).eq(idName.getPuid()), TeacherInfoForm.class);
                    if (teacherInfoForm != null) {
                        skjsghList.add(teacherInfoForm.getJsjbxx_jsgh());
                        skjsList.add(teacherInfoForm.getJsjbxx_xm());
                    }
                }
                courseInformationForm.setKkxxb_skjsgh(String.join(",", skjsghList));
                courseInformationForm.setKkxxb_skjs(String.join(",", skjsList));
            }
            if (MyUtils.isNotEmpty(assistantList)) {
                List<String> zjjsghList = new ArrayList<>();
                for (IdName idName : assistantList) {
                    TeacherInfoForm teacherInfoForm = FormUtils.getOne(SearchStrBodyType.AND, dataPushBO.getDeptId(), TeacherInfoForm.ALIAS, searchStrBody -> searchStrBody.createAndAdd(TeacherInfoForm::getJsjbxx_uid).eq(idName.getPuid()), TeacherInfoForm.class);
                    if (teacherInfoForm != null) {
                        zjjsghList.add(teacherInfoForm.getJsjbxx_jsgh());
                    }
                }
                courseInformationForm.setKkxxb_zjjsgh(String.join(",", zjjsghList));
            }
            if (MyUtils.isNotEmpty(scoreList)) {
                List<String> cjlrjsghList = new ArrayList<>();
                List<String> cjlrjsxmList = new ArrayList<>();
                for (IdName idName : scoreList) {
                    TeacherInfoForm teacherInfoForm = FormUtils.getOne(SearchStrBodyType.AND, dataPushBO.getDeptId(), TeacherInfoForm.ALIAS, searchStrBody -> searchStrBody.createAndAdd(TeacherInfoForm::getJsjbxx_uid).eq(idName.getPuid()), TeacherInfoForm.class);
                    if (teacherInfoForm != null) {
                        cjlrjsghList.add(teacherInfoForm.getJsjbxx_jsgh());
                        cjlrjsxmList.add(teacherInfoForm.getJsjbxx_xm());
                    }
                }
                courseInformationForm.setKkxxb_cjlrjsgh(String.join(",", cjlrjsghList));
                courseInformationForm.setKkxxb_cjlrjsxm(String.join(",", cjlrjsxmList));
            }
        }
        //教材信息
        if (MyUtils.isNotEmpty(courseManageForm.getKkgl_jc())) {
            List<Kkxxb_jcxx> materialList = new ArrayList<>();
            for (Kkgl_jc material : courseManageForm.getKkgl_jc()) {
                materialList.add(new Kkxxb_jcxx().setKkxxb_isbn(material.getKkgl_isbn()).setKkxxb_jch(material.getKkgl_jch()).setKkxxb_jcmc(material.getKkgl_jcmc()));
            }
            courseInformationForm.setKkxxb_jcxx(materialList);
        }
        //智慧大脑
        courseInformationForm.setKkxxb_ssxq(courseManageForm.getKkgl_ssxq()).setKkxxb_kcshx(courseManageForm.getKkgl_kcshx());
        courseInformationForm.setKkxxb_kcfl(courseManageForm.getKkgl_kcfl()).setKkxxb_xklb(courseManageForm.getKkgl_xklb());
        courseInformationForm.setKkxxb_sfzyhxk(courseManageForm.getKkgl_sfzyhxk()).setKkxxb_sfxskc(courseManageForm.getKkgl_sfxskc());
        courseInformationForm.setKkxxb_xskcwz(courseManageForm.getKkgl_xskcwz()).setKkxxb_sfszsf(courseManageForm.getKkgl_sfszsf());
        courseInformationForm.setKkxxb_kczy(courseManageForm.getKkgl_kczy()).setKkxxb_xnfzks(courseManageForm.getKkgl_xnfzks());
        courseInformationForm.setKkxxb_xnfzxm(courseManageForm.getKkgl_xnfzxm()).setKkxxb_syxm(courseManageForm.getKkgl_syxm());
        courseInformationForm.setKkxxb_sxxm(courseManageForm.getKkgl_sxxm()).setKkxxb_sxixm(courseManageForm.getKkgl_sxixm());
        courseInformationForm.setKkxxb_sfkzrtkc(courseManageForm.getKkgl_xfkzrtkc()).setKkxxb_skfs(courseManageForm.getKkgl_skfs());
        courseInformationForm.setKkxxb_zsh(courseManageForm.getKkgl_zsh()).setKkxxb_xgjsmc(courseManageForm.getKkgl_xgjsmc());
        CourseLibraryForm courseLibraryForm = FormUtils.getOne(SearchStrBodyType.AND, dataPushBO.getDeptId(), CourseLibraryForm.ALIAS, searchStrBody -> searchStrBody.createAndAdd(CourseLibraryForm::getKck_kcbh).eq(courseManageForm.getKkgl_kcbh()), CourseLibraryForm.class);
        if (courseLibraryForm != null) {
            courseInformationForm.setKkxxb_ssxqbh(courseLibraryForm.getKck_ssxqbh()).setKkxxb_sfzyhxkcm(courseLibraryForm.getKck_sfzyhxkcm());
        }
        //数据联动项处理
        SearchParam examSearchParam = SearchParam.builder().deptId(dataPushBO.getDeptId()).appName("218617").build();
        SearchStrBody examSearchStrBody = SearchStrBody.and();
        examSearchStrBody.createAndAdd("exam_form", FormComponentConstants.dan_hang_shu_ru).eq(courseManageForm.getKkgl_ksxs());
        JSONObject examJson = FormTemplate.search(examSearchParam, examSearchStrBody);
        if (FormUtil.isSuccess(examJson) && examJson.containsKey("data")) {
            JSONArray dataArray = examJson.getJSONObject("data").getJSONArray("dataList");
            for (int j = 0; j < dataArray.size(); j++) {
                JSONObject dataJson = dataArray.getJSONObject(j);
                Map<String, String> formJsonData = FormUtils.getFormJsonData(dataJson, "form_code");
                courseInformationForm.setKkxxb_ksxsm(formJsonData.get("form_code"));
            }
        }
        SearchParam courseSearchParam = SearchParam.builder().deptId(dataPushBO.getDeptId()).appName("sfbzdm").build();
        SearchStrBody courseSearchStrBody = SearchStrBody.and();
        courseSearchStrBody.createAndAdd("mc", FormComponentConstants.dan_hang_shu_ru).eq(courseManageForm.getKkgl_xfkzrtkc());
        JSONObject courseJson = FormTemplate.search(courseSearchParam, courseSearchStrBody);
        if (FormUtil.isSuccess(courseJson) && courseJson.containsKey("data")) {
            JSONArray dataArray = courseJson.getJSONObject("data").getJSONArray("dataList");
            for (int j = 0; j < dataArray.size(); j++) {
                JSONObject dataJson = dataArray.getJSONObject(j);
                Map<String, String> formJsonData = FormUtils.getFormJsonData(dataJson, "dm");
                courseInformationForm.setKkxxb_sfkzrtkcm(formJsonData.get("dm"));
            }
        }
        SearchParam clazzSearchParam = SearchParam.builder().deptId(dataPushBO.getDeptId()).appName("218612").build();
        SearchStrBody clazzSearchStrBody = SearchStrBody.and();
        clazzSearchStrBody.createAndAdd("teacher_type", FormComponentConstants.dan_hang_shu_ru).eq(courseManageForm.getKkgl_skfs());
        JSONObject clazzJson = FormTemplate.search(clazzSearchParam, clazzSearchStrBody);
        if (FormUtil.isSuccess(clazzJson) && clazzJson.containsKey("data")) {
            JSONArray dataArray = clazzJson.getJSONObject("data").getJSONArray("dataList");
            for (int j = 0; j < dataArray.size(); j++) {
                JSONObject dataJson = dataArray.getJSONObject(j);
                Map<String, String> formJsonData = FormUtils.getFormJsonData(dataJson, "teacher_dm");
                courseInformationForm.setKkxxb_skfsm(formJsonData.get("teacher_dm"));
            }
        }
        getCourseInfo(courseInformationForm, dataPushBO);
        return courseInformationForm;
    }

    private void modifyStatus(DataPushBO dataPushBO, CourseManageForm courseManageForm) {
        TeachPlanForm tpf = FormUtils.getById(dataPushBO.getDeptId(), TeachPlanForm.ALIAS, courseManageForm.getKkgl_jxbbzid(), TeachPlanForm.class);
        if (tpf == null) {
            return;
        }
        int count = Math.toIntExact(classStartsInfoService.lambdaQuery().eq(CultivationClassStartsInfo::getFormUserId, courseManageForm.getKkgl_jxbbzid()).eq(CultivationClassStartsInfo::getStatus, 1).last(" and class_num = distribute_num").count());
        Integer clazzCount = FormUtils.count(SearchStrBodyType.AND, dataPushBO.getDeptId(), TeachingPlanClassForm.ALIAS, searchStrBody -> searchStrBody.createAndAdd(TeachingPlanClassForm::getJxjhbjb_jxjhdataid).eq(courseManageForm.getKkgl_jxbbzid()));
        Long total = classStartsInfoDetailService.lambdaQuery().eq(CultivationClassStartsInfoDetail::getFormUserId, courseManageForm.getKkgl_jxbbzid()).eq(CultivationClassStartsInfoDetail::getStatus, 0).count();
        String courseStatus = count == clazzCount ? "完成开课" : "部分开课";
        String clazzStatus = count == clazzCount ? "完成开班" : "部分开班";
        courseStatus = total == 0 ? "待开课" : courseStatus;
        clazzStatus = total == 0 ? "待开班" : clazzStatus;
        TeachPlanForm teachPlanForm = new TeachPlanForm();
        teachPlanForm.setJxjhgl_kbzt(clazzStatus);
        if ("通过".equals(courseManageForm.getKkgl_shzt())) {
            teachPlanForm.setJxjhgl_kkzt(courseStatus);
        }
        checkAlterThenRecordLog(FormUtils.update(dataPushBO.getDeptId(), courseManageForm.getKkgl_jxbbzid(), TeachPlanForm.ALIAS, teachPlanForm), TeachPlanForm.POINT, dataPushBO, ErrorType._2B00035, msg -> new Object[]{"表单响应结果：" + msg}, null, () -> JSONObject.toJSONString(tpf), () -> JSONObject.toJSONString(teachPlanForm), () -> "更新教学计划开班、开课状态");
    }

    private void modifyDistributeNum(DataPushBO dataPushBO, CultivationClassStartsInfoDetail detail) {
        String[] codeArray = StrUtil.split(detail.getClassFormCode(), ",");
        String[] numArray = detail.getClassFormNum().split(",");
        for (int i = 0; i < codeArray.length; i++) {
            LambdaQueryWrapper<CultivationClassStartsInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CultivationClassStartsInfo::getFormUserId, detail.getFormUserId()).eq(CultivationClassStartsInfo::getCode, codeArray[i]).last("limit 1");
            CultivationClassStartsInfo classStartsInfo = classStartsInfoService.getOne(queryWrapper);
            if (classStartsInfo == null) {
                continue;
            }
            int distributeNum = classStartsInfo.getDistributeNum() == 0 ? 0 : classStartsInfo.getDistributeNum() - Integer.parseInt(numArray[i]);
            distributeNum = FormUtils.recover(dataPushBO.getOp()) ? classStartsInfo.getDistributeNum() + Integer.parseInt(numArray[i]) : distributeNum;
            distributeNum = Math.max(distributeNum, 0);
            classStartsInfo.setDistributeNum(distributeNum);
            classStartsInfo.setStatus(FormUtils.recover(dataPushBO.getOp()) ? 1 : 0);
            classStartsInfoService.updateById(classStartsInfo);
            if (distributeNum == 0) {
                CourseInformationForm courseInformationForm = FormUtils.getOne(SearchStrBodyType.AND, dataPushBO.getDeptId(), CourseInformationForm.ALIAS, searchStrBody -> searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_kkgldataid).eq(String.valueOf(dataPushBO.getIndexID())), CourseInformationForm.class);
                if (courseInformationForm == null) {
                    continue;
                }
                SearchStrBody searchStrBody = SearchStrBody.and();
                searchStrBody.createAndAdd(TeachClassGroupForm::getJxbbz_bzid).eq(courseInformationForm.getKkxxb_jxbbzid());
                List<TeachClassGroupForm> list = FormUtils.getFormData(dataPushBO.getDeptId(), TeachClassGroupForm.ALIAS, searchStrBody, TeachClassGroupForm.class);
                checkRemoveThenRecordLog(FormUtils.batchDelete(dataPushBO.getDeptId(), TeachClassGroupForm.ALIAS, searchStrBody), TeachClassGroupForm.POINT, dataPushBO, ErrorType._2B00032, msg -> new Object[]{"编组id为：" + courseInformationForm.getKkxxb_jxbbzid() + ",[表单响应结果：" + msg + "]"}, null, () -> JSONObject.toJSONString(list), () -> "删除编组信息");
            }
        }
    }

    private void addCourseInformation(DataPushBO dataPushBO, CourseManageForm courseManageForm, List<Kksz> list) {
        boolean flag = "其他（拆班，拆合班，选修课）".equals(courseManageForm.getKkgl_jxbzclx()) && !courseManageForm.getKkgl_jxbzcbh().contains(",");
        String term = courseManageForm.getKkgl_kkxq().replace("-", "");
        String groupName = !flag ? courseManageForm.getKkgl_jxbmc() + "b1" : term + courseManageForm.getKkgl_kcmc() + courseManageForm.getKkgl_nj() + StrUtil.join(",", courseManageForm.getKkgl_jxbzc()) + "a1";
        String groupNo = !flag ? courseManageForm.getKkgl_jxbbh() + "b1" : term + courseManageForm.getKkgl_kcbh() + courseManageForm.getKkgl_nj() + courseManageForm.getKkgl_jxbzcbh() + "a1";
        String groupType = !flag ? "开课自动编组" : "行政班拆班自动编组";
        groupName = list.size() == 1 ? "" : groupName;
        groupNo = list.size() == 1 ? "" : groupNo;
        String enterScore = FormUtils.NO;
        List<CourseInformationForm> courseInformationFormList = FormUtils.listAll(SearchStrBodyType.AND, dataPushBO.getDeptId(), CourseInformationForm.ALIAS, searchStrBody -> searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_kkgldataid).eq(String.valueOf(dataPushBO.getIndexID())), CourseInformationForm.class);
        if (MyUtils.isNotEmpty(list) && (list.size() != courseInformationFormList.size() || list.size() > 1) && !"待审核".equals(courseManageForm.getKkgl_shzt())) {
            removeCourseInformation(dataPushBO);
            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(2000));
            int no = 0;
            for (Kksz kksz : list) {
                no++;
                enterScore = list.size() > 1 && "是".equals(kksz.getKkgl_sflcj()) ? FormUtils.YES : enterScore;
                CourseInformationForm courseInformationForm = getCourseInformationForm(courseManageForm, kksz, dataPushBO, no, groupName, groupNo);
                checkAddThenRecordLog(FormUtils.save(dataPushBO.getDeptId(), dataPushBO.getUid(), CourseInformationForm.ALIAS, courseInformationForm), CourseInformationForm.POINT, dataPushBO, ErrorType._2B00026, msg -> new Object[]{"表单响应结果：" + msg}, () -> JSONObject.toJSONString(courseInformationForm), () -> "开课管理流转开课信息表");
            }
        }
        if (MyUtils.isNotEmpty(list) && list.size() == 1 && courseInformationFormList.size() == 1) {
            Kksz kksz = list.get(0);
            CourseInformationForm cif = courseInformationFormList.get(0);
            CourseInformationForm courseInformationForm = getCourseInformationForm(courseManageForm, kksz, dataPushBO, 1, groupName, groupNo);
            checkAlterThenRecordLog(FormUtils.update(dataPushBO.getDeptId(), String.valueOf(cif.getRowInfo().getFormUserId()), CourseInformationForm.ALIAS, courseInformationForm), CourseInformationForm.POINT, dataPushBO, ErrorType._2B00026, msg -> new Object[]{"表单响应结果：" + msg}, () -> JSONObject.toJSONString(cif), () -> JSONObject.toJSONString(courseInformationForm), () -> "开课管理流转开课信息表");
        }
        String key = "groupNo_" + groupNo;
        String redisGroupNo = RedisUtils.str(key);
        String finalGroupNo = groupNo;
        int count = FormUtils.count(SearchStrBodyType.AND, dataPushBO.getDeptId(), TeachClassGroupForm.ALIAS, searchStrBody -> searchStrBody.createAndAdd(TeachClassGroupForm::getJxbbz_bzid).eq(finalGroupNo));
        if (count == 0 && StringUtils.isBlank(redisGroupNo) && list.size() > 1) {
            RedisUtils.set(key, IdUtil.fastSimpleUUID(), 30000);
            TeachClassGroupForm teachClassGroupForm = new TeachClassGroupForm().setJxbbz_bzid(groupNo).setJxbbz_bzlx(groupType).setJxbbz_cjlrjslxr(list.get(0).getKkgl_cjlrjs())
                    .setJxbbz_bzmc(groupName).setJxbbz_sfazpk(FormUtils.NO).setJxbbz_sfazxk(FormUtils.NO).setJxbbz_sfazlrcj(enterScore).setJxbbz_kkxq(courseManageForm.getKkgl_kkxq());
            JSONObject saveJson = FormUtils.save(dataPushBO.getDeptId(), dataPushBO.getUid(), TeachClassGroupForm.ALIAS, teachClassGroupForm);
            checkAddThenRecordLog(saveJson, TeachClassGroupForm.POINT, dataPushBO, ErrorType._2B00026, msg -> new Object[]{"表单响应结果：" + msg}, () -> JSONObject.toJSONString(teachClassGroupForm), () -> "生成编组数据");
        }
    }

    private void removeCourseInformation(DataPushBO args) {
        //删除开课信息表
        SearchStrBody strBody = SearchStrBody.and();
        strBody.createAndAdd(CourseInformationForm::getKkxxb_kkgldataid).eq(String.valueOf(args.getIndexID()));
        List<CourseInformationForm> courseInformationForm = FormUtils.getFormData(args.getDeptId(), CourseInformationForm.ALIAS, strBody, CourseInformationForm.class);
        checkRemoveThenRecordLog(FormUtils.batchDelete(args.getDeptId(), CourseInformationForm.ALIAS, strBody), CourseInformationForm.POINT, args, ErrorType._2B00033, msg -> new Object[]{"开课管理行id为：" + args.getIndexID() + ",[表单响应结果：" + msg + "]"}, () -> JSONObject.toJSONString(courseInformationForm), () -> "开课管理删除同步删除开课信息表");
    }

    /**
     * @param args             顶部按钮参数对象
     * @param courseManageForm 开课管理参数
     * @description 获取开课管理其它信息
     * <AUTHOR>
     * @date 2024/10/9 14:50
     * @retrun void
     **/
    private void getOtherCourseManageInfo(DataPushBO args, CourseManageForm courseManageForm) {
        TeachPlanForm teachPlanForm = FormUtils.getById(args.getDeptId(), TeachPlanForm.ALIAS, courseManageForm.getKkgl_jxbbzid(), TeachPlanForm.class);
        if (teachPlanForm != null) {
            MajorInfoForm one = FormUtils.getOne(SearchStrBodyType.AND, args.getDeptId(), MajorInfoForm.ALIAS, searchStrBody ->
                    searchStrBody.createAndAdd(MajorInfoForm::getZysj_zybh).eq(teachPlanForm.getJxjhgl_zybh()), MajorInfoForm.class);
            if (one != null) {
                courseManageForm.setKkgl_xz(one.getZysj_xz());
            }
            MajorCourseSetForm majorCourseSetForm = FormUtils.getOne(SearchStrBodyType.AND, args.getDeptId(), MajorCourseSetForm.ALIAS,
                    body -> body.createAndAdd(MajorCourseSetForm::getZykzdbh).eq(teachPlanForm.getZykszbh()), MajorCourseSetForm.class);
            if (majorCourseSetForm != null) {
                TrainingProgramForm trainingProgramForm = FormUtils.getOne(SearchStrBodyType.AND, args.getDeptId(), TrainingProgramForm.ALIAS,
                        body -> body.createAndAdd(TrainingProgramForm::getPyfagl_bh).eq(majorCourseSetForm.getZyksz_pyfahbh()), TrainingProgramForm.class);
                if (trainingProgramForm != null) {
                    CultivationMajorCourseSet cultivationMajorCourseSet = majorCourseSetService.lambdaQuery()
                            .eq(CultivationMajorCourseSet::getFormUserId, trainingProgramForm.getRowInfo().getFormUserId())
                            .eq(CultivationMajorCourseSet::getCourseId, teachPlanForm.getJxjhgl_kcid()).last("limit 1").one();
                    courseManageForm.setKkgl_bz(cultivationMajorCourseSet != null ? cultivationMajorCourseSet.getNotes() : "");
                }
            }
        }
    }

    /**
     * @param info 开课信息表详情
     * @param bo   数据推送参数对象
     * @description 补充课程相关信息
     * <AUTHOR>
     * @date 2025/6/6 19:27
     * @retrun void
     **/
    private void getCourseInfo(CourseInformationForm info, DataPushBO bo) {
        try {
            CourseLibraryForm one = FormUtils.getOne(SearchStrBodyType.AND, bo.getDeptId(), CourseLibraryForm.ALIAS, searchStrBody -> {
                searchStrBody.createAndAdd(CourseLibraryForm::getKck_kcbh).eq(info.getKkxxb_kcbh());
            }, CourseLibraryForm.class);
            if (one != null) {
                info.setKkxxb_gsbn(one.getKck_kkssbm())
                        .setKkxxb_gsjyz(one.getKck_kkssjys());
            }
            TeachPlanForm byId = FormUtils.getById(bo.getDeptId(), TeachPlanForm.ALIAS, info.getJw_jxjhformUserId(), TeachPlanForm.class);
            if (byId != null) {
                CultivationMajorCourseSet mcs = majorCourseSetService
                        .lambdaQuery()
                        .eq(CultivationMajorCourseSet::getFormUserId, byId.getJw_pyformUserId())
                        .eq(CultivationMajorCourseSet::getCourseId, info.getKkxxb_kcbh())
                        .last("limit 1")
                        .one();
                if (mcs != null && StringUtils.isNotBlank(mcs.getChooseCompulsory())) {
                    info.setKkxxb_xbx(mcs.getChooseCompulsory());
                }
            }
        } catch (Exception e) {
            log.error("补充课程相关信息异常", e);
        }

    }
}
