body {
  background-color: #F7F8FA;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.flex {
  display: flex;
  align-items: center;
}
.layui-table-view .layui-table tr th > div {
  color: #4E5969;
  font-weight: bold;
}
.layui-table-filter i {
  color: #4E5969;
}
.layui-table-view .layui-table td {
  padding: 10px 16px;
}
.z-main {
  margin: 20px;
  background-color: #ffffff;
  overflow: hidden;
  min-height: calc(100vh - 68px);
  border-radius: 6px;
}
.z-main .z-title {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #E8EBF1;
}
.z-main .z-title .z-title-con {
  display: flex;
  align-items: center;
}
.z-main .z-title .z-title-con h3 {
  font-size: 16px;
  line-height: 22px;
  color: #1D2129;
  padding-left: 9px;
  position: relative;
  margin-left: 30px;
}
.z-main .z-title .z-title-con h3::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 3px;
}
.z-main .z-title .z-title-con span {
  color: #86909C;
  font-size: 14px;
  margin-left: 16px;
  margin-top: 2px;
}
.z-main .z-title .z-btn {
  display: flex;
  align-items: center;
}
.z-main .z-title .z-btn #clearData {
  height: 36px;
  line-height: 34px;
  padding: 0 20px;
  font-size: 14px;
  margin-right: 16px;
  cursor: pointer;
  border-radius: 4px;
  border: 1px solid #F76560;
  color: #F76560;
  background: #FFF;
  box-shadow: 0px 0px 10px 0px rgba(247, 101, 96, 0.2);
}
.z-main .z-title .z-btn #save {
  cursor: pointer;
  height: 36px;
  line-height: 34px;
  margin-right: 30px;
  border-radius: 4px;
  background: #4D88FF;
  border: 1px solid #4D88FF;
  padding: 0 20px;
  font-size: 14px;
  color: #fff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  width: 96px;
}
.z-main .z-con {
  display: flex;
  overflow: hidden;
  position: relative;
}
.z-main .z-con .z-menu {
  width: 268px;
  border-right: 1px solid #E8EBF1;
}
.z-main .z-con .z-menu .z-nav {
  border-radius: 4px;
  overflow: hidden;
  color: #1D2129;
  font-size: 14px;
  margin: 24px 30px;
}
.z-main .z-con .z-menu .z-nav ul {
  display: flex;
  align-items: center;
  height: 26px;
  background-color: #F1F3F6;
  padding: 4px;
}
.z-main .z-con .z-menu .z-nav ul li {
  flex: 1;
  text-align: center;
  line-height: 26px;
  cursor: pointer;
  background: #F2F3F5;
}
.z-main .z-con .z-menu .z-nav ul li.active {
  background: #fff;
  color: #4D88FF;
  border-radius: 4px;
  box-shadow: 0px 0px 4px 0px rgba(77, 136, 255, 0.15);
}
.z-main .z-con .z-menu .z-search {
  display: flex;
  align-items: center;
  margin: 0 30px 24px;
}
.z-main .z-con .z-menu .z-search .search-icon {
  width: 36px;
  height: 34px;
  background: #e1ebff url('/css/examination/images/icon-search.png') no-repeat center;
  background-size: 16px;
  border-radius: 4px;
  margin-left: 8px;
}
.z-main .z-con .z-menu .z-search .search-box {
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  width: 164px;
  height: 36px;
  padding: 0 10px 0 30px;
  box-sizing: border-box;
  font-size: 14px;
  background: url('/css/examination/images/search-icon.png') no-repeat 10px center;
}
.z-main .z-con .z-menu .z-search .search-box::placeholder {
  color: #86909C;
}
.z-main .z-con .z-menu .z-search .j-search-con.single-box .j-select-year ul li.active {
  background-image: url('/css/examination/images/check2.png');
  background-size: 16px;
  background-color: unset;
  color: #4E5969;
  background-position: 90% center;
}
.z-main .z-con .z-menu .z-search .j-search-con.single-box .j-select-year ul li:hover {
  background-color: #E1EBFF;
  color: #4D88FF;
}
.z-main .z-con .z-menu .z-box {
  width: 100%;
}
.z-main .z-con .z-menu .z-box .title {
  margin: 0 30px 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #86909C;
  height: 20px;
  overflow: hidden;
}
.z-main .z-con .z-menu .z-box .con-list {
  margin-left: 30px;
  overflow: hidden;
  overflow-y: auto;
  height: calc(100vh - 328px);
}
.z-main .z-con .z-menu .z-box .con-list ul {
  margin-right: 30px;
  width: 208px;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item {
  height: 48px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-sizing: border-box;
  cursor: pointer;
  color: #1D2129;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item.slide .arrow {
  transform: rotate(0);
}
.z-main .z-con .z-menu .z-box .con-list ul li .item .arrow {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #4E5969;
  transform: rotate(-90deg);
}
.z-main .z-con .z-menu .z-box .con-list ul li .item h3 {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 400;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item span.remaining {
  margin-left: 8px;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item span.check {
  width: 16px;
  height: 16px;
  margin-left: 8px;
  border: 1px solid #C9CDD4;
  border-radius: 3px;
  background-size: 16px;
  box-sizing: border-box;
  display: none;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item span.check.check-half {
  border: 1px solid #4D88FF;
  background: #4d88ff url('/css/examination/images/check1.png') no-repeat center;
  background-size: 10px;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item span.check.checked {
  border: 1px solid #4D88FF;
  background: #4d88ff url('/css/examination/images/check.png') no-repeat center;
  background-size: 10px;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item:hover {
  background-color: #E1EBFF;
  color: #4D88FF;
  border-radius: 4px;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item:hover .arrow {
  color: #4D88FF;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item.parentActive {
  background: #FAFBFC;
  border-radius: 4px;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item.active {
  background: #4D88FF;
  border-radius: 4px;
  color: #fff;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item.active .arrow {
  color: #fff;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item.active h3 {
  font-weight: bold;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item.active span.check {
  border: 1px solid #fff;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item.active span.check.checked {
  border: 1px solid #fff;
}
.z-main .z-con .z-menu .z-box .con-list > ul > li > ul {
  display: none;
}
.z-main .z-con .z-batch {
  display: flex;
  align-items: center;
  height: 56px;
  border-top: 1px dashed #E5E6EB;
  margin: 0 30px;
  justify-content: space-between;
  overflow: hidden;
}
.z-main .z-con .z-batch .z-batch-wrap {
  display: flex;
  align-items: center;
  color: #1D2129;
  font-size: 14px;
}
.z-main .z-con .z-batch .z-batch-wrap .switch {
  margin-left: 14px;
  width: 28px;
  height: 14px;
  background-color: #e4e5ea;
  border-radius: 3px;
  position: relative;
  cursor: pointer;
}
.z-main .z-con .z-batch .z-batch-wrap .switch span {
  display: block;
  background-color: #ffffff;
  width: 12px;
  height: 10px;
  border-radius: 2px;
  position: absolute;
  left: 2px;
  top: 2px;
  transition: left 0.2s linear;
}
.z-main .z-con .z-batch .z-batch-wrap .switch.switch-on {
  background-color: #4d88ff;
}
.z-main .z-con .z-batch .z-batch-wrap .switch.switch-on span {
  left: 14px;
}
.z-main .z-con .z-batch .slideLeft {
  width: 24px;
  height: 24px;
  background: #dbeaff url(/css/examination/images/fold.png) no-repeat center;
  background-size: 16px;
  transform: rotate(180deg);
  cursor: pointer;
}
.z-main .z-con .z-table {
  flex: 1;
  overflow: hidden;
  padding: 24px 30px;
  height: calc(100vh - 150px);
  overflow-y: auto;
}
.z-main .z-con .z-table .layui-table-view {
  border-radius: 8px;
}
.z-main .z-con .z-table .form-search {
  padding-bottom: 52px;
  position: relative;
  display: flex;
  flex-wrap: wrap;
}
.z-main .z-con .z-table .form-search.slideDown {
  padding-right: 68px;
}
.z-main .z-con .z-table .form-search.slideDown #formSlide span.arrow {
  transform: rotate(0deg);
}
.z-main .z-con .z-table .form-search .layui-inline {
  width: 25%;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}
.z-main .z-con .z-table .form-search .layui-inline .layui-form-label {
  padding: 7px 15px 7px 0;
}
.z-main .z-con .z-table .form-search .layui-inline .layui-input-inline {
  flex: 1;
  margin-right: 24px;
}
.z-main .z-con .z-table .form-search .layui-inline .j-search-con {
  width: 100%;
}
.z-main .z-con .z-table .form-search .layui-inline .j-search-con .j-select-year {
  min-width: 200px;
}
.z-main .z-con .z-table .form-search .layui-inline:last-child {
  margin-right: 0;
}
.z-main .z-con .z-table .form-search .layui-inline:nth-child(4n) .layui-input-inline {
  margin-right: 0;
}
.z-main .z-con .z-table .form-search .search-btn {
  width: auto;
  padding: 0 16px;
  height: 34px;
  line-height: 32px;
  background: #4d88ff;
  border-radius: 4px;
  text-align: center;
  color: #fff;
  display: inline-block;
  font-size: 14px;
  box-sizing: border-box;
  cursor: pointer;
}
.z-main .z-con .z-table .form-search .search-btn.clear {
  background-color: unset;
  border: 1px solid #4d88ff;
  color: #4d88ff;
  margin-right: 16px;
}
.z-main .z-con .z-table #formSlide {
  position: absolute;
  bottom: 75px;
  right: 0;
  color: #4D88FF;
  font-size: 14px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.z-main .z-con .z-table #formSlide span.arrow {
  width: 8px;
  height: 8px;
  background: url('/css/examination/images/arrow.png') no-repeat right center;
  background-size: 8px;
  margin-left: 4px;
  transform: rotate(180deg);
}
.z-main .z-con .z-table .z-opt {
  display: flex;
  align-items: center;
  color: #1D2129;
  font-size: 14px;
  position: absolute;
  bottom: 24px;
  right: 0;
}
.z-main .z-con .z-table .z-opt .z-export {
  color: #207af9;
  margin-right: 16px;
  padding-left: 20px;
  background: url('/css/examination/images/export.png') no-repeat left center;
  background-size: 16px;
  cursor: pointer;
}
.z-main .z-con .z-table .z-batch-course {
  display: flex;
  align-items: center;
  color: #1D2129;
  font-size: 14px;
}
.z-main .z-con .z-table .z-batch-course .switch {
  margin-left: 14px;
  width: 28px;
  height: 14px;
  background-color: #e4e5ea;
  border-radius: 3px;
  position: relative;
  cursor: pointer;
}
.z-main .z-con .z-table .z-batch-course .switch span {
  display: block;
  background-color: #ffffff;
  width: 12px;
  height: 10px;
  border-radius: 2px;
  position: absolute;
  left: 2px;
  top: 2px;
  transition: left 0.2s linear;
}
.z-main .z-con .z-table .z-batch-course .switch.switch-on {
  background-color: #4d88ff;
}
.z-main .z-con .z-table .z-batch-course .switch.switch-on span {
  left: 14px;
}
.z-main .z-con .z-table .z-btn {
  display: flex;
  justify-content: flex-end;
}
.z-main .z-con .z-table .t-room {
  line-height: 18px;
  font-size: 13px;
}
.z-main .z-con .z-table .t-class span {
  display: block;
}
.z-main .z-con .z-table .t-title {
  font-size: 14px;
  line-height: 20px;
  overflow: hidden;
}
.z-main .z-con .z-table .t-capacity {
  font-size: 13px;
  line-height: 18px;
}
.z-main .z-con .z-table .t-teacher {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.z-main .z-con .z-table .t-teacher .teacher {
  margin-right: 8px;
  line-height: 18px;
  font-size: 13px;
}
.z-main .z-con .z-table .t-teacher .teacher:last-child {
  margin-right: 0;
}
.z-main .z-con .z-table .t-check {
  width: 16px;
  height: 16px;
  border: 1px solid #C9CDD4;
  border-radius: 3px;
  background-size: 16px;
  box-sizing: border-box;
  position: absolute;
  left: 5px;
  top: 5px;
  box-shadow: 11px 5px 10px 0px #fff;
  display: none;
}
.z-main .z-con .z-table .t-check.checked {
  border: 1px solid #4D88FF;
  background: #4d88ff url(/css/examination/images/check.png) no-repeat center;
  background-size: 10px;
}
.z-main .z-con .z-table .blue {
  color: #4D88FF;
}
.z-main .z-con .z-table .t-con {
  padding: 8px 16px;
}
.z-main .z-con .z-table td {
  position: relative;
}
.z-main .z-con .z-table td .layui-table-cell {
  min-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.z-main .z-con .z-table td .t-status {
  position: absolute;
  right: 5px;
  top: 5px;
  border-radius: 2px;
  background: #3EB35A;
  padding: 0 4px;
  line-height: 16px;
  box-shadow: -11px 2px 10px 0px #ffffff;
}
.z-main .z-con .z-table td .t-status span {
  font-size: 12px;
  color: #FFF;
  transform: scale(0.83);
  display: inline-block;
}
.z-main .z-con .z-table td .t-status.t-status1 {
  background: #FFB026;
}
.z-main .z-con .z-table td .t-status.t-status2 {
  background: #FF96B9;
}
.z-main .z-con .z-table .layui-table-fixed-l .t-class {
  flex-direction: column;
  height: 84px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  align-items: center;
  flex-wrap: wrap;
  text-align: center;
  justify-content: center;
}
.z-main .z-con .z-table .layui-table-fixed-l td[lay-event="setCell"]:hover {
  position: relative;
}
.z-main .z-con .z-table .layui-table-fixed-l td[lay-event="setCell"]:hover .layui-table-cell {
  position: absolute;
  left: 0;
  top: 0;
  height: auto;
  z-index: 9;
  background-color: #fff;
  border: 1px solid #4D88FF;
  box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
}
.z-main .z-con .z-table .layui-table-fixed-l td[lay-event="setCell"]:hover .layui-table-cell .t-class {
  height: unset !important;
  height: auto;
  margin: 8px 0;
  -webkit-line-clamp: unset;
}
.z-main .z-con .z-table .layui-table-main td:hover {
  position: relative;
  width: 192px !important;
}
.z-main .z-con .z-table .layui-table-main td:hover .layui-table-cell {
  position: absolute;
  left: 0;
  top: 0;
  height: auto;
  z-index: 9;
  background-color: #fff;
  border: 1px solid #4D88FF;
  box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
}
.z-main .z-con .z-table .layui-table-main td:hover .layui-table-cell .t-con .t-teacher {
  margin-top: 6px;
}
.z-main .z-con .z-table .layui-table-main td:hover .layui-table-cell .t-con .t-teacher .teacher {
  padding: 0 14px;
  color: #4D88FF;
  background: #E1EBFF;
  border-radius: 12px;
  position: relative;
  margin-right: 12px;
  line-height: 34px;
  margin-bottom: 6px;
}
.z-main .z-con .z-table .layui-table-main td:hover .layui-table-cell .t-con .t-teacher .teacher .close {
  width: 14px;
  height: 14px;
  background: url('/css/examination/images/icon-del.png') no-repeat center;
  background-size: 14px;
  cursor: pointer;
  position: absolute;
  right: -4px;
  top: 0;
}
.z-main .z-con .z-table .layui-table-main td:hover .layui-table-cell .t-con .t-room {
  padding: 0 16px;
  color: #4D88FF;
  background: #E1EBFF;
  border-radius: 12px;
  position: relative;
  display: inline-block;
  line-height: 34px;
}
.z-main .z-con .z-table .layui-table-main td:hover .layui-table-cell .t-con .t-room .close {
  width: 14px;
  height: 14px;
  background: url('/css/examination/images/icon-del.png') no-repeat center;
  background-size: 14px;
  cursor: pointer;
  position: absolute;
  right: -4px;
  top: 0;
}
.j-search-con.single-box .j-select-year ul li[value=''] {
  background-image: unset;
  opacity: 0.8;
}
@media screen and (max-width: 1500px) {
  .z-main .z-con .z-table .form-search .j-search-con {
    width: 220px;
  }
}
@media screen and (max-width: 1300px) {
  .z-main .z-con .z-table .form-search .j-search-con {
    width: 170px;
  }
}
@media screen and (max-width: 1000px) {
  .z-main .z-con .z-table .form-search .j-search-con {
    width: 160px;
  }
}
.layui-table-view .layui-table td {
  height: 76px;
}
.layui-table-cell {
  font-size: 14px;
  padding: 0;
  height: auto;
  overflow: visible;
  text-overflow: inherit;
  white-space: normal;
  word-break: break-all;
  color: #4E5969;
  overflow: hidden;
}
.layui-table-cell .t-con {
  overflow: hidden;
  width: 100%;
}
#slideMenu {
  position: fixed;
  z-index: 99;
  left: -32px;
  width: 32px;
  height: 28px;
  background: #dbeaff url('/css/examination/images/fold.png') no-repeat center;
  background-size: 16px;
  cursor: pointer;
  transition: all 0.2s linear;
  bottom: 180px;
  border-radius: 0px 16px 16px 0px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
}
#slideMenu:hover {
  width: 40px;
  background: #dbeaff url('/css/examination/images/fold.png') no-repeat 16px center;
  background-size: 16px;
}
#slideMenu.expand {
  left: 0;
}
.layui-table thead tr,
.layui-table-header {
  background: #F1F3F6;
}
table tbody tr:nth-child(even) {
  background: #FAFBFC;
}
.layui-table-view .layui-table td[lay-event="setCell"] .layui-table-cell {
  display: flex;
  align-items: center;
}
.layui-table-view .layui-table td[lay-event="setCell"].gray {
  background-color: #e7e7e7 !important;
  pointer-events: none;
}
.layui-table-view .layui-table td[lay-event="setCell"].gray .t-con .t-teacher .teacher.height-light {
  color: #4D88FF;
  font-weight: bolder;
}
.layui-table-view .layui-table td[lay-event="setCell"].gray .t-con .t-room.height-light {
  color: #4D88FF;
  font-weight: bolder;
}
/*.layui-table-view .layui-table td[lay-event="setCell"].gray .layui-table-cell > div {*/
/*  display: none !important;*/
/*}*/
.layui-table-view .layui-table td[lay-event="setCell"].optCell .layui-table-cell {
  border: 1px solid #4D88FF !important;
  box-sizing: border-box;
  /* 蓝色投影 */
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  height: auto;
}
/* .layui-table-view .layui-table td[data-field="class"] .layui-table-cell {
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-height: unset;
} */
.layui-table-view .layui-table tr th > div {
  color: #4E5969;
  font-weight: bold;
}
.layui-table-filter i {
  color: #4E5969;
}
.layui-table-view .layui-table td {
  padding: 0;
}
.layui-table tbody tr:hover,
.layui-table-hover {
  background-color: #fff;
}
.layui-table td,
.layui-table th {
  min-height: auto;
  height: 36px;
  line-height: 36px;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item span.surplus {
  padding-left: 2px;
  display: none;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item span.surplus i {
  padding-left: 2px;
  color: #207af9;
}
.z-main .z-con .z-menu .z-box .con-list ul li .item.active span.surplus i {
  color: #fff;
}