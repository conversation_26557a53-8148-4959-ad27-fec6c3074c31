@font-face {
    font-family: 'icomoon';
    src:  url('./fonts/icomoon.eot?i9r3w7');
    src:  url('./fonts/icomoon.eot?i9r3w7#iefix') format('embedded-opentype'),
      url('./fonts/icomoon.ttf?i9r3w7') format('truetype'),
      url('./fonts/icomoon.woff?i9r3w7') format('woff'),
      url('./fonts/icomoon.svg?i9r3w7') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
  }
  
  [class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  .icon-delete-trash:before {
    content: "\ea63";
  }
  .icon-circle-back:before {
    content: "\ea58";
  }
  .icon-field-rate:before {
    content: "\ea4e";
  }
  .icon-field-imagechecklist:before {
    content: "\ea51";
  }
  .icon-field-numberinput:before {
    content: "\ea52";
  }
  .icon-field-imageradiobutton:before {
    content: "\ea53";
  }
  .icon-field-edittextarea:before {
    content: "\ea54";
  }
  .icon-field-computeinput:before {
    content: "\ea55";
  }
  .icon-field-editinput:before {
    content: "\ea56";
  }
  .icon-field-department-1:before {
    content: "\ea57";
  }
  .icon-field-fileupload:before {
    content: "\ea59";
  }
  .icon-field-videobox:before {
    content: "\ea5a";
  }
  .icon-field-imagebox:before {
    content: "\e920";
  }
  .icon-field-datetimerange:before {
    content: "\ea5b";
  }
  .icon-field-dateinput:before {
    content: "\ea5c";
  }
  .icon-field-multipleselect:before {
    content: "\ea5d";
  }
  .icon-field-selectmultibox:before {
    content: "\ea5e";
  }
  .icon-field-selectbox:before {
    content: "\ea60";
  }
  .icon-field-areamultiselect:before {
    content: "\ea61";
  }
  .icon-field-checklist:before {
    content: "\ea62";
  }
  .icon-field-radiobutton:before {
    content: "\ea64";
  }
  .icon-field-livevideo:before {
    content: "\ea65";
  }
  .icon-field-captiontext:before {
    content: "\ea66";
  }
  .icon-field-richtext:before {
    content: "\ea50";
  }
  .icon-field-relateddata:before {
    content: "\ea67";
  }
  .icon-theme0-add{
    color: #fff;
  }
  .icon-theme0-add:before {
    content: "\ea2f";
  }
  .icon-theme1-add,
  .icon-theme1-userImport,
  .icon-theme1-excelImport,
  .icon-theme1-export,
  .icon-theme1-delete,
  .icon-theme1-gather,
  .icon-theme1-gatherForUpdate,
  .icon-theme1-copy,
  .icon-theme1-batchEdit,
  .icon-theme1-unCommit,
  .icon-theme1-common{
    color: #a0a4c6;
  }
  .icon-theme1-add:before {
    content: "\ea30";
  }
  .icon-theme1-userImport:before {
    content: "\ea31";
  }
  .icon-theme1-excelImport:before {
    content: "\ea32";
  }
  .icon-theme1-export:before {
    content: "\ea33";
  }
  .icon-theme1-delete:before {
    content: "\ea34";
  }
  .icon-theme1-gather:before {
    content: "\ea35";
  }
  .icon-theme1-gatherForUpdate:before {
    content: "\ea36";
  }
  .icon-theme1-copy:before {
    content: "\ea37";
  }
  .icon-theme1-batchEdit:before {
    content: "\ea38";
  }
  .icon-theme1-unCommit:before {
    content: "\ea39";
  }
  .icon-theme1-common:before {
    content: "\ea3a";
  }
  .icon-theme2-add,
  .icon-theme2-userImport,
  .icon-theme2-excelImport,
  .icon-theme2-export,
  .icon-theme2-exportRecord,
  .icon-theme2-delete,
  .icon-theme2-gather,
  .icon-theme2-gatherForUpdate,
  .icon-theme2-copy,
  .icon-theme2-batchEdit,
  .icon-theme2-unCommit,
  .icon-theme2-notice,
  .icon-theme2-flushData,
  .icon-theme2-print{
    color: #2e5db7;
  }
  .icon-theme2-add:before {
    content: "\ea3b";
  }
  .icon-theme2-userImport:before {
    content: "\ea3c";
  }
  .icon-theme2-excelImport:before {
    content: "\ea3d";
  }
  .icon-theme2-export:before {
    content: "\ea3e";
  }
  .icon-theme2-exportRecord:before {
    content: "\ea3f";
  }
  .icon-theme2-delete:before {
    content: "\ea40";
  }
  .icon-theme2-gather:before {
    content: "\ea41";
  }
  .icon-theme2-gatherForUpdate:before {
    content: "\ea42";
  }
  .icon-theme2-copy:before {
    content: "\ea43";
  }
  .icon-theme2-batchEdit:before {
    content: "\ea44";
  }
  .icon-theme2-unCommit:before {
    content: "\ea45";
  }
  .icon-theme2-notice:before {
    content: "\ea46";
  }
  .icon-theme2-flushData:before {
    content: "\ea47";
  }
  .icon-theme2-print:before {
    content: "\ea48";
  }
  .icon-theme4-add{
    color: #fff;
  }
  .icon-theme4-add:before {
    content: "\ea2e";
  }
  .icon-id-card-fill:before {
    content: "\e9a3";
  }
  .icon-id-card:before {
    content: "\e9a4";
  }
  .icon-car-fill:before {
    content: "\e9a5";
  }
  .icon-car:before {
    content: "\e9a6";
  }
  .icon-message-fill:before {
    content: "\e9a7";
  }
  .icon-message:before {
    content: "\e9a8";
  }
  .icon-credit-card-fill:before {
    content: "\e9a9";
  }
  .icon-credit-card:before {
    content: "\e9aa";
  }
  .icon-building-fill:before {
    content: "\e9ab";
  }
  .icon-building:before {
    content: "\e9ac";
  }
  .icon-coupon-fill:before {
    content: "\e9ad";
  }
  .icon-coupon:before {
    content: "\ea21";
  }
  .icon-math-avg-fill:before {
    content: "\e9af";
  }
  .icon-math-avg:before {
    content: "\e9b0";
  }
  .icon-telephone-fill:before {
    content: "\e9b1";
  }
  .icon-telephone:before {
    content: "\e9b2";
  }
  .icon-share-fill:before {
    content: "\e9b3";
  }
  .icon-share:before {
    content: "\e9b4";
  }
  .icon-funnel-fill:before {
    content: "\e9b5";
  }
  .icon-funnel:before {
    content: "\e9b6";
  }
  .icon-grass-fill:before {
    content: "\e9b7";
  }
  .icon-grass:before {
    content: "\e9b8";
  }
  .icon-chart-fill:before {
    content: "\e9b9";
  }
  .icon-chart:before {
    content: "\e9ba";
  }
  .icon-cn-fill:before {
    content: "\e9bb";
  }
  .icon-cn:before {
    content: "\e9bc";
  }
  .icon-en-fill:before {
    content: "\e9bd";
  }
  .icon-en:before {
    content: "\e9be";
  }
  .icon-chinese-fill:before {
    content: "\e9bf";
  }
  .icon-chinese:before {
    content: "\e9c0";
  }
  .icon-english-fill:before {
    content: "\e9c1";
  }
  .icon-english:before {
    content: "\e9c2";
  }
  .icon-clock-fill:before {
    content: "\e9c3";
  }
  .icon-clock:before {
    content: "\e9c4";
  }
  .icon-link-o-fill:before {
    content: "\e9c5";
  }
  .icon-link-o:before {
    content: "\e9c6";
  }
  .icon-star-o-fill:before {
    content: "\e9c7";
  }
  .icon-star-o:before {
    content: "\ea20";
  }
  .icon-hat-o-fill:before {
    content: "\e9c9";
  }
  .icon-hat-o:before {
    content: "\ea1f";
  }
  .icon-file-paper-fill:before {
    content: "\e9cb";
  }
  .icon-file-paper:before {
    content: "\e9cc";
  }
  .icon-book-fill:before {
    content: "\e9cd";
  }
  .icon-book:before {
    content: "\e9ce";
  }
  .icon-handbag-fill:before {
    content: "\e9cf";
  }
  .icon-handbag:before {
    content: "\e9d0";
  }
  .icon-store-fill:before {
    content: "\e9d1";
  }
  .icon-store:before {
    content: "\e9d2";
  }
  .icon-room-fill:before {
    content: "\e9d3";
  }
  .icon-room:before {
    content: "\e9d4";
  }
  .icon-app-fill:before {
    content: "\e9d5";
  }
  .icon-app:before {
    content: "\e9d6";
  }
  .icon-box-fill:before {
    content: "\e9d7";
  }
  .icon-box:before {
    content: "\e9d8";
  }
  .icon-internet-fill:before {
    content: "\e9d9";
  }
  .icon-internet:before {
    content: "\e9da";
  }
  .icon-gear-fill:before {
    content: "\e9db";
  }
  .icon-gear:before {
    content: "\e9dc";
  }
  .icon-layer-fill:before {
    content: "\e9dd";
  }
  .icon-layer:before {
    content: "\e9de";
  }
  .icon-coins-fill:before {
    content: "\e9df";
  }
  .icon-coins:before {
    content: "\e9e0";
  }
  .icon-calculator-fill:before {
    content: "\e9e1";
  }
  .icon-calculator:before {
    content: "\e9e2";
  }
  .icon-phone-fill:before {
    content: "\e9e3";
  }
  .icon-phone:before {
    content: "\e9e4";
  }
  .icon-medal-fill:before {
    content: "\e9e5";
  }
  .icon-medal:before {
    content: "\e9e6";
  }
  .icon-seal-fill:before {
    content: "\e9e7";
  }
  .icon-seal:before {
    content: "\e9e8";
  }
  .icon-pot-fill:before {
    content: "\e9e9";
  }
  .icon-pot:before {
    content: "\e9ea";
  }
  .icon-aircraft-fill:before {
    content: "\e9eb";
  }
  .icon-aircraft:before {
    content: "\e9ec";
  }
  .icon-thumbtack-fill:before {
    content: "\e9ed";
  }
  .icon-thumbtack:before {
    content: "\e9ee";
  }
  .icon-lovers-fill:before {
    content: "\e9ef";
  }
  .icon-lovers:before {
    content: "\e9f0";
  }
  .icon-castle-fill:before {
    content: "\e9f1";
  }
  .icon-castle:before {
    content: "\e9f2";
  }
  .icon-route-fill:before {
    content: "\e9f3";
  }
  .icon-route:before {
    content: "\e9f4";
  }
  .icon-math-min-fill:before {
    content: "\e9f5";
  }
  .icon-math-min:before {
    content: "\e9f6";
  }
  .icon-math-max-fill:before {
    content: "\e9f7";
  }
  .icon-math-max:before {
    content: "\e9f8";
  }
  .icon-math-cnt-fill:before {
    content: "\e9f9";
  }
  .icon-math-cnt:before {
    content: "\e9fa";
  }
  .icon-math-integral-fill:before {
    content: "\e9fb";
  }
  .icon-math-integral:before {
    content: "\e9fc";
  }
  .icon-math-func-fill:before {
    content: "\e9fd";
  }
  .icon-math-func:before {
    content: "\e9fe";
  }
  .icon-math-number-fill:before {
    content: "\e9ff";
  }
  .icon-math-number:before {
    content: "\ea00";
  }
  .icon-jing-fill:before {
    content: "\ea01";
  }
  .icon-jing:before {
    content: "\ea02";
  }
  .icon-money-rmb-fill:before {
    content: "\ea03";
  }
  .icon-money-rmb:before {
    content: "\ea04";
  }
  .icon-money-euro-fill:before {
    content: "\ea05";
  }
  .icon-money-euro:before {
    content: "\ea06";
  }
  .icon-money-dollar-fill:before {
    content: "\ea07";
  }
  .icon-money-dollar:before {
    content: "\ea08";
  }
  .icon-html-fill:before {
    content: "\ea09";
  }
  .icon-html:before {
    content: "\ea0a";
  }
  .icon-year-fill:before {
    content: "\ea0b";
  }
  .icon-year:before {
    content: "\ea0c";
  }
  .icon-month-fill:before {
    content: "\ea0d";
  }
  .icon-month:before {
    content: "\ea0e";
  }
  .icon-week-fill:before {
    content: "\ea0f";
  }
  .icon-week:before {
    content: "\ea10";
  }
  .icon-day-fill:before {
    content: "\ea11";
  }
  .icon-day:before {
    content: "\ea12";
  }
  .icon-today-fill:before {
    content: "\ea13";
  }
  .icon-today:before {
    content: "\ea14";
  }
  .icon-calendar-fill:before {
    content: "\ea15";
  }
  .icon-calendar:before {
    content: "\ea16";
  }
  .icon-head-fill:before {
    content: "\e900";
  }
  .icon-head:before {
    content: "\ea1e";
  }
  .icon-stu-card-fill:before {
    content: "\ea19";
  }
  .icon-stu-card:before {
    content: "\ea1a";
  }
  .icon-math-simple-fill:before {
    content: "\ea1b";
  }
  .icon-math-simple:before {
    content: "\ea1c";
  }
  .icon-symbol:before {
    content: "\e9ca";
  }
  .icon-edit-color .path1:before {
    content: "\e9ae";
    color: rgb(255, 255, 255);
  }
  .icon-edit-color .path2:before {
    content: "\e9c8";
    margin-left: -1em;
    color: rgb(137, 137, 137);
  }
  .icon-ban:before {
    content: "\ea1d";
  }
  .icon-remove .path1:before {
    content: "\ea18";
    color: rgb(255, 76, 38);
  }
  .icon-remove .path2:before {
    content: "\ea22";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-status-warning .path1:before {
    content: "\e901";
    color: rgb(255, 156, 38);
  }
  .icon-status-warning .path2:before {
    content: "\e902";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-status-warning .path3:before {
    content: "\e903";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-status-common .path1:before {
    content: "\e904";
    color: rgb(0, 108, 226);
  }
  .icon-status-common .path2:before {
    content: "\e905";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-status-common .path3:before {
    content: "\e906";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-status-help .path1:before {
    content: "\e907";
    color: rgb(0, 108, 226);
  }
  .icon-status-help .path2:before {
    content: "\e908";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-status-success .path1:before {
    content: "\e909";
    color: rgb(46, 191, 99);
  }
  .icon-status-success .path2:before {
    content: "\e90a";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-status-error .path1:before {
    content: "\e90b";
    color: rgb(255, 76, 38);
  }
  .icon-status-error .path2:before {
    content: "\e90c";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-status-error .path3:before {
    content: "\e90d";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-status-wait .path1:before {
    content: "\e90e";
    color: rgb(255, 156, 38);
  }
  .icon-status-wait .path2:before {
    content: "\e90f";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-order-table3:before {
    content: "\ea2b";
  }
  .icon-order:before {
    content: "\ea23";
  }
  .icon-order-ascending:before {
    content: "\ea24";
  }
  .icon-order-reverse:before {
    content: "\ea25";
  }
  .icon-sort-circular:before {
    content: "\ea49";
  }
  .icon-order,
  .icon-order-ascending,
  .icon-order-reverse,
  .icon-sort-circular,
  .icon-theme1-reset{
    color: #6581ba;
  }
  .icon-sort,
  .icon-mobile,
  .icon-computer,
  .icon-progress-gray,
  .icon-edit,
  .icon-return,
  .icon-arrow-line-close,
  .icon-arrow-line-open{
    color: #c0c0c3;
  }
  .icon-sort:before {
    content: "\e910";
  }
  .icon-sort-portrait:before {
    content: "\e96b";
  }
  .icon-switch-close .path1:before {
    content: "\e914";
    color: rgb(222, 223, 224);
  }
  .icon-switch-close .path2:before {
    content: "\e915";
    margin-left: -1em;
    color: rgb(0, 0, 0);
  }
  .icon-switch-close .path3:before {
    content: "\e916";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-switch-open .path1:before {
    content: "\e911";
    color: rgb(0, 108, 226);
  }
  .icon-switch-open .path2:before {
    content: "\e912";
    margin-left: -1em;
    color: rgb(0, 0, 0);
  }
  .icon-switch-open .path3:before {
    content: "\e913";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-radio-checked .path1:before {
    content: "\e917";
    color: rgb(222, 223, 224);
  }
  .icon-radio-checked .path2:before {
    content: "\e918";
    margin-left: -1em;
    color: rgb(0, 108, 226);
  }
  .icon-radio-disabled .path1:before {
    content: "\e919";
    color: rgb(222, 223, 224);
  }
  .icon-radio-disabled .path2:before {
    content: "\e91a";
    margin-left: -1em;
    color: rgb(192, 192, 195);
  }
  .icon-radio-default:before {
    content: "\e951";
  }
  .icon-radio-default-block:before {
    content: "\e95a";
  }
  .icon-checkbox-checked .path1:before {
    content: "\e932";
    color: rgb(0, 108, 226);
  }
  .icon-checkbox-checked .path2:before {
    content: "\e933";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-checkbox-disabled .path1:before {
    content: "\e934";
    color: rgb(192, 192, 195);
  }
  .icon-checkbox-disabled .path2:before {
    content: "\e935";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-checkbox-default:before {
    content: "\e954";
  }
  .icon-checkbox-default-block:before {
    content: "\e959";
  }
  .icon-radio-default,
  .icon-checkbox-default{
    color: #dedfe0;
  }
  .icon-radio-default-block,
  .icon-checkbox-default-block{
    color: #f1f1f2;
  }
  .icon-selected .path1:before {
    content: "\e926";
    color: rgb(0, 108, 226);
  }
  .icon-selected .path2:before {
    content: "\e927";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-mobile:before {
    content: "\e925";
  }
  .icon-computer:before {
    content: "\e929";
  }
  .icon-arrow-solid-up:before {
    content: "\e939";
  }
  .icon-arrow-solid-down:before {
    content: "\e93a";
  }
  .icon-arrow-solid-left:before {
    content: "\e93d";
  }
  .icon-arrow-solid-right:before {
    content: "\e93c";
  }
  .icon-sort-portrait,
  .icon-arrow-solid-up,
  .icon-arrow-solid-down,
  .icon-arrow-solid-left,
  .icon-arrow-solid-right,
  .icon-equal,
  .icon-ban,
  .icon-symbol{
    color: #898989;
  }
  .icon-arrow-solid-prev:before {
    content: "\e940";
  }
  .icon-arrow-solid-next:before {
    content: "\e941";
  }
  .icon-arrow-line-prev:before {
    content: "\e93e";
  }
  .icon-arrow-line-next:before {
    content: "\e93f";
  }
  .icon-arrow-line-close:before {
    content: "\e99d";
  }
  .icon-arrow-line-open:before {
    content: "\e99e";
  }
  .icon-arrow-line-right:before {
    content: "\e93b";
  }
  .icon-arrow-line-down:before {
    content: "\e9a0";
  }
  .icon-double-arrow:before {
    content: "\e958";
  }
  .icon-arrow-line-down,
  .icon-double-arrow,
  .icon-align-left,
  .icon-align-center,
  .icon-align-right{
    color: #4d4d4d;
  }
  .icon-go-back .path1:before {
    content: "\e96f";
    color: rgb(255, 255, 255);
  }
  .icon-go-back .path2:before {
    content: "\e970";
    margin-left: -1em;
    color: none;
  }
  .icon-go-back .path3:before {
    content: "\e971";
    margin-left: -1em;
    color: rgb(74, 76, 77);
  }
  .icon-progress-gray:before {
    content: "\e94e";
  }
  .icon-progress-blue:before {
    content: "\e94f";
  }
  .icon-select-down:before {
    content: "\e95d";
  }
  .icon-equal:before {
    content: "\e99b";
  }
  .icon-tick:before {
    content: "\e99c";
    color: #2ebf63;
  }
  .icon-fx-txt:before {
    content: "\e9a2";
  }
  .icon-fx:before {
    content: "\e947";
  }
  .icon-link:before {
    content: "\e949";
  }
  .icon-link{
    color: #ffa468;
  }
  .icon-align-left:before {
    content: "\e94a";
  }
  .icon-align-center:before {
    content: "\e94b";
  }
  .icon-align-right:before {
    content: "\e94c";
  }
  .icon-font-underline:before {
    content: "\e975";
  }
  .icon-font-italic:before {
    content: "\e94d";
  }
  .icon-font-bold:before {
    content: "\e950";
  }
  .icon-content:before {
    content: "\e946";
  }
  .icon-search:before {
    content: "\e945";
  }
  .icon-clean-input .path1:before {
    content: "\e995";
    color: rgb(237, 241, 245);
  }
  .icon-clean-input .path2:before {
    content: "\e996";
    margin-left: -1em;
    color: rgb(163, 183, 204);
  }
  .icon-set:before {
    content: "\e998";
  }
  .icon-theme4-reset:before {
    content: "\ea4a";
  }
  .icon-theme1-reset:before {
    content: "\ea4b";
  }
  .icon-reset:before {
    content: "\e948";
  }
  .icon-delete:before {
    content: "\e953";
  }
  .icon-recovery:before {
    content: "\e99f";
  }
  .icon-cancel-interlock:before {
    content: "\ea2a";
  }
  .icon-arrow-solid-prev,
  .icon-arrow-solid-next,
  .icon-arrow-line-prev,
  .icon-arrow-line-next,
  .icon-fx-txt,
  .icon-set,
  .icon-reset,
  .icon-delete,
  .icon-recovery,
  .icon-cancel-interlock,
  .icon-form{
    color: #557ca7;
  }
  .icon-copy:before {
    content: "\e952";
  }
  .icon-write-in:before {
    content: "\e937";
  }
  .icon-add:before {
    content: "\e936";
  }
  .icon-scale:before {
    content: "\e91b";
  }
  .icon-style:before {
    content: "\e938";
  }
  .icon-save:before {
    content: "\e999";
  }
  .icon-publich:before {
    content: "\e98c";
  }
  .icon-preview-table3:before {
    content: "\ea2c";
  }
  .icon-preview:before {
    content: "\e98b";
  }
  .icon-preview-no:before {
    content: "\e997";
  }
  .icon-adapter:before {
    content: "\ea28";
  }
  .icon-radio-button:before {
    content: "\e91c";
  }
  .icon-edit:before {
    content: "\e955";
  }
  .icon-popup-zoom-out,
  .icon-popup-zoom-in,
  .icon-popup-close{
    color: #a3b7cc;
  }
  .icon-popup-zoom-out:before {
    content: "\e91d";
  }
  .icon-popup-zoom-in:before {
    content: "\e967";
  }
  .icon-popup-close:before {
    content: "\e95b";
  }
  .icon-return:before {
    content: "\e96e";
  }
  .icon-add-circular:before {
    content: "\e95c";
  }
  .icon-add-circular{
    color: #f5f9ff;
  }
  .icon-upload .path1:before {
    content: "\e942";
    color: rgb(0, 108, 226);
  }
  .icon-upload .path2:before {
    content: "\e943";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-upload .path3:before {
    content: "\e944";
    margin-left: -1em;
    color: rgb(0, 108, 226);
  }
  .icon-file-ppt .path1:before {
    content: "\e972";
    color: rgb(255, 94, 94);
  }
  .icon-file-ppt .path2:before {
    content: "\e973";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-file-ppt .path3:before {
    content: "\e974";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-file-excel .path1:before {
    content: "\e956";
    color: rgb(181, 225, 199);
  }
  .icon-file-excel .path2:before {
    content: "\e957";
    margin-left: -1em;
    color: rgb(99, 204, 141);
  }
  .icon-filter:before {
    content: "\e9a1";
  }
  .icon-theme4-filter:before {
    content: "\ea4c";
  }
  .icon-theme2-filter:before {
    content: "\ea4d";
  }
  .icon-theme2-filter,
  .icon-clean,
  .icon-import,
  .icon-export,
  .icon-export-record,
  .icon-print,
  .icon-more,
  .icon-show-field{
    color: #2e5db7;
  }
  .icon-clean:before {
    content: "\e976";
  }
  .icon-import:before {
    content: "\e977";
  }
  .icon-export:before {
    content: "\e978";
  }
  .icon-export-record:before {
    content: "\e979";
  }
  .icon-print-table3:before {
    content: "\ea2d";
  }
  .icon-print:before {
    content: "\e97a";
  }
  .icon-more:before {
    content: "\e98a";
  }
  .icon-more-1:before {
    content: "\ea4f";
  }
  .icon-form:before {
    content: "\e99a";
  }
  .icon-show-field:before {
    content: "\e97b";
  }
  .icon-home-solid:before {
    content: "\e97c";
  }
  .icon-video-pause:before {
    content: "\e97d";
  }
  .icon-filter,
  .icon-home-solid,
  .icon-video-pause{
    color: #fff;
  }
  
  .icon-live-unstart .path1:before {
    content: "\e97e";
    color: rgb(255, 180, 109);
    opacity: 0.6;
  }
  .icon-live-unstart .path2:before {
    content: "\e97f";
    margin-left: -1em;
    color: rgb(255, 180, 109);
    opacity: 0.8;
  }
  .icon-live-unstart .path3:before {
    content: "\e980";
    margin-left: -1em;
    color: rgb(255, 180, 109);
  }
  .icon-live-living .path1:before {
    content: "\e981";
    color: rgb(120, 184, 255);
    opacity: 0.6;
  }
  .icon-live-living .path2:before {
    content: "\e982";
    margin-left: -1em;
    color: rgb(120, 184, 255);
    opacity: 0.8;
  }
  .icon-live-living .path3:before {
    content: "\e983";
    margin-left: -1em;
    color: rgb(120, 184, 255);
  }
  .icon-live-back .path1:before {
    content: "\e984";
    color: rgb(89, 206, 185);
    opacity: 0.6;
  }
  .icon-live-back .path2:before {
    content: "\e985";
    margin-left: -1em;
    color: rgb(89, 206, 185);
    opacity: 0.8;
  }
  .icon-live-back .path3:before {
    content: "\e986";
    margin-left: -1em;
    color: rgb(89, 206, 185);
  }
  .icon-live-end .path1:before {
    content: "\e987";
    color: rgb(255, 255, 255);
    opacity: 0.6;
  }
  .icon-live-end .path2:before {
    content: "\e988";
    margin-left: -1em;
    color: rgb(255, 255, 255);
    opacity: 0.8;
  }
  .icon-live-end .path3:before {
    content: "\e989";
    margin-left: -1em;
    color: rgb(255, 255, 255);
  }
  .icon-scan-code-contact:before {
    content: "\e98f";
  }
  .icon-scan-code:before {
    content: "\e991";
  }
  .icon-identify-text:before {
    content: "\e992";
  }
  .icon-help:before {
    content: "\e91e";
  }
  .icon-position:before {
    content: "\e963";
  }
  .icon-relation-approve:before {
    content: "\e993";
  }
  .icon-time:before {
    content: "\e968";
  }
  .icon-remove-vacation:before {
    content: "\e994";
  }
  .icon-go-out:before {
    content: "\e969";
  }
  .icon-work-overtime:before {
    content: "\e96a";
  }
  .icon-clock-in:before {
    content: "\e96c";
  }
  .icon-arrow-line-right,
  .icon-progress-blue,
  .icon-select-down,
  .icon-fx,
  .icon-font-underline,
  .icon-font-italic,
  .icon-font-bold,
  .icon-content,
  .icon-search,
  .icon-copy,
  .icon-write-in,
  .icon-add,
  .icon-scale,
  .icon-style,
  .icon-publich,
  .icon-preview,
  .icon-preview-no,
  .icon-radio-button,
  .icon-scan-code-contact,
  .icon-scan-code,
  .icon-identify-text,
  .icon-help,
  .icon-position,
  .icon-relation-approve,
  .icon-time,
  .icon-remove-vacation,
  .icon-go-out,
  .icon-work-overtime,
  .icon-clock-in,
  .icon-field-compute-input,
  .icon-field-video-box,
  .icon-field-caption-text,
  .icon-field-img-box,
  .icon-field-edit-textarea,
  .icon-filed-number-input,
  .icon-field-auto-number,
  .icon-field-edit-input,
  .icon-field-location,
  .icon-field-select-box,
  .icon-field-select-multi-box,
  .icon-field-multiple-select,
  .icon-field-matrix-radio,
  .icon-field-matrix-checkbox,
  .icon-field-detail-box,
  .icon-field-rich-text,
  .icon-field-radio-button,
  .icon-field-check-list,
  .icon-field-signature,
  .icon-field-live-video,
  .icon-field-related-data,
  .icon-field-button,
  .icon-field-file-upload,
  .icon-field-slider,
  .icon-field-department,
  .icon-field-date-input,
  .icon-field-date-range,
  .icon-field-belonger,
  .icon-field-contact{
    color: #006ce2;
  }
  .icon-field-compute-input:before {
    content: "\e98e";
  }
  .icon-field-video-box:before {
    content: "\e990";
  }
  .icon-field-caption-text:before {
    content: "\e91f";
  }
  .icon-field-img-box:before {
    content: "\e920";
  }
  .icon-field-edit-textarea:before {
    content: "\e921";
  }
  .icon-filed-number-input:before {
    content: "\e922";
  }
  .icon-field-auto-number:before {
    content: "\e923";
  }
  .icon-field-edit-input:before {
    content: "\e924";
  }
  .icon-field-location:before {
    content: "\e928";
  }
  .icon-field-select-box:before {
    content: "\e92a";
  }
  .icon-field-select-multi-box:before {
    content: "\e98d";
  }
  .icon-field-multiple-select:before {
    content: "\e92b";
  }
  .icon-field-matrix-radio:before {
    content: "\e92c";
  }
  .icon-field-matrix-checkbox:before {
    content: "\e92d";
  }
  .icon-field-detail-box:before {
    content: "\e92e";
  }
  .icon-field-rich-text:before {
    content: "\e92f";
  }
  .icon-field-radio-button:before {
    content: "\e930";
  }
  .icon-field-check-list:before {
    content: "\e931";
  }
  .icon-field-signature:before {
    content: "\e95e";
  }
  .icon-field-signature-1:before {
    content: "\ea5f";
  }
  .icon-field-live-video:before {
    content: "\e95f";
  }
  .icon-field-related-data:before {
    content: "\e960";
  }
  .icon-field-button:before {
    content: "\e961";
  }
  .icon-field-file-upload:before {
    content: "\e962";
  }
  .icon-field-slider:before {
    content: "\e964";
  }
  .icon-field-department:before {
    content: "\e965";
  }
  .icon-field-date-input:before {
    content: "\e966";
  }
  .icon-field-date-range:before {
    content: "\ea29";
  }
  .icon-field-belonger:before {
    content: "\e96d";
  }
  .icon-field-contact:before {
    content: "\ea17";
  }
  .icon-star:before {
    content: "\ea26";
  }
  .icon-flow-type:before {
    content: "\ea27";
  }
  