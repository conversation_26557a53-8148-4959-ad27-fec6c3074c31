.borDer {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.transforms {
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}

textarea::-webkit-input-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

textarea:-moz-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

textarea:-ms-input-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

.clearfixs {
    zoom: 1;
}

.flex {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.clearfixs:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

input::-webkit-input-placeholder {
    color: #b8bec7 !important;
    font-size: 14px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

::-webkit-scrollbar {
    width: 4px;
    height: 10px;
}

::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background: #d9d9d9;
}

::-webkit-scrollbar-track {
    border-radius: 6px;
}

body {
    font-family: 'PingFang SC';
    background: #F6F7FE;
}

.yz-header {
    width: 100%;
    height: 68px;
    background: linear-gradient(80deg, #4A7CFE 0%, #769CFF 100%);
    background: -webkit-linear-gradient(80deg, #4A7CFE 0%, #769CFF 100%);
    box-shadow: 0px 0px 10px rgba(30, 29, 46, 0.1);
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    padding: 0 30px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.yz-header .name {
    font-weight: normal;
    font-size: 24px;
    color: #FFFFFF;
}

.yz-con {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 20px;
    padding-top: 20px;
    align-items: flex-start;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.yz-con .yz-scroll {
    width: 200px;
    background: linear-gradient(53deg, #4A7CFE 0%, #769CFF 100%);
    background: -webkit-linear-gradient(53deg, #4A7CFE 0%, #769CFF 100%);
    border-radius: 8px;
    flex-shrink: 0;
    padding-top: 30px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.yz-con .yz-scroll .layui-input-inline {
    padding-bottom: 24px;
    background: url(../../../images/form/printUtil/liner-icon.png) no-repeat bottom center;
}

.yz-con .yz-scroll .layui-input-inline .layui-form-select .layui-select-title .layui-input {
    color: #fff;
    padding-left: 16px;
}

.yz-con .yz-menu {
    width: 200px;
    background-size: cover;
    overflow-y: auto;
    position: relative;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.yz-con .yz-menu .menu-list {
    padding: 10px 16px;
}

.yz-con .yz-menu .menu-list ul li {
    width: 100%;
    height: auto;
}

.yz-con .yz-menu .menu-list ul li .label-top {
    width: 100%;
    display: flex;
    /* 让字段高度自适应显示 */
    /*height: 40px;*/
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    cursor: pointer;
    padding: 0 16px;
    margin-bottom: 12px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.yz-con .yz-menu .menu-list ul li .label-top.cur {
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
}

.yz-con .yz-menu .menu-list ul li .label-top.active .slide-arrow {
    transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
}

.yz-con .yz-menu .menu-list ul li .label-top .name {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 40px;
    color: rgba(255, 255, 255, 0.8);
}

.yz-con .yz-menu .menu-list ul li .label-top .slide-arrow {
    position: absolute;
    top: 10px;
    right: 16px;
    width: 20px;
    height: 20px;
    background: url(../../../images/form/printUtil/slide-arrow.png) no-repeat center;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}

.yz-con .yz-menu .menu-list ul li .label-con {
    display: none;
}

.yz-con .yz-menu .menu-list ul li .label-con .item-list .item {
    width: 100%;
    height: 40px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    padding-left: 26px;
    color: rgba(255, 255, 255, 0.8);
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    text-align: left;
    line-height: 40px;
    cursor: pointer;
    margin-bottom: 12px;
}

.yz-con .yz-menu .menu-list ul li .label-con .item-list .item.cur {
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
}

.yz-con #cons {
    overflow-y: auto;
}

.yz-con .yz-main {
    flex: 1;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.yz-con .yz-main .yz-cons {
    margin: 0 20px;
    background-color: #fff;
    border-radius: 8px;
    overflow-y: auto;
}

.yz-con .yz-main .yz-cons .c-item {
    margin-bottom: 40px;
    padding: 20px 30px;
}

.yz-con .yz-main .yz-cons .c-item .layui-table-view {
    border-right: none;
}

.yz-con .yz-main .yz-cons .c-item .c-top {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: space-between;
    height: 34px;
    margin-bottom: 20px;
}

.yz-con .yz-main .yz-cons .c-item h3 {
    height: 20px;
    line-height: 20px;
    position: relative;
    padding-left: 7px;
    font-weight: 400;
    font-size: 16px;
    color: #6581ba;
}

.yz-con .yz-main .yz-cons .c-item h3:after {
    content: '';
    position: absolute;
    width: 3px;
    height: 18px;
    left: 0px;
    top: 1px;
    background: #6581BA;
    border-radius: 1.5px;
}

.yz-con .yz-main .yz-cons .c-item .c-table {
    margin-bottom: 30px;
}

.yz-scroll .layui-form-select .layui-edge {
    border: none;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    background: url(../../../images/form/printUtil/arrows-icon.png) no-repeat center;
}

.yz-scroll .layui-form-select dl {
    padding: 0;
    border: none;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
    top: 38px;
    border-radius: 4px;
}

.yz-scroll .layui-form-select dl dt,
.yz-scroll .layui-form-select dl dd {
    height: 32px;
    color: #4E5969;
    font-weight: 400;
    font-size: 14px;
    padding: 0 20px;
    line-height: 32px;
}

.yz-scroll .layui-form-select dl dt:hover,
.yz-scroll .layui-form-select dl dd:hover {
    background: #F7F8FA;
}

.yz-scroll .layui-form-select dl dd.layui-this {
    background: #DDE7FF;
    color: #4A7CFE;
}

.layui-input,
.layui-textarea,
.layui-select {
    height: 32px;
    border-radius: 4px;
    border-color: #C9CDD4;
}

.yz-header .login-after {
    cursor: pointer;
    float: right;
    display: flex;
    display: -webkit-flex;
    display: -ms-flex;
    display: -moz-flex;
    align-items: center;
    position: absolute;
    right: 18px;
    height: 60px;
}

.yz-header .login-after .user-photo {
    float: left;
    margin-right: 10px;
    cursor: pointer;
}

.yz-header .login-after .user-photo img {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: block;
    border: 1px solid #4f79ff;
    margin-right: 0;
}

.yz-header .login-after .user-mes h3 {
    font-size: 14px;
    line-height: 20px;
    font-weight: 600;
    margin-right: 8px;
    color: #D3D2D6;
    white-space: nowrap;
}

.yz-header .login-after .login-opt {
    display: none;
    position: absolute;
    z-index: 9999;
    width: 114px;
    height: 124px;
    top: 50px;
    left: -6px;
}

.yz-header .login-after .login-opt ul {
    overflow: hidden;
    width: 92px;
    height: 90px;
    font-size: 12px;
    position: relative;
    margin-left: 10px;
    margin-top: 16px;
    -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.2);
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    background: #232232;
}

.yz-header .login-after .login-opt ul:after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
    width: 92px;
    height: 90px;
    background: #232232;
    border-radius: 8px;
}

.yz-header .login-after .login-opt ul li {
    height: 26px;
    text-align: center;
    line-height: 30px;
    overflow: hidden;
    cursor: pointer;
    display: flex;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    align-items: center;
    color: #fff;
}

.yz-header .login-after .login-opt ul li:first-child {
    margin-top: 4px;
}

.yz-header .login-after .login-opt ul li span.icon {
    font-size: 14px;
    margin-right: 6px;
    margin-left: 11px;
}

.yz-header .login-after .login-opt ul li:hover {
    color: #FFFFFF;
}

.yz-header .login-after .user-arrow {
    float: left;
    /*width: 14px;
          height: 14px;*/
    font-size: 14px;
    -webkit-transition: all 200ms;
    -moz-transition: all 200ms;
    -ms-transition: all 200ms;
    -o-transition: all 200ms;
    transition: all 200ms;
    opacity: 0.6;
}

.yz-header .login-after .user-arrow span {
    width: 10px;
    height: 10px;
    background: url(../../../images/form/printUtil/arrows-gray-icon.png) no-repeat center;
    display: block;
}

.yz-header .login-after:hover .login-opt {
    display: block;
}

.yz-header .login-after:hover .user-arrow {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}

.yz-scroll .layui-form-select .layui-input {
    border-radius: 8px;
    border: 1px solid var(--border-color-border-1, #E5E6EB);
    background: rgba(255, 255, 255, 0.2);
    width: 160px;
    height: 37px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
}

.yz-cons .top {
    width: 100%;
    height: 60px;
    border-bottom: 1px solid #e8ebf1;
    position: relative;
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
    align-items: center;
}

.yz-cons .top .title {
    padding-left: 30px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 36px;
}

.yz-cons .top .title .back {
    padding-left: 22px;
    background: url(../../../images/form/printUtil/back-icons.png) no-repeat left center;
    background-size: 16px;
    color: #7d92b3;
    font-size: 14px;
    cursor: pointer;
    margin-right: 16px;
}

.yz-cons .top .title .levelone {
    padding-left: 9px;
    position: relative;
    color: #1d2129;
    font-weight: 700;
    font-size: 16px;
    margin-right: 6px;
}

.yz-cons .top .title .levelone:after {
    content: "";
    position: absolute;
    left: 0;
    top: 2px;
    width: 3px;
    height: 16px;
    background: #4d88ff;
    border-radius: 2px;
}

.yz-cons .top .title .icon {
    width: 12px;
    height: 12px;
    background: url(../../../images/form/printUtil/arrow-right.png) no-repeat center;
    background-size: 12px;
    margin-right: 6px;
}

.yz-cons .top .title .leveltwo {
    color: #1d2129;
    font-weight: 700;
    font-size: 16px;
}

.yz-cons .top .btn {
    position: absolute;
    top: 12px;
    right: 30px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.yz-cons .top .btn .cancle {
    width: 86px;
    height: 36px;
    text-align: center;
    line-height: 34px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid #4D88FF;
    background: #FFF;
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
    font-size: 14px;
    color: #4d88ff;
    margin-right: 16px;
}

.yz-cons .top .btn .save {
    width: 86px;
    height: 36px;
    text-align: center;
    line-height: 36px;
    cursor: pointer;
    font-size: 14px;
    color: #ffffff;
    background: #4d88ff;
    box-shadow: 0px 0px 10px #4d88ff;
    border-radius: 4px;
}

.yz-cons .top h4 {
    position: relative;
    color: #1d2129;
    font-size: 16px;
    margin-left: 30px;
    padding-left: 9px;
    font-weight: bold;
}

.yz-cons .top h4::after {
    content: "";
    width: 3px;
    height: 16px;
    background: #4d88ff;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 4px;
}

.c-item .btns-list {
    overflow: hidden;
}

.c-item .btns-list span {
    float: left;
    height: 34px;
    line-height: 34px;
    background: #4D88FF;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    border: 1px solid #4D88FF;
    border-radius: 4px;
    color: #FFFFFF;
    font-weight: 400;
    font-size: 14px;
    margin-left: 16px;
    padding-right: 11px;
    width: auto;
    text-align: center;
    cursor: pointer;
}

.c-item .btns-list span.set {
    width: 104px;
}

.c-item .btns-list span.add {
    padding-left: 29px;
    text-align: left;
    position: relative;
    cursor: pointer;
}

.c-item .btns-list span.add:after {
    content: '';
    position: absolute;
    left: 11px;
    top: 11px;
    width: 12px;
    height: 12px;
    background: url(../../../images/form/printUtil/plus-icon.png) no-repeat center;
    background-size: 12px;
}

.yz-cons .flex-cons {
    padding: 20px 30px;
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: space-between;
}

.yz-cons .flex-cons .layui-form {
    width: 668px;
    flex-shrink: 0;
}

.yz-cons .flex-cons .preview {
    width: 700px;
    height: auto;
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
    border-radius: 10px;
    background-color: #fff;
    overflow: hidden;
}

.yz-cons .flex-cons .preview h4 {
    padding: 0 30px;
    height: 56px;
    line-height: 56px;
    border-radius: 10px 10px 0px 0px;
    border-bottom: 1px solid #E5E6EB;
    background: #FFF;
    font-size: 16px;
    color: #1d2129;
}

.yz-cons .flex-cons .preview .p-cons {
    background: #F7F8FA;
    padding: 24px;
}

.yz-cons .flex-cons .preview .p-cons .bg {
    width: 100%;
}

.layui-form .layui-form-item {
    margin-bottom: 30px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    align-items: flex-start;
}

.layui-form .layui-form-item .limit-switch {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    align-items: flex-start;
}

.layui-form .layui-form-item .limit-switch span {
    line-height: 34px;
    color: #c9cdd4;
    margin-left: 10px;
}

.layui-form .layui-form-item .limit-switch .layui-form-switch {
    border-radius: 3px;
    background: #D2D3D8;
    height: 14px;
    line-height: 14px;
    min-width: 28px;
    padding: 0 0;
    margin-top: 10px;
    border: none;
}

.layui-form .layui-form-item .limit-switch .layui-form-switch i {
    left: 2px;
    top: 2px;
    width: 12px;
    height: 10px;
    border-radius: 1px;
    background: #FFF;
    margin-left: 0;
}

.layui-form .layui-form-item .limit-switch .layui-form-onswitch {
    border-radius: 3px;
    background: #537AF6;
}

.layui-form .layui-form-item .limit-switch .layui-form-onswitch i {
    left: 100%;
    margin-left: -14px;
    background-color: #fff;
}

.layui-form .layui-form-item .limit-switch .tit {
    margin-left: 10px;
    padding-top: 9px;
}

.layui-form .layui-form-item .limit-switch .tit h4 {
    color: #131B26;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 17px;
    margin-bottom: 4px;
}

.layui-form .layui-form-item .limit-switch .tit p {
    color: #8A8B99;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 17px;
}

.layui-form .layui-form-item .tips {
    font-size: 14px;
    color: #86909c;
    margin-left: 10px;
    line-height: 34px;
}

.layui-form .layui-form-item .layui-form-label {
    color: #1D2129;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    margin-right: 16px;
    height: 34px;
    line-height: 34px;
    width: 140px;
}

.layui-form .layui-form-item .layui-form-label em {
    font-size: 16px;
    color: #f76560;
    display: inline-block;
    margin-right: 5px;
}

.layui-form .layui-form-item .times {
    margin-right: 16px;
    width: 240px;
    height: 34px;
    border-radius: 4px;
    box-sizing: border-box;
    cursor: pointer;
    background: url(../../../images/form/printUtil/times.png) no-repeat 220px center;
    background-size: 12px;
}

.layui-form .layui-form-item .times .layui-input {
    background-color: transparent;
    color: #4E5969;
    font-size: 14px;
    cursor: pointer;
}

.layui-form .layui-form-item .layui-input-block {
    margin-left: 0;
}

.layui-form .layui-form-item .layui-input-block .select-file {
    overflow: hidden;
    margin-bottom: 20px;
}

.layui-form .layui-form-item .layui-input-block .select-file .btn {
    float: left;
    width: 100px;
    height: 34px;
    background: url(../../../images/form/printUtil/select-file.png) no-repeat center;
    cursor: pointer;
    margin-right: 10px;
}

.layui-form .layui-form-item .layui-input-block .select-file .tip {
    font-size: 14px;
    color: #86909c;
    line-height: 34px;
    white-space: nowrap;
}

.layui-form .layui-form-item .layui-input-block .file-list ul li {
    margin-bottom: 8px;
    border-radius: 4px;
    border: 1px dashed #93BEFF;
    background: #E1EBFF;
    width: 340px;
    height: 31px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    padding: 0 10px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    justify-content: space-between;
}

.layui-form .layui-form-item .layui-input-block .file-list ul li .name {
    padding-left: 20px;
    background: url(../../../images/form/printUtil/links-icon.png) no-repeat left center;
    font-size: 14px;
    color: #4d88ff;
}

.layui-form .layui-form-item .layui-input-block .file-list ul li .del {
    width: 14px;
    height: 14px;
    background: url(../../../images/form/printUtil/delet-icons.png) no-repeat center;
    cursor: pointer;
}

.layui-form .layui-form-item .layui-input-block .file-list ul li:last-child {
    margin-bottom: 0;
}

.layui-form .layui-form-item .layui-input-block.w240 {
    width: 240px;
}

.layui-form .layui-form-item .layui-input-block.w60 {
    width: 60px;
}

.layui-form .layui-form-item .layui-input-block.w275 {
    width: 275px;
}

.layui-form .layui-form-item .layui-input-block .lab {
    height: 34px;
    margin-bottom: 6px;
    position: relative;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.layui-form .layui-form-item .layui-input-block .lab:last-child {
    margin-bottom: 0;
}

.layui-form .layui-form-item .layui-input-block .lab .layui-form-radio {
    margin: 0;
}

.layui-form .layui-form-item .layui-input-block .lab .introIcon:hover .bubble {
    display: block;
}

.layui-form .layui-form-item .layui-input-block .lab .bubble {
    display: none;
    position: absolute;
    left: 100%;
    top: 50%;
    margin-top: -15px;
    padding-left: 17px;
}

.layui-form .layui-form-item .layui-input-block .lab .bubble:after {
    content: '';
    position: absolute;
    left: 13px;
    top: 50%;
    width: 4px;
    height: 10px;
    margin-top: -5px;
    background: url(../../../images/form/printUtil/triangle.png) no-repeat left center;
}

.layui-form .layui-form-item .layui-input-block .lab .bubble span {
    display: block;
    border-radius: 4px;
    background: #F2F5F7;
    height: 29px;
    padding: 0 16px;
    font-size: 12px;
    line-height: 29px;
    color: #737B86;
    white-space: nowrap;
}

.layui-form .layui-form-item .layui-input-block .lab .bubble span em {
    color: #1A79FF;
    cursor: pointer;
    margin-left: 9px;
}

.flex-cons .sel .select-input {
    width: 240px;
    margin-left: 0;
}

.layui-form-item .add {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    background: url(../../../images/form/printUtil/group-icon.png) no-repeat center;
    cursor: pointer;
    margin-top: 6px;
}

.final {
    margin-right: 10px;
    width: 240px;
    flex-shrink: 0;
    min-height: 32px;
    border-radius: 4px;
    border: 1px solid #D4D6D9;
    background-color: #fff;
    position: relative;
    padding: 0 10px;
    flex-wrap: wrap;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.final span {
    height: 26px;
    line-height: 26px;
    padding: 0 10px;
    background-color: #F2F5FA;
    margin-right: 16px;
    position: relative;
    font-size: 14px;
    color: #131B26;
    margin-bottom: 3px;
    margin-top: 3px;
}

.final span i {
    position: absolute;
    width: 12px;
    height: 12px;
    top: -2px;
    right: -6px;
    cursor: pointer;
    background: url(../../../images/form/printUtil/group-close.png) no-repeat center;
}

.final span em {
    font-size: 14px;
    color: #131B26;
}

#dataAssociation {
    width: 520px;
    /*background-color: pink;*/
}

#dataAssociation ul,
#dataAssociation ul li {
    padding: 0;
    margin: 0;
}

#dataAssociation > ul > li {
    float: left;
}

#dataAssociation > ul > li .dataAssociation {
    width: 460px;
    height: auto;
    margin-right: 0;
}

#dataAssociation > ul > li .dataAssociation > ul {
    float: left;
    width: 440px;
    /*background-color: skyblue;*/
    padding-left: 74px;
}

#dataAssociation > ul > li .dataAssociation>ul > li {
    width: 100%;
    height: 32px;
    padding-top: 5px;
}
#dataAssociation > ul > li .dataAssociation ul.dropdown-lists > li {
    padding-left: 10px;
}

#dataAssociation > ul > li .dataAssociation ul > li .condition {
    float: left;
    width: 50px;
    height: 30px;
    margin-right: 10px;
    border: 1px solid #ccc;
    border-radius: 3px;
    background-color: #fff;
    text-align: center;
}

#dataAssociation > ul > li .dataAssociation ul > li > span {
    display: inline-block;
    height: 32px;
    padding-left: 0;
    background: none;
    line-height: 32px;
}

#dataAssociation > ul > li .select-input {
    float: left;
}

#dataAssociation > ul > li .select-input:nth-of-type(1),
#dataAssociation .dataAssociation > ul > li .select-input:nth-of-type(1),
#dataAssociation .dataAssociation > ul > li .select-input:nth-of-type(3) {
    float: left;
    width: 150px;
}
#dataAssociation .dataAssociation .reset-form-info {
    float: left;
    width: 100px;
    padding: 0 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
}

#dataAssociation .dataAssociation .reset-form-info:hover {
    background-color: #F16D5C;
    color: #fff;
}

#dataAssociation .add1 {
    background: url(../../../images/form/printUtil/group-icon.png) no-repeat center;
}
#dataAssociation .add2 {
    background: url(../../../images/form/printUtil/add-icon.png) no-repeat center;
}

#dataAssociation .delete1 {
    background: url(../../../images/form/printUtil/group-reduce-icon.png) no-repeat center;
}
#dataAssociation .delete2 {
    background: url(../../../images/form/printUtil/delet-icon.png) no-repeat center;
}

#dataAssociation .add1,
#dataAssociation .add2,
#dataAssociation .delete1,
#dataAssociation .delete2 {
    float: left;
    width: 20px;
    height: 20px;
    padding-top: 5px;
    margin-left: 5px;
    cursor: pointer;
}

/* 清除浮动方式四：给父元素添加双伪元素 */
.clearfix:before,
.clearfix:after {
    content: '';
    display: table;
}
.clearfix:after {
    clear: both;
}

