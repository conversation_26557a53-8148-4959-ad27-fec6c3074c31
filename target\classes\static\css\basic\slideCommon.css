.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.j-search-con {
  display: flex;
  align-items: center;
  position: relative;
  width: 240px;
  cursor: pointer;
}
.j-search-con.slideShow {
  display: block;
}
.j-search-con.slideShow .j-arrow {
  transform: rotate(180deg);
  background: url(../../images/cultivation/drop-down-icon1.png) no-repeat center;
}
.j-search-con.slideShow .j-select-year {
  display: block;
}
.j-search-con.slideShow .schoolSel {
  border: 1px solid #4D88FF;
}
.j-search-con.slideShow .fuzzy-query-input {
  border: 1px solid #4D88FF;
}
.j-search-con.slideShow .j-select-search {
  display: block;
}
.j-search-con .j-select-year {
  left: 0;
}
.j-search-con input {
  width: 100%;
  height: 34px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  padding: 0 20px 0 10px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  cursor: pointer;
}
.j-search-con input::placeholder {
  color: #86909c;
}
.j-search-con input:focus {
  border: 1px solid #4D88FF;
}
.j-search-con input.error {
  border-color: #F76560;
}
.j-search-con .j-arrow {
  width: 10px;
  height: 10px;
  background: url(../../images/basic/drop-down-icon1.png) no-repeat center;
  position: absolute;
  right: 12px;
  top: 12px;
  pointer-events: none;
}
.j-search-con .j-arrow.j-arrow-slide {
  transform: rotate(180deg);
}
.j-search-con .j-select-year {
  position: absolute;
  top: 40px;
  left: -1px;
  z-index: 999;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  display: none;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
  /*  &.slideShow {
            display: block;
        } */
}
.j-search-con .j-select-year.slideShowTop {
  display: block;
  top: unset;
  bottom: 40px;
}
.j-search-con .j-select-year .search {
  height: 36px;
  background: #f5f7fa;
  border-radius: 18px;
  margin: 11px 10px;
}
.j-search-con .j-select-year .search input {
  border: none;
  width: 84%;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  float: left;
}
.j-search-con .j-select-year .search span {
  cursor: pointer;
  float: left;
  width: 16px;
  height: 16px;
  background: url(../images/search-icon.png) no-repeat center;
  margin-top: 10px;
}
.j-search-con .j-select-year .all-selects {
  line-height: 17px;
  margin-bottom: 4px;
  height: 17px;
  padding: 0 14px;
  font-size: 12px;
  color: #6b89b3;
  cursor: pointer;
  user-select: none;
}
.j-search-con .j-select-year ul {
  overflow: hidden;
  max-height: 200px;
  overflow-y: auto;
}
.j-search-con .j-select-year ul li {
  line-height: 40px;
  text-align: left;
  text-indent: 16px;
  cursor: pointer;
  font-size: 14px;
  color: #4e5969;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-right: 30px;
  background-color: #ffffff;
  background-image: url("../../images/cultivation/check-icon.png");
  background-repeat: no-repeat;
  background-position: 96% center;
}
.j-search-con .j-select-year ul li:hover {
  background-color: #e1ebff;
  color: #4d88ff;
  font-weight: 500;
}
.j-search-con .j-select-year ul li.active {
  background-image: url("../../images/cultivation/check-cur.png");
  font-weight: 500;
}
.j-search-con .j-select-search {
  position: absolute;
  top: 40px;
  left: -1px;
  z-index: 999;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  display: none;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}
.j-search-con .j-select-search ul {
  overflow: hidden;
  max-height: 200px;
  overflow-y: auto;
}
.j-search-con .j-select-search ul li {
  line-height: 40px;
  text-align: left;
  text-indent: 16px;
  cursor: pointer;
  font-size: 14px;
  color: #4e5969;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-right: 30px;
  background-color: #ffffff;
}
.j-search-con .j-select-search ul li:hover {
  background-color: #e1ebff;
  color: #4d88ff;
  font-weight: 500;
}
.j-search-con .j-select-search ul li.active {
  color: #4d88ff;
  font-weight: 500;
}
.j-search-con.single-box .j-select-year ul li {
  background-image: url("../images/radio-icon.png");
}
.j-search-con.single-box .j-select-year ul li.active {
  background-image: url("../images/radio-cur-icon.png");
}
