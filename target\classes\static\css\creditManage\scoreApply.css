.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dialog {
  margin: 0 20px;
  position: fixed;
  top: 50%;
  left: 20px;
  right: 20px;
  transform: translateY(-50%);
  background-color: #f9fafb;
}
.dialog .head {
  height: 40px;
  border-bottom: 1px solid #f2f2f2;
  line-height: 40px;
  margin: 0 14px;
  color: #131b26;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.dialog .head h1 {
  text-indent: 30px;
  font-size: 16px;
}
.dialog .head span {
  width: 29px;
  height: 29px;
  background: url("../images/close1.png") no-repeat center;
  cursor: pointer;
}
.dialog .head .head-opt {
  display: flex;
  align-items: center;
}
.dialog .head .head-opt button {
  width: 86px;
  height: 34px;
  border-radius: 4px;
  background: #fff;
  font-size: 14px;
  cursor: pointer;
  /* 灰色投影 */
}
.dialog .head .head-opt button.btn-cancel {
  margin-right: 16px;
  color: #86909c;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  border: 1px solid #c9cdd4;
}
.dialog .head .head-opt button.btn-submit {
  border-radius: 4px;
  background: #4d88ff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  color: #fff;
  border: 1px solid #4d88ff;
}
.dialog .con-title {
  color: #6581ba;
  font-size: 16px;
  line-height: 20px;
  padding-left: 10px;
  position: relative;
  margin-bottom: 25px;
}
.dialog .con-title::after {
  content: "";
  width: 3px;
  height: 18px;
  background: #6581ba;
  position: absolute;
  left: 0;
  top: 1px;
  border-radius: 2px;
}
.dialog .sel-stu {
  overflow: hidden;
  margin: 22px 14px 0px;
  background-color: #fff;
  padding: 14px 19px;
}
.dialog .sel-stu .form-stu {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.dialog .dialog-conten {
  max-height: calc(100vh - 300px);
  overflow: hidden;
  overflow-y: auto;
}
.dialog-apply {
  overflow: hidden;
  position: unset;
  transform: translate(0, 0);
  margin: 0;
  background-color: #ffffff;
  min-height: calc(100vh);
  border-radius: 6px;
}
.dialog-apply .head {
  height: 50px;
  line-height: 50px;
}
.dialog-apply .head h1 {
  position: relative;
  text-indent: 10px;
}
.dialog-apply .head h1:after {
  content: "";
  width: 3px;
  height: 16px;
  background: #6581ba;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 14px;
}
.dialog-apply .dialog-conten {
  max-height: unset;
}
.dialog-apply .dialog-btn {
  justify-content: center;
  margin-bottom: 30px;
}
.sel-result-wrap {
  display: flex;
}
.sel-result-wrap .sel-result {
  width: 56%;
}
.sel-result-wrap .set-result {
  width: 44%;
  flex-shrink: 0;
  border-left: 1px solid #e8ebf1;
  padding: 0 30px;
  box-sizing: border-box;
}
.sel-result-wrap .set-result .set-title {
  color: #1d2129;
  font-size: 16px;
  margin: 20px 0;
}
.item {
  padding: 0 14px;
  margin-bottom: 40px;
}
.item .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.item .i-top span {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #484f5d;
  position: relative;
  display: block;
  margin-right: 24px;
}
.item .i-top span:after {
  content: "";
  position: absolute;
  left: 0;
  top: 4px;
  background: #4d88ff;
  border-radius: 1px;
  width: 3px;
  height: 14px;
}
.item .i-top .arrow {
  padding-right: 14px;
  position: relative;
  font-weight: 400;
  font-size: 14px;
  color: #4c88ff;
  cursor: pointer;
}
.item .i-top .arrow:after {
  content: "";
  position: absolute;
  right: 0;
  top: 5px;
  width: 10px;
  height: 10px;
  background: url(../../images/creditManage/stow-icon.png) no-repeat right center;
}
.item .i-con {
  overflow: hidden;
  /*    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start; */
  width: 100%;
}
.item .i-con .course-inform {
  flex-shrink: 0;
  width: 452px;
  min-height: 520px;
  margin-right: 24px;
  padding: 20px 24px;
  background: #f9fafb;
  border-radius: 4px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.item .i-con .course-inform h4 {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #484f5d;
  margin-bottom: 16px;
}
.item .i-con .course-inform ul {
  overflow: hidden;
}
.item .i-con .course-inform ul li {
  width: 50%;
  height: 20px;
  margin-bottom: 16px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  float: left;
}
.item .i-con .course-inform ul li .name {
  font-weight: 400;
  font-size: 14px;
  color: #717b91;
  flex-shrink: 0;
}
.item .i-con .course-inform ul li .tit {
  color: #484f5d;
  font-weight: 400;
  font-size: 14px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.item .i-con .mutate {
  width: 78px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  flex-wrap: wrap;
}
.item .i-con .mutate .up {
  width: 30px;
  height: 30px;
  background: url(../../images/creditManage/switchLeft.png) no-repeat;
  background-size: 30px 30px;
  margin-bottom: 12px;
}
.item .i-con .class-box {
  flex: 1;
  padding: 20px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  border-radius: 4px;
  flex-shrink: 0;
  background: #fff;
}
.item .i-con .class-box .j-search-con {
  width: 200px;
}
.item .i-con .class-box .j-search-vague input {
  width: 200px;
}
.item .i-con .class-box .cb-top {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 20px;
  margin-bottom: 16px;
}
.item .i-con .class-box .cb-top .tit {
  font-weight: 600;
  font-size: 14px;
  color: #484f5d;
  margin-right: 20px;
}
.item .i-con .class-box .cb-top .radio {
  overflow: hidden;
}
.item .i-con .class-box .cb-top .radio span {
  float: left;
  padding-left: 20px;
  background: url(../../images/creditManage/radio-icon.png) no-repeat left center;
  background-size: 14px;
  font-size: 14px;
  color: #717b91;
  margin-right: 36px;
  cursor: pointer;
}
.item .i-con .class-box .cb-top .radio span.cur {
  background: url(../../images/creditManage/radio-cur.png) no-repeat left center;
  background-size: 14px;
}
.item .i-con .class-box .cb-top .arrow {
  padding-right: 14px;
  position: relative;
  font-weight: 400;
  font-size: 14px;
  color: #4c88ff;
  cursor: pointer;
}
.item .i-con .class-box .cb-top .arrow:after {
  content: "";
  position: absolute;
  right: 0;
  top: 5px;
  width: 10px;
  height: 10px;
  background: url(../../images/creditManage/stow-icon.png) no-repeat right center;
}
.item .i-con .class-box .j-search {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
}
.item .i-con .class-box .j-search-item {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 22px;
  position: relative;
  float: left;
  margin-bottom: 12px;
}
.item .i-con .class-box .j-search-item h5 {
  width: 84px;
  color: #474c59;
  font-size: 14px;
}
.item .i-con .class-box .j-search-item .j-search-con input {
  width: 100%;
}
.item .i-con .class-box .j-search-item .j-search-con .j-select-year {
  width: 100%;
}
.item .i-con .class-box .j-search-item .j-search-con.multiple-box .j-select-year ul li {
  background: url(../../images/creditManage/check-icon.png) no-repeat 92% center;
}
.item .i-con .class-box .j-search-item .j-search-con.multiple-box .j-select-year ul li.active {
  background: url(../../images/creditManage/check-cur.png) no-repeat 92% center;
  background-size: 16px;
}
.item .i-con .class-box .j-search-item .j-search-con.multiple-box .allSelect {
  background: url(../../images/creditManage/check-icon.png) no-repeat 92% center;
  background-size: 16px;
}
.item .i-con .class-box .j-search-item .j-search-con.multiple-box .allSelect.cur {
  background: url(../../images/creditManage/check-cur.png) no-repeat 92% center;
}
.j-table {
  position: relative;
}
.j-table .sel-score {
  padding-left: 20px;
  background: url("../../images/creditManage/notes.png") no-repeat left center;
  position: absolute;
  bottom: 10px;
  left: 10px;
  font-size: 14px;
  color: #8f97a8;
}
.j-table .sel-score span {
  color: #6aa1ff;
  font-weight: bold;
}
.j-table .sel-score.sel-total {
  padding-left: 0;
  background: none;
}
.j-table .sel-score-tips {
  padding: 10px 24px;
  display: flex;
  align-items: center;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  position: absolute;
  left: 0;
  top: 99%;
  z-index: 999;
  font-size: 14px;
  display: none;
}
.j-table .sel-score-tips::after {
  content: "";
  background: url("../../images/creditManage/arrow.png") no-repeat center;
  width: 12px;
  height: 8px;
  position: absolute;
  top: -7px;
  left: 49px;
  z-index: 99;
}
.j-table .sel-score-tips::before {
  content: "";
  width: 4px;
  height: 4px;
  background-color: #4d88ff;
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
}
.dialog-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 70px;
}
.dialog-btn button {
  width: 92px;
  height: 38px;
  font-size: 14px;
  border-radius: 6px;
  cursor: pointer;
}
.dialog-btn button.dialog-cancel {
  border: 1px solid #8cbbff;
  color: #3a8bff;
  margin-right: 24px;
  border-radius: 6px;
  background-color: #f9fafb;
}
.dialog-btn button.dialog-submit {
  color: #fff;
  background: #3a8bff;
  border: 1px solid #3a8bff;
  margin-right: 44px;
}
.set-button {
  border: 1px solid #1a79ff;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 4px;
  width: 88px;
  height: 34px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  color: #1a79ff;
  float: left;
  box-sizing: border-box;
  margin-bottom: 12px;
  box-shadow: unset;
}
.set-button.layui-bg-blue {
  background-color: #1a79ff !important;
  margin-right: 13px;
}
.set-button.layui-border-blue {
  margin-left: 0;
}
.dialog-wrap {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}
.dialog-wrap .dialog-stu {
  background: #fff;
}
.dialog-wrap .dialog-stu .form-stu {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 67px;
  margin: 67px 24px 10px;
}
.dialog-wrap .dialog-stu .form-stu .item-search-con {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  flex: 1;
}
.dialog-wrap .dialog-stu .form-stu .item-search-con .layui-form-item {
  width: 29.33%;
}
.dialog-wrap .dialog-stu .form-stu .item-search-con .layui-form-item .layui-input {
  width: 100%;
}
.dialog-wrap .dialog-stu .form-stu .item-search-con .layui-form-item .j-search-vague {
  width: 100%;
}
.dialog-wrap .dialog-stu .form-stu .item-search-con .layui-form-item .j-search-vague input {
  width: 100%;
}
.dialog-wrap .dialog-stu .form-stu .item-search-btn .layui-form-item {
  margin-right: 0;
  width: 118px;
  border-left: 1px solid #ddd;
}
.dialog-wrap .dialog-stu .form-stu .item-search-btn .layui-form-item .set-button {
  width: 86px;
  margin-right: 0;
  margin-left: 32px;
}
.dialog-wrap .dialog-stu .form-stu .item-search-btn .layui-form-item .set-button.layui-bg-blue {
  margin-bottom: 16px;
}
.dialog-wrap .dialog-stu .form-stu .layui-form-label {
  width: 48px;
}
.dialog-wrap .dialog-stu .form-stu .layui-input-block {
  margin-left: 56px;
}
.dialog-wrap .dialog-stu .form-stu .set-button {
  margin-bottom: 0;
  width: 64px;
}
.dialog-wrap .dialog-stu .layui-form-item {
  margin-right: 24px;
}
.dialog-wrap .dialog-stu .j-table {
  margin: 0 19px;
}
.dialog-wrap .dialog-stu .dialogCon {
  min-height: calc(100vh - 400px);
}
.dialog-wrap .dialog-stu .dialog-btn .dialog-cancel {
  background-color: #ffffff;
}
.dialog-wrap .dialog-stu .dialog-close {
  width: 20px;
  height: 20px;
  background: url("../../images/creditManage/close1.png") no-repeat center;
  background-size: 20px;
  cursor: pointer;
  position: absolute;
  right: 28px;
  top: 16px;
}
.j-search-vague {
  width: 240px;
  position: relative;
}
.j-search-vague input {
  width: 240px;
  height: 34px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  padding: 0 20px 0 10px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  cursor: pointer;
}
.j-search-vague .j-select-list {
  overflow: hidden;
  border: 1px solid #ddd;
  border-radius: 4px;
  position: absolute;
  z-index: 999;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  display: none;
}
.j-search-vague .j-select-list ul li {
  line-height: 36px;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: #4e5969;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: borDer-box;
  padding: 0 16px;
  background-color: #ffffff;
}
.j-search-vague .j-select-list ul li:hover {
  background-color: #f2f2f2;
}
.j-search-vague .j-select-list.slideShow {
  top: 40px;
  -webkit-animation-name: layui-up;
  animation-name: layui-up;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
.j-search-con .j-select-year {
  max-height: 210px !important;
}
@keyframes layui-up {
  from {
    transform: translate3d(0, 100%, 0);
    opacity: 0.3;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@-webkit-keyframes layui-upbit {
  /* 微微往上滑入 */
  from {
    -webkit-transform: translate3d(0, 30px, 0);
    opacity: 0.3;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes layui-upbit {
  from {
    transform: translate3d(0, 30px, 0);
    opacity: 0.3;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.layui-anim-upbit {
  -webkit-animation-name: layui-upbit;
  animation-name: layui-upbit;
}
.layui-input {
  width: 200px;
  border-color: #e5e6eb;
  height: 34px;
}
.layui-form-label {
  padding: 0;
  width: 154px;
  line-height: 34px;
  color: #595959;
  font-size: 16px;
  text-align: left;
  margin-right: 5px;
  display: flex;
  align-items: center;
}
.layui-form-label em {
  color: #ef0e0e;
  padding-right: 2px;
}
.layui-input-block {
  margin-left: 124px;
}
.layui-form-item {
  margin-right: 50px;
}
.layui-table-page {
  text-align: right;
}
.layui-layer-tips .layui-layer-content {
  color: #737b86 !important;
}
