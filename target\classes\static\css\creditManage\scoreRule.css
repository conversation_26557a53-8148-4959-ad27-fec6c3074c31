/*body {*/
/*  background-color: #FFFFFF;*/
/*  padding: 0;*/
/*}*/
.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.layui-form-radioed > i,
.layui-form-radio > i:hover {
    color: #3A8BFF;
}

.layui-form-label {
    padding: 0;
    width: 154px;
    line-height: 34px;
    color: #131B26;
    font-size: 14px;
    text-align: left;
    margin-right: 5px;
}

.layui-form-label em {
    color: #E23131;
}

.layui-form-radio * {
    color: #86909C;
}

.layui-input-block {
    margin-left: 159px;
}

.layui-form-radio {
    margin: 0;
    margin-right: 42px;
    line-height: 34px;
}

.layui-input {
    width: 240px;
    border-color: #E5E6EB;
}

.layui-input.input-disabled {
    color: #CBD0D7;
}

.mar30 {
    margin-top: 30px;
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.j-search-con.single-box .j-select-year ul li {
    padding-left: 20px;
    padding-right: 36px;
    /*background: url(../../images/creditManage/radio-icon.png) no-repeat 204px center;*/
    background-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.j-search-con.single-box .j-select-year ul li:hover {
    /*background: #e1ebff url(../../images/creditManage/radio-icon.png) no-repeat 204px center;*/
}

.j-search-con.single-box .j-select-year ul li.active {
    /*background: url(../../images/creditManage/radio-cur-icon.png) no-repeat 204px center;*/
    background-size: 16px;
}

.con-title {
    color: #6581BA;
    font-size: 15px;
    line-height: 20px;
    padding-left: 10px;
    position: relative;
    margin-bottom: 25px;
}

.con-title::after {
    content: "";
    width: 4px;
    height: 16px;
    background: #3A8BFF;
    position: absolute;
    left: 0;
    top: 2px;
}

.top {
    width: 100%;
    height: 60px;
    position: relative;
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
    align-items: center;
}

.top .title {
    padding-left: 30px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 36px;
}

.top .title .back {
    padding-left: 22px;
    background: url(../../images/creditManage/back-icon.png) no-repeat left center;
    background-size: 16px;
    color: #7D92B3;
    font-size: 14px;
    cursor: pointer;
    margin-right: 16px;
}

.top .title .levelone {
    padding-left: 9px;
    position: relative;
    color: #1D2129;
    font-weight: 700;
    font-size: 16px;
    margin-right: 6px;
}

.top .title .levelone:after {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    width: 3px;
    height: 16px;
    background: #4D88FF;
    border-radius: 2px;
}

.top .title .icon {
    width: 12px;
    height: 12px;
    background: url(../../images/creditManage/arrow-right.png) no-repeat center;
    background-size: 12px;
    margin-right: 6px;
}

.top .title .leveltwo {
    color: #1D2129;
    font-weight: 700;
    font-size: 16px;
}

.top .btn {
    position: absolute;
    top: 17px;
    right: 28px;
    width: 116px;
    height: 36px;
    text-align: center;
    line-height: 36px;
    cursor: pointer;
    font-size: 14px;
    color: #FFFFFF;
    background: #4D88FF;
    box-shadow: 0px 0px 10px #4D88FF;
    border-radius: 4px;
}

.top h4 {
    position: relative;
    color: #1D2129;
    font-size: 16px;
    margin-left: 30px;
    padding-left: 9px;
    font-weight: bold;
}

.top h4::after {
    content: "";
    width: 3px;
    height: 16px;
    background: #4D88FF;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 4px;
}

.container {
    overflow: hidden;
    background-color: #fff;
    /*margin-left: 13px;*/
    padding: 35px 56px;
    height: 100%;
}

.container.ruleContainer {
    margin-left: 0;
    padding: 10px 0;
}

.container .showMore {
    padding: 16px 0;
    margin-bottom: 16px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
    border-bottom: 1px solid #F2F2F2;
}

.container .showMore span {
    height: 20px;
    padding-left: 7px;
    position: relative;
    color: #6581BA;
    font-size: 16px;
    line-height: 20px;
    margin-right: 24px;
}

.container .showMore span:after {
    content: "";
    position: absolute;
    left: 0;
    background: #6581BA;
    border-radius: 1.5px;
    width: 3px;
    height: 18px;
    top: 1px;
}

.container .showMore em {
    width: 12px;
    height: 12px;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    background: url(../../images/creditManage/slide.png) no-repeat center;
    transform: rotate(-90deg);
    -webkit-transform: rotate(-90deg);
    cursor: pointer;
}

.container .showMore.clicked em {
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
}

.container #formCon {
    min-height: calc(100vh - 280px);
}

.container #formCon .radio {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
}

.container #formCon .radio span {
    padding-left: 24px;
    font-size: 14px;
    color: #86909C;
    background: url(../../images/creditManage/radio-icon.png) no-repeat left center;
    background-size: 16px;
    cursor: pointer;
    margin-right: 24px;
}

.container #formCon .radio span.cur {
    background: url(../../images/creditManage/radio-cur-icon.png) no-repeat left center;
    background-size: 16px;
}

.container .layui-form {
    color: #131B26;
}

.container .examCon {
    margin-bottom: 14px;
    display: none;
}

.container .examCon .addRule {
    width: 104px;
    height: 28px;
    background: #3A8BFF;
    border-radius: 6px;
    text-align: center;
    line-height: 28px;
    color: #FFFFFF;
    cursor: pointer;
    margin-bottom: 28px;
}

.container .actualCredits {
    width: auto;
    height: 34px;
    text-align: center;
    line-height: 32px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    color: #ACB4BF;
    font-size: 14px;
    box-sizing: border-box;
    display: inline-block;
    padding: 0 10px;
    cursor: pointer;
}

.container .save-btn {
    background: #3A8BFF;
    border-radius: 6px;
    width: 96px;
    height: 36px;
    color: #FFFFFF;
    font-size: 14px;
    text-align: center;
    line-height: 36px;
    cursor: pointer;
    margin: 30px auto 30px;
}

.oprate-table {
    display: flex;
    justify-content: center;
    font-size: 14px;
}

.oprate-table span {
    color: #4C88FF;
    margin-right: 10px;
    cursor: pointer;
}

.oprate-table span.delete {
    color: #F76560;
    margin-right: 0;
}

.dialogRule {
    width: 887px;
    background-color: #FFFFFF;
    border-radius: 10px;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.dialogRule .dialog-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: inset 0px -1px 0px #f2f2f2;
    height: 66px;
}

.dialogRule .dialog-title h3 {
    color: #131B26;
    font-size: 18px;
    text-indent: 10px;
}

.dialogRule .dialog-title .dialog-close {
    width: 18px;
    height: 22px;
    background: url('../../images/creditManage/close.png') no-repeat center;
    background-size: 18px;
    margin-right: 14px;
    cursor: pointer;
}

.dialogRule .con-title {
    font-weight: 900;
}

.dialogRule .dialog-con {
    padding: 56px 20px 0;
    box-sizing: border-box;
    height: 663px;
    overflow-y: auto;
}

.dialogRule .dialog-con .layui-form-label {
    width: 110px;
}

.dialogRule .dialog-con .layui-input-block {
    margin-left: 110px;
}

.dialogRule .dialog-con .showMore {
    padding: 16px 0;
    margin-bottom: 16px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
}

.dialogRule .dialog-con .showMore span {
    height: 20px;
    padding-left: 7px;
    position: relative;
    color: #6581BA;
    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    margin-right: 24px;
}

.dialogRule .dialog-con .showMore span:after {
    content: "";
    position: absolute;
    left: 0;
    background: #6581BA;
    border-radius: 2px;
    width: 3px;
    height: 18px;
    top: 1px;
}

.dialogRule .dialog-con .addScoreRule {
    background: #3A8BFF;
    border-radius: 5px;
    width: 49px;
    height: 23px;
    color: #FFFFFF;
    text-align: center;
    line-height: 23px;
    margin-bottom: 30px;
    cursor: pointer;
}

.dialogRule .dialog-con .lab-con .lab {
    margin-bottom: 20px;
}

.dialogRule .dialog-con .lab-con .lab.t-lab {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
}

.dialogRule .dialog-con .lab-con .lab.t-lab .f-top {
    font-weight: 400;
    font-size: 14px;
    line-height: 34px;
    color: #1D2129;
    margin-right: 14px;
    margin-bottom: 0;
}

.dialogRule .dialog-con .lab-con .lab.t-lab .input {
    width: 240px;
    height: 34px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    line-height: 32px;
    padding-left: 14px;
    font-size: 14px;
    color: #ACB4BF;
    font-weight: 400;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.dialogRule .dialog-con .lab-con .lab:last-child {
    margin-bottom: 0;
}

.dialogRule .dialog-con .lab-con .lab.btns {
    overflow: hidden;
}

.dialogRule .dialog-con .lab-con .lab .f-top {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #1D2129;
    margin-bottom: 10px;
}

.dialogRule .dialog-con .lab-con .lab .f-top em {
    color: #F76560;
    display: inline-block;
    margin-right: 4px;
    vertical-align: sub;
}

.dialogRule .dialog-con .lab-con .lab .keyboard {
    float: right;
    width: 240px;
}

.dialogRule .dialog-con .lab-con .lab .keyboard h3 {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 10px;
    color: #1D2129;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con {
    overflow: hidden;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span {
    float: left;
    width: 54px;
    height: 54px;
    border: 1px solid #C9CDD4;
    border-radius: 4px;
    background-color: #FFFFFF;
    margin-right: 8px;
    margin-bottom: 8px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    font-weight: 400;
    font-size: 14px;
    color: #4E5969;
    text-align: center;
    line-height: 52px;
    cursor: pointer;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span:nth-child(4n) {
    margin-right: 0;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.sign {
    overflow: hidden;
    text-indent: 99em;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.spot {
    position: relative;
    overflow: hidden;
    text-indent: 99em;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.spot:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 4px;
    height: 4px;
    background: #4E5969;
    margin-left: -2px;
    margin-top: -2px;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.spot:hover:after {
    background-color: #4D88FF;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.zero {
    width: 116px;
    height: 54px;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span:hover {
    background: #E1EBFF;
    color: #4D88FF;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span:last-child {
    margin-right: 0;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.lbracket {
    background: #ffffff url(../../images/creditManage/sign-lkh.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.lbracket:hover {
    background: #e1ebff url(../../images/creditManage/sign-lkh-cur.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.rbracket {
    background: #ffffff url(../../images/creditManage/sign-rkh.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.rbracket:hover {
    background: #e1ebff url(../../images/creditManage/sign-rkh-cur.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.delet {
    background: #ffffff url(../../images/creditManage/sign-back.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.delet:hover {
    background: #e1ebff url(../../images/creditManage/sign-back-cur.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.sign-add {
    background: #ffffff url(../../images/creditManage/sign-add.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.sign-add:hover {
    background: #e1ebff url(../../images/creditManage/sign-add-cur.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.sign-cancle {
    background: #ffffff url(../../images/creditManage/sign-jian.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.sign-cancle:hover {
    background: #e1ebff url(../../images/creditManage/sign-jian-cur.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.sign-mul {
    background: #ffffff url(../../images/creditManage/sign-ceng.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.sign-mul:hover {
    background: #e1ebff url(../../images/creditManage/sign-ceng-cur.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.sign-except {
    background: #ffffff url(../../images/creditManage/sign-chu.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .keyboard .k-con span.sign-except:hover {
    background: #e1ebff url(../../images/creditManage/sign-chu-cur.png) no-repeat center;
}

.dialogRule .dialog-con .lab-con .lab .available {
    float: left;
}

.dialogRule .dialog-con .lab-con .lab .available h3 {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 10px;
    color: #1D2129;
}

.dialogRule .dialog-con .lab-con .lab .available .a-con {
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    background: #F7F8FA;
    width: 250px;
    height: 264px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding: 20px 19px;
}

.dialogRule .dialog-con .lab-con .lab .available .a-con ul {
    overflow: hidden;
}

.dialogRule .dialog-con .lab-con .lab .available .a-con ul li {
    float: left;
    width: 100px;
    height: 28px;
    border: 1px solid #4D88FF;
    border-radius: 15px;
    background: #FFFFFF;
    color: #4D88FF;
    font-size: 14px;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
    margin-right: 10px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    margin-bottom: 10px;
}

.dialogRule .dialog-con .lab-con .lab .available .a-con ul li:nth-child(2n) {
    margin-right: 0;
}

.dialogRule .dialog-con .lab-con .lab .available .a-con ul li.disabled {
    border: 1px solid #A3B7CC;
    font-size: 14px;
    color: #C9CDD4;
    cursor: default;
}

.dialogRule .dialog-con .lab-con .lab .available .a-con ul li.disabled:hover {
    background: #FFFFFF;
}

.dialogRule .dialog-con .lab-con .lab .available .a-con ul li:hover {
    background: #E1EBFF;
}

.dialogRule .dialog-con .lab-con .lab .keyboard {
    float: left;
    margin-left: 40px;
}

.dialogRule .dialog-con .lab-con .lab .section {
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    background: #F7F8FA;
    width: 640px;
    min-height: 60px;
    height: auto;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding: 10px 19px;
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
    color: #4E5969;
    font-size: 14px;
    line-height: 28px;
}

.dialogRule .dialog-con .lab-con .lab .section .tit {
    flex: 0 0 68px;
}

.dialogRule .dialog-con .lab-con .lab .section .s-con {
    overflow: hidden;
}

.dialogRule .dialog-con .lab-con .lab .section .s-con span {
    float: left;
    margin: 0 5px;
    width: 100px;
    height: 28px;
    background: #4D88FF;
    border-radius: 15px;
    text-align: center;
    line-height: 28px;
    font-size: 14px;
    color: #FFFFFF;
    margin-bottom: 10px;
}

.dialogRule .dialog-con .lab-con .lab .section .s-con .sign {
    font-weight: 500;
    font-size: 20px;
    color: #86909C;
    line-height: 28px;
    margin: 0 5px;
    width: auto;
    height: 28px;
    background: transparent;
    border-radius: 0;
}

.dialogRule .dialog-con .lab-con .lab .section .s-con .num {
    font-weight: 500;
    font-size: 18px;
    color: #86909C;
    line-height: 28px;
    margin: 0 5px;
    width: auto;
    height: 28px;
    background: transparent;
    border-radius: 0;
}

.dialogRule .dialog-con .lab-con .lab .section .s-con .num.spot {
    margin: 0;
}

.dialogRule .dialog-con .lab-con .lab .oprate {
    display: flex;
    height: 34px;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.dialogRule .dialog-con .lab-con .lab .oprate .inputs {
    margin-right: 10px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    width: 199px;
    height: 34px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.dialogRule .dialog-con .lab-con .lab .oprate .inputs.errors {
    border: 1px solid #F98981;
}

.dialogRule .dialog-con .lab-con .lab .oprate .inputs.errors .error {
    display: block;
}

.dialogRule .dialog-con .lab-con .lab .oprate .inputs.errors .inp {
    border-right: 1px solid #F98981 !important;
}

.dialogRule .dialog-con .lab-con .lab .oprate .inputs.right-inp .error {
    left: 80px;
}

.dialogRule .dialog-con .lab-con .lab .oprate .inputs.right-inp input {
    border-right: none !important;
}

.dialogRule .dialog-con .lab-con .lab .oprate .inputs.right-inp .select-input {
    border-right: 1px solid #E5E6EB;
}

.dialogRule .dialog-con .lab-con .lab .oprate .inputs.right-inp.errors .error {
    display: block;
}

.dialogRule .dialog-con .lab-con .lab .oprate .inputs.right-inp.errors .select-input {
    border-right: 1px solid #F98981 !important;
}

.dialogRule .dialog-con .lab-con .lab .oprate .inputs input {
    width: 120px;
    height: 32px;
    background-color: transparent;
    border: none !important;
    border-right: 1px solid #E5E6EB !important;
    border-radius: 0 !important;
}

.dialogRule .dialog-con .lab-con .lab .oprate .inputs .error {
    position: absolute;
    display: none;
    left: 0;
    top: 34px;
    font-weight: 400;
    font-size: 12px;
    line-height: 17px;
    color: #F76560;
}

.dialogRule .dialog-con .lab-con .lab .oprate .select-input {
    width: 80px;
    height: 32px;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    cursor: pointer;
}

.dialogRule .dialog-con .lab-con .lab .oprate .select-input.score-sel {
    width: 120px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    margin-right: 10px;
    height: 34px;
}

.dialogRule .dialog-con .lab-con .lab .oprate .select-input em {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 10px;
    height: 10px;
    background: url(../../images/creditManage/down-icon.png) no-repeat center;
    background-size: 10px;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}

.dialogRule .dialog-con .lab-con .lab .oprate .select-input .name {
    font-size: 14px;
    color: #86909C;
    padding-left: 10px;
    width: 100%;
    height: 32px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    line-height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dialogRule .dialog-con .lab-con .lab .oprate .select-input .name.ckd {
    color: #1D2129;
}

.dialogRule .dialog-con .lab-con .lab .oprate .select-input.clicked em {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    background: url(../../images/creditManage/down-icon.png) no-repeat center;
    background-size: 10px;
}

.dialogRule .dialog-con .lab-con .lab .oprate .select-input.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
}

.dialogRule .dialog-con .lab-con .lab .oprate .select-input .select-dropdown {
    width: inherit;
    max-height: 320px;
    overflow: auto;
    left: -1px;
    margin: 5px 0;
    padding: 6px 0;
    background-color: #fff;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
    border-radius: 8px;
    position: absolute;
    transform: translate(0, -50px);
    -webkit-transform: translate(0, -50px);
    z-index: 900;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
    z-index: -1;
}

.dialogRule .dialog-con .lab-con .lab .oprate .select-input .select-dropdown .dropdown-list li {
    margin: 0;
    line-height: normal;
    padding: 0 16px;
    clear: both;
    color: #131B26;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
}

.dialogRule .dialog-con .lab-con .lab .oprate .select-input .select-dropdown .dropdown-list li:hover {
    background-color: #F5F7FA;
}

.dialogRule .dialog-con .lab-con .lab .oprate .select-input .select-dropdown .dropdown-list li.cur {
    color: #616EE6;
}

.dialogRule .dialog-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 80px;
}

.dialogRule .dialog-footer span {
    width: 68px;
    height: 36px;
    font-size: 14px;
    text-align: center;
    line-height: 34px;
    border-radius: 6px;
    box-sizing: border-box;
    cursor: pointer;
}

.dialogRule .dialog-footer span.btn-cancel {
    color: #3A8BFF;
    border: 1px solid #3A8BFF;
    margin-right: 42px;
}

.dialogRule .dialog-footer span.btn-sure {
    color: #FFFFFF;
    border: 1px solid #3A8BFF;
    background-color: #3A8BFF;
    margin-right: 42px;
}

.formulaConversion .popup-box {
    width: 703px;
    height: auto;
}

.formulaConversion .popup-box .pu-con {
    padding: 20px 40px;
}

.formulaConversion .popup-box .pu-con .lab {
    margin-bottom: 20px;
}

.formulaConversion .popup-box .pu-con .lab.t-lab {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
}

.formulaConversion .popup-box .pu-con .lab.t-lab .f-top {
    font-weight: 400;
    font-size: 14px;
    line-height: 34px;
    color: #1D2129;
    margin-right: 14px;
    margin-bottom: 0;
}

.formulaConversion .popup-box .pu-con .lab.t-lab .input {
    width: 240px;
    height: 34px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    line-height: 32px;
    padding-left: 14px;
    font-size: 14px;
    color: #ACB4BF;
    font-weight: 400;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.formulaConversion .popup-box .pu-con .lab.t-lab1 .f-top {
    font-weight: 400;
    font-size: 14px;
    line-height: 34px;
    color: #1D2129;
    margin-right: 14px;
    margin-bottom: 0;
}

.formulaConversion .popup-box .pu-con .lab.t-lab1 .input {
    width: 128px;
    height: 34px;
    border: 1px solid #E5E6EB;
    background: #F8F8FA;
    border-radius: 4px;
    line-height: 32px;
    padding-left: 14px;
    font-size: 14px;
    color: #474C59;
    font-weight: 400;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.formulaConversion .popup-box .pu-con .lab.t-lab1 .f-item-wrap {
    display: flex;
    align-items: center;
}

.formulaConversion .popup-box .pu-con .lab.t-lab1 .f-item-wrap .f-item {
    display: flex;
    align-items: center;
    margin-right: 52px;
}

.formulaConversion .popup-box .pu-con .lab:last-child {
    margin-bottom: 0;
}

.formulaConversion .popup-box .pu-con .lab.btns {
    overflow: hidden;
}

.formulaConversion .popup-box .pu-con .lab .f-top {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #1D2129;
    margin-bottom: 10px;
}

.formulaConversion .popup-box .pu-con .lab .f-top em {
    color: #F76560;
    display: inline-block;
    margin-right: 4px;
    vertical-align: sub;
}

.formulaConversion .popup-box .pu-con .lab .keyboard {
    float: right;
    width: 240px;
}

.formulaConversion .popup-box .pu-con .lab .keyboard h3 {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 10px;
    color: #1D2129;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con {
    overflow: hidden;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span {
    float: left;
    width: 54px;
    height: 54px;
    border: 1px solid #C9CDD4;
    border-radius: 4px;
    background-color: #FFFFFF;
    margin-right: 8px;
    margin-bottom: 8px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    font-weight: 400;
    font-size: 14px;
    color: #4E5969;
    text-align: center;
    line-height: 52px;
    cursor: pointer;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span:nth-child(4n) {
    margin-right: 0;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign {
    overflow: hidden;
    text-indent: 99em;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.spot {
    position: relative;
    overflow: hidden;
    text-indent: 99em;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.spot:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 4px;
    height: 4px;
    background: #4E5969;
    margin-left: -2px;
    margin-top: -2px;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.spot:hover:after {
    background-color: #4D88FF;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.zero {
    width: 116px;
    height: 54px;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span:hover {
    background: #E1EBFF;
    color: #4D88FF;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span:last-child {
    margin-right: 0;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.lbracket {
    background: #ffffff url(../../images/creditManage/sign-lkh.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.lbracket:hover {
    background: #e1ebff url(../../images/creditManage/sign-lkh-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.rbracket {
    background: #ffffff url(../../images/creditManage/sign-rkh.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.rbracket:hover {
    background: #e1ebff url(../../images/creditManage/sign-rkh-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.delet {
    background: #ffffff url(../../images/creditManage/sign-back.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.delet:hover {
    background: #e1ebff url(../../images/creditManage/sign-back-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-add {
    background: #ffffff url(../../images/creditManage/sign-add.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-add:hover {
    background: #e1ebff url(../../images/creditManage/sign-add-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-cancle {
    background: #ffffff url(../../images/creditManage/sign-jian.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-cancle:hover {
    background: #e1ebff url(../../images/creditManage/sign-jian-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-mul {
    background: #ffffff url(../../images/creditManage/sign-ceng.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-mul:hover {
    background: #e1ebff url(../../images/creditManage/sign-ceng-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-except {
    background: #ffffff url(../../images/creditManage/sign-chu.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-except:hover {
    background: #e1ebff url(../../images/creditManage/sign-chu-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .available {
    float: left;
}

.formulaConversion .popup-box .pu-con .lab .available h3 {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 10px;
    color: #1D2129;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con {
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    background: #F7F8FA;
    width: 250px;
    height: 302px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding: 20px 19px;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul {
    overflow: hidden;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul li {
    float: left;
    width: 100px;
    height: 28px;
    border: 1px solid #4D88FF;
    border-radius: 15px;
    background: #FFFFFF;
    color: #4D88FF;
    font-size: 14px;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
    margin-right: 10px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    margin-bottom: 10px;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul li:nth-child(2n) {
    margin-right: 0;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul li.disabled {
    border: 1px solid #A3B7CC;
    font-size: 14px;
    color: #C9CDD4;
    cursor: default;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul li.disabled:hover {
    background: #FFFFFF;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul li:hover {
    background: #E1EBFF;
}

.formulaConversion .popup-box .pu-con .lab .keyboard {
    float: left;
    margin-left: 40px;
}

.formulaConversion .popup-box .pu-con .lab .section {
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    background: #F7F8FA;
    width: 640px;
    min-height: 140px;
    height: auto;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding: 20px 19px;
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
    color: #4E5969;
    font-size: 14px;
    line-height: 28px;
}

.formulaConversion .popup-box .pu-con .lab .section .tit {
    flex: 0 0 68px;
}

.formulaConversion .popup-box .pu-con .lab .section .s-con {
    overflow: hidden;
}

.formulaConversion .popup-box .pu-con .lab .section .s-con span {
    float: left;
    margin: 0 5px;
    width: 100px;
    height: 28px;
    background: #4D88FF;
    border-radius: 15px;
    text-align: center;
    line-height: 28px;
    font-size: 14px;
    color: #FFFFFF;
    margin-bottom: 10px;
}

.formulaConversion .popup-box .pu-con .lab .section .s-con .sign {
    font-weight: 500;
    font-size: 20px;
    color: #86909C;
    line-height: 28px;
    margin: 0 5px;
    width: auto;
    height: 28px;
    background: transparent;
    border-radius: 0;
}

.formulaConversion .popup-box .pu-con .lab .section .s-con .num {
    font-weight: 500;
    font-size: 18px;
    color: #86909C;
    line-height: 28px;
    margin: 0 5px;
    width: auto;
    height: 28px;
    background: transparent;
    border-radius: 0;
}

.formulaConversion .popup-box .pu-con .lab .section .s-con .num.spot {
    margin: 0;
}

.formulaConversion .popup-box .pu-con .lab .oprate {
    display: flex;
    height: 34px;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs {
    margin-right: 10px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    width: 199px;
    height: 34px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.errors {
    border: 1px solid #F98981;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.errors .error {
    display: block;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.errors .inp {
    border-right: 1px solid #F98981 !important;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.right-inp .error {
    left: 80px;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.right-inp input {
    border-right: none !important;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.right-inp .select-input {
    border-right: 1px solid #E5E6EB;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.right-inp.errors .error {
    display: block;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.right-inp.errors .select-input {
    border-right: 1px solid #F98981 !important;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs input {
    width: 120px;
    height: 32px;
    background-color: transparent;
    border: none !important;
    border-right: 1px solid #E5E6EB !important;
    border-radius: 0 !important;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs .error {
    position: absolute;
    display: none;
    left: 0;
    top: 34px;
    font-weight: 400;
    font-size: 12px;
    line-height: 17px;
    color: #F76560;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input {
    width: 80px;
    height: 32px;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    cursor: pointer;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input.select-score {
    line-height: 32px;
    text-indent: 6px;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input.score-sel {
    width: 120px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    margin-right: 10px;
    height: 34px;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input em {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 10px;
    height: 10px;
    background: url(../../images/creditManage/down-icon.png) no-repeat center;
    background-size: 10px;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .name {
    font-size: 14px;
    color: #86909C;
    padding-left: 10px;
    width: 100%;
    height: 32px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    line-height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .name.ckd {
    color: #1D2129;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input.clicked em {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    background: url(../../images/creditManage/down-icon.png) no-repeat center;
    background-size: 10px;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .select-dropdown {
    width: inherit;
    max-height: 320px;
    overflow: auto;
    left: -1px;
    margin: 5px 0;
    padding: 6px 0;
    background-color: #fff;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
    border-radius: 8px;
    position: absolute;
    transform: translate(0, -50px);
    -webkit-transform: translate(0, -50px);
    z-index: 900;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
    z-index: -1;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li {
    margin: 0;
    line-height: normal;
    padding: 0 16px;
    clear: both;
    color: #131B26;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li:hover {
    background-color: #F5F7FA;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li.cur {
    color: #616EE6;
}

.popup-tips .popup-box {
    width: 640px;
}

.popup-tips .popup-box .pu-con {
    margin: 58px 90px;
    overflow: hidden;
    min-height: 150px;
    padding: 0;
    font-size: 20px;
    line-height: 28px;
    color: rgba(0, 0, 0, 0.8);
}

.popup-tips .popup-box .pu-con img {
    margin-right: 17px;
    vertical-align: middle;
}

.popup-tips .popup-box .pu-btn {
    height: 70px;
    justify-content: flex-end;
    padding-right: 0;
}

.popup-tips .popup-box .pu-btn button.pu-cancel {
    border: 1px solid #474C59;
    border-radius: 6px;
    width: 68px;
    height: 36px;
    color: #474C59;
    font-size: 14px;
    margin-right: 30px;
}

.popup-tips .popup-box .pu-btn button.pu-sure {
    border: 1px solid #F36161;
    background: #F36161;
    border-radius: 6px;
    width: 68px;
    height: 36px;
    color: #FFFFFF;
    font-size: 14px;
    margin-right: 30px;
    box-shadow: unset;
    margin-right: 25px;
}
