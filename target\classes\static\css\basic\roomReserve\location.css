.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#choose-location {
  width: 830px;
}
#choose-location .popup-con {
  max-height: 600px;
  overflow-y: auto;
}
#choose-location .popup-con .rs-main .t-form {
  position: relative;
}
#choose-location .popup-con .rs-main .t-form .auto-allocation {
  position: absolute;
  top: 0;
  right: 0;
  color: #4D88FF;
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  padding-left: 20px;
  background: url(../images/zdfp-icon.png) no-repeat left center;
  cursor: pointer;
}
#choose-location .popup-con .rs-main .b-table {
  width: 100%;
}
#choose-location .popup-con .rs-main .b-table .thead {
  margin-bottom: 4px;
}
#choose-location .popup-con .rs-main .b-table .thead ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#choose-location .popup-con .rs-main .b-table .thead ul li {
  flex: 1;
  color: #4e5969;
  font-size: 14px;
  margin-right: 4px;
  border-radius: 4px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  background-color: #E8EBF3;
}
#choose-location .popup-con .rs-main .b-table .thead ul li:first-child {
  flex: 0 0 30px;
  flex-shrink: 0;
}
#choose-location .popup-con .rs-main .b-table .thead ul li:last-child {
  margin-right: 0;
}
#choose-location .popup-con .rs-main .b-table .tbody ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 4px;
}
#choose-location .popup-con .rs-main .b-table .tbody ul.carve-up {
  height: 4px;
  background: url(../images/line-bg.png) repeat-x;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li {
  flex: 1;
  border-radius: 4px;
  background: #F7F8FA;
  margin-right: 4px;
  color: #4e5969;
  font-size: 14px;
  font-weight: 400;
  height: 56px;
  width: 0;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area {
  width: 100%;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 7px 15px;
  border: 1px solid #F7F8FA;
  position: relative;
  border-radius: 4px;
  height: 56px;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area.has {
  cursor: pointer;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area.has:hover {
  border: 1px solid #4D88FF;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area.active {
  background-color: #4D88FF;
  border-color: #4D88FF;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area.active .name {
  color: #fff;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area.active .grades {
  color: #fff;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area:hover {
  border: 1px solid #F7F8FA;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area:hover .oprate {
  display: flex;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area:hover .tage {
  display: block;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage {
  display: none;
  position: absolute;
  top: 6px;
  right: 4px;
  line-height: 16px;
  font-size: 10px;
  color: #FF6B6B;
  border-radius: 4px;
  height: 72px;
  top: 4px;
  z-index: 99;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em {
  display: block;
  width: auto;
  height: 16px;
  padding: 0 4px;
  border-radius: 4px;
  line-height: 16px;
  font-size: 12px;
  color: #FFFFFF;
  transform: scale(0.9);
  -webkit-transform: scale(0.9);
  margin-bottom: 2px;
  white-space: nowrap;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em.c1 {
  background: #FA9191;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em.c2 {
  background: #8ED66B;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em.c3 {
  background: #6ECBFA;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em.c4 {
  background: #61b769;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em.c5 {
  background: #53BDF6;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em.c6 {
  background: #527FF3;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em.c7 {
  background: #A67EF0;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em:hover .tips {
  display: block;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em .tips {
  display: none;
  position: absolute;
  z-index: 1999;
  left: 38px;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  padding-left: 4px;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em .tips .tts {
  background: rgba(24, 30, 51, 0.8);
  border-radius: 4px;
  padding: 10px 16px;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  color: #FFFFFF;
  width: 236px;
  height: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  white-space: pre-wrap;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .tage em .tips:after {
  content: '';
  position: absolute;
  left: -4px;
  top: 50%;
  transform: translateY(-50%) rotate(-90deg);
  -webkit-transform: translateY(-50%) rotate(-90deg);
  width: 12px;
  height: 6px;
  background: url(../images/black-tringle-icon.png) no-repeat center;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .oprate {
  position: absolute;
  right: 5px;
  bottom: 11px;
  z-index: 99;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  display: none;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .oprate span {
  font-size: 16px;
  height: 16px;
  line-height: 16px;
  color: #4d88ff;
  margin-left: 4px;
  cursor: pointer;
  position: relative;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .oprate span.cur:hover em {
  display: block;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .oprate span em {
  display: none;
  position: absolute;
  right: -22px;
  top: -48px;
  width: auto;
  padding: 8px 12px;
  line-height: 22px;
  border-radius: 6px;
  background: #4E5969;
  text-align: center;
  white-space: nowrap;
  font-size: 14px;
  color: #ffffff;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .oprate span em:after {
  content: '';
  position: absolute;
  bottom: -4px;
  right: 24px;
  width: 12px;
  height: 6px;
  background: url(../images/black-tringle-icon.png) no-repeat center;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .name {
  font-weight: 500;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .teacher {
  font-weight: 400;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .teacher.self {
  color: #f76560;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li .select-area .classroom {
  font-weight: 400;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li:first-child {
  flex: 0 0 30px;
  flex-shrink: 0;
  display: flex;
  display: -webkit-flex;
  justify-content: flex-start;
  align-items: center;
  justify-content: center;
  cursor: default;
  background-color: #E8EBF3;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li:first-child span {
  font-size: 14px;
  color: #4e5969;
}
#choose-location .popup-con .rs-main .b-table .tbody ul li:last-child {
  margin-right: 0;
}
#clear-course-scope {
  width: 458px;
}
#clear-course-scope .popup-con {
  max-height: 600px;
  overflow-y: auto;
  padding: 30px 60px;
}
#clear-course-scope .popup-con .rs-main .t-form {
  position: relative;
}
#clear-course-scope .popup-con .rs-main .t-form .clear-table {
  height: 34px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  color: #4D88FF;
  line-height: 34px;
  cursor: pointer;
}
#clear-course-scope .popup-con .rs-main .t-form .clear-table span {
  margin-right: 4px;
}
.sel-item {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.sel-item .sel-title {
  color: #1D2129;
  font-size: 14px;
  width: 98px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.sel-item .sel {
  width: 240px;
  height: 34px;
  line-height: 34px;
}
.sel-item .sel .select-input {
  height: 34px;
  border-radius: 4px;
  border: 1px solid #E5E6EB;
  background: #FFF;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
}
.sel-item .sel .select-input i {
  position: absolute;
  top: 11px;
  right: 6px;
  width: 12px;
  height: 12px;
  background: url(../images/drop-down4.png) no-repeat center;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.sel-item .sel .select-input .name {
  font-size: 14px;
  color: #ACB4BF;
  padding-left: 10px;
  line-height: 32px;
  width: 210px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.sel-item .sel .select-input .name.ckd {
  color: #1D2129;
}
.sel-item .sel .select-input.clicked i {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.sel-item .sel .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
  display: block;
}
.sel-item .sel .select-input .select-dropdown {
  width: 240px;
  min-width: 140px;
  display: none;
  margin: 5px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: fixed;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
  overflow: hidden;
}
.sel-item .sel .select-input .select-dropdown .search {
  margin: 8px;
  height: 36px;
  box-sizing: border-box;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.sel-item .sel .select-input .select-dropdown .search input {
  border: none;
  flex: 1;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  font-size: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  float: left;
}
.sel-item .sel .select-input .select-dropdown .search input::placeholder {
  color: #8F97A8;
}
.sel-item .sel .select-input .select-dropdown .search span {
  cursor: pointer;
  float: left;
  width: 16px;
  height: 16px;
  background: url(../images/search-icons.png) no-repeat center;
  margin: 9px;
}
.sel-item .sel .select-input .select-dropdown .all-selects {
  color: #4E5969;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  margin: 0 20px;
  padding-left: 24px;
  background: url(../images/check-icon.png) no-repeat left center;
}
.sel-item .sel .select-input .select-dropdown .all-selects.cur {
  background: url(../images/checked-icon.png) no-repeat left center;
}
.sel-item .sel .select-input .select-dropdown .dropdown-lists {
  padding: 0 0 6px;
  max-height: 240px;
  overflow: auto;
}
.sel-item .sel .select-input .select-dropdown .dropdown-lists li {
  margin: 0;
  line-height: normal;
  line-height: 40px;
  padding: 0 20px;
  clear: both;
  color: #4E5969;
  font-size: 14px !important;
  list-style: none;
  cursor: pointer;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.sel-item .sel .select-input .select-dropdown .dropdown-lists li span {
  display: block;
  padding-left: 27px;
  background: url(../images/check-icon.png) no-repeat left center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}
.sel-item .sel .select-input .select-dropdown .dropdown-lists li:hover {
  background: #F5F7FA;
}
.sel-item .sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #E1EBFF;
}
.sel-item .sel .select-input .select-dropdown .dropdown-lists li.cur span {
  background: url(../images/checked-icon.png) no-repeat left center;
}
