#jwTips {
  width: 560px;
  box-shadow: 0px 0px 20px 0px rgba(68, 104, 230, 0.15);
  overflow: hidden;
  font-family: PingFang SC;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  font-weight: 400;
  border-radius: 10px;
  background-color: #fff;
}
.jw-dialog {
  content: '';
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 9999;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}
#jwTips .tip-con {
  margin: 40px 100px;
  font-size: 16px;
}
#jwTips .tip-con img {
  display: block;
  margin: 0 auto 24px;
}
#jwTips .tip-con h4 {
  color: #1D2129;
  text-align: center;
  margin-bottom: 4px;
  font-weight: 400;
  font-size: 16px;
}
#jwTips .tip-con p {
  color: #4E5969;
  text-align: center;
  line-height: 1.5;
  font-size: 16px;
}
#jwTips .tip-con .btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 24px;
  cursor: pointer;
}
#jwTips .tip-con .btn .btn-cancel {
  border: 1px solid #E5E6EB;
  padding: 0 30px;
  height: 36px;
  font-size: 14px;
  border-radius: 18px;
  color: #4E5969;
  margin-right: 16px;
  background-color: #fff;
}
#jwTips .tip-con .btn .btn-sure {
  color: #fff;
  background: #4d88ff;
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  border: 1px solid #4d88ff;
  padding: 0 30px;
  height: 36px;
  font-size: 14px;
  border-radius: 18px;
  display: block;
}
#tipLoading {
  animation: rotate 1.5s linear infinite;
}
@keyframes rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
