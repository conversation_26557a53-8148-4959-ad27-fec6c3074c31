.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.flex {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.transforms {
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}

textarea::-webkit-input-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

textarea:-moz-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

input[type=search]::-webkit-search-cancel-button {
    -webkit-appearance: none;
}

textarea:-ms-input-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

input[type="number"] {
    -moz-appearance: textfield;
}

.clearfixs {
    zoom: 1;
}

.clearfixs:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

input::-webkit-input-placeholder {
    color: #86909C;
    font-size: 14px;
}

/*body {*/
/*  background-color: #F7F8FA;*/
/*  padding: 20px 20px 0;*/
/*  font-size: 14px;*/
/*  color: #4E5969;*/
/*}*/
.hide {
    display: none !important;
}

/*.main {*/
/*  width: calc(100vw - 40px);*/
/*  min-height: calc(100vh - 20px);*/
/*  background: #FFFFFF;*/
/*  border-radius: 8px;*/
/*}*/
.main .top {
    width: 100%;
    height: 60px;
    border-bottom: 1px solid #E8EBF1;
    position: relative;
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
    align-items: center;
}

.main .top .tab ul {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.main .top .tab ul li {
    padding: 0 30px;
    height: 60px;
    line-height: 60px;
    cursor: pointer;
    color: #86909c;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    position: relative;
}

.main .top .tab ul li.cur {
    font-size: 16px;
    color: #1d2129;
}

.main .top .tab ul li.cur:after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 60px;
    height: 3px;
    background-color: #4D88FF;
    margin-left: -30px;
    border-radius: 6px 6px 0 0;
}

.main .top .title {
    padding-left: 30px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 36px;
}

.main .top .title .back {
    padding-left: 22px;
    background: url(../../images/creditManage/back-icon.png) no-repeat left center;
    background-size: 16px;
    color: #7D92B3;
    font-size: 14px;
    cursor: pointer;
    margin-right: 16px;
}

.main .top .title .levelone {
    padding-left: 9px;
    position: relative;
    color: #1D2129;
    font-weight: 700;
    font-size: 16px;
    margin-right: 6px;
}

.main .top .title .levelone:after {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    width: 3px;
    height: 16px;
    background: #4D88FF;
    border-radius: 2px;
}

.main .top .title .icon {
    width: 12px;
    height: 12px;
    background: url(../../images/creditManage/arrow-right.png) no-repeat center;
    background-size: 12px;
    margin-right: 6px;
}

.main .top .title .leveltwo {
    color: #1D2129;
    font-weight: 700;
    font-size: 16px;
}

.main .top .btn-list {
    position: absolute;
    top: 12px;
    right: 30px;
}

.main .top .btn-list span {
    border-radius: 4px;
    border: 1px solid #4D88FF;
    background: #FFF;
    display: block;
    width: 104px;
    height: 34px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
    font-size: 14px;
    color: #4d88ff;
}

.main .top .btn {
    position: absolute;
    top: 12px;
    right: 30px;
    width: 116px;
    height: 36px;
    text-align: center;
    line-height: 36px;
    cursor: pointer;
    font-size: 14px;
    color: #FFFFFF;
    background: #4D88FF;
    box-shadow: 0px 0px 10px #4D88FF;
    border-radius: 4px;
}

.main .top h4 {
    position: relative;
    color: #1D2129;
    font-size: 16px;
    margin-left: 30px;
    padding-left: 9px;
    font-weight: bold;
}

.main .top h4::after {
    content: "";
    width: 3px;
    height: 16px;
    background: #4D88FF;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 4px;
}

.main .con {
    padding: 30px;
}

.main .con .save-btn {
    background: #3A8BFF;
    border-radius: 6px;
    width: 96px;
    height: 36px;
    color: #FFFFFF;
    font-size: 14px;
    text-align: center;
    line-height: 36px;
    cursor: pointer;
    margin: 30px auto 30px;
}

.main .con .c-item {
    margin-bottom: 40px;
    /*display: none;*/
}

.main .con .c-item.active {
    display: block;
}

.main .con .c-item h3 {
    height: 20px;
    line-height: 20px;
    position: relative;
    padding-left: 7px;
    font-weight: 700;
    font-size: 16px;
    color: #6581BA;
    margin-bottom: 24px;
}

.main .con .c-item h3:after {
    content: '';
    position: absolute;
    width: 3px;
    height: 18px;
    left: 0px;
    top: 1px;
    background: #6581BA;
    border-radius: 1.5px;
}

.main .con .c-item .selectBox {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.main .con .c-item .selectBox .name {
    width: 196px;
    line-height: 34px;
    text-align: left;
    font-weight: 400;
    font-size: 14px;
    color: #1D2129;
    margin-right: 14px;
}

.main .con .c-item .lab {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
    margin-bottom: 24px;
}

.main .con .c-item .lab .name {
    width: 196px;
    line-height: 34px;
    text-align: left;
    font-weight: 400;
    font-size: 14px;
    color: #1D2129;
    margin-right: 14px;
}

.main .con .c-item .lab .input .layui-input {
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    width: 240px;
    height: 34px;
}

.main .con .c-item .lab .radio {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
}

.main .con .c-item .lab .radio span {
    padding-left: 24px;
    font-size: 14px;
    color: #86909C;
    background: url(../../images/creditManage/radio-icon.png) no-repeat left center;
    background-size: 16px;
    cursor: pointer;
    margin-right: 24px;
}

.main .con .c-item .lab .radio span.cur {
    background: url(../../images/creditManage/radio-cur-icon.png) no-repeat left center;
    background-size: 16px;
}

.main .con .c-item .lab .radio .limit-switch {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    align-items: flex-start;
}

.main .con .c-item .lab .radio .limit-switch em {
    line-height: 34px;
    color: #c9cdd4;
    margin-left: 10px;
}

.main .con .c-item .lab .radio .limit-switch .layui-form-switch {
    border-radius: 3px;
    background: #D2D3D8;
    height: 14px;
    line-height: 14px;
    min-width: 28px;
    padding: 0 0;
    margin-top: 10px;
    border: none;
    width: 31px;
}

.main .con .c-item .lab .radio .limit-switch .layui-form-switch i {
    left: 2px;
    top: 2px;
    width: 12px;
    height: 10px;
    border-radius: 1px;
    background: #FFF;
    margin-left: 0;
}

.main .con .c-item .lab .radio .limit-switch .layui-form-onswitch {
    border-radius: 3px;
    background: #537AF6;
}

.main .con .c-item .lab .radio .limit-switch .layui-form-onswitch i {
    left: 100%;
    margin-left: -14px;
    background-color: #fff;
}

.main .con .c-item .lab .switc-con,
.popup .popup-box .pu-con .lab .switc-con {
    display: flex;
    align-items: center;
}

.main .con .c-item .lab .switc-con .switch,
.popup .popup-box .pu-con .lab .switc-con .switch {
    position: relative;
    width: 28px;
    height: 14px;
    background: #cdcece;
    border-radius: 4px;
}

.main .con .c-item .lab .switc-con .switch span,
.popup .popup-box .pu-con .lab .switc-con .switch span {
    width: 12px;
    height: 10px;
    position: absolute;
    top: 2px;
    left: 2px;
    background-color: #FFFFFF;
    border-radius: 2px;
}

.main .con .c-item .lab .switc-con .switch.switch-open,
.popup .popup-box .pu-con .lab .switc-con .switch.switch-open {
    background: #4D88FF;
}

.main .con .c-item .lab .switc-con .switch.switch-open span,
.popup .popup-box .pu-con .lab .switc-con .switch.switch-open span {
    left: unset;
    right: 2px;
}

.main .con .c-item .lab .switc-con .switch-con,
.popup .popup-box .pu-con .lab .switc-con .switch-con {
    color: #C9CDD4;
    margin-left: 10px;
}

.main .con .c-item .c-top {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: space-between;
    height: 34px;
    margin-bottom: 20px;
}

.main .con .c-item .c-top.new-lab {
    justify-content: flex-start;
}

.main .con .c-item .c-top.new-lab .delet {
    margin-left: 15px;
}

.main .con .c-item .c-top.new-lab .delet span {
    display: block;
    width: 16px;
    height: 16px;
    background: url(../../images/creditManage/delet-icon.png) no-repeat center;
    background-size: 16px;
    cursor: pointer;
}

.main .con .c-item .c-top .selectBox {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
}

.main .con .c-item .c-top .selectBox .name {
    color: #1D2129;
    font-weight: 400;
    font-size: 14px;
    margin-right: 14px;
}

.main .con .c-item .c-top h4 {
    height: 20px;
    line-height: 20px;
    position: relative;
    padding-left: 7px;
    font-weight: 700;
    font-size: 16px;
    color: #6581BA;
}

.main .con .c-item .c-top h4:after {
    content: '';
    position: absolute;
    width: 3px;
    height: 18px;
    left: 0px;
    top: 1px;
    background: #6581BA;
    border-radius: 1.5px;
}

.main .con .c-item .c-top .btns-list {
    overflow: hidden;
}

.main .con .c-item .c-top .btns-list span {
    float: left;
    height: 34px;
    line-height: 34px;
    background: #4D88FF;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    border: 1px solid #4D88FF;
    border-radius: 4px;
    color: #FFFFFF;
    font-weight: 400;
    font-size: 14px;
    margin-left: 16px;
    width: 80px;
    text-align: center;
    cursor: pointer;
}

.main .con .c-item .c-top .btns-list span.set {
    width: 104px;
}

.main .con .c-item .c-top .btns-list span.add {
    padding-left: 20px;
    text-align: center;
    position: relative;
    width: 134px;
    cursor: pointer;
}

.main .con .c-item .c-top .btns-list span.add:after {
    content: '';
    position: absolute;
    left: 15px;
    top: 10px;
    width: 14px;
    height: 14px;
    background: url(../../images/creditManage/icon-add.png) no-repeat center;
    background-size: 14px;

}

.main .con .c-item .c-top .btns-list span.delet {
    background: #FFFFFF;
    border: 1px solid #4D88FF;
    border-radius: 4px;
    width: 80px;
    color: #4D88FF;
    text-align: center;
}

.main .con .c-item .c-table {
    margin-bottom: 30px;
}

.main .con .c-item .add-rule {
    line-height: 20px;
}

.main .con .c-item .add-rule span {
    display: inline-block;
    padding-left: 25px;
    background: url(../../images/creditManage/add-icons.png) no-repeat left center;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #3A8BFF;
    margin-left: 8px;
    cursor: pointer;
}

.oprate-table,
.oprates-table {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
}

.oprate-table div,
.oprates-table div {
    margin: 0 8px;
    color: #4C88FF;
    font-size: 14px;
    cursor: pointer;
}

.oprate-table div.delet,
.oprates-table div.delet {
    color: #F76560;
}

.creditGroupSet .popup-box {
    width: 860px;
    height: auto;
}

.creditGroupSet .popup-box .pu-con {
    padding: 32px 30px;
}

.creditGroupSet .popup-box .pu-con .p-top {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-end;
    height: 34px;
    margin-bottom: 20px;
}

.creditGroupSet .popup-box .pu-con .p-top .btns-list {
    overflow: hidden;
}

.creditGroupSet .popup-box .pu-con .p-top .btns-list span {
    float: left;
    height: 34px;
    line-height: 34px;
    background: #4D88FF;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    border: 1px solid #4D88FF;
    border-radius: 4px;
    color: #FFFFFF;
    font-weight: 400;
    font-size: 14px;
    margin-left: 16px;
    width: 80px;
    text-align: center;
    cursor: pointer;
}

.creditGroupSet .popup-box .pu-con .p-top .btns-list span.set {
    width: 104px;
}

.creditGroupSet .popup-box .pu-con .p-top .btns-list span.add {
    padding-left: 37px;
    text-align: left;
    position: relative;
}

.creditGroupSet .popup-box .pu-con .p-top .btns-list span.add:after {
    content: '';
    position: absolute;
    left: 15px;
    top: 10px;
    width: 14px;
    height: 14px;
    background: url(../../images/creditManage/icon-add.png) no-repeat center;
    background-size: 14px;
}

.creditGroupSet .popup-box .pu-con .p-top .btns-list span.delet {
    background: #FFFFFF;
    border: 1px solid #4D88FF;
    border-radius: 4px;
    width: 80px;
    color: #4D88FF;
    text-align: center;
}

.creditGroupSet .popup-box .pu-con .p-table {
    min-height: 180px;
}

.addGroup .popup-box {
    height: auto !important;
}

.addPopup .popup-box,
.addGroup .popup-box,
.copyGroup .popup-box {
    width: 538px;
    height: auto;
}

.addPopup .popup-box .pu-con .lab,
.addGroup .popup-box .pu-con .lab,
.copyGroup .popup-box .pu-con .lab {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
    margin-bottom: 24px;
}

.addPopup .popup-box .pu-con .lab:last-child,
.addGroup .popup-box .pu-con .lab:last-child,
.copyGroup .popup-box .pu-con .lab:last-child {
    margin-bottom: 0;
}

.addPopup .popup-box .pu-con .lab.last-lab,
.addGroup .popup-box .pu-con .lab.last-lab,
.copyGroup .popup-box .pu-con .lab.last-lab {
    display: none;
}

.addPopup .popup-box .pu-con .lab.last-lab.active,
.addGroup .popup-box .pu-con .lab.last-lab.active,
.copyGroup .popup-box .pu-con .lab.last-lab.active {
    display: flex;
    display: -webkit-flex;
}

.addPopup .popup-box .pu-con .lab.last-labs,
.addGroup .popup-box .pu-con .lab.last-labs,
.copyGroup .popup-box .pu-con .lab.last-labs {
    display: none;
}

.addPopup .popup-box .pu-con .lab.last-labs.active,
.addGroup .popup-box .pu-con .lab.last-labs.active,
.copyGroup .popup-box .pu-con .lab.last-labs.active {
    display: flex;
    display: -webkit-flex;
}

.addPopup .popup-box .pu-con .lab .name,
.addGroup .popup-box .pu-con .lab .name,
.copyGroup .popup-box .pu-con .lab .name {
    width: 138px;
    text-align: left;
    color: #1D2129;
    font-size: 14px;
}

.addPopup .popup-box .pu-con .lab .name em,
.addGroup .popup-box .pu-con .lab .name em,
.copyGroup .popup-box .pu-con .lab .name em {
    color: #F76560;
    display: inline-block;
    margin-right: 5px;
    vertical-align: middle;
}

.addPopup .popup-box .pu-con .lab .input input,
.addGroup .popup-box .pu-con .lab .input input,
.copyGroup .popup-box .pu-con .lab .input input {
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    width: 240px;
    height: 34px;
}

.addPopup .popup-box .pu-con .lab .radio,
.addGroup .popup-box .pu-con .lab .radio,
.copyGroup .popup-box .pu-con .lab .radio {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
}

.addPopup .popup-box .pu-con .lab .radio span,
.addGroup .popup-box .pu-con .lab .radio span,
.copyGroup .popup-box .pu-con .lab .radio span {
    padding-left: 24px;
    font-size: 14px;
    color: #86909C;
    background: url(../../images/creditManage/radio-icon.png) no-repeat left center;
    background-size: 16px;
    cursor: pointer;
    margin-right: 24px;
}

.addPopup .popup-box .pu-con .lab .radio span.cur,
.addGroup .popup-box .pu-con .lab .radio span.cur,
.copyGroup .popup-box .pu-con .lab .radio span.cur {
    background: url(../../images/creditManage/radio-cur-icon.png) no-repeat left center;
    background-size: 16px;
}

#isRegisterCredit {
    display: none;
}

#isRegisterCredit .tips {
    position: relative;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    width: 15px;
    height: 15px;
    margin-left: 5px;
    background: url(../../images/creditManage/tips-icons.png) no-repeat left center;
    background-size: 138px 17px;
    background-position-x: -12px;
}

#isRegisterCredit .tips:hover em {
    display: inline-block;
    color: #aaa;
}

#isRegisterCredit .tips em {
    position: absolute;
    top: -15px;
    left: 12px;
    display: none;
    width: 150px;
    height: 15px;
    background-color: #F2F5F7;
    border-radius: 2px;
    font-size: 12px;
    text-align: center;
    line-height: 15px;
}

.addPopup .popup-box .pu-con .setGroup,
.addGroup .popup-box .pu-con .setGroup,
.copyGroup .popup-box .pu-con .setGroup {
    color: #2663FF;
    font-size: 14px;
    padding-left: 20px;
    background: url('../../images/creditManage/icon-set.png') no-repeat left center;
    margin-left: 14px;
    cursor: pointer;
}

.popup .popup-box .pu-con .lab .switc-con .tips em {
    display: block;
    transform: scale(0.7);
    transform: -webkit-scale(0.7);
    margin-left: -20px;
}

.layui-upload-drag {
    padding: 0;
    border: none;
}

.layui-elem-quote {
    background-color: transparent;
    border-color: transparent;
}

.formulaConversion .popup-box {
    width: 720px;
    height: auto;
}

.formulaConversion .popup-box .pu-con {
    padding: 20px 40px;
}

.formulaConversion .popup-box .pu-con .lab {
    margin-bottom: 20px;
}

.formulaConversion .popup-box .pu-con .lab.t-lab {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
}

.formulaConversion .popup-box .pu-con .lab.t-lab .f-top {
    font-weight: 400;
    font-size: 14px;
    line-height: 34px;
    color: #1D2129;
    margin-right: 14px;
    margin-bottom: 0;
}

.formulaConversion .popup-box .pu-con .lab.t-lab .input {
    width: 240px;
    height: 34px;
    background: #F8F8FA;
    border: 1px solid #D4D6D9;
    border-radius: 4px;
    line-height: 32px;
    padding-left: 14px;
    font-size: 14px;
    color: #ACB4BF;
    font-weight: 400;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.formulaConversion .popup-box .pu-con .lab:last-child {
    margin-bottom: 0;
}

.formulaConversion .popup-box .pu-con .lab.btns {
    overflow: hidden;
}

.formulaConversion .popup-box .pu-con .lab .f-top {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #1D2129;
    margin-bottom: 10px;
}

.formulaConversion .popup-box .pu-con .lab .f-top em {
    color: #F76560;
    display: inline-block;
    margin-right: 4px;
    vertical-align: sub;
}

.formulaConversion .popup-box .pu-con .lab .keyboard {
    float: right;
    width: 240px;
}

.formulaConversion .popup-box .pu-con .lab .keyboard h3 {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 10px;
    color: #1D2129;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con {
    overflow: hidden;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span {
    float: left;
    width: 54px;
    height: 54px;
    border: 1px solid #C9CDD4;
    border-radius: 4px;
    background-color: #FFFFFF;
    margin-right: 8px;
    margin-bottom: 8px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    font-weight: 400;
    font-size: 14px;
    color: #4E5969;
    text-align: center;
    line-height: 52px;
    cursor: pointer;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span:nth-child(4n) {
    margin-right: 0;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign {
    overflow: hidden;
    text-indent: 99em;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.spot {
    position: relative;
    overflow: hidden;
    text-indent: 99em;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.spot:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 4px;
    height: 4px;
    background: #4E5969;
    margin-left: -2px;
    margin-top: -2px;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.spot:hover:after {
    background-color: #4D88FF;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.zero {
    width: 116px;
    height: 54px;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span:hover {
    background: #E1EBFF;
    color: #4D88FF;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span:last-child {
    margin-right: 0;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.lbracket {
    background: #ffffff url(../../images/creditManage/sign-lkh.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.lbracket:hover {
    background: #e1ebff url(../../images/creditManage/sign-lkh-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.rbracket {
    background: #ffffff url(../../images/creditManage/sign-rkh.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.rbracket:hover {
    background: #e1ebff url(../../images/creditManage/sign-rkh-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.delet {
    background: #ffffff url(../../images/creditManage/sign-back.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.delet:hover {
    background: #e1ebff url(../../images/creditManage/sign-back-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-add {
    background: #ffffff url(../../images/creditManage/sign-add.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-add:hover {
    background: #e1ebff url(../../images/creditManage/sign-add-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-cancle {
    background: #ffffff url(../../images/creditManage/sign-jian.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-cancle:hover {
    background: #e1ebff url(../../images/creditManage/sign-jian-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-mul {
    background: #ffffff url(../../images/creditManage/sign-ceng.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-mul:hover {
    background: #e1ebff url(../../images/creditManage/sign-ceng-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-except {
    background: #ffffff url(../../images/creditManage/sign-chu.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .keyboard .k-con span.sign-except:hover {
    background: #e1ebff url(../../images/creditManage/sign-chu-cur.png) no-repeat center;
}

.formulaConversion .popup-box .pu-con .lab .available {
    float: left;
}

.formulaConversion .popup-box .pu-con .lab .available h3 {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 10px;
    color: #1D2129;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con {
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    background: #F7F8FA;
    width: 360px;
    height: 302px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding: 20px 19px;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul {
    overflow: hidden;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul li {
    float: left;
    width: 100px;
    height: 28px;
    border: 1px solid #4D88FF;
    border-radius: 15px;
    background: #FFFFFF;
    color: #4D88FF;
    font-size: 14px;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
    margin-right: 10px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    margin-bottom: 10px;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul li:nth-child(3n) {
    margin-right: 0;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul li.disabled {
    border: 1px solid #A3B7CC;
    font-size: 14px;
    color: #C9CDD4;
    cursor: default;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul li.disabled:hover {
    background: #FFFFFF;
}

.formulaConversion .popup-box .pu-con .lab .available .a-con ul li:hover {
    background: #E1EBFF;
}

.formulaConversion .popup-box .pu-con .lab .keyboard {
    float: right;
}

.formulaConversion .popup-box .pu-con .lab .section {
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    background: #F7F8FA;
    width: 640px;
    min-height: 140px;
    height: auto;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding: 20px 19px;
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
    color: #4E5969;
    font-size: 14px;
    line-height: 28px;
}

.formulaConversion .popup-box .pu-con .lab .section .tit {
    flex: 0 0 68px;
}

.formulaConversion .popup-box .pu-con .lab .section .s-con {
    overflow: hidden;
}

.formulaConversion .popup-box .pu-con .lab .section .s-con span {
    float: left;
    margin: 0 5px;
    width: 100px;
    height: 28px;
    background: #4D88FF;
    border-radius: 15px;
    text-align: center;
    line-height: 28px;
    font-size: 14px;
    color: #FFFFFF;
    margin-bottom: 10px;
}

.formulaConversion .popup-box .pu-con .lab .section .s-con .sign {
    font-weight: 500;
    font-size: 20px;
    color: #86909C;
    line-height: 28px;
    margin: 0 5px;
    width: auto;
    height: 28px;
    background: transparent;
    border-radius: 0;
}

.formulaConversion .popup-box .pu-con .lab .section .s-con .num {
    font-weight: 500;
    font-size: 18px;
    color: #86909C;
    line-height: 28px;
    margin: 0 5px;
    width: auto;
    height: 28px;
    background: transparent;
    border-radius: 0;
}

.formulaConversion .popup-box .pu-con .lab .section .s-con .num.spot {
    margin: 0;
}

.formulaConversion .popup-box .pu-con .lab .oprate {
    display: flex;
    height: 34px;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.formulaConversion .popup-box .pu-con .lab .oprate .txt {
    font-weight: 400;
    font-size: 14px;
    color: #1D2129;
    margin-right: 14px;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inp-lay {
    width: 120px;
    position: relative;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inp-lay .error {
    position: absolute;
    display: none;
    left: 0;
    top: 34px;
    font-weight: 400;
    font-size: 12px;
    line-height: 17px;
    color: #F76560;
}

.formulaConversion .popup-box .pu-con .lab .oprate .symbol {
    font-weight: 400;
    font-size: 14px;
    color: #86909C;
    margin: 0 4px;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .name {
    font-size: 14px;
    color: #86909C;
    padding-left: 10px;
    width: 100%;
    height: 32px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    line-height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input.score-sel .errorMsg {
    position: absolute;
    display: none;
    left: 0;
    top: 34px;
    font-weight: 400;
    font-size: 12px;
    line-height: 17px;
    color: #F76560;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input.score-sel {
    width: 120px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    margin-right: 10px;
    height: 34px;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs {
    margin-right: 10px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    width: 199px;
    height: 34px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.errors {
    border: 1px solid #F98981;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.errors .error {
    display: block;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.errors .inp {
    border-right: 1px solid #F98981 !important;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.right-inp .error {
    left: 80px;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.right-inp input {
    border-right: none !important;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.right-inp .select-input {
    border-right: 1px solid #E5E6EB;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.right-inp.errors .error {
    display: block;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs.right-inp.errors .select-input {
    border-right: 1px solid #F98981 !important;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs input {
    width: 120px;
    height: 32px;
    background-color: transparent;
    border: none !important;
    border-right: 1px solid #E5E6EB !important;
    border-radius: 0 !important;
}

.formulaConversion .popup-box .pu-con .lab .oprate .inputs .error {
    position: absolute;
    display: none;
    left: 0;
    top: 34px;
    font-weight: 400;
    font-size: 12px;
    line-height: 17px;
    color: #F76560;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input {
    width: 80px;
    height: 32px;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    cursor: pointer;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input.score-sel {
    width: 120px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    margin-right: 10px;
    height: 34px;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input em {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 10px;
    height: 10px;
    background: url(../../images/creditManage/down-icon.png) no-repeat center;
    background-size: 10px;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .name {
    font-size: 14px;
    color: #86909C;
    padding-left: 10px;
    width: 100%;
    height: 32px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    line-height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .name.ckd {
    color: #1D2129;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input.clicked em {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    background: url(../../images/creditManage/down-icon.png) no-repeat center;
    background-size: 10px;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .select-dropdown {
    width: inherit;
    max-height: 320px;
    overflow: auto;
    left: -1px;
    margin: 5px 0;
    padding: 6px 0;
    background-color: #fff;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
    border-radius: 8px;
    position: absolute;
    transform: translate(0, -50px);
    -webkit-transform: translate(0, -50px);
    z-index: 900;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
    z-index: -1;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li {
    margin: 0;
    line-height: normal;
    padding: 0 16px;
    clear: both;
    color: #131B26;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li:hover {
    background-color: #F5F7FA;
}

.formulaConversion .popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li.cur {
    color: #616EE6;
}

.formulaConversion .popup-box .label-box {
    width: 800px;
    height: 184px;
    border: 1px dashed #D4D6D9;
    border-radius: 2px;
    border: 1px dashed #E5E6EB;
    background: #F2F3F5;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.formulaConversion .popup-box .label-box .plus {
    margin: 50px auto 24px;
    width: 14px;
    height: 14px;
    background: url(../images/plus.png) no-repeat center;
    display: block;
}

.formulaConversion .popup-box .label-box p {
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    color: #1d2129;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    margin: 0 auto 4px;
}

.formulaConversion .popup-box .label-box p.tips {
    font-size: 12px;
    color: #86909c;
    line-height: 20px;
}

.formulaConversion .popup-box .label-box .import {
    width: 120px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    cursor: pointer;
    background: #3A8BFF;
    margin: 0 auto;
    border-radius: 50px;
}

.formulaConversion .popup-box .label-box .import span {
    display: inline-block;
    padding-left: 22px;
    background: url(../images/down-icons.png) no-repeat left center;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
}

.formulaConversion .popup-box .pu-btn {
    position: relative;
}

.formulaConversion .popup-box .pu-btn .download {
    position: absolute;
    left: 30px;
    top: 10px;
    width: 140px;
    height: 34px;
    line-height: 34px;
    border-radius: 4px;
    background: #4D88FF;
    padding-left: 40px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    background: #4D88FF url(../images/xz-icons.png) no-repeat 16px center;
    font-size: 14px;
    color: #ffffff;
    cursor: pointer;
}

.dialogRules {
    width: 887px;
    background-color: #FFFFFF;
    border-radius: 10px;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.downloadTemplate .popup-box .lab-list {
    width: 340px;
    margin: 0 auto;
}

.downloadTemplate .popup-box .lab-list .lab {
    margin-bottom: 24px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.downloadTemplate .popup-box .lab-list .lab .name {
    padding-right: 10px;
    font-size: 14px;
    color: #1d2129;
    text-align: left;
    line-height: 34px;
}
