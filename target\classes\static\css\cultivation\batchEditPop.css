input, select, textarea {
    border: none;
    vertical-align: middle;
    outline: none;
}
.mb20 {
    margin-bottom: 20px;
}
.mould_pop {
    z-index: 14;
    position: fixed;
    width: 640px;
    height: 381px;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    background: #fff;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.mould_pop .mould_pop_mask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: .4;
    background: #000;
}

.mould_pop .pop_top {
    height: 54px;
    padding: 0 30px;
    line-height: 54px;
    font-size: 16px;
    color: #000000;
    border-bottom: 1px solid #E4E4EB;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: center;
}

.mould_pop .pop_cont {
    height: calc(100% - 80px);
    overflow: auto;
}

.mould_pop .pop_cont .trg_cont {
    padding: 30px;
}

.mould_pop .pop_cont .trg_cont .trg_left {
    width: 100px;
    padding-left: 60px;
    padding-top: 5px;
    line-height: 12px;
    font-size: 12px;
    color: #48515A;
}

.mould_pop .pop_cont .trg_cont .trg_right {
    width: calc(100% - 175px);
    padding-left: 15px;
}

.mould_pop .pop_cont .trg_cont .trg_right .trg_datapush_input {
    width: calc(100% - 22px);
}

.mould_pop .pop_cont .trg_cont .trg_right .prp_fields_set {
    display: inline-block;
    width: 158px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    color: #6C4BFF;
    text-align: center;
    background: rgba(108, 75, 255, 0.1);
    border: 1px solid #6C4BFF;
    border-radius: 4px;
    cursor: pointer;
}

.mould_pop .pop_cont .trg_cont .trg_right .prp_fields_res {
    display: inline-block;
    width: 138px;
    height: 30px;
    padding: 0 10px;
    line-height: 30px;
    font-size: 14px;
    color: #787983;
    border: 1px solid #A9AAB4;
    border-radius: 4px;
    cursor: pointer;
}

.mould_pop .pop_cont .trg_cont .trg_right .prp_fields_res span {
    width: 16px;
    height: 16px;
    margin-top: 7px;
    background: url("./images/input_edit.png") no-repeat center;
    background-size: contain;
}

.mould_pop .pop_cont .trg_cont .trg_right .trg_li {
    width: 50%;
    margin-bottom: 14px;
    line-height: 14px;
    font-size: 12px;
    color: #48515A;
}

.mould_pop .pop_cont .trg_cont .trg_right .trg_li input {
    vertical-align: top;
}

.mould_pop .pop_cont .trg_cont .trg_right .trg_txt {
    width: 320px;
    height: 58px;
    padding: 10px 12px;
    border: 1px solid #D9DDE1;
    border-radius: 5px;
    line-height: 16px;
    font-size: 12px;
    color: #48515A;
    outline: none;
    resize: none;
}

.mould_pop .pop_cont .trg_cont .trg_right .trg_tip {
    line-height: 16px;
    margin-top: 6px;
    font-size: 12px;
    color: #A8A8A8;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short {
    position: relative;
    width: 150px;
    height: 32px;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_short_noClick {
    width: 128px;
    height: 30px;
    padding: 0 10px 0 10px;
    border: 1px solid #D9DDE1;
    border-radius: 5px;
    font-size: 12px;
    color: #787983;
    text-align: center;
    background: #F0F2F4;
    cursor: not-allowed;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_short_val {
    width: 108px;
    height: 30px;
    padding: 0 30px 0 10px;
    border: 1px solid #D9DDE1;
    border-radius: 5px;
    font-size: 12px;
    color: #48515A;
    background: url("../../images/cultivation/lead_icon.png") no-repeat center right 8px;
    background-size: 13px 8px;
    cursor: pointer;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_search_sel {
    z-index: 4;
    position: absolute;
    width: calc(100% - 2px);
    overflow: auto;
    top: 32px;
    left: 0;
    background: #fff;
    border-left: 1px solid #D9DDE1;
    border-right: 1px solid #D9DDE1;
    border-bottom: 1px solid #D9DDE1;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_search_sel .trp_sch_top {
    position: relative;
    height: 32px;
    border-bottom: 1px solid #D9DDE1;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_search_sel .trp_sch_top .trp_search_input {
    width: calc(100% - 54px);
    height: 32px;
    padding: 0 44px 0 10px;
    margin: 0;
    font-size: 12px;
    color: #48515A;
    border: none;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_search_sel .trp_sch_top .trp_search_icon {
    position: absolute;
    width: 20px;
    height: 20px;
    top: 6px;
    right: 10px;
    background: url("./images/pop_search_icon.png") no-repeat center;
    background-size: contain;
    cursor: pointer;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_short_ul {
    z-index: 2;
    position: absolute;
    width: 148px;
    left: 0;
    top: 32px;
    border: 1px solid #D9DDE1;
    border-radius: 5px;
    background: #fff;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_short_ul .trp_short_lis {
    line-height: 30px;
    padding: 0 10px;
    font-size: 12px;
    color: #2B333B;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_short_ul .trp_short_lis input[type='checkbox'] {
    vertical-align: top;
    margin-top: 6px;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_short_ul .trp_short_lis input[type='checkbox']:after {
    background: url("./images/icon_check.png") no-repeat center;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_short_ul .trp_short_lis input[type='checkbox']:checked:after {
    background: url("./images/icon_checked.png") no-repeat center;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_short_ul .trp_short_lis input[type='checkbox']:checked + span {
    color: #116ae4;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_short_ul .trp_short_lis .icon-check {
    box-sizing: border-box !important;
    margin-top: 6px;
    margin-right: 4px;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_short_ul .trp_short_lis:hover {
    color: #6C4BFF;
}

.mould_pop .pop_cont .trg_cont .trg_right .trp_short .trp_short_initial {
    position: initial;
    width: 100%;
    max-height: 150px;
    overflow: auto;
    border: none;
    border-radius: unset;
}

.mould_pop .pop_btm {
    height: 72px;
    line-height: 72px;
    text-align: center;
    border-top: 1px solid #E4E4EB;
}

.mould_pop .pop_btm span {
    display: inline-block;
    width: 98px;
    height: 30px;
    line-height: 30px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
}

.mould_pop .pop_btm .pop_cal {
    border: 1px solid #8A8B90;
    color: #8A8B90;
}

.mould_pop .pop_btm .pop_sure {
    margin-left: 20px;
    border: 1px solid #6C4BFF;
    background: #6C4BFF;
    color: #fff;
}

.mould_pop .pop_btm .pop_cal:hover {
    background: rgba(228, 228, 235, 0.2);
}

.mould_pop .pop_btm .pop_sure:hover {
    background: rgba(108, 75, 255, 0.7);
}

.mould_pop .pop_btm .noClick {
    border: 1px solid #d9dde1;
    background: #d9dde1;
    color: #fff;
    pointer-events: none;
}

.mould_pop .pop_btm .noClick:hover {
    background: #d9dde1;
    cursor: not-allowed;
}

.batch_editor_pop {
    height: 480px;
}

.batch_editor_pop .bep_cont {
    padding: 30px 30px;
}

.batch_editor_pop .bep_cont .bep_tip {
    line-height: 16px;
    font-size: 12px;
    color: #A8A8A8;
}

.batch_editor_pop .bep_cont .bep_tip span {
    font-size: 14px;
    color: #6C4BFF;
    font-weight: 400;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_left {
    width: 100px;
    line-height: 32px;
    font-size: 14px;
    color: #48515A;
    text-align: right;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right {
    width: calc(100% - 115px);
    padding-left: 15px;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_tip {
    line-height: 16px;
    margin-top: 6px;
    font-size: 12px;
    color: #A8A8A8;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .error_tip {
    color: #FF7A93;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel {
    position: relative;
    width: 400px;
    height: 30px;
    border: 1px solid #D9DDE1;
    border-radius: 5px;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel .bep_per_input {
    width: calc(100% - 40px);
    height: 100%;
    padding: 0 30px 0 10px;
    border-radius: 4px;
    cursor: pointer;
    background: url("../../images/cultivation/lead_icon.png") no-repeat center right 8px;
    background-size: 13px 8px;
    font-size: 12px;
    color: #48515A;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel .bep_search_sel {
    z-index: 1;
    position: absolute;
    width: 100%;
    overflow: auto;
    top: 32px;
    left: -1px;
    background: #fff;
    border-left: 1px solid #D9DDE1;
    border-right: 1px solid #D9DDE1;
    border-bottom: 1px solid #D9DDE1;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel .bep_search_sel .bep_sch_top {
    position: relative;
    height: 32px;
    border-bottom: 1px solid #D9DDE1;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel .bep_search_sel .bep_sch_top .bep_search_input {
    width: calc(100% - 54px);
    padding: 0 44px 0 10px;
    height: 32px;
    font-size: 12px;
    color: #48515A;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel .bep_search_sel .bep_sch_top .bep_search_icon {
    position: absolute;
    width: 20px;
    height: 20px;
    top: 6px;
    right: 10px;
    background: url("./images/pop_search_icon.png") no-repeat center;
    background-size: contain;
    cursor: pointer;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel .bep_per_uls {
    min-height: 90px;
    max-height: 150px;
    overflow: auto;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel .bep_per_uls .bep_per_lis {
    line-height: 30px;
    padding: 0 10px;
    font-size: 12px;
    color: #2B333B;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel .bep_per_uls .bep_per_lis:hover {
    background: #F7F7FA;
    color: #787983;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel .bep_per_uls .bep_per_lis.custom {
    color: #787983;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel .bep_per_uls .bep_per_lis.system {
    color: #6C4BFF;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_sel .bep_per_uls .bep_per_lis.active {
    background: #F0EDFF;
    color: #6C4BFF;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_text {
    width: calc(100% - 20px);
    height: 100%;
    padding: 0 10px;
    border-radius: 4px;
    font-size: 12px;
    color: #48515A;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .bep_per_date {
    width: calc(100% - 32px);
    height: 100%;
    padding: 0 22px 0 10px;
    border-radius: 4px;
    font-size: 12px;
    color: #48515A;
    background: url("./images/time_icon.png") no-repeat center right 4px;
    background-size: 18px 18px;
    cursor: pointer;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont {
    position: relative;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_res {
    width: 170px;
    height: 62px;
    border-radius: 4px;
    background: #fff;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_res .sign_btn {
    width: 100%;
    height: 60px;
    border: 1px solid #DEDFE0;
    background: #fff;
    border-radius: 4px;
    font-size: 12px;
    color: #C0C0C3;
    text-align: center;
    line-height: 60px;
    cursor: pointer;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_res .sign_btn:hover {
    box-shadow: 0px 0px 6px 0px rgba(0, 108, 226, 0.5);
    border: 1px solid #006CE2;
    color: #006CE2;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_res .sign_new {
    height: 60px;
    line-height: 60px;
    border: 1px solid #DEDFE0;
    border-radius: 4px;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_res .sign_new img {
    width: 115px;
    height: 52px;
    margin-top: 3px;
    margin-left: 3px;
    border: 1px solid #006CE2;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_res .sign_new .icon-delete {
    margin: 22px 15px 0 0;
    font-size: 16px;
    color: #FF4C26;
    cursor: pointer;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_code {
    position: absolute;
    left: 180px;
    bottom: 0;
    z-index: 1;
    width: 140px;
    height: 158px;
    box-sizing: border-box !important;
    padding-top: 10px;
    border-radius: 4px;
    border: 1px solid #DEDFE0;
    background: #fff;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_code .sign_code_info {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_code .sign_code_info .sign_code_shade {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(241, 241, 241, 0.8);
    font-size: 12px;
    color: #898989;
    text-align: center;
    line-height: 98px;
    cursor: pointer;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_code .sign_code_info .sign_code_img {
    display: inline-block;
    width: 120px;
    height: 120px;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_code .sign_code_tip {
    margin-top: 6px;
    font-size: 12px;
    color: #C0C0C3;
    text-align: center;
    line-height: 16px;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_save {
    position: absolute;
    left: 180px;
    bottom: 0;
    z-index: 1;
    width: 170px;
    height: 138px;
    padding: 0 10px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #DEDFE0;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_save .sign_save_title {
    padding: 10px 0;
    font-size: 12px;
    color: #202020;
    line-height: 16px;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_save .sign_save_img {
    height: 60px;
    border: 1px solid #DEDFE0;
    border-radius: 4px;
    text-align: center;
    line-height: 60px;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_save .sign_save_img img {
    display: inline-block;
    width: 115px;
    height: 52px;
    border: 1px solid #006CE2;
    vertical-align: top;
    margin-top: 3px;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_save .sign_save_btm {
    height: 18px;
    margin-top: 10px;
    text-align: right;
    line-height: 18px;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_save .sign_save_btm span {
    font-size: 12px;
    color: #898989;
    cursor: pointer;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .sign_cont .sign_save .sign_save_btm .sure {
    margin-left: 10px;
    color: #006CE2;
}

.batch_editor_pop .bep_cont .bep_per .bep_per_right .error_data {
    border: 1px solid #FF7A93;
}