.container {
  overflow: hidden;
}
.container .logList {
  margin: 0 24px 24px;
}
.container .logList .layui-table-page {
  text-align: center;
}
.container .logList .layui-table-view select[lay-ignore] {
  height: 20px;
}
.container .logList .layui-table-page .layui-laypage input:focus,
.container .logList .layui-table-page .layui-laypage select:focus {
  border-color: #4d88ff !important;
}
.container .logList .layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: #4d88ff !important;
}
.container .logList .layui-table-page .layui-laypage a:hover,
.container .logList .layui-table-page .layui-laypage span.layui-laypage-curr:hover {
  color: #4d88ff !important;
}
.container .logList .layui-form-select dl dd.layui-this {
  color: #4d88ff !important;
}
.form-log {
  margin-top: 20px;
}
.form-log .layui-form-label {
  width: 60px;
}
.form-log .j-search-con .j-select-year .all-selects {
  line-height: 24px;
  height: 24px;
  margin-top: 8px;
  color: #4c85fa;
}
.form-log .j-search-con {
  width: 200px;
}
.form-log .layui-inline {
  margin-bottom: 20px;
}
.form-log .layui-inline-button {
  margin-left: 32px;
}
.form-log .layui-inline-button .layui-btn {
  width: 64px;
  height: 34px;
  line-height: 32px;
  background: #4d88ff;
  border-radius: 6px;
}
.form-log .layui-inline-button .layui-btn:hover {
  border-color: #4d88ff;
}
.form-log .layui-inline-button .layui-btn.layui-btn-primary {
  background: none;
}
.form-log .layui-inline-wrap {
  display: flex;
  flex-wrap: wrap;
}
.layui-inline-wrap .layui-inline {
  display: flex;
  align-items: center;
}
.form-log .layui-input-block {
  margin-left: 0;
}
#selDate {
  width: 270px;
  height: 34px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  padding: 0 10px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  cursor: pointer;
}
.container {
  background: #ffffff;
  border-radius: 8px;
  margin: 20px;
  overflow: hidden;
}
.container .top {
  width: 100%;
  height: 60px;
  border-bottom: 1px solid #e8ebf1;
  position: relative;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  align-items: center;
}
.container .top .title {
  padding-left: 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 36px;
}
.container .top .title .back {
  padding-left: 22px;
  background: url(../../images/cultivation/back.png) no-repeat left center;
  background-size: 16px;
  color: #7d92b3;
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
}
.container .top .title .levelone {
  padding-left: 9px;
  position: relative;
  color: #1d2129;
  font-weight: 700;
  font-size: 16px;
  margin-right: 6px;
}
.container .top .title .levelone:after {
  content: "";
  position: absolute;
  left: 0;
  top: 2px;
  width: 3px;
  height: 16px;
  background: #4d88ff;
  border-radius: 2px;
}
.container .top .title .icon {
  width: 12px;
  height: 12px;
  background: url(../../images/cultivation/arrow-right.png) no-repeat center;
  background-size: 12px;
  margin-right: 6px;
}
.container .top .title .leveltwo {
  color: #1d2129;
  font-weight: 700;
  font-size: 16px;
}
.container .top .btn {
  position: absolute;
  top: 17px;
  right: 28px;
  width: 116px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  background: #4d88ff;
  box-shadow: 0px 0px 10px #4d88ff;
  border-radius: 4px;
}
.container .top h4 {
  position: relative;
  color: #1d2129;
  font-size: 16px;
  margin-left: 30px;
  padding-left: 9px;
  font-weight: bold;
}
.container .top h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4d88ff;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}