.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #ACB4BF;
  font-size: 14px;
}
.hide {
  display: none;
}
::-webkit-scrollbar {
  width: 6px;
  height: 10px;
}
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #d9d9d9;
}
::-webkit-scrollbar-track {
  border-radius: 6px;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.layui-layer {
  border-radius: 10px !important;
}
.popup {
  background: #FFFFFF;
  display: none;
}
.popup .title {
  height: 56px;
  line-height: 56px;
  font-size: 16px;
  font-weight: 400;
  padding: 0 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #E5E6EB;
}
.popup .title .name {
  font-size: 16px;
  color: #1d2129;
  text-align: left;
}
.popup .popup-con {
  padding: 30px;
}
.popup .bottom {
  border-top: 1px solid #E5E6EB;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 0;
  padding: 0 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popup .bottom .exam-cancle {
  border: 1px solid #E5E6EB;
  padding: 0 30px;
  height: 36px;
  font-size: 14px;
  border-radius: 18px;
  color: #4E5969;
  margin-right: 16px;
  line-height: 34px;
  background-color: #fff;
}
.popup .bottom .exam-sure {
  color: #fff;
  background: #4d88ff;
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  border: 1px solid #4d88ff;
  padding: 0 30px;
  height: 36px;
  font-size: 14px;
  line-height: 34px;
  border-radius: 18px;
  display: block;
}
.layui-table-grid-down {
  display: none!important;
}