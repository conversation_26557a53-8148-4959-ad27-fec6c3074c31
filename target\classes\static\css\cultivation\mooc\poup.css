.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.layui-laypage .layui-disabled {
  background-color: transparent !important;
  border: none !important;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #ACB4BF !important;
  font-size: 14px;
}
::-webkit-scrollbar {
  width: 6px;
  height: 10px;
}
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #d9d9d9;
}
::-webkit-scrollbar-track {
  border-radius: 6px;
}
.popups {
  background: #FFFFFF;
  border-radius: 10px;
  display: none;
}
.popups .title {
  height: 52px;
  line-height: 52px;
  font-size: 16px;
  font-weight: 400;
  padding: 0 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #F2F2F2;
}
.popups .title .name {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  color: #1d2129;
  text-align: left;
}
.popups .title .close {
  width: 20px;
  height: 20px;
  background: url(../../../images/cultivation/mooc/close.png) no-repeat center;
  cursor: pointer;
}
.popups .popup-con {
  padding: 30px;
}
.popups .bottom {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 66px;
  border-top: 1px solid #E5E6EB;
  padding: 0 24px;
}
.popups .bottom div {
  width: 88px;
  height: 34px;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
  border-radius: 18px;
  cursor: pointer;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popups .bottom div.cancle {
  border: 1px solid #C9CDD4;
  color: #4E5969;
  background-color: #fff;
  margin-right: 16px;
}
.popups .bottom div.confirm {
  color: #fff;
  background: #4D88FF;
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  border: 1px solid #4D88FF;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: #4D88FF !important;
}
.layui-laypage a:hover {
  color: #4D88FF !important;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #4D88FF !important;
}
.layui-layer {
  border-radius: 10px !important;
}
.style1 .tips-popup .popup-con .checkbox.selected {
  background: url(../images/checked-hy-icon.png) no-repeat left center;
}
.style1 .popups .bottom div.confirm {
  background-color: #3eb35a;
  box-shadow: 0px 0px 8px 0px rgba(62, 179, 90, 0.3);
  border-color: #3eb35a;
}
.tips-popup * {
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* IE/Edge */
  user-select: none;
  /* 标准语法 */
}
#edit-poups,
#edit-ref-poups {
  width: 832px;
}
#edit-poups .popup-con,
#edit-ref-poups .popup-con {
    padding: 40px 80px;
    height: 502px;
    overflow-y: auto;
    box-sizing: border-box;
}
#edit-poups .popup-con .lable,
#edit-ref-poups .popup-con .lable {
  margin-bottom: 24px;
}
#edit-poups .popup-con .lable h3,
#edit-ref-poups .popup-con .lable h3 {
  margin-bottom: 14px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#edit-poups .popup-con .lable h3 span,
#edit-ref-poups .popup-con .lable h3 span {
  font-size: 14px;
  color: #1d2129;
  margin-right: 6px;
}
#edit-poups .popup-con .lable h3 em,
#edit-ref-poups .popup-con .lable h3 em {
  width: 16px;
  height: 16px;
  background: url(../images/feedback.png) no-repeat center;
  cursor: pointer;
  position: relative;
}
#edit-poups .popup-con .lable h3 em:hover .tip,
#edit-ref-poups .popup-con .lable h3 em:hover .tip {
  display: block;
}
#edit-poups .popup-con .lable h3 em .tip,
#edit-ref-poups .popup-con .lable h3 em .tip {
  display: none;
  position: absolute;
  left: -44px;
  top: -42px;
  padding: 8px 12px;
  border-radius: 6px;
  background: #4E5969;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  z-index: 99;
}
#edit-poups .popup-con .lable h3 em .tip:after,
#edit-ref-poups .popup-con .lable h3 em .tip:after {
  content: '';
  position: absolute;
  left: 48px;
  top: 31px;
  width: 8px;
  height: 8px;
  background-color: #4E5969;
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
}
#edit-poups .popup-con .lable h3 em .tip i,
#edit-ref-poups .popup-con .lable h3 em .tip i {
  color: #FFFFFF;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  display: block;
  white-space: nowrap;
}
#edit-poups .popup-con .lable .inputs,
#edit-ref-poups .popup-con .lable .inputs {
  min-width: 360px;
  display: inline-block;
    min-height:34px;
  /*height: 34px;*/
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  border: 1px solid #e5e6eb;
  background-color: #f7f8fa;
  border-radius: 4px;
  padding: 7px 10px;
  font-size: 14px;
  color: #c9cdd4;
  line-height: 18px;
}
#edit-poups .popup-con .lable .lab-top,
#edit-ref-poups .popup-con .lable .lab-top {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  height: 22px;
  margin-bottom: 12px;
}
#edit-poups .popup-con .lable .lab-top h3,
#edit-ref-poups .popup-con .lable .lab-top h3 {
  line-height: 22px;
  margin-bottom: 0;
}
#edit-poups .popup-con .lable .lab-top h3 i,
#edit-ref-poups .popup-con .lable .lab-top h3 i {
  color: #f76560;
  display: inline-block;
  margin-right: 6px;
}
#edit-poups .popup-con .lable .lab-top .clear-rule,
#edit-ref-poups .popup-con .lable .lab-top .clear-rule {
  padding-left: 20px;
  background: url(../../../images/cultivation/mooc/refresh-icons.png) no-repeat left center;
  cursor: pointer;
  font-size: 14px;
  color: #4d88ff;
}
#edit-poups .popup-con .lable .lab-con,
#edit-ref-poups .popup-con .lable .lab-con {
  border-radius: 4px;
  background-color: #f6f7fe;
  padding: 16px 24px 0;
}
#edit-poups .popup-con .lable .lab-con h4,
#edit-ref-poups .popup-con .lable .lab-con h4 {
  font-size: 14px;
  color: #1d2129;
  margin-bottom: 14px;
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li {
  margin-right: 16px;
  margin-bottom: 16px;
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li:active,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li:active {
  box-shadow: 0px 0px 8px 0px rgba(68, 104, 230, 0.3);
  background-color: #4d88ff;
  color: #fff;
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number {
  height: 34px;
  border-radius: 12px;
  background-color: #e1ebff;
  width: 90px;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
  border: 1px solid #e1ebff;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 15px;
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number:hover,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number:hover {
  border: 1px solid #4d88ff;
  box-shadow: 0px 0px 8px 0px rgba(68, 104, 230, 0.3);
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number:hover em,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number:hover em {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number:hover .select-dropdown,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number:hover .select-dropdown {
  /*transform: translate(0, 0);*/
  /*-webkit-transform: translate(0, 0);*/
  display: block;
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number em,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number em {
  position: absolute;
  top: 8px;
  right: 11px;
  width: 16px;
  height: 16px;
  background: url(../../../images/cultivation/mooc/arrow-icon.png) no-repeat center;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .name,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .name {
  font-size: 14px;
  color: #4d88ff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .name.ckd,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .name.ckd {
  color: #131B26;
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .select-dropdown,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .select-dropdown {
    /* width: 100%; */
    width: 90px;
    position: fixed;
     display: none;
    /* left: -1px; */
    z-index: 900;
    /* top: 34px; */
    padding-top: 6px;
    transform: translate(-17px, 118px);
    -webkit-transform: translate(-17px, 118px);
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .select-dropdown .dropdown-lists,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .select-dropdown .dropdown-lists {
  background-color: #fff;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .select-dropdown .dropdown-lists li,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .select-dropdown .dropdown-lists li {
  width: 100%;
  height: 40px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 40px;
  padding-left: 20px;
  font-size: 14px;
  color: #4e5969;
  margin-right: 0;
  margin-bottom: 0;
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .select-dropdown .dropdown-lists li:active,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .select-dropdown .dropdown-lists li:active {
  background-color: #e1ebff;
  color: #4d88ff;
  box-shadow: none;
}
#edit-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .select-dropdown .dropdown-lists li:hover,
#edit-ref-poups .popup-con .lable .lab-con .cell-group.parameter ul li.serial-number .select-dropdown .dropdown-lists li:hover {
  background-color: #e1ebff;
  color: #4d88ff;
}
#edit-poups .popup-con .lable .lab-con .cell-group ul,
#edit-ref-poups .popup-con .lable .lab-con .cell-group ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
#edit-poups .popup-con .lable .lab-con .cell-group ul li,
#edit-ref-poups .popup-con .lable .lab-con .cell-group ul li {
  cursor: pointer;
  margin-bottom: 16px;
}
#edit-poups .popup-con .lable .lab-con .cell-group ul li.fixed,
#edit-ref-poups .popup-con .lable .lab-con .cell-group ul li.fixed {
  background-color: #e1ebff;
  border-radius: 12px;
  height: 34px;
  line-height: 34px;
  padding: 0 16px;
  font-size: 14px;
  color: #4d88ff;
}
#edit-poups .popup-con .lable .lab-con .cell-group ul li.default,
#edit-ref-poups .popup-con .lable .lab-con .cell-group ul li.default {
  background-color: #e1ebff;
  border-radius: 12px;
  height: 34px;
  line-height: 34px;
  padding: 0 16px;
  font-size: 14px;
  color: #4d88ff;
}
#edit-poups .popup-con .lable .lab-con .cell-group ul li.symbol,
#edit-ref-poups .popup-con .lable .lab-con .cell-group ul li.symbol {
  font-size: 14px;
  color: #86909c;
  margin: 0 5px;
  height: 34px;
  line-height: 34px;
  margin-bottom: 16px;
}
#edit-poups .popup-con .lable .lab-con .cell-group ul li.input,
#edit-ref-poups .popup-con .lable .lab-con .cell-group ul li.input {
  background-color: #e1ebff;
  border-radius: 12px;
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  color: #4d88ff;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
#edit-poups .popup-con .lable .lab-con .cell-group ul li.input input,
#edit-ref-poups .popup-con .lable .lab-con .cell-group ul li.input input {
  border-radius: 12px;
  padding: 0 16px;
  background-color: transparent;
  height: 34px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  width: 88px;
  font-size: 14px;
  caret-color: #4d88ff;
  border: 1px solid #e1ebff !important;
}
#edit-poups .popup-con .lable .lab-con .cell-group ul li.input input::placeholder,
#edit-ref-poups .popup-con .lable .lab-con .cell-group ul li.input input::placeholder {
  color: #b8d3ff !important;
}
#edit-poups .popup-con .lable .lab-con .cell-group ul li.input input:hover,
#edit-ref-poups .popup-con .lable .lab-con .cell-group ul li.input input:hover {
  border: 1px solid #e1ebff !important;
}
#edit-poups .popup-con .lable .lab-con .cell-group ul li.input input:focus,
#edit-ref-poups .popup-con .lable .lab-con .cell-group ul li.input input:focus {
  box-shadow: 0px 0px 8px 0px rgba(68, 104, 230, 0.3);
  border-color: #4d88ff !important;
  /* 光标变色效果 */
}
