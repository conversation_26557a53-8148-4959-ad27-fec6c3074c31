.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 14px;
  color: #86909c;
}
textarea:-moz-placeholder {
  font-size: 14px;
  color: #86909c;
}
input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea:-ms-input-placeholder {
  font-size: 14px;
  color: #86909c;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #ACB4BF !important;
  font-size: 14px;
}
::-webkit-scrollbar {
  width: 6px;
  height: 10px;
}
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #d9d9d9;
}
::-webkit-scrollbar-track {
  border-radius: 6px;
}
.popups {
  background: #FFFFFF;
  border-radius: 10px;
  display: none;
}
.popups .title {
  height: 52px;
  line-height: 52px;
  font-size: 16px;
  font-weight: 400;
  padding: 0 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #F2F2F2;
}
.popups .title .name {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  color: #1d2129;
  text-align: left;
}
.popups .title .close {
  width: 20px;
  height: 20px;
  background: url(../../images/creditManage/close-icon.png) no-repeat center;
  cursor: pointer;
}
.popups .popup-con {
  padding: 30px;
}
.popups .bottom {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 66px;
  border-top: 1px solid #E5E6EB;
  padding: 0 24px;
}
.popups .bottom div {
  width: 88px;
  height: 34px;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
  border-radius: 18px;
  cursor: pointer;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popups .bottom div.cancle {
  border: 1px solid #C9CDD4;
  color: #4E5969;
  background-color: #fff;
  margin-right: 16px;
}
.popups .bottom div.confirm {
  color: #fff;
  background: #4D88FF;
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  border: 1px solid #4D88FF;
}
.loading {
  width: 520px;
  height: 196px;
}
.loading .popup-con {
  padding: 40px 80px;
  position: relative;
}
.loading .popup-con .close {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 20px;
  height: 20px;
  background: url(../../images/creditManage/close-icon.png) no-repeat center;
  cursor: pointer;
}
.loading .popup-con .exporting img {
  width: 48px;
  height: 48px;
  display: block;
  margin: 0 auto 24px;
  animation: rotate 2s linear infinite;
}
.loading .popup-con .exporting p {
  color: #1d2129;
  font-size: 16px;
  text-align: center;
  line-height: 22px;
}
.prompt {
  width: 560px;
  height: 240px;
}
.prompt .popup-con {
  padding: 40px 100px;
}
.prompt .popup-con .err-tips {
  padding-top: 72px;
  background: url(../../images/creditManage/close-icons.png) no-repeat center top;
}
.prompt .popup-con .err-tips p {
  color: #1d2129;
  font-size: 16px;
  line-height: 22px;
  text-align: center;
  margin-bottom: 32px;
}
.prompt .popup-con .err-tips .btn {
  box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
  background-color: #4d88ff;
  border-radius: 18px;
  width: 88px;
  height: 34px;
  text-align: center;
  line-height: 34px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  margin: 0 auto;
}
#creditCart {
  width: 620px;
}
#creditCart .popup-con {
  padding: 40px 70px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  overflow-y: auto;
}
#creditCart .popup-con .export-cons .sel-row {
  width: 100%;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
#creditCart .popup-con .export-cons .sel-row:last-child {
  margin-bottom: 0;
}
#creditCart .popup-con .export-cons .sel-row .name {
  flex-shrink: 0;
  width: auto;
  white-space: nowrap;
  margin-right: 14px;
  font-size: 14px;
  line-height: 34px;
  color: #1d2129;
}
#creditCart .popup-con .export-cons .sel-row .sel-item {
  display: flex;
  align-items: center;
  flex: 1;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  margin-bottom: 30px;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .tips {
  padding-left: 20px;
  color: #969dab;
  font-size: 14px;
  background: url(../../images/creditManage/tips.png) no-repeat left center;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel {
  width: 180px;
  flex-shrink: 0;
  height: 34px;
  line-height: 34px;
  margin-bottom: 0;
  padding-right: 10px;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel.disabled {
  cursor: default;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel.disabled .select-input {
  background-color: #e5e7eb;
  border: 1px solid #e5e7eb;
  cursor: default;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel.disabled .select-input .name {
  cursor: default;
  color: #505762 !important;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel em {
  float: left;
  font-size: 14px;
  color: #474C59;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input {
  height: 34px;
  border-radius: 4px;
  border: 1px solid #D4D6D9;
  background-color: #fff;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input em {
  position: absolute;
  top: 11px;
  right: 11px;
  width: 12px;
  height: 12px;
  background: url(../../images/creditManage/drop-down.png) no-repeat center;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  z-index: 0;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .name {
  font-size: 14px;
  color: #ACB4BF;
  padding-left: 13px;
  line-height: 32px;
  width: 100%;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
  z-index: 1;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .name.ckd {
  color: #131B26;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input.clicked em {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
  display: block;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown {
  width: 100%;
  display: none;
  margin: 5px 0;
  padding: 6px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: fixed;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .search {
  margin: 8px;
  height: 36px;
  box-sizing: border-box;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  display: flex;
  align-items: center;
  overflow: hidden;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .search input {
  border: none;
  flex: 1;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  font-size: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  width: 50px;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .search input::placeholder {
  color: #8F97A8;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .search span {
  cursor: pointer;
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  background: url(../../images/creditManage/search-icon.png) no-repeat center;
  margin: 9px;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .all-selects {
  color: #4E5969;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  margin: 0 20px;
  padding-left: 24px;
  background: url(../../images/creditManage/check-icon.png) no-repeat left center;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .all-selects.cur {
  background: url(../../images/creditManage/checked-icon.png) no-repeat left center;
  background-size: 18px ;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .dropdown-lists {
  max-height: 140px;
  overflow-y: auto;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .dropdown-lists li {
  margin: 0;
  line-height: normal;
  line-height: 40px;
  padding: 0 20px;
  clear: both;
  color: #4E5969;
  font-size: 14px !important;
  list-style: none;
  cursor: pointer;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .dropdown-lists li span {
  display: block;
  padding-left: 27px;
  background: url(../../images/creditManage/check-icon.png) no-repeat left center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .dropdown-lists li:hover {
  background: #F5F7FA;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #E1EBFF;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .dropdown-lists li.cur span {
  background: url(../../images/creditManage/checked-icon.png) no-repeat left center;
  background-size: 18px;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .dropdown-list li {
  margin: 0;
  line-height: normal;
  padding: 0 16px;
  clear: both;
  color: #4E5969;
  font-size: 14px !important;
  white-space: nowrap;
  list-style: none;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown .dropdown-list li:hover {
  background: #E1EBFF;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date {
  min-width: 296px;
  box-shadow: 0px 8px 24px 0px rgba(36, 36, 37, 0.08);
  border-radius: 6px;
  overflow: auto;
  margin: 8px 0;
  padding: 0;
  background: #ffffff;
  box-sizing: border-box;
  position: absolute;
  left: 0;
  top: 33px;
  background: #FFFFFF;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-top {
  width: 100%;
  height: 60px;
  display: flex;
  display: -webkit-flex;
  display: -ms-flex;
  display: -moz-flex;
  align-items: center;
  position: relative;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-top:after {
  content: "";
  width: 100%;
  height: 1px;
  background: rgba(25, 59, 104, 0.06);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-top ul.select-item {
  overflow: hidden;
  width: 64px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid rgba(25, 59, 104, 0.08);
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  display: flex;
  display: -webkit-flex;
  display: -ms-flex;
  display: -moz-flex;
  align-items: center;
  margin-left: 16px;
  margin-right: 8px;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-top ul.select-item li {
  width: 50%;
  color: #193B68;
  text-align: center;
  line-height: 26px;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-top ul.select-item li.active {
  background: #ebf4ff;
  font-weight: bold;
  color: #1479FF;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-top .select-date-week {
  display: flex;
  display: -webkit-flex;
  display: -ms-flex;
  display: -moz-flex;
  align-items: center;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-top .select-date-week input {
  width: 92px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid rgba(25, 59, 104, 0.08);
  text-align: center;
  font-size: 12px;
  /*  &:focus{
                                    border: 1px solid #1479ff;
                                    box-shadow: 0 0 0 3px rgba(20, 121, 255, 0.2);
                                    font-weight: bold;
                                    color: #1479FF;
                                  }*/
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-top .select-date-week input::placeholder {
  content: "";
  color: rgba(25, 59, 104, 0.4);
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-top .select-date-week span {
  color: #193b68;
  margin: 0 2px;
  text-align: center;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-con {
  overflow: hidden;
  padding-left: 16px;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-con ul {
  padding-top: 16px;
  overflow: hidden;
  height: 232px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  overflow-y: auto;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-con ul li {
  height: 24px;
  line-height: 24px;
  border-radius: 4px;
  margin-bottom: 4px;
  color: #193B68;
  position: relative;
  text-align: center;
  width: 264px;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-con ul li.active1 {
  background: #EBF4FF;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-con ul li.active {
  color: #1479FF;
  background: #EBF4FF;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-con ul li.active h3 {
  font-weight: bold;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-con ul li span {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  line-height: 24px;
  color: rgba(25, 59, 104, 0.4);
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-bot {
  height: 46px;
  position: relative;
  display: flex;
  display: -webkit-flex;
  display: -ms-flex;
  display: -moz-flex;
  align-items: center;
  justify-content: flex-end;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-bot:after {
  content: "";
  width: 100%;
  height: 1px;
  background: rgba(25, 59, 104, 0.06);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-bot span {
  width: 48px;
  height: 24px;
  border-radius: 4px;
  text-align: center;
  font-weight: 400;
  margin-left: 8px;
  line-height: 24px;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-bot span.btn-sure {
  margin-right: 16px;
  background: #1479FF;
  color: #ffffff;
}
#creditCart .popup-con .export-cons .sel-row .sel-item .sel .select-input .select-dropdown-date .select-date-bot span.btn-cancel {
  border: 1px solid rgba(25, 59, 104, 0.08);
  line-height: 22px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: #193B68;
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
