.layui-form-label {
  width: 106px;
  padding: 5px 15px 5px 0;
  color: #83889D;
  text-align: left;
}
.layui-input-block {
  margin-left: 123px;
  min-height: 30px;
}
.layui-form-item {
  padding-left: 0;
  margin-bottom: 20px;
}
.layui-form-item .layui-inline {
  margin-right: 0;
}
.layui-input,
.layui-textarea,
.layui-select {
  height: 30px;
  border-radius: 4px;
  border-color: #BCBCC5;
}
.layui-input {
  border-color: #BCBCC5;
  border-radius: 4px;
}
.layui-input::placeholder {
  color: #BCBCC5;
}
.layui-form-select dl dd.layui-this {
  background-color: #4C85FA;
}
.layui-btn {
  display: block;
  width: 100px;
  height: 32px;
  border-radius: 4px;
  text-align: center;
  line-height: 32px;
  font-size: 16px;
  cursor: pointer;
  float: left;
}
.layui-btn.btn-cancel {
  background: #F5F6F8;
  border: 1px solid #4C85FA;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: #4C85FA;
}
.layui-btn.btn-related {
  border: 1px solid #525669;
  color: #525669;
  background: none;
}
.layui-btn.btn-search {
  background: #525669;
  color: #FFFFFF;
}
.layui-btn.btn-save {
  background: #4C85FA;
  color: #FFFFFF;
}
.layui-form-radio {
  margin: 0 10px 0 0;
}
.layui-form-radio * {
  color: #4C85FA;
}
.layui-form-radio > i {
  margin-right: 6px;
}
.layui-form-radioed > i {
  color: #4C85FA;
}
.layui-form-radioed > i,
.layui-form-radio > i:hover {
  color: #4C85FA;
}
.layui-form-checkbox {
  margin: 0 10px 0 0;
}
.layui-form-checkbox[lay-skin="primary"] span {
  color: #4C85FA;
}
.layui-form-checkbox[lay-skin="primary"] {
  padding-left: 24px;
  min-width: 16px;
  min-height: 16px;
}
.layui-form-checkbox[lay-skin="primary"] i {
  margin-right: 6px;
}
.layui-form-checkbox[lay-skin="primary"]:hover i {
  border-color: #4C85FA;
  color: #fff;
}
.layui-form-checked[lay-skin="primary"] i {
  border-color: #4C85FA !important;
  background-color: #4C85FA;
}
.layui-textarea::placeholder {
  color: #BCBCC5;
}
.layui-upload-drag {
  padding: 6px 15px;
  margin-right: 10px;
}
.layui-upload-drag .layui-icon {
  color: #4C85FA;
}
.uploadIntro p {
  color: #BCBCC5;
  margin: 5px 0px;
}
.layui-form-onswitch {
  border-color: #BCBCC5;
  background-color: #BCBCC5;
}
.layui-table {
  color: #4E5969;
  margin: 0;
}
.layui-table-view .layui-table th,
.layui-table-view .layui-table td {
  border-color: #E8EBF1;
}
.layui-table thead tr,
.layui-table-header,
.layui-table-tool,
.layui-table-total,
.layui-table-total tr,
.layui-table-patch,
.layui-table-mend,
.layui-table[lay-even] tr:nth-child(even),
.layui-table tbody tr:hover,
.layui-table-hover,
.layui-table-click {
  border-color: #E8EBF1;
}
.layui-table-view .layui-table tr th > div {
  margin: -5px 0px;
  height: 36px;
  line-height: 36px;
}
.layui-table-view .layui-table tr th:nth-child(1) > div {
  border: none;
}
.layui-table thead tr,
.layui-table-header,
.layui-table-tool,
.layui-table-total,
.layui-table-total tr,
.layui-table-patch,
.layui-table-mend,
.layui-table[lay-even] tr:nth-child(even) {
  background-color: #FAFBFC;
}
.layui-table thead tr,
.layui-table-header {
  background-color: #F1F3F6;
}
.layui-table-click {
  background: none;
}
table tbody tr:nth-child(even) {
  background-color: #f8f8fa;
}
.layui-table tbody tr:hover,
.layui-table-hover {
  background-color: #edf2fd;
}
.layui-table-view .layui-table td {
  padding: 4px 0;
}
.layui-table-view {
  margin: 0;
}
