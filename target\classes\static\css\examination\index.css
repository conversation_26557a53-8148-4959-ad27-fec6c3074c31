body {
  background-color: #F7F8FA;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.j-search-con {
  display: flex;
  align-items: center;
  position: relative;
  width: 240px;
  cursor: pointer;
}
.j-search-con .j-select-year {
  left: 0;
}
.j-search-con input {
  width: 100%;
  height: 34px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  padding: 0 20px 0 10px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  cursor: pointer;
}
.j-search-con input::placeholder {
  color: #86909C;
}
.j-search-con .j-arrow {
  width: 10px;
  height: 10px;
  background: url(../../images/examination/down-icon.png) no-repeat center;
  position: absolute;
  right: 12px;
  top: 12px;
}
.j-search-con .j-arrow.j-arrow-slide {
  transform: rotate(180deg);
}
.j-search-con .j-select-year {
  position: absolute;
  top: 40px;
  left: -1px;
  z-index: 9;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  display: none;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}
.j-search-con .j-select-year.slideShow {
  display: block;
}
.j-search-con .j-select-year .search,.j-search-con .j-select-year .search1 {
  height: 36px;
  background: #F5F7FA;
  border-radius: 18px;
  margin: 11px 10px;
}
.j-search-con .j-select-year .search input,.j-search-con .j-select-year .search1 input {
  border: none;
  width: 176px;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  float: left;
}
.j-search-con .j-select-year .search span ,.j-search-con .j-select-year .search1 span {
  cursor: pointer;
  float: left;
  width: 16px;
  height: 16px;
  background: url(../../images/examination/search-icon.png) no-repeat center;
  margin-top: 10px;
}
.j-search-con .j-select-year .all-selects {
  line-height: 17px;
  margin-bottom: 4px;
  height: 17px;
  padding: 0 14px;
  font-size: 12px;
  color: #6B89B3;
  cursor: pointer;
  user-select: none;
}
.j-search-con .j-select-year ul {
  overflow: hidden;
  max-height: 200px;
  overflow-y: auto;
}
.j-search-con .j-select-year ul li {
  line-height: 40px;
  text-align: left;
  text-indent: 16px;
  cursor: pointer;
  font-size: 14px;
  color: #4E5969;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-right: 30px;
  background-color: #ffffff;
  /*background-image: url("../../images/examination/check-icon.png");*/
  background-repeat: no-repeat;
  background-position: 96% center;
}
.j-search-con .j-select-year ul li:hover {
  background-color: #E1EBFF;
  color: #4D88FF;
  font-weight: 500;
}
.j-search-con .j-select-year ul li.active {
  background-color: #E1EBFF;
  /*background-image: url("../../images/examination/check-cur.png");*/
  color: #4D88FF;
  font-weight: 500;
}
/*.j-search-con.single-box .j-select-year ul li {*/
/*  background-image: url("../../images/examination/radio-icon.png");*/
/*}*/
/*.j-search-con.single-box .j-select-year ul li.active {*/
/*  background-image: url("../../images/examination/radio-cur-icon.png");*/
/*}*/
.z-main {
  max-width: 1660px;
  margin: 8px auto 0;
  background-color: #ffffff;
  overflow: hidden;
  min-height: calc(100vh - 8px);
}
.z-main .z-title {
  padding: 29px 0 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #E8EBF1;
}
.z-main .z-title h3 {
  font-size: 16px;
  line-height: 22px;
  color: #1D2129;
  padding-left: 9px;
  position: relative;
  margin-left: 30px;
}
.z-main .z-title h3::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 3px;
}
.z-main .z-title span {
  color: #86909C;
  font-size: 14px;
  margin-left: 16px;
  margin-top: 2px;
}
.z-main .z-search {
  margin: 20px 30px 0;
}
.z-main .z-search .layui-form {
  display: flex;
}
.z-main .z-search .layui-form .layui-form-item {
  margin-right: 32px;
}
.z-main .z-search .layui-form .layui-form-label {
  width: 60px;
}
.z-main .z-search .layui-form .layui-input-block {
  margin-left: 75px;
}
.z-main .z-sel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 30px 0 20px;
}
.z-main .z-sel .set-limit {
  background: #4D88FF;
  box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
  border-radius: 4px;
  width: 152px;
  height: 36px;
  font-size: 14px;
  color: #FFFFFF;
  cursor: pointer;
  text-align: center;
  line-height: 36px;
}
.z-main .z-lab {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 34px;
  margin-right: 24px;
  margin: 30px 0 24px;
}
.z-main .z-lab .name {
  text-align: left;
  color: #1D2129;
  font-size: 14px;
  margin-right: 14px;
}
.z-main .z-lab .name em {
  color: #F76560;
  display: inline-block;
  margin-right: 5px;
  vertical-align: middle;
}
.z-main .z-lab .input input {
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  width: 240px;
  height: 34px;
}
.z-main .z-lab .radio {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 34px;
}
.z-main .z-lab .radio span {
  padding-left: 24px;
  font-size: 14px;
  color: #86909C;
  background: url(../../images/examination/radio-icon.png) no-repeat left center;
  background-size: 16px;
  cursor: pointer;
  margin-right: 24px;
}
.z-main .z-lab .radio span.cur {
  background: url(../../images/examination/radio-cur-icon.png) no-repeat left center;
  background-size: 16px;
}
.z-main .z-tab {
  /*overflow: hidden;*/
  margin: 0 30px;
}
.z-main .z-tab ul {
  display: flex;
  align-items: center;
  /*height: 60px;*/
  line-height: 40px;
  padding:10px 0;
  flex-wrap: wrap;
}
.z-main .z-tab ul li {
  margin-right: 62px;
  color: #86909C;
  font-size: 14px;
  cursor: pointer;
}
.z-main .z-tab ul li.active {
  position: relative;
  color: #1D2129;
  font-size: 16px;
}
.z-main .z-tab ul li.active::after {
  content: "";
  width: 100%;
  height: 3px;
  background: #4D88FF;
  border-radius: 3px 3px 0 0;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
}
.z-main .z-box {
  margin: 0 30px;
}
.z-main .z-box .z-table {
  overflow: hidden;
  margin-bottom: 24px;
}
.z-main .z-box .z-table .oprate-table .edit {
  color: #4C88FF;
  margin-right: 16px;
  cursor: pointer;
}
.z-main .z-box .z-table .oprate-table .delete {
  color: #F76560;
  cursor: pointer;
}
.z-main .z-box .btnSave {
  width: 88px;
  height: 32px;
  background-color: #368CFF;
  border-radius: 4px;
  color: #ffffff;
  text-align: center;
  line-height: 30px;
  margin: 60px auto 40px;
  cursor: pointer;
  border: none;
}
.z-main .box-classroom {
  margin-top: 30px;
}
.z-main .box-classroom .layui-form .layui-form-label {
  width: 204px;
}
.z-main .box-classroom .layui-form .layui-input-block {
  margin-left: 219px;
}
.z-main .box-classroom .layui-form .layui-input-block .readonly-input {
  background: #F7F8FA;
}
.z-main .box-classroom .layui-form .layui-input-block .layui-input {
  width: 240px;
}
.z-main .layui-form-label span {
  width: 16px;
  height: 17px;
  background: url("../../images/examination/tips-icon.png") no-repeat right center;
  background-size: 16px;
  display: inline-block;
  vertical-align: top;
  padding-left: 6px;
  cursor: pointer;
}
.z-main .box-invigilate {
  margin-top: 30px;
}
.z-main .box-invigilate .layui-form .layui-form-label {
  width: 176px;
}
.z-main .box-invigilate .layui-form .layui-form-label span {
  width: 16px;
  height: 17px;
  background: url("../../images/examination/tips-icon.png") no-repeat right center;
  background-size: 16px;
  display: inline-block;
  vertical-align: middle;
  padding-left: 6px;
  cursor: pointer;
}
.z-main .box-invigilate .layui-form .layui-input-block {
  margin-left: 191px;
}
.z-main .box-invigilate .layui-form .layui-input-block .readonly-input {
  background: #F7F8FA;
}
.z-main .box-invigilate .layui-form .layui-input-block .layui-input {
  width: 240px;
}
.z-main .box-invigilate .layui-form .layui-input-block #addRule {
  background: #4D88FF;
  box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
  border-radius: 4px;
  width: 114px;
  height: 36px;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  float: right;
}
.z-main .box-invigilate .layui-form .layui-input-block #addRule img {
  margin-right: 6px;
}
.z-main .box-teacher {
  margin-top: 30px;
}
.z-main .box-teacher .layui-form .layui-form-label {
  width: 220px;
}
.z-main .box-teacher .layui-form .layui-input-block {
  margin-left: 155px;
}
.z-main .box-teacher .layui-form .layui-input-block .readonly-input {
  background: #F7F8FA;
}
.z-main .box-teacher .layui-form .layui-input-block .layui-input {
  width: 240px;
}
.z-main .box-teacher .layui-form .layui-input-block .addRule{
  background: #4D88FF;
  box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
  border-radius: 4px;
  width: 114px;
  height: 36px;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  float: right;
}
.z-main .box-teacher .layui-form .layui-input-block .addRule img {
  margin-right: 6px;
}
.dialog {
  border-radius: 10px;
  background-color: #ffffff;
}
.dialog .dialog-title {
  border-bottom: 1px solid #E5E6EB;
  height: 56px;
  line-height: 56px;
  color: #1D2129;
  font-size: 16px;
  text-indent: 30px;
}
.dialog .dialog-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 70px;
  border-top: 1px solid #E5E6EB;
  padding-right: 30px;
}
.dialog .dialog-btn button {
  width: 88px;
  height: 36px;
  font-size: 14px;
  border-radius: 18px;
  cursor: pointer;
}
.dialog .dialog-btn button.pu-cancel {
  border: 1px solid #C9CDD4;
  color: #4E5969;
  background-color: #fff;
  margin-right: 16px;
}
.dialog .dialog-btn button.pu-sure {
  color: #fff;
  background: #4D88FF;
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  border: 1px solid #4D88FF;
}
#examLimitMax {
  width: 512px;
  display: none;
}
#examLimitMax .layui-form {
  margin: 30px 60px;
}
#examLimitMax .layui-form .layui-form-label {
  color: #1D2129;
  font-size: 14px;
  width: 86px;
}
#examLimitMax .layui-form .layui-input-block {
  margin-left: 101px;
}
#examLimitMax .layui-form .layui-input-block .readonly-input {
  background: #F7F8FA;
}
#invigilateMax {
  width: 488px;
  display: none;
}
#invigilateMax .layui-form {
  margin: 30px 60px 10px;
}
#invigilateMax .layui-form .layui-form-label {
  width: 112px;
}
#invigilateMax .layui-form .layui-input-block {
  margin-left: 127px;
}
#invigilateMax .layui-form .layui-input-block .readonly-input {
  background: #F7F8FA;
}
.forbitRule {
  width: 720px;
  display: none;
}
.forbitRule .layui-form {
  margin: 30px 60px 10px;
}
.forbitRule .layui-form .layui-form-label {
  width: 100px;
  text-indent: 14px;
}
.forbitRule .layui-form .layui-form-label.label-require {
  position: relative;
}
.forbitRule .layui-form .layui-form-label.label-require::after {
  display: block;
  content: "*";
  color: #F76560;
  width: 10px;
  height: 10px;
  position: absolute;
  left: 0;
  top: 7px;
  text-indent: 0;
  font-size: 14px;
}
.forbitRule .layui-form .layui-input-block {
  margin-left: 115px;
}
.forbitRule .layui-form .layui-input-block .layui-input {
  width: 240px;
}
.forbitRule .table-con {
  overflow: hidden;
  margin-bottom: 30px;
  display: flex;
}
.forbitRule .table-con ul {
  flex: 1;
}
.forbitRule .table-con ul li {
  display: block;
  line-height: 36px;
  height: 36px;
  text-align: center;
  font-size: 14px;
}
.forbitRule .table-con .head {
  background: #F1F3F6;
  color: #86909C;
  font-family: 'Palanquin Dark';
}
.forbitRule .table-con .con {
  margin-top: 4px;
  font-family: 'ABeeZee';
  color: #4E5969;
}
.forbitRule .table-con .con li {
  margin-right: 4px;
  background: #F7F8FA;
  border-radius: 4px;
  margin-bottom: 4px;
  cursor: pointer;
}
.forbitRule .table-con .con li i {
  display: none;
  font-size: 16px;
  color: #4d88ff;
  font-weight: bold;
}
.forbitRule .table-con .con li.time {
  background: #e4ecf7;
}
.forbitRule .table-con .con li.active {
  background: #E1EBFF;
}
.forbitRule .table-con .con li.active i {
  display: inherit;
}
.forbitRule .table-con .con li.disabled {
  background: #ebebeb;
  cursor: no-drop;
}
@media screen and (max-width: 1720px) {
  .z-main {
    margin: 8px 30px 0;
  }
}

/*20240318 add*/
textarea::placeholder {
  color: #86909C;
  font-size: 14px;
  line-height: 1;
  line-height: 20px;
}


.textarea textarea {
  width: 800px;
  height: 400px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  box-sizing: border-box;
  padding: 20px;
  line-height: 20px;
}

.not-parameters .note-content .handle {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 800px;
}

.not-parameters .note-content .handle .texts {
  color: #86909c;
  font-size: 14px;
  line-height: 36px;
}

.not-parameters .note-content .handle .button {
  background: #4d88ff;
  box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
  border-radius: 4px;
  width: 74px;
  height: 36px;
  font-size: 14px;
  color: #ffffff;
  line-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.not-parameters .z-main .layui-form-label span {
  vertical-align: text-bottom;
}

.not-parameters .layui-form-label {
  padding: 4px 15px 4px 0;
}

.not-parameters .z-table .top {
  width: 100%;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.not-parameters .z-table .top .title {
  color: #1D2129;
  font-size: 14px;
  line-height: 36px;
}

.not-parameters .z-table .top .button {
  background: #4d88ff;
  box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
  border-radius: 4px;
  width: 134px;
  height: 36px;
  font-size: 14px;
  color: #ffffff;
  line-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.not-parameters .z-table .top .button img {
  margin-right: 10px;
}

.layui-layer {
  border-radius: 10px !important;
}

#addSendingContent {
  width: 842px;
  display: none;
}

#addSendingContent .dialog-con {
  padding: 30px;
}

.j-search-con input {
  padding-right: 25px;
}

#addSendingContent .check-list ul {
  overflow: hidden;
}

#addSendingContent .check-list ul li {
  float: left;
  padding-left: 30px;
  background: url(../../images/examination/check-icon.png) no-repeat left center;
  margin-right: 20px;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
}

#addSendingContent .check-list ul li.cur {
  background: url(../../images/examination/check-cur.png) no-repeat left center;
}

.layui-layer-title{
  border-radius: 10px !important;
}
.subjectExams {
  width: 480px;
  display: none;
}
.subjectExams .layui-form {
  margin: 30px 60px 10px;
}
.subjectExams .layui-form .layui-form-label {
  width: 100px;
  text-indent: 14px;
}
.subjectExams .layui-form .layui-form-label.label-require {
  position: relative;
}
.subjectExams .layui-form .layui-form-label.label-require::after {
  display: block;
  content: "*";
  color: #f76560;
  width: 10px;
  height: 10px;
  position: absolute;
  left: 0;
  top: 7px;
  text-indent: 0;
  font-size: 14px;
}
.subjectExams .layui-form .layui-input-block {
  margin-left: 115px;
}
.subjectExams .layui-form .layui-input-block .layui-input {
  width: 240px;
}
.subjectExams .layui-form .layui-input-block .layui-input.disabled {
  background: #f7f8fa;
  color: #c6c7cd !important;
}
.subjectExams .layui-form .layui-input-block .layui-input.disabled:hover {
  border-color: #E5E6EB !important;
}
.subjectExams .table-con {
  overflow: hidden;
  margin-bottom: 30px;
  display: flex;
}
.subjectExams .table-con ul {
  flex: 1;
}
.subjectExams .table-con ul li {
  display: block;
  line-height: 36px;
  height: 36px;
  text-align: center;
  font-size: 14px;
}
.subjectExams .table-con .head {
  background: #f1f3f6;
  color: #86909c;
  font-family: "Palanquin Dark";
}
.subjectExams .table-con .con {
  margin-top: 4px;
  font-family: "ABeeZee";
  color: #4e5969;
}
.subjectExams .table-con .con li {
  margin-right: 4px;
  background: #f7f8fa;
  border-radius: 4px;
  margin-bottom: 4px;
  cursor: pointer;
}
.subjectExams .table-con .con li i {
  display: none;
  font-size: 16px;
  color: #4d88ff;
  font-weight: bold;
}
.subjectExams .table-con .con li.time {
  background: #e4ecf7;
}
.subjectExams .table-con .con li.active {
  background: #e1ebff;
}
.subjectExams .table-con .con li.active i {
  display: inherit;
}
.subjectExams .table-con .con li.disabled {
  background: #ebebeb;
  cursor: no-drop;
}
/* notice note-content  pubRole  z-table  not-parameters */
