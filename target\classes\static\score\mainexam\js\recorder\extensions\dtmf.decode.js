/*
录音
https://github.com/xiangyuecn/Recorder
src: extensions/dtmf.decode.js
*/
!function(){"use strict";Recorder.DTMF_Decode=function(r,e,o){o||(o={});var t=o.lastIs||"",a=null==o.lastCheckCount?99:o.lastCheckCount,s=o.prevIs||"",l=o.totalLen||0,n=o.pcm,c=o.checkFactor||0,h=o.debug,u=[];if(!Recorder.LibFFT)throw new Error("需要lib.fft.js支持");var d=256,v=Recorder.LibFFT(d),f=c||3,g=l,i=e/4e3,b=Math.floor(r.length/i);l+=b;var k=0;n&&n.length>d&&(b+=k=64*(f+1),g-=k);var F=new Int16Array(b);k&&F.set(n.subarray(n.length-k));for(var p=0;p<r.length;k++,p+=i)F[k]=r[Math.round(p)];r=F;for(var M=(e=4e3)/d,y=0;y+d<=r.length;y+=64){F=r.subarray(y,y+d);for(var C=v.transform(F),m=[],I=0,w=0,L=0,x=0,D=0,R=0,T=0,A=0,j=0;j<C.length;j++){var B=C[j],E=Math.log(B);m.push(E);var _=(j+1)*M;20<E&&(I<B&&_<1050?(I=B,w=E,L=_,x=j):D<B&&1050<_&&(D=B,R=E,T=_,A=j))}var q=-1,z=-1;if(600<L&&T<1700&&Math.abs(w-R)<2.5){var G=1,H=w,J=0;for(j=x;j<A;j++){(_=m[j])&&_<H&&(H=_,J=j)}var K=.5*(w-H),N=w;for(j=x;G&&j<J;j++){(_=m[j])<=N?N=_:K<_-N&&(G=0)}var O=H;for(j=J;G&&j<A;j++){O<=(_=m[j])?O=_:K<O-_&&(G=0)}G&&(q=U(L,Q[0],M),z=U(T,Q[1],M))}var P="";0<=q&&0<=z?(P=S[q][z],h&&console.log(P,Math.round((g+y)/e*1e3),w.toFixed(2),R.toFixed(2),Math.abs(w-R).toFixed(2)),t?t.key==P?a++:(P="",a=t.old+a):(s&&s.old2&&s.key==P&&g+y-s.start<100*e/1e3&&(a=(t=s).old2+1,h&&console.warn("接续了开叉的信号"+a)),t||(3<=a?(t={key:P,old:a,old2:a,start:g+y,pcms:[],use:0},a=1):(P="",a=0)))):t&&(t.old2=a,a=t.old+a),P?(h&&t.pcms.push(F),f<=a&&!t.use&&(t.use=1,u.push({key:P,time:Math.round(t.start/e*1e3)})),t.use&&(h&&console.log(P+"有效按键",t),t.old=0,a=t.old2=0)):(t&&(h&&console.log(t),s=t),t="",a++)}return{keys:u,lastIs:t,lastCheckCount:a,prevIs:s,totalLen:l,pcm:r,checkFactor:c,debug:h}};var Q=[[697,770,852,941],[1209,1336,1477,1633]],S=[["1","2","3","A"],["4","5","6","B"],["7","8","9","C"],["*","0","#","D"]],U=function(r,e,o){for(var t=-1,a=1e3,s=0;s<e.length;s++){var l=Math.abs(e[s]-r);l<a&&(a=l)<2*o&&(t=s)}return t}}();