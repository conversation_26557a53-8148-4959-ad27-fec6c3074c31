.marker {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.dialog {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: #ffffff;
    border-radius: 10px;
}

.dialog .dialog-title {
    height: 54px;
    border-bottom: 1px solid #e5e6eb;
    color: #1d2129;
    font-size: 16px;
    line-height: 54px;
    text-indent: 30px;
    position: relative;
}

.dialog .dialog-title h5 {
    font-weight: normal;
}

.dialog .dialog-title .close-btn {
    width: 20px;
    height: 20px;
    background: url('../../images/cultivation/close.png') no-repeat center;
    background-size: 20px;
    position: absolute;
    right: 24px;
    top: 17px;
    cursor: pointer;
}

.dialog .dialog-con {
    padding: 40px 80px;
}

.dialog .dialog-footer {
    height: 70px;
    border-top: 1px solid #e5e6eb;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.dialog .dialog-footer button {
    width: 88px;
    height: 34px;
    border: 1px solid #c9cdd4;
    border-radius: 18px;
    color: #4e5969;
    font-size: 14px;
    background-color: #ffffff;
    cursor: pointer;
}

.dialog .dialog-footer button:last-child {
    background: #4d88ff;
    border-color: #4d88ff;
    box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
    color: #ffffff;
    margin: 0 30px 0 16px;
}

#teachPlanDialog,
#offerCourseDialog {
    display: block;
    width: 526px;
}

#teachPlanDialog .dialog-title,
#offerCourseDialog .dialog-title {
    display: flex;
    justify-content: space-between;
}

#teachPlanDialog .dialog-title .opt-right,
#offerCourseDialog .dialog-title .opt-right {
    display: flex;
    align-items: center;
}

#teachPlanDialog .dialog-title .opt-right .btn-export,
#offerCourseDialog .dialog-title .opt-right .btn-export {
    font-size: 14px;
    color: #4d88ff;
    padding-left: 20px;
    background: url('../../images/cultivation/record.png') no-repeat left center;
    cursor: pointer;
    margin-right: 16px;
    text-indent: 0;
}

#teachPlanDialog .dialog-title .opt-right .close-btn,
#offerCourseDialog .dialog-title .opt-right .close-btn {
    width: 16px;
    height: 16px;
    background: url('../../images/cultivation/close.png') no-repeat center;
    background-size: 16px;
    margin-right: 24px;
    cursor: pointer;
    position: unset;
}

#teachPlanDialog .item,
#offerCourseDialog .item {
    display: flex;
    align-items: center;
}

#teachPlanDialog .item .label,
#offerCourseDialog .item .label {
    color: #1d2129;
    font-size: 14px;
    margin-right: 14px;
}

#teachPlanDialog .error-tips,
#offerCourseDialog .error-tips {
    font-size: 14px;
    color: red;
    margin: 6px 0 0 126px;
    display: none;
}

#offerCourseDialog {
    width: 520px;
}

#offerCourseDialog .dialog-title .opt-right {
    display: flex;
    align-items: center;
}

#offerCourseDialog .dialog-title .opt-right .btn-export {
    background: url('../../images/cultivation/record1.png') no-repeat left center;
    background-size: 16px;
}

#offerCourseDialog .dialog-con img {
    display: block;
    width: 48px;
    height: 48px;
    margin: 0 auto 20px;
}

#offerCourseDialog .dialog-con p {
    width: 360px;
    text-align: center;
    line-height: 22px;
    font-size: 16px;
    color: #1D2129;
}

#loadingDialog {
    width: 520px;
    padding: 40px 80px;
    box-sizing: border-box;
    display: none;
}

#loadingDialog .close-btn {
    width: 16px;
    height: 16px;
    background: url('../../images/cultivation/close.png') no-repeat center;
    background-size: 16px;
    cursor: pointer;
    position: absolute;
    right: 16px;
    top: 16px;
}

#loadingDialog img {
    width: 48px;
    display: block;
    margin: 0 auto 24px;
    animation: rotate 2s linear infinite;
}

#loadingDialog p {
    color: #1d2129;
    font-size: 16px;
    text-align: center;
}

#recordDialog {
    min-width: 800px;
    width: 80%;
    display: none;
}

#recordDialog .dialog-con {
    padding: 40px 80px 40px;
    position: relative;
}

#recordDialog .dialog-con .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

#recordDialog .dialog-con .item .label {
    color: #1d2129;
    font-size: 14px;
    margin-right: 14px;
}

#recordDialog .dialog-con .recordList {
    margin-bottom: 20px;
}

#recordDialog .dialog-con .recordList .layui-form {
    margin: 4px 0 0;
}

#recordDialog .dialog-con .recordList #recordList,
#recordDialog .dialog-con .recordList .layui-table-view {
    border: unset;
}

#recordDialog .dialog-con .recordList #recordList .layui-table-box,
#recordDialog .dialog-con .recordList .layui-table-view .layui-table-box {
    border: 1px solid #e8ebf3;
}

#recordDialog .dialog-con .recordList .layui-table-page {
    text-align: center;
    border-top: unset;
    padding: 0;
    margin-top: 20px;
}

#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage a,
#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage span {
    border: 1px solid #e5e6eb;
    border-radius: 0;
    line-height: 32px;
    margin-bottom: 0;
    width: 41px;
    height: 32px;
    box-sizing: border-box;
    font-size: 14px;
    color: #4e5969;
}

#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage a .layui-laypage-em,
#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage span .layui-laypage-em {
    border-radius: 0;
}

#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-prev,
#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-prev {
    width: auto;
    border-radius: 2px 0 0 2px;
}

#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-next,
#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-next {
    width: auto;
    border-radius: 0 2px 2px 0;
    margin-right: 16px;
}

#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-skip,
#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-skip {
    color: #86909c;
    margin-right: 16px;
}

#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-skip input,
#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-skip input {
    width: 41px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #e5e6eb;
    margin: 0 8px;
}

#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-skip .layui-laypage-btn,
#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-skip .layui-laypage-btn {
    border-radius: 2px;
    width: 60px;
    height: 32px;
    color: #4e5969;
}

#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-count,
#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-count {
    margin-right: 16px;
}

#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage a.layui-laypage-limits select,
#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage span.layui-laypage-limits select {
    width: 104px;
    height: 30px;
    color: #4e5969;
    padding: 0;
    text-align: center;
    border-radius: 2px;
    margin-top: -2px;
}

#recordDialog .dialog-con .recordList .layui-table-page .layui-laypage .layui-laypage-next ~ * {
    border: none;
    width: auto;
}

#recordDialog .dialog-con .recordList .selRecord {
    position: absolute;
    left: 80px;
    bottom: 36px;
    color: #86909c;
    font-size: 14px;
    line-height: 40px;
    display: flex;
    align-items: center;
}

#recordDialog .dialog-con .recordList .selRecord em,
#recordDialog .dialog-con .recordList .selRecord i {
    color: #4d88ff;
    padding: 0 4px;
}

#recordDialog .dialog-con .recordList .selRecord .z-check {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #4e5969;
    margin-right: 16px;
}

#recordDialog .dialog-con .recordList .selRecord .z-check .check {
    width: 28px;
    height: 28px;
    cursor: pointer;
    margin-right: 6px;
    background: url(../../images/cultivation/check.png) no-repeat center;
    background-size: 28px;
}

#recordDialog .dialog-con .recordList .selRecord .z-check .check.checked {
    background: url(../../images/cultivation/check1.png) no-repeat center;
}

#recordDialog .dialog-con .recordList .refresh {
    font-size: 14px;
    color: #4d88ff;
    padding-left: 20px;
    background: url('../../images/cultivation/refresh.png') no-repeat left center;
    background-size: 16px;
    position: absolute;
    right: 80px;
    bottom: 36px;
    line-height: 40px;
    cursor: pointer;
}

.progress-content {
    width: 96%;
    margin: 0 auto;
    display: flex;
    align-items: center;
}

.progress-content .progress-bar {
    flex: 1;
    border-radius: 3px;
    background: #E5E6EB;
    height: 3px;
    text-align: left;
}

.progress-content .progress-bar .progress {
    background-color: #F76560;
    border-radius: 3px;
    display: block;
    height: 3px;
}

.progress-content .progress-bar .progress.proSuccess {
    background-color: #00B42A;
}

.progress-content .progress-text {
    font-family: "Nunito Sans";
    font-size: 12px;
    color: #4E5969;
    margin-left: 8px;
}

.progress-content .progress-tip {
    width: 12px;
    height: 12px;
    background: url('../../images/cultivation/error-tip2.png') no-repeat center;
    background-size: 12px;
    margin-left: 4px;
    cursor: pointer;
}

.recordSuccess {
    color: #3EB35A;
}

.recordStatus {
    color: #F76560;
}

.recordDetail {
    color: #4C88FF;
    cursor: pointer;
}

.recordDetail:hover {
    color: #4C88FF;
}

#setTipErrorDialog {
    width: 560px;
    overflow: hidden;
    display: none;
}

#setTipErrorDialog .dialog-con {
    padding: 40px 100px;
}

#setTipErrorDialog .dialog-con img {
    display: block;
    width: 48px;
    height: 48px;
    margin: 0 auto 20px;
}

#setTipErrorDialog .dialog-con h5 {
    color: #1D2129;
    text-align: center;
    font-size: 16px;
    margin-bottom: 4px;
}

#setTipErrorDialog .dialog-con p {
    color: #4E5969;
    text-align: center;
    font-size: 16px;
}

#setTipErrorDialog .dialog-con #tipErrorBtn {
    width: 88px;
    height: 34px;
    border-radius: 18px;
    background: #4D88FF;
    box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
    color: #ffffff;
    outline: none;
    display: block;
    margin: 32px auto 0;
    border: none;
    cursor: pointer;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.layui-table-cell {
    padding: 0 10px;
    line-height: 38px;
}

.layui-table-header thead tr {
    background: #f1f3f6;
}

.layui-table-view .layui-table th {
    font-weight: normal;
    color: #86909c;
    font-size: 14px;
}

.layui-table-body tr:nth-child(2n) {
    background: #fafbfc;
}

.layui-table-view .layui-table td {
    color: #4e5969;
    height: 38px;
}

.layui-table-view .layui-table td,
.layui-table-view .layui-table th {
    border-color: #e8ebf3;
}

.layui-layer-page .layui-layer-content {
    overflow: unset;
}

.layui-form-checked.layui-checkbox-disabled:hover {
    border-color: #eee !important;
}

.dialog .dialog-con .layui-form-checked.layui-checkbox-disabled i {
    border-color: #eee !important;
}

.dialog .dialog-con .layui-form-checked.layui-checkbox-disabled i:hover {
    border-color: #eee !important;
}

.layui-form-checkbox[lay-skin="primary"] i:hover {
    border-color: #4d88ff !important;
}

.layui-form-checked[lay-skin="primary"] i {
    border-color: #4d88ff !important;
    background-color: #4d88ff;
}

.layui-layer-tips {
    width: auto !important;
    max-width: 500px;
    margin-left: -32px;
}

.layui-layer-tips .layui-layer-content {
    background-color: rgba(32, 32, 32, 0.8);
    border-radius: 6px;
    box-shadow: unset;
    font-size: 14px;
}

.layui-layer-tips i.layui-layer-TipsB,
.layui-layer-tips i.layui-layer-TipsT {
    left: 34px;
    border-right-color: rgba(32, 32, 32, 0.8);
    transform: rotate(-90deg);
    bottom: -12px;
    border-width: 6px;
}

.layui-table-view select[lay-ignore] {
    height: 20px;
}

.layui-table-page .layui-laypage input:focus,
.layui-table-page .layui-laypage select:focus {
    border-color: #4d88ff !important;
}

.layui-laypage .layui-laypage-curr {
    border-color: #4d88ff !important;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: #4d88ff !important;
}

.layui-table-page .layui-laypage a:hover,
.layui-table-page .layui-laypage span.layui-laypage-curr:hover {
    color: #4d88ff !important;
}

.layui-table-page .layui-laypage a.layui-disabled:hover:hover,
.layui-table-page .layui-laypage span.layui-laypage-curr.layui-disabled:hover {
    color: #d2d2d2 !important;
}

.layui-form-select dl dd.layui-this {
    color: #4d88ff !important;
}

.layui-table-hover {
    background-color: #E1EBFF !important;
}

.layui-table-page > div {
    height: 32px;
}

.layui-table-column {
    min-height: 33px;
}

#addCourse {
    width: 1297px;
}

#addCourse .dialog-con {
    padding: 0 40px;
}

#addCourse .dialog-con .layui-form {
    margin: 30px 0 24px;
}

#addCourse .dialog-con .layui-form .layui-form-label {
    padding: 0;
    line-height: 34px;
    padding-right: 8px;
}

#addCourse .dialog-con .layui-form .layui-input-inline {
    margin-right: 17px;
}

#addCourse .dialog-con .layui-form .layui-input-inline .layui-input {
    height: 34px;
    line-height: 34px;
    width: 120px;
}

#addCourse .dialog-con .layui-form .layui-input-inline .layui-form-select {
    width: 100px;
}

#addCourse .dialog-con .layui-form .layui-input-inline .layui-form-select .layui-input {
    width: 100px;
}

#addCourse .dialog-con .layui-form .layui-btn {
    width: 64px;
    height: 34px;
    line-height: 32px;
    background: #4d88ff;
    border-radius: 6px;
}

#addCourse .dialog-con .layui-form .layui-btn.layui-btn-primary {
    background-color: unset;
    border: 1px solid #4d88ff;
    color: #4d88ff;
}

#addCourse .dialog-con .layui-form .layui-btn.layui-btn-primary:hover {
    border-color: #4d88ff;
}

#addCourse .dialog-con .layui-input-block {
    margin-left: 0;
}

#addCourse .dialog-con .form-course {
    display: flex;
    align-items: center;
}

#addCourse .dialog-con .form-course .layui-inline-wrap {
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

#addCourse .dialog-con .courseList {
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
}

#addCourse .dialog-con .courseList .layui-form {
    margin: 4px 0 31px;
}

#addCourse .dialog-con .courseList #courseList,
#addCourse .dialog-con .courseList .layui-table-view {
    width: 1216px;
    border: unset;
}

#addCourse .dialog-con .courseList #courseList .layui-table-box,
#addCourse .dialog-con .courseList .layui-table-view .layui-table-box {
    border: 1px solid #e8ebf3;
}

#addCourse .dialog-con .courseList .layui-table-page {
    text-align: center;
    border-top: unset;
    padding: 0;
    margin-top: 20px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span {
    border: 1px solid #E5E6EB;
    border-radius: 0;
    line-height: 32px;
    margin-bottom: 0;
    width: 41px;
    height: 32px;
    box-sizing: border-box;
    font-size: 14px;
    color: #4E5969;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a .layui-laypage-em,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span .layui-laypage-em {
    border-radius: 0;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-prev,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-prev {
    width: auto;
    border-radius: 2px 0 0 2px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-next,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-next {
    width: auto;
    border-radius: 0 2px 2px 0;
    margin-right: 16px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-skip,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-skip {
    color: #86909C;
    margin-right: 16px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-skip input,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-skip input {
    width: 41px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #E5E6EB;
    margin: 0 8px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-skip .layui-laypage-btn,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-skip .layui-laypage-btn {
    border-radius: 2px;
    width: 60px;
    height: 32px;
    color: #4E5969;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-count,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-count {
    margin-right: 16px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-limits select,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-limits select {
    width: 104px;
    height: 30px;
    color: #4E5969;
    padding: 0;
    text-align: center;
    border-radius: 2px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage .layui-laypage-next ~ * {
    border: none;
    width: auto;
}

#addCourse .dialog-con .courseList .selCourse {
    position: absolute;
    left: 0;
    bottom: 8px;
    color: #86909C;
    font-size: 14px;
    line-height: 40px;
    display: flex;
    align-items: center;
}

#addCourse .dialog-con .courseList .selCourse em {
    color: #4d88ff;
    padding: 0 4px;
}

#addCourse .dialog-con .courseList .selCourse .z-check {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #4E5969;
    margin-right: 16px;
}

#addCourse .dialog-con .courseList .selCourse .z-check .check {
    width: 28px;
    height: 28px;
    cursor: pointer;
    margin-right: 6px;
    background: url(../../images/cultivation/check.png) no-repeat center;
    background-size: 28px;
}

#addCourse .dialog-con .courseList .selCourse .z-check .check.checked {
    background: url(../../images/cultivation/check1.png) no-repeat center;
}

#addCourse .dialog-con .courseList .refresh {
    font-size: 14px;
    color: #4D88FF;
    padding-left: 20px;
    background: url('../../images/cultivation/refresh.png') no-repeat left center;
    background-size: 16px;
    position: absolute;
    right: 0;
    bottom: 8px;
    line-height: 40px;
    cursor: pointer;
}

#addCourse .form-course {
    display: flex;
    flex-wrap: wrap;
    margin: 14px 0 0;
}

#addCourse .form-course .layui-inline {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    margin-right: 24px;
}

#addCourse .form-course .layui-inline:last-child .layui-form-label {
    width: 70px;
    text-align: left;
}

#addCourse .form-course .layui-inline .j-search-con {
    width: 182px;
}

#addCourse .form-course .layui-inline .layui-input-inline {
    margin-right: 0;
}

#addCourse .form-course .layui-inline .layui-input-inline .layui-input {
    width: 240px;
    border-radius: 4px;
}

#addCourse .form-course .layui-inline .layui-input-inline .layui-input::placeholder {
    color: #86909C;
}

#addCourse .form-course .layui-inline .layui-input-inline .layui-form-select {
    width: 100%;
}

#addCourse .form-course .layui-form-label {
    width: auto;
    white-space: nowrap;
    padding-right: 14px;
}

#addCourse .form-course .layui-inline-button {
    display: block;
    position: relative;
    margin-right: 0;
    width: 120px;
}

#addCourse .form-course .layui-inline-button::after {
    content: "";
    width: 1px;
    height: 84px;
    background-color: #E8EBF3;
    position: absolute;
    top: 0;
    left: 0;
}

#addCourse .form-course .layui-inline-button .layui-btn {
    width: 96px;
    height: 34px;
    margin-left: 24px;
}

#addCourse .form-course .layui-inline-button .layui-btn:first-child {
    margin-bottom: 16px;
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
}