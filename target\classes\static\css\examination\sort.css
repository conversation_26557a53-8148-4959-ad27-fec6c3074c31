@charset "UTF-8";
input, textarea {
    margin: 0;
    padding: 0;
    outline: 0;
    -webkit-appearance: none;
    border-radius: 0;
    border:none;
}
html,
body {
    height: 100%;
}
.mr10{
    margin-right:10px;
}

.mr8{
    margin-right:8px;
}


.sel-search-box {
    position: relative;
    width: 100%;
    height: 31px;
    border-bottom: 1px solid #DEDFE0;
    box-sizing: border-box !important;
}

.sel-search-box * {
    box-sizing: border-box !important;
}

.sel-search-box .sel-search-val {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0 22px 0 28px;
    font-size: 14px;
    color: #4D4D4D;
}

.sel-search-box .sel-search-val::-webkit-input-placeholder,
.sel-search-box .sel-search-val::-moz-placeholder,
.sel-search-box .sel-search-val::-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #898989;
}

.sel-search-box .sel-search-val:focus+.icon-search {
    color: #006CE2;
}

.sel-search-box .icon-search {
    position: absolute;
    top: 50%;
    left: 7px;
    margin-top: -10px;
    font-size: 20px;
    color: #898989;
}

.sel-search-box .icon-clean-input {
    position: absolute;
    top: 50%;
    right: 6px;
    margin-top: -8px;
    font-size: 16px;
    cursor: pointer;
}

.select-line-ctrl .select-list-box .select-list .select-option,
.opt_data_top .opt-order-popup .order-popup-body .order-lis,
.opt_data_top .opt-order-popup .order-popup-btm,
.opt_data_top .opt-order-popup .order-popup-btm .clean-btn{
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex; 
}

.opt_data_top {
  
}

.opt_data_top .opt_data_num {
    font-size: 12px;
    color: #A8ACB3;
}

.opt_data_top .opt_data_num em {
    margin: 0 4px;
    color: #4C88FF;
    font-style: normal;
}

.opt_data_top .opt_data_btn {
    margin-left: 20px;
    font-size: 14px;
    color: #6581BA;
    cursor: pointer;
}

.opt_data_top .opt_data_btn i {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("../../images/examination/icon_log.png") no-repeat center;
    background-size: contain;
    vertical-align: top;
    margin-top: 2px;
}

.opt_data_top .opt_data_print {
    position: relative;
}

.opt_data_top .opt_data_print i {
    background: url("../../images/examination/icon_print.png") no-repeat center;
    background-size: contain;
}

.opt_data_top .opt_data_print .print_btn i {
    background: none;
    font-size: 16px;
    color: #6581BA;
}

.opt_data_top .opt_data_print .print_btn:hover {
    opacity: .7;
}

.opt_data_top .opt_data_print .print_temp_pop {
    display: none;
    position: absolute;
    top: 30px;
    right: 0;
    z-index: 8;
    min-width: 100px;
    max-width: 202px;
    background: #FFFFFF;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.opt_data_top .opt_data_print .print_temp_pop::before {
    content: '';
    position: absolute;
    top: -20px;
    width: 100%;
    height: 20px;
}

.opt_data_top .opt_data_print .print_template {
    max-height: 160px;
    padding: 4px;
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-color: #DEDFE0 #ffffff;
    scrollbar-width: thin;
}

.opt_data_top .opt_data_print .print_template::-webkit-scrollbar {
    display: block;
    width: 4px;
    height: 4px;
}

.opt_data_top .opt_data_print .print_template::-webkit-scrollbar-thumb {
    display: block;
    width: 4px;
    height: 4px;
    background: #DEDFE0;
    border-radius: 4px;
}

.opt_data_top .opt_data_print .print_template .temp_item {
    padding: 6px 10px;
    color: #4D4D4D;
    line-height: 20px;
    text-align: left;
    border-radius: 2px;
}

.opt_data_top .opt_data_print .print_template .temp_item:hover {
    color: #557CA7;
    background: rgba(85, 124, 167, 0.1);
}

.opt_data_top .opt_data_print .print_template span {
    max-width: 168px;
}

.opt_data_top .opt_data_print:hover .print_temp_pop {
    display: block;
}

.opt_data_top .opt_data_print .print_list_btn {
    padding: 0 10px;
    color: #4D4D4D;
    line-height: 28px;
    text-align: left;
    border-top: 1px solid #D5D9E2;
    border-radius: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.opt_data_top .opt_data_print .print_list_btn:hover {
    color: #557CA7;
    background: rgba(85, 124, 167, 0.1);
}

.opt_data_top .opt_data_print .print_list_btn i {
    margin-top: 6px;
    margin-right: 4px !important;
}

.opt_data_top .opt_data_btn.s-printing {
    opacity: .3;
    cursor: not-allowed;
}

.opt_data_top .opt_data_btn.s-printing:hover {
    opacity: .3;
}

.opt_data_top .opt_data_order {
    position: relative;
}

.opt_data_top .opt_data_order i {
    background: none;
    font-size: 16px;
}

.opt_data_top .opt-order-popup {
    position: absolute;
    top: 28px;
    right: 0;
    width: 315px;
    height: 280px;
    background: #FFFFFF;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    font-size: 14px;
    line-height: 1;
    cursor: default;
    z-index: 9999;
}

.opt_data_top .opt-order-popup .order-popup-body {
    height: 228px;
    padding: 8px 0 8px 10px;
    box-sizing: border-box !important;
    overflow: auto;
    scrollbar-color: #DEDFE0 #ffffff;
    scrollbar-width: thin;
}

.opt_data_top .opt-order-popup .order-popup-body::-webkit-scrollbar {
    display: block;
    width: 4px;
    height: 4px;
}

.opt_data_top .opt-order-popup .order-popup-body::-webkit-scrollbar-thumb {
    display: block;
    width: 4px;
    height: 4px;
    background: #DEDFE0;
    border-radius: 4px;
}

.opt_data_top .opt-order-popup .order-popup-body .order-ul {
    margin-bottom: 13px;
}

.opt_data_top .opt-order-popup .order-popup-body .order-lis {
    align-items: center;
    margin-bottom: 5px;
}

.opt_data_top .opt-order-popup .order-popup-body .icon-sort-portrait {
    font-size: 16px;
    cursor: move;
}

.opt_data_top .opt-order-popup .order-popup-body .icon-delete {
    margin-left: 5px;
    font-size: 16px;
    color: #557CA7;
    cursor: pointer;
}

.opt_data_top .opt-order-popup .order-popup-body .x-add-icon-txt-btn {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    color: #557CA7;
    line-height: 20px;
    cursor: pointer;
}

.opt_data_top .opt-order-popup .order-popup-body .x-add-icon-txt-btn span {
    color: #557CA7;
}

.opt_data_top .opt-order-popup .order-popup-body .x-add-icon-txt-btn .icon-add {
    font-size: 16px;
    margin-right: 10px;
    color: #557CA7;
}

.opt_data_top .opt-order-popup .order-popup-btm {
    justify-content: flex-end;
    align-items: center;
    height: 52px;
    padding-right: 10px;
    box-shadow: inset 0px 1px 0px 0px #DEDFE0;
}

.opt_data_top .opt-order-popup .order-popup-btm .clean-btn {
    align-items: center;
    color: #557CA7;
    line-height: 20px;
    cursor: pointer;
}

.opt_data_top .opt-order-popup .order-popup-btm .clean-btn .icon-clean {
    margin-right: 4px;
    font-size: 16px;
    color: #557CA7;
}

.opt_data_top .opt-order-popup .order-popup-btm .sure-btn {
    width: 100px;
    height: 32px;
    margin-left: 20px;
    background: #4C88FF;
    box-shadow: 0px 2px 10px 0px rgba(0, 108, 226, 0.3);
    border-radius: 6px;
    cursor: pointer;
    text-align: center;
    line-height: 32px;
    color: #fff;
}


.select-line-ctrl {
    width: 100px;
    height: 32px;
    position: relative;
    font-size: 14px;
}

.select-line-ctrl * {
    box-sizing: border-box !important;
}

.select-line-ctrl.widthFull {
    width: 100%;
}

.select-line-ctrl.width120 {
    width: 120px;
}

.select-line-ctrl .select-line-box {
    position: relative;
    width: 100%;
    height: 100%;
    background: #FFF;
}

.select-line-ctrl .select-line-box .icon-select-down {
    font-size: 20px;
    position: absolute;
    top: 6px;
    right: 6px;
}

.select-line-ctrl .select-line-box input[type='text'] {
    display: inline-block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: none;
    padding: 0 25px 0 9px;
    border-radius: 4px;
    border: 1px solid #DEDFE0;
    font-size: 14px;
    color: #4D4D4D;
    cursor: pointer;
}

.select-line-ctrl .select-line-box input[type='text']:focus {
    color: #4D4D4D;
    box-shadow: 0px 0px 6px 0px rgba(0, 108, 226, 0.5);
    border: 1px solid #006CE2;
}

.select-line-ctrl .select-list-box {
    position: absolute;
    top: 34px;
    left: 0;
    width: 100%;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #DEDFE0;
    overflow: hidden;
    z-index: 2;
    display:none;
}

.select-line-ctrl .select-list-box.up {
    top: auto;
    bottom: 34px;
}

.select-line-ctrl .select-list-box .select-list {
    width: 100%;
    max-height: 150px;
    overflow: auto;
    scrollbar-color: #C0C0C3 #ffffff;
    scrollbar-width: thin;
}

.select-line-ctrl .select-list-box .select-list::-webkit-scrollbar {
    display: block;
    width: 4px;
    height: 4px;
}

.select-line-ctrl .select-list-box .select-list::-webkit-scrollbar-thumb {
    display: block;
    width: 4px;
    height: 4px;
    background: #C0C0C3;
    border-radius: 4px;
}

.select-line-ctrl .select-list-box .select-list .select-option {
    width: 100%;
    min-height: 30px;
    padding: 0 8px;
    align-items: center;
    cursor: pointer;
    color: #4D4D4D;
}

.select-line-ctrl .select-list-box .select-list .select-option .icon-radio,
.select-line-ctrl .select-list-box .select-list .select-option .icon-check {
    margin-right: 10px;
    top: 0;
}

.select-line-ctrl .select-list-box .select-list .select-option.default-option {
    color: #898989;
}

.select-line-ctrl .select-list-box .select-list .select-option:hover {
    background: #F9F9FA;
}

.select-line-ctrl .select-list-box .select-list .select-option.s-selected {
    color: #006CE2;
    background: #E0EFFF;
}

.select-line-ctrl .select-list-box .select-list .select-option.s-checked {
    color: #006CE2;
}

.select-line-ctrl.s-selected .select-line-box .icon-select-down {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
}

.select-line-ctrl.s-selected .select-line-box input[type='text'] {
    border: 1px solid #006CE2;
    box-shadow: 0px 0px 6px 0px rgba(0, 108, 226, 0.5);
}

.select-line-ctrl.s-selected .select-list-box {
    display: block;
}

.select-line-ctrl.disable-style .select-line-box {
    background: #F1F1F2;
}

.select-line-ctrl.disable-style .select-line-box .icon-select-down {
    display: none;
}

.select-line-ctrl.disable-style .select-line-box input[type='text'] {
    padding-right: 9px;
    cursor: not-allowed;
}

.select-line-ctrl.disable-style .select-line-box input[type='text']:focus {
    color: #898989;
    box-shadow: none;
    border: 1px solid #DEDFE0;
}