/*
录音
https://github.com/xiangyuecn/Recorder
src: extensions/wavesurfer.view.js
*/
!function(){"use strict";var e=function(e){return new t(e)},f="WaveSurferView",t=function(e){var t=this,a={scale:2,fps:50,duration:2500,direction:1,position:0,centerHeight:1,linear:[0,"rgba(0,187,17,1)",.7,"rgba(255,215,0,1)",1,"rgba(255,102,0,1)"],centerColor:""};for(var r in e)a[r]=e[r];t.set=e=a;var i=e.elem;i&&("string"==typeof i?i=document.querySelector(i):i.length&&(i=i[0])),i&&(e.width=i.offsetWidth,e.height=i.offsetHeight);var n=e.scale,o=e.width*n,l=e.height*n;if(!o||!l)throw new Error(f+"无宽高");var c=t.elem=document.createElement("div"),h=["","transform-origin:0 0;","transform:scale("+1/n+");"];c.innerHTML='<div style="width:'+e.width+"px;height:"+e.height+'px;overflow:hidden"><div style="width:'+o+"px;height:"+l+"px;"+h.join("-webkit-")+h.join("-ms-")+h.join("-moz-")+h.join("")+'"><canvas/></div></div>';var s=t.canvas=c.querySelector("canvas");t.ctx=s.getContext("2d");s.width=o,s.height=l;var d=t.canvas2=document.createElement("canvas");t.ctx2=d.getContext("2d");d.width=2*o,d.height=l,i&&(i.innerHTML="",i.appendChild(c)),t.x=0};t.prototype=e.prototype={genLinear:function(e,t,a,r){for(var i=e.createLinearGradient(0,a,0,r),n=0;n<t.length;)i.addColorStop(t[n++],t[n++]);return i},input:function(e,t,a){var r=this;r.sampleRate=a,r.pcmData=e,r.pcmPos=0,r.inputTime=Date.now(),r.schedule()},schedule:function(){var e=this,t=e.set,a=Math.floor(1e3/t.fps);e.timer||(e.timer=setInterval(function(){e.schedule()},a));var r=Date.now();if(!(r-(e.drawTime||0)<a)){e.drawTime=r;for(var i=e.sampleRate/t.fps,n=e.pcmData,o=e.pcmPos,l=new Int16Array(Math.min(i,n.length-o)),c=0;c<l.length;c++,o++)l[c]=n[o];e.pcmPos=o,l.length?e.draw(l,e.sampleRate):1300<r-e.inputTime&&(clearInterval(e.timer),e.timer=0)}},draw:function(e,t){var a=this,r=a.set,i=a.ctx2,n=r.scale,o=r.width*n,l=2*o,c=r.height*n,h=1*n,s=r.position,d=Math.abs(r.position),f=1==s?0:c,v=c;d<1&&(f=v/=2,v=Math.floor(v*(1+d)),f=Math.floor(0<s?f*(1-d):f*(1+d)));var m=1e3*e.length/t*o/r.duration,g=0;(m+=a.drawLoss||0)<h?a.drawLoss=m:(a.drawLoss=0,g=Math.floor(m/h));for(var p=a.genLinear(i,r.linear,f,f-v),w=a.genLinear(i,r.linear,f,f+v),u=a.x,M=e.length/g,x=0,y=0;x<g;x++){var R=Math.floor(y),L=Math.floor(y+M);y+=M;for(var S=0;R<L;R++)S=Math.max(S,Math.abs(e[R]));var b=v*Math.min(1,S/32767);0!=f&&(i.fillStyle=p,i.fillRect(u,f-b,h,b)),f!=c&&(i.fillStyle=w,i.fillRect(u,f,h,b)),l<=(u+=h)&&(i.clearRect(0,0,o,c),i.drawImage(a.canvas2,o,0,o,c,0,0,o,c),i.clearRect(o,0,o,c),u=o)}a.x=u,(i=a.ctx).clearRect(0,0,o,c);var C=r.centerHeight*n;if(C){var I=f-Math.floor(C/2);I=Math.max(I,0),I=Math.min(I,c-C),i.fillStyle=r.centerColor||r.linear[1],i.fillRect(0,I,o,C)}var T=0,H=u,j=0;o<H?(T=H-o,H=o):j=o-H,-1==r.direction?i.drawImage(a.canvas2,T,0,H,c,j,0,H,c):(i.save(),i.scale(-1,1),i.drawImage(a.canvas2,T,0,H,c,-o+j,0,H,c),i.restore())}},Recorder[f]=e}();