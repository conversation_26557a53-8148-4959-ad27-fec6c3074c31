* {
  margin: 0;
  padding: 0;
}
.dialog-wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
#syncSetDialog {
  position: fixed;
  width: 320px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #FFF;
  z-index: 9;
  font-family: "PingFang SC";
}
#syncSetDialog .dialog-title {
  position: relative;
  border-bottom: 1px solid #DEDFE0;
  padding: 0 30px;
  overflow: hidden;
}
#syncSetDialog .dialog-title h4 {
  color: #557CA7;
  font-size: 16px;
  line-height: 22px;
  margin: 11px 0;
}
#syncSetDialog .dialog-title p {
  color: #A3B7CC;
  font-size: 14px;
  margin: 10px 0;
}
#syncSetDialog .dialog-title .dialog-close {
  position: absolute;
  right: 30px;
  top: 11px;
  width: 18px;
  height: 18px;
  background: url('../../images/basic/close.png') no-repeat center;
  background-size: 18px;
  cursor: pointer;
}
#syncSetDialog .dialog-con {
  overflow: hidden;
}
#syncSetDialog .dialog-con ul {
  overflow: hidden;
}
#syncSetDialog .dialog-con ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  line-height: 40px;
}
#syncSetDialog .dialog-con ul li h5 {
  color: #4D4D4D;
  font-size: 14px;
}
#syncSetDialog .dialog-con ul li span {
  width: 16px;
  height: 16px;
  background: url('../../images/basic/check.png') no-repeat center;
  background-size: 16px;
  cursor: pointer;
}
#syncSetDialog .dialog-con ul li span.active {
  background: url('../../images/basic/checked.png') no-repeat center;
  background-size: 16px;
}
#syncSetDialog .dialog-con ul li.active {
  background: #F7FBFF;
}
#syncSetDialog .dialog-con ul li:hover {
  background: #F7FBFF;
}
#syncSetDialog .dialog-btn {
  height: 50px;
  border-top: 1px solid #DEDFE0;
  display: flex;
  align-items: center;
  justify-content: center;
}
#syncSetDialog .dialog-btn button {
  width: 100px;
  height: 32px;
  border-radius: 6px;
  background: #006CE2;
  outline: none;
  border: none;
  color: #fff;
  line-height: 30px;
  cursor: pointer;
}
