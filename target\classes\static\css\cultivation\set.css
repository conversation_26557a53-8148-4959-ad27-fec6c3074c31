body {

  background-color: #F7F8FA;

}

.main {

  margin: 20px;

  background-color: #fff;

  border-radius: 8px;

  min-height: calc(100vh - 40px);

  min-width: 1000px;

}

.main .m-top {

  width: 100%;

  height: 57px;

  border-bottom: 1px solid #E8EAF1;

  display: flex;

  display: -webkit-flex;

  align-items: center;

  justify-content: flex-start;

  padding-left: 30px;

  box-sizing: border-box;

}

.main .m-top .back {

  width: 60px;

  height: 24px;

  background: url(../../images/cultivation/back-icon.png) no-repeat left center;

  cursor: pointer;

  margin-right: 10px;

}

.main .m-top .title {

  font-size: 16px;

  color: #A5AFC4;

}

.main .m-top span {

  width: 12px;

  height: 12px;

  background: url(../../images/cultivation/icon-right.png) no-repeat center;

  background-size: 12px;

  margin: 0 4px;

}

.main .m-top h3 {

  font-weight: 600;

  font-size: 16px;

  color: #6581BA;

}

.main .item {

  padding: 0 30px;

}

.main .item-set .i-top {

  width: 100%;

  height: 22px;

  margin-bottom: 20px;

  margin-top: 30px;

}

.main .item-set .i-top span {

  padding-left: 8px;

  font-weight: 600;

  font-size: 16px;

  line-height: 22px;

  color: #484F5D;

  position: relative;

  display: block;

}

.main .item-set .i-top span:after {

  content: '';

  position: absolute;

  left: 0;

  top: 4px;

  background: #4D88FF;

  border-radius: 1px;

  width: 3px;

  height: 14px;

}

.main .item-set .set-con {

  background: #F9F9FB;

  border-radius: 8px;

  padding: 16px 6px 10px 6px;

  margin-bottom: 30px;

}

.main .item-set .set-con .layui-inline {

  margin-right: 120px;

  margin-bottom: 0;

}

.main .item-set .set-con .layui-form-label {

  width: 86px;

  text-align: right;

  font-size: 14px;

  line-height: 34px;

  color: #717B91;

  padding: 0;

  margin-right: 12px;

}

.main .item-set .set-con .layui-input,

.main .item-set .set-con .layui-textarea,

.main .item-set .set-con .layui-select {

  height: 34px;

  font-size: 14px;

}

.main .item-set .set-con .layui-form-select .layui-edge {

  border-top-color: #99B2E6 !important;

  border-radius: 4px;

}

.main .item-set .set-con .layui-input {

  border-color: #D5D9E2;

  border-radius: 4px;

}

.main .item-set .set-con .layui-input::placeholder {

  color: #8F97A8 !important;

}

.main .item-set .set-con p.txt {

  font-size: 14px;

  line-height: 34px;

  color: #484F5D;

  width: 240px;

}

.main .item-set .set-con .layui-form-select {

  width: 240px;

}

.main .item-set .btn button {

  width: 88px;

  height: 34px;

  left: 0px;

  top: 0px;

  background: linear-gradient(0deg, #4c88ff, #4c88ff), #d9d9d9;

  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);

  border-radius: 4px;

  padding: 0;

  margin: 0;

  line-height: 34px;

}

.main .item-set .layui-form-select dl dd.layui-this {

  background-color: #4C88FF;

}

