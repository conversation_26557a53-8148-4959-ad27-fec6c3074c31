.common_popup {
    z-index: 15;
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 10px;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.common_popup .popup_top {
    position: relative;
    line-height: 56px;
    padding: 0 30px;
    font-size: 18px;
    color: #242933;
    border-bottom: 1px solid #F2F2F2;
}

.common_popup .popup_top .popup_close {
    position: absolute;
    width: 18px;
    height: 18px;
    top: 19px;
    right: 30px;
    background: url("../../images/basic/icon_close.png") no-repeat center;
    background-size: contain;
    cursor: pointer;
}

.common_popup .popup_top .popup_close:hover {
    opacity: 0.7;
}

.common_popup .popup_top .popup_title_tip {
    margin-left: 6px;
    font-size: 14px;
    color: #898989;
}

.common_popup .popup_cont {
    height: calc(100% - 117px);
    padding: 0 30px;
}

.common_popup .popup_btm {
    position: relative;
    width: 100%;
    height: 60px;
    padding: 0 30px;
    box-sizing: border-box !important;
    border-top: 1px solid #E8EBF1;
}

.common_popup .popup_btm .btm_btn_box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.common_popup .popup_btm .btm_btn_box.justify_content_end {
    justify-content: flex-end;
}

.common_popup .popup_btm .btm_btn_box .btn_style {
    display: inline-block;
    min-width: 60px;
    height: 32px;
    line-height: 30px;
    padding: 0 16px;
    margin: 0 10px;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
}

.common_popup .popup_btm .btm_btn_box .btn_style:hover {
    opacity: .8;
}

.common_popup .popup_btm .btm_btn_box .btn_line_gray {
    border: 1px solid #A8ACB3;
    color: #A8ACB3;
}

.common_popup .popup_btm .btm_btn_box .btn_solid_light {
    border: 1px solid #4C88FF;
    background: #4C88FF;
    color: #fff;
    box-shadow: 0px 2px 8px 0px rgba(39, 111, 255, 0.3);
}

.export_record_popup {
    width: 860px;
    height: 600px;
    z-index: 100;
}

.export_record_popup .popup_cont {
    box-sizing: border-box !important;
    padding-top: 16px;
}

.export_record_popup .popup_cont .table_wrap {
    position: relative;
    height: 468px;
    border-top: 1px solid #E8EBF1;
    overflow: auto;
}

.export_record_popup .popup_cont .nodata {
    position: absolute;
    top: 36px;
    left: 0;
    width: 100%;
    box-sizing: border-box !important;
    border-left: 1px solid #E8EBF1;
    border-right: 1px solid #E8EBF1;
    font-size: 14px;
    line-height: 431px;
    color: #8F97A8;
    text-align: center;
}

.export_record_popup .popup_cont table {
    width: 799px;
    border-collapse: collapse;
    table-layout: fixed;
    margin: 0;
    padding: 0;
    word-break: break-all;
    word-wrap: break-word;
}

.export_record_popup .popup_cont table thead {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    left: 0;
    z-index: 1;
}

.export_record_popup .popup_cont table th {
    height: 36px;
    box-sizing: border-box !important;
    padding: 0 16px;
    background: #F1F3F6;
    border: 1px solid #E8EBF1;
    border-top: none;
    font-size: 12px;
    font-weight: normal;
    color: #6581BA;
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.export_record_popup .popup_cont table tbody tr:nth-child(2n) {
    background: #FAFBFC;
}

.export_record_popup .popup_cont table td {
    height: 32px;
    box-sizing: border-box !important;
    padding: 0 16px;
    border: 1px solid #E8EBF1;
    font-size: 13px;
    color: #484F5D;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.export_record_popup .popup_cont table span.link {
    color: #5076D6;
    cursor: pointer;
}

.export_record_popup .popup_cont table span.orange {
    color: #FFBD56;
}

.export_record_popup .popup_cont table span.yellow {
    color: #FFBD56;
}

.export_record_popup .popup_cont table span.gray {
    color: #989A9F;
}

.export_record_popup .popup_cont table span.blue {
    color: #5076D6;
}

.export_record_popup .popup_cont table span.red {
    color: #FF6E60;
    margin-left: 10px;
    cursor: pointer;
}

.export_record_popup .popup_cont table span.green {
    color: #68D8BF;
}

.export_record_popup .popup_cont table span.violet {
    color: #986DF9;
}

.export_record_popup .popup_cont table span.lightGray {
    color: #BABBC0;
}

.export_record_popup .popup_btm .totals {
    font-size: 14px;
    color: #8F97A8;
    line-height: 60px;
}

.export_record_popup .popup_btm .pages_box {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
    line-height: 32px;
    text-align: center;
}

.export_record_popup .popup_btm .refresh_btn {
    position: absolute;
    top: 20px;
    right: 30px;
    z-index: 1;
    height: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 14px;
    color: #6581BA;
    line-height: 1;
    cursor: pointer;
}

.export_record_popup .popup_btm .refresh_btn span {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 6px;
    background: url("../../images/basic/icon_refresh.png") no-repeat center/contain;
}

.common_popup .popup_top .popup_close {
    position: absolute;
    width: 18px;
    height: 18px;
    top: 19px;
    right: 30px;
    background: url(../../images/basic/icon_close.png) no-repeat center;
    background-size: contain;
    cursor: pointer;
}