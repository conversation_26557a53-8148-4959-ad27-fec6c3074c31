.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.hide {
  display: none !important;
}
input::-webkit-input-placeholder {
  color: #86909C;
  font-size: 14px;
  line-height: 32px;
}
body {
  background-color: #F7F8FA;
}
.addschem-diag .window .p-content .tab-con .moudle .lable.lab-morning-time {
  display: none;
}
.addschem-diag .window .p-content .tab-con .moudle .lable.lab-night-time {
  display: none;
}
.layui-form-item {
  margin-bottom: 0;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.layui-form-item .layui-form-label {
  font-size: 14px;
  color: #1D2129;
  float: left;
  width: auto;
  float: none;
}
.layui-form-item .layui-input-block {
  margin-left: 14px;
  width: 240px;
}
.layui-form-item .layui-input-block .layui-input {
  font-size: 14px;
  color: #4E5969;
  line-height: 34px;
}
.main {
  background-color: #fff;
  border-radius: 8px;
  min-height: calc(100vh);
  min-width: 1300px;
}
.main .m-head {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 60px;
  border-bottom: 1px solid #E8EBF1;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0 30px;
}
.main .m-head h4 {
  position: relative;
  font-size: 16px;
  color: #6581BA;
  padding-left: 9px;
  font-weight: 400;
}
.main .m-head h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4C85FA;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}
.main .m-con .mc-top {
  padding: 20px 30px 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
}
.main .m-con .mc-top .btn {
  background: #C9CDD4;
  width: 124px;
  height: 36px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  text-align: center;
  line-height: 36px;
  font-size: 14px;
  color: #FFFFFF;
}
.dis-btn {
  pointer-events: none;
  cursor: default;
}
.main .m-con .mc-top .btn.active {
  background: #4D88FF;
  box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
  border-radius: 4px;
  cursor: pointer;
}
.main .m-con .tips {
  padding-left: 22px;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  margin-left: 30px;
  color: #8F97A8;
  background: url(../../images/timetable/ntips-icon.png) no-repeat left center;
}
.main .m-con .tips i {
  color: #6aa1ff;
}
.main .m-con .no-datas {
  display: none;
  position: absolute;
  left: 50%;
  top: 50%;
  width: 88px;
  height: 88px;
  margin-top: -44px;
  background: url(../../images/timetable/no-data.png) no-repeat center;
}
.main .m-con .p-tables {
  margin: 20px 30px;
  height: auto;
  border: 1px solid #E8EBF3;
  border-bottom: none;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  position: relative;
  border-right: none;
  border-left: none;
}
.main .m-con .p-tables .timing {
  width: 145px;
  border: none;
  margin-right: 0;
  height: 36px;
  padding-left: 0;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  background: #E1EBFF;
  border-radius: 0;
}
.main .m-con .p-tables .timing .select-dropdown {
  top: 36px;
  padding: 0;
  border-radius: 0;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}
.main .m-con .p-tables .timing .select-dropdown ul li {
  text-align: left;
  padding: 0 20px;
}
.main .m-con .p-tables .timing .select-dropdown ul li:hover {
  background-color: transparent;
}
.main .m-con .p-tables .timing .select-dropdown ul li.cur {
  color: #4D88FF;
  background: #E1EBFF;
  font-size: 14px;
}
.main .m-con .p-tables .timing .name {
  padding-left: 0;
  width: 100%;
  font-size: 14px;
  color: #4D88FF;
}
.main .m-con .p-tables .timing em {
  top: 14px;
  right: 35px;
  background: url(../../images/timetable/icon-arrow2.png) no-repeat center;
  margin-left: 6px;
  background-size: 10px;
}
.main .m-con .p-tables.no-data .tbody {
  border-bottom: 1px solid #E8EBF3;
}
.main .m-con .p-tables.no-data .tbody .row {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  background-color: #fff;
}
.main .m-con .p-tables.no-data .tbody .row:nth-last-child(2) .lab:after {
  display: none;
}
.main .m-con .p-tables.no-data .tbody .row .lab {
  position: relative;
}
.main .m-con .p-tables.no-data .tbody .row .lab:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  width: 100%;
  background-color: #E8EBF3;
}
.main .m-con .p-tables.no-data .tbody .row.linear {
  display: none !important;
}
.main .m-con .p-tables .tbody .row:nth-child(4n+2) {
  background: #fff;
}
.main .m-con .p-tables .tbody .row:nth-child(4n+2) .lab {
  background-color: #fff;
}
.main .m-con .p-tables .tbody .row:nth-child(4n+2) .lab .time-sel {
  border: 1px solid #fff !important;
}
.main .m-con .p-tables .tbody .row:nth-child(4n+2) .lab .time-sel:hover {
  border: 1px solid #D4D6D9 !important;
}
.main .m-con .p-tables .row {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  height: 58px;
  background: #FAFBFC;
}
.main .m-con .p-tables .row .lab {
  background-color: #FAFBFC;
}
.main .m-con .p-tables .row .lab .time-sel {
  border: 1px solid #FAFBFC !important;
}
.main .m-con .p-tables .row .lab .time-sel:hover {
  border: 1px solid #D4D6D9 !important;
}
.main .m-con .p-tables .row.row-colw {
  background: #fff;
}
.main .m-con .p-tables .row.row-colw .lab {
  background-color: #fff;
}
.main .m-con .p-tables .row.row-colw .lab .time-sel {
  border: 1px solid #fff !important;
}
.main .m-con .p-tables .row.row-colw .lab .time-sel:hover {
  border: 1px solid #D4D6D9 !important;
}
.main .m-con .p-tables .row.row-colb {
  background: transparent;
}
.main .m-con .p-tables .row.row-colb .lab {
  background: #FAFBFC;
}
.main .m-con .p-tables .row.row-colb .lab .time-sel {
  border: 1px solid #FAFBFC;
}
.main .m-con .p-tables .row.row-colb .lab .time-sel:hover {
  border: 1px solid #fff !important;
}
.main .m-con .p-tables .row:first-child {
  height: 36px;
  line-height: 36px;
  background: #F1F3F6;
}
.main .m-con .p-tables .row:first-child .lab {
  background: #F1F3F6;
  color: #86909C;
  height: 36px;
  line-height: 36px;
  font-size: 14px;
}
.main .m-con .p-tables .row.break {
  background: #F1F3F6;
  position: relative;
}
.main .m-con .p-tables .row.break:after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 48px;
  height: 48px;
  background: url(../../images/timetable/rest-icons.png) no-repeat center;
  z-index: 99;
}
.main .m-con .p-tables .row.break .delet {
  position: absolute;
  right: -26px;
  top: 0;
  width: 26px;
  height: 58px;
  background: url(../../images/timetable/huishou.png) no-repeat right center;
  cursor: pointer;
  display: none;
}
.main .m-con .p-tables .row.break:hover .delet {
  display: block;
}
.main .m-con .p-tables .row.break .noborder {
  border-right: none;
}
.main .m-con .p-tables .row .lab {
  text-align: center;
  height: 58px;
  font-size: 14px;
  color: #4E5969;
}
.main .m-con .p-tables .row .lab .time-sels {
  width: 160px;
  height: 34px;
  border-radius: 4px;
  margin: 12px auto 0;
  font-size: 14px;
  color: #4E5969;
  padding: 0 24px;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-con .p-tables .row .lab .time-sels .t-start {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  margin-top: 7px;
}
.main .m-con .p-tables .row .lab .time-sels .sign {
  display: inline-block;
  line-height: 34px;
  margin: 0 9px;
}
.main .m-con .p-tables .row .lab .time-sels .t-end {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  margin-top: 7px;
}
.main .m-con .p-tables .row .lab .time-sel {
  width: 160px;
  height: 34px;
  border: 1px solid #fff;
  border-radius: 4px;
  margin: 12px auto 0;
  font-size: 14px;
  color: #4E5969;
  padding: 0 21px;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-con .p-tables .row .lab .time-sel:hover {
  border: 1px solid #D4D6D9;
}
.main .m-con .p-tables .row .lab .time-sel .t-start {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  margin-top: 7px;
  cursor: pointer;
}
.main .m-con .p-tables .row .lab .time-sel .t-start:hover {
  background: rgba(97, 110, 230, 0.2);
}
.main .m-con .p-tables .row .lab .time-sel .sign {
  display: inline-block;
  line-height: 32px;
  margin: 0 9px;
}
.main .m-con .p-tables .row .lab .time-sel .t-end {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  margin-top: 7px;
  cursor: pointer;
}
.main .m-con .p-tables .row .lab .time-sel .t-end:hover {
  background: rgba(97, 110, 230, 0.2);
}
.main .m-con .p-tables .row .lab .time-sel.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
}
.main .m-con .p-tables .row .lab .time-sel .select-dropdown {
  width: inherit;
  max-height: 200px;
  overflow: auto;
  margin: 5px 0;
  padding: 5px 0;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  position: absolute;
  left: 0;
  top: 32px;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
.main .m-con .p-tables .row .lab .time-sel .select-dropdown .dropdown-list {
  padding: 6px 0;
}
.main .m-con .p-tables .row .lab .time-sel .select-dropdown .dropdown-list li {
  margin: 0;
  line-height: normal;
  line-height: 40px;
  padding: 0 20px;
  clear: both;
  color: #131B26;
  font-size: 14px !important;
  white-space: nowrap;
  list-style: none;
  cursor: pointer;
  text-align: left;
}
.main .m-con .p-tables .row .lab .time-sel .select-dropdown .dropdown-list li:hover {
  background: #E1EBFF;
}
.main .m-con .p-tables .row .lab .time-sel .select-dropdown .dropdown-list li.cur {
  color: #616EE6;
}
.main .m-con .p-tables .row .lab:last-child {
  border-right: none;
}
.main .m-con .p-tables .row .lab:first-child {
  width: 145px;
  display: flex;
  display: -webkit-flex;
  justify-content: center;
  align-items: center;
  line-height: 20px;
  position: relative;
}
.main .m-con .p-tables .row .lab:first-child span {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.main .m-con .p-tables .row .lab:first-child .ckable {
  position: absolute;
  left: 0;
  top: 0;
  background-color: transparent;
  width: 100%;
  height: 100%;
  z-index: 9;
}
.main .m-con .p-tables .row .lab:first-child .ckable .up {
  width: 100%;
  height: 50%;
  cursor: pointer;
}
.main .m-con .p-tables .row .lab:first-child .ckable .down {
  width: 100%;
  height: 50%;
  cursor: pointer;
}
.main .m-con .p-tables .row .lab.fb {
  flex: 1;
}
.main .m-con .p-tables .row.linear {
  width: 100%;
  height: 1px;
  background-color: #E8EBF3;
  position: relative;
  display: block !important;
}
.main .m-con .p-tables .row.linear .icons {
  display: none;
  position: absolute;
  left: 0;
  top: -13px;
  width: 33px;
  height: 26px;
  cursor: pointer;
  background: url(../../images/timetable/add-kj.png) no-repeat left center;
  z-index: 99;
}
.main .m-con .p-tables .row.linear .icons em {
  display: none;
  position: absolute;
  left: -30px;
  top: -51px;
  width: 80px;
  height: 47px;
  background: url(../../images/timetable/tip-icon.png) no-repeat center;
}
.main .m-con .p-tables .row.linear .icons:hover em {
  display: block;
}
.main .m-con .p-tables .row.linear.hov {
  background: #4D88FF;
}
.main .m-con .p-tables .row.linear.hov .icons {
  display: block;
}
.main .m-con .p-tables .row.linear.hov:after {
  content: "";
  position: absolute;
  left: 0;
  top: -1px;
  width: 100%;
  height: 1px;
  background: #4D88FF;
}
.main .m-con .p-tables .row.linear:hover {
  background: #4D88FF;
}
.main .m-con .p-tables .row.linear:hover .icons {
  display: block;
}
.main .m-con .p-tables .row.linear:hover:after {
  content: "";
  position: absolute;
  left: 0;
  top: -1px;
  width: 100%;
  height: 1px;
  background: #4D88FF;
}
.main .m-con .p-tables .row.rev-row .lab .time-sel.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, -244px) !important;
  -webkit-transform: translate(0, -244px) !important;
}
.main .m-con .p-tables .row.rev-row .lab .time-sel .select-dropdown {
  transform: translate(0, -274px) !important;
  -webkit-transform: translate(0, -274px) !important;
}
.addBreak-diag .window {
  width: 512px;
  height: 282px;
}
.addBreak-diag .window .p-content {
  padding: 24px 30px;
  padding-left: 100px;
}
.addBreak-diag .window .p-content .lable {
  margin-bottom: 30px;
}
.addBreak-diag .window .p-content .lable .name {
  float: left;
  width: 66px;
  height: 34px;
  line-height: 34px;
  text-align: left;
  font-size: 14px;
}
.addBreak-diag .window .p-content .lable .sel {
  float: left;
  margin-bottom: 0;
}
.addBreak-diag .window .p-content .lable .sel em {
  font-weight: 400;
  font-size: 14px;
  line-height: 34px;
  color: #1D2129;
  white-space: nowrap;
}
.addBreak-diag .window .p-content .lable .sel .select-input {
  margin-left: 0;
  position: relative;
  width: 80px;
  float: left;
  margin-right: 14px;
}
.addBreak-diag .window .p-content .lable .sel .select-input .select-dropdown {
  position: absolute;
  left: -1px;
  top: 33px;
}
.addBreak-diag .window .p-content .lable .input {
  float: left;
  background: #FFFFFF;
  border: 1px solid #E1E1E5;
  box-sizing: border-box;
  border-radius: 4px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  width: 240px;
  height: 34px;
}
.addBreak-diag .window .p-content .lable .input input {
  border: none;
  background: transparent;
  outline: none;
  width: 100%;
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-left: 10px;
}
.addschem-diag .window {
  width: 942px;
  height: auto;
}
.addschem-diag .window .p-content {
  padding: 40px 80px;
  padding-bottom: 70px;
}
.addschem-diag .window .p-content .p-top .lab {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 20px;
  margin-bottom: 24px;
}
.addschem-diag .window .p-content .p-top .lab .name {
  font-size: 14px;
  color: #1D2129;
  margin-right: 14px;
}
.addschem-diag .window .p-content .p-top .lab.time-lab {
  height: 34px;
}
.addschem-diag .window .p-content .p-top .lab.time-lab .times {
  margin-right: 16px;
  width: 240px;
  height: 32px;
  border-radius: 4px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
  background: url(../../images/timetable/time-select.png) no-repeat 212px center;
  background-size: 16px;
}
.addschem-diag .window .p-content .p-top .lab.time-lab .times input {
  cursor: pointer;
  outline: none;
  border: 1px solid #C9CDD4;
  background: transparent;
  height: 32px;
  width: 100%;
  line-height: 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  text-align: left;
  font-size: 14px;
  color: #1D2129;
  padding-left: 12px;
}
.addschem-diag .window .p-content .p-top .lab.time-lab .times input:hover {
  background-color: transparent !important;
}
.addschem-diag .window .p-content .p-top .lab .switc-con {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  cursor: pointer;
}
.addschem-diag .window .p-content .p-top .lab .switc-con .switch {
  position: relative;
  width: 28px;
  height: 14px;
  background: #cdcece;
  border-radius: 4px;
}
.addschem-diag .window .p-content .p-top .lab .switc-con .switch span {
  width: 12px;
  height: 10px;
  position: absolute;
  top: 2px;
  left: 2px;
  background-color: #FFFFFF;
  border-radius: 2px;
}
.addschem-diag .window .p-content .p-top .lab .switc-con .switch.switch-open {
  background: #4D88FF;
}
.addschem-diag .window .p-content .p-top .lab .switc-con .switch.switch-open span {
  left: unset;
  right: 2px;
}
.addschem-diag .window .p-content .p-top .lab .switc-con .switch-con {
  color: #C9CDD4;
  margin-left: 10px;
  font-size: 14px;
}
.addschem-diag .window .p-content .p-top .lab .switc-con .tips {
  position: relative;
  margin-left: 14px;
}
.addschem-diag .window .p-content .p-top .lab .switc-con .tips:hover span {
  display: block;
}
.addschem-diag .window .p-content .p-top .lab .switc-con .tips em {
  display: block;
  width: 16px;
  height: 16px;
  background: url(../../images/timetable/ntips-icon.png) no-repeat center;
}
.addschem-diag .window .p-content .p-top .lab .switc-con .tips span {
  display: none;
  position: absolute;
  left: -57px;
  top: 15px;
  background: url(../../images/timetable/tips-bg.png) no-repeat left center;
  width: 658px;
  height: 68px;
  font-size: 14px;
  color: #4E5969;
  line-height: 74px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-left: 35px;
}
.addschem-diag .window .p-content .p-top .lab .switc-con .tips span:after {
  content: '';
  position: absolute;
  left: 25px;
  top: 34px;
  width: 4px;
  height: 4px;
  background: #4D88FF;
}
.addschem-diag .window .p-content .nav {
  padding-top: 24px;
  width: 100%;
  height: 64px;
  border-bottom: 1px solid #E8EBF1;
  margin-bottom: 32px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.addschem-diag .window .p-content .nav ul {
  overflow: hidden;
}
.addschem-diag .window .p-content .nav ul li {
  float: left;
  line-height: 20px;
  cursor: pointer;
  width: 92px;
  margin-right: 3px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-left: 6px;
  position: relative;
  font-size: 14px;
  color: #86909C;
  height: 40px;
}
.addschem-diag .window .p-content .nav ul li.cur {
  font-size: 16px;
  color: #1D2129;
}
.addschem-diag .window .p-content .nav ul li.cur:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 60px;
  height: 3px;
  background: #4D88FF;
  border-radius: 3px 3px 0 0;
}
.addschem-diag .window .p-content .tab-con .moudle {
  display: none;
}
.addschem-diag .window .p-content .tab-con .moudle.cur {
  display: block;
}
.addschem-diag .window .p-content .tab-con .moudle h3 {
  width: 100%;
  height: 22px;
  font-size: 16px;
  line-height: 22px;
  color: #6581BA;
  text-align: left;
  margin-bottom: 24px;
}
.addschem-diag .window .p-content .tab-con .moudle .lable {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.addschem-diag .window .p-content .tab-con .moudle .lable.lab-time .sel:nth-child(4n) {
  margin-right: 50px;
}
.addschem-diag .window .p-content .tab-con .moudle .lable.lab-time .sel:nth-child(3n) {
  margin-right: 0;
}
.addschem-diag .window .p-content .tab-con .moudle .lable .sel {
  flex-shrink: 0;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 32px;
  margin-bottom: 24px;
}
.addschem-diag .window .p-content .tab-con .moudle .lable .sel.times .select-input {
  width: 235px;
  margin-right: 0;
}
.addschem-diag .window .p-content .tab-con .moudle .lable .sel:nth-child(4n) {
  margin-right: 0;
}
.addschem-diag .window .p-content .tab-con .moudle .lable .sel em {
  font-weight: 400;
  font-size: 14px;
  line-height: 34px;
  color: #1D2129;
}
.addschem-diag .window .p-content .tab-con .moudle .lable .sel .select-input {
  width: 80px;
  margin-left: 14px;
  margin-right: 14px;
}
.addschem-diag .window .p-content .tab-con .moudle .lable .sel .select-input.w240 {
  width: 240px;
}
/**2023-06-13 start**/
.tab-tips{
  display: flex;
  display:-webkit-flex;
  align-items: center;
  justify-content: space-between;
  padding:0 30px;
}
.tab-tips .nav{
  width: 148px;
  height: 34px;
  position: relative;
}
.tab-tips .nav:after{
  content:'';
  position: absolute;
  left:0;
  top:0;
  width: 148px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  height: 34px;
  background: #FFFFFF;
  border: 1px solid #C9CDD4;
  border-radius: 18px;
}
.tab-tips .nav ul{
  overflow: hidden;
  position: relative;
  z-index: 99;
}
.tab-tips .nav ul li{
  float: left;
  width: 74px;
  height: 34px;
  cursor: pointer;
  text-align: center;
  line-height: 32px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  font-size:14px;
  color: #4E5969;
  border:1px solid transparent;
  border-radius: 18px;
}
.tab-tips .nav ul li.cur{
  border: 1px solid #4D88FF;
  border-radius: 18px;
  font-size:14px;
  color: #4D88FF;
}
.addschem-diag .window .p-content .p-top .lab .time-range{
  display: flex;
  display:-webkit-flex;
  align-items: center;
  justify-content: flex-start;

}

.addschem-diag .window .p-content .p-top .lab .time-range span{
  width: 110px;
  height: 34px;
  background: #F7F8FA;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  box-sizing: border-box;
  box-sizing: -webkit-border-box;
  font-size:14px;
  color:#86909C;
  text-align: center;
  line-height: 32px;
  cursor: default;
}
.addschem-diag .window .p-content .p-top .lab .time-range em{
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  margin:0 5px;
  color: #86909C;
}
/**2023-06-13 end**/

/**2024-09-19 start**/
.main .m-con .p-tables .row .lab:first-child .ckable .up{
  width: 100%;
  height: 25%;
  cursor: pointer;
  position: absolute;
  left:0;
  top:0;
}
.main .m-con .p-tables .row .lab:first-child .ckable .down{
  width: 100%;
  height: 25%;
  cursor: pointer;
  position: absolute;
  left:0;
  bottom:0;
}

.main .m-con .p-tables .row .lab .kalamu-area {
  outline: none;
  background-color: transparent;
  border: 1px solid transparent;
  width: 130px;
  margin:0 auto;
  min-height: 30px;
  padding: 3px;
  line-height: 22px;
  overflow: hidden;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  position: relative;
  z-index: 99;
  border-radius: 4px;
  font-size: 14px;
  color: #4E5969;
  text-align: center;
  position: relative;
  z-index: 99;
}

.main .m-con .p-tables .row .lab .kalamu-area:hover {
  border: 1px solid rgba(78, 89, 105, 0.2);
}
/**2024-09-19 end**/