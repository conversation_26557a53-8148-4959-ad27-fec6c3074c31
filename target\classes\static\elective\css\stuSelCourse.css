body {
  background-color: #f7f8fa;
}
.z-main {
  max-width: 1660px;
  margin: 0 auto 0;
  background-color: #ffffff;
  overflow: hidden;
  min-height: calc(100vh - 8px);
}
.z-main .z-title {
  padding: 29px 0 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e8ebf1;
  justify-content: space-between;
}
.z-main .z-title h3 {
  font-size: 16px;
  line-height: 22px;
  color: #1d2129;
  padding-left: 9px;
  position: relative;
  margin-left: 30px;
}
.z-main .z-title h3::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4d88ff;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 3px;
}
.z-main .z-title #selType {
  display: flex;
  align-items: center;
}
.z-main .z-title #selType li {
  padding-left: 24px;
  font-size: 14px;
  color: #86909c;
  margin-right: 24px;
  background: url("../images/radio1.png") no-repeat left center;
  background-size: 16px;
  cursor: pointer;
}
.z-main .z-title #selType li.active {
  background: url("../images/radio2.png") no-repeat left center;
  background-size: 16px;
}
.z-main .selectBox {
  position: relative;
  height: 24px;
  padding: 42px 0;
  box-sizing: content-box;
  text-align: center;
}
.z-main .selectBox .selectWeek {
  display: inline-block;
  height: 24px;
}
.z-main .selectBox .prevWeek,
.z-main .selectBox .nextWeek {
  float: left;
  width: 24px;
  height: 24px;
  background: url(../images/prevnext.png) center center / 48px auto no-repeat;
  background-position: 0 -24px;
  cursor: pointer;
}
.z-main .selectBox .nextWeek {
  background-position: -24px -24px;
}
.z-main .selectBox .nextWeek:hover {
  background-position: -24px -48px;
}
.z-main .selectBox .nextWeek.disabled {
  background-position: -24px 0;
  cursor: default;
}
.z-main .selectBox .prevWeek:hover {
  background-position: 0 -48px;
}
.z-main .selectBox .prevWeek.disabled {
  background-position: 0 0;
  cursor: default;
}
.z-main .selectBox span.week {
  float: left;
  font-size: 16px;
  color: #1d2129;
  margin: 0 14px;
  letter-spacing: 3px;
  cursor: pointer;
}
.z-main .selectBox span.week:hover {
  color: #3a4b87;
}
.z-main .selectBox.fixed {
  position: fixed;
  top: 40px;
  width: 100%;
  max-width: 1140px;
  min-width: 840px;
  background: #ffffff;
  z-index: 2;
}
.z-main .selectBox.fixed .headRight {
  right: 0px;
}
.z-main .selectMask {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1;
}
.z-main .selectMask .selectList {
  position: absolute;
  top: 150px;
  left: 50%;
  margin-left: -130px;
  width: 260px;
  padding: 16px 10px 4px 10px;
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0px 2px 12px 0px rgba(175, 187, 204, 0.75);
}
.z-main .selectMask .selectList li {
  margin-bottom: 12px;
}
.z-main .selectMask .selectList li.curWeek {
  position: relative;
}
.z-main .selectMask .selectList li.curWeek:after {
  position: absolute;
  content: "本周";
  top: 35px;
  left: 0px;
  width: 100%;
  text-align: center;
  color: #4d79ff;
  font-size: 12px;
  transform: scale(0.83);
  -ms-transform: scale(0.83);
  -webkit-transform: scale(0.83);
}
.z-main .selectList {
  padding: 12px 10px;
  overflow: hidden;
}
.z-main .selectList li {
  float: left;
  width: 20%;
}
.z-main .selectList li p {
  width: 28px;
  height: 28px;
  margin: 0 auto;
  font-size: 14px;
  border-radius: 50%;
  text-align: center;
  line-height: 28px;
  color: #646873;
  cursor: pointer;
}
.z-main .selectList li p.disable {
  opacity: 0.3;
}
.z-main .selectList li p:hover {
  background: #e2e3e6;
  color: #ffffff;
}
.z-main .selectList li p.active {
  background: #4d88ff;
  color: #ffffff;
}
.z-main .z-table {
  margin: 0 10px;
}
.z-main .z-table .thead {
  background: #f1f3f6;
  border-radius: 4px;
  margin-bottom: 8px;
  overflow: hidden;
}
.z-main .z-table .thead ul {
  height: 56px;
  display: flex;
  align-items: center;
}
.z-main .z-table .thead ul li {
  flex: 1;
  color: #86909c;
  font-size: 14px;
}
.z-main .z-table .thead ul li span {
  display: block;
  text-align: center;
}
.z-main .z-table .tbody ul {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.z-main .z-table .tbody ul li {
  min-height: 56px;
  flex: 1;
  border-radius: 4px;
  background-color: #f7f8fa;
  margin-right: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4e5969;
  font-size: 14px;
  cursor: not-allowed;
}
.z-main .z-table .tbody ul li span {
  display: block;
  text-align: center;
  width: 100%;
}
.z-main .z-table .tbody ul li:first-child {
  flex-direction: column;
}
.z-main .z-table .tbody ul li:last-child {
  margin-right: 0;
}
.z-main .z-table .tbody ul li.selCourse {
  cursor: pointer;
  background-color: #e1ebff;
  color: #4d88ff;
}
.z-main .z-list {
  overflow: hidden;
  padding: 0 30px;
  cursor: pointer;
}
.z-main .z-list .z-course {
  padding: 13px 20px 20px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #e1ebff;
}
.z-main .z-list .z-course .z-course-mes {
  flex: 1;
}
.z-main .z-list .z-course .z-btn {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}
.z-main .z-list .z-course .z-btn .btn-detail {
  border-radius: 36px;
  background: #4d88ff;
  width: 108px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  margin-right: 14px;
  color: #ffffff;
  font-size: 14px;
  display: none;
}
.z-main .z-list .z-course .z-btn .btn-exit {
  display: none;
}
.z-main .z-list .z-course .z-btn .btn-exit,
.z-main .z-list .z-course .z-btn .btn-sel {
  width: 108px;
  height: 32px;
  border: 1px solid #4d88ff;
  box-sizing: border-box;
  color: #4d88ff;
  background-color: #fff;
  border-radius: 36px;
  font-size: 14px;
  text-align: center;
  line-height: 32px;
}
.z-main .z-list .z-course .z-btn span {
  width: 96px;
  height: 34px;
  border: 1px solid #4d88ff;
  box-sizing: border-box;
  background-color: #4D88FF;
  border-radius: 36px;
  font-size: 14px;
  text-align: center;
  line-height: 32px;
  border-radius: 22px;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.2);
  margin-right: 14px;
  color: #ffffff;
}
.z-main .z-list .z-course .z-btn span.btn-detail {
  background: #fff;
  text-align: center;
  margin-right: 14px;
  color: #4d88ff;
  font-size: 14px;
}
.z-main .z-list .z-course .z-btn span:last-child {
  margin-right: 0;
}
.z-main .z-list .z-course .z-btn .btn-sel {
  background-color: #4D88FF;
  color: #ffffff;
}
.z-main .z-list .z-course .z-btn .btn-exit {
  display: none;
  border-radius: 22px;
  background: #F76560;
  border-color: #F76560;
  color: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(247, 101, 96, 0.3);
  margin-right: 0;
}
.z-main .z-list .z-course .z-btn .btn-full {
  border-radius: 18px;
  background: #B8D3FF;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  cursor: not-allowed;
  border-color: #B8D3FF;
  color: #ffffff;
}
.z-main .z-list .z-course .z-btn .btn-review {
  border-radius: 18px;
  background:  #FFB026;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  cursor: not-allowed;
  border-color: #B8D3FF;
  color: #ffffff;
}
.z-main .z-list .z-course .z-course-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.z-main .z-list .z-course .z-course-title h3 {
  font-size: 16px;
  color: #1d2129;
}
.z-main .z-list .z-course .z-course-title span {
  font-size: 14px;
  color: #4d88ff;
  padding-left: 13px;
}
.z-main .z-list .z-course .z-mes {
  color: #1d2129;
  font-size: 14px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 6px;
}
.z-main .z-list .z-course .z-mes p {
  margin-right: 24px;
}
.z-main .z-list .z-course .z-mes p span {
  color: #4e5969;
}
.z-main .z-list .z-course.active {
  background: #e1ebff;
}
.z-main .z-list .z-course.active .z-btn .btn-detail {
  display: block;
}
.z-tab-search {
  overflow: hidden;
  margin-bottom: 18px;
  position: relative;
  margin-right: 24px;
}
.z-tab-search input {
  display: block;
  float: right;
  width: 220px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #c9cdd4;
  padding: 0 28px 0 12px;
  box-sizing: border-box;
}
.z-tab-search input::placeholder {
  font-size: 14px;
  color: #86909c;
}
.z-tab-search img {
  display: block;
  position: absolute;
  right: 12px;
  top: 9px;
}
.z-search {
  display: flex;
  flex-wrap: wrap;
  margin: 30px 28px 10px;
}
.z-search .z-item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-right: 32px;
  margin-bottom: 16px;
}
.z-search .z-item .item-title {
  margin-right: 14px;
  line-height: 34px;
  font-size: 14px;
  color: #1d2129;
}
.z-search .btn-search {
  width: 80px;
  height: 34px;
  border-radius: 4px;
  background: #4d88ff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  text-align: center;
  line-height: 34px;
  color: #ffffff;
  cursor: pointer;
}
.dialog-wrap {
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}
.dialog-wrap .dialog {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  border-radius: 10px;
}
.dialog-wrap .dialog-title {
  font-size: 16px;
  color: #1d2129;
  height: 56px;
  line-height: 56px;
  border-bottom: 1px solid #e5e6eb;
  text-indent: 30px;
}
.dialog-wrap .dialog-btn {
  height: 70px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.dialog-wrap .dialog-btn button.sure {
  width: 88px;
  height: 36px;
  outline: none;
  border: none;
  border-radius: 18px;
  background: #4d88ff;
  box-shadow: 0px 0px 10px 0px rgba(90, 177, 118, 0.3);
  color: #ffffff;
  margin-right: 30px;
  cursor: pointer;
}
.dialog-wrap .dialog-btn button.cancel {
  width: 88px;
  height: 36px;
  outline: none;
  border: none;
  border-radius: 18px;
  border: 1px solid #c9cdd4;
  color: #4e5969;
  margin-right: 16px;
  cursor: pointer;
  background-color: #fff;
}
.dialog-wrap.dialog-plan .dialog {
  width: 586px;
}
.dialog-wrap.dialog-plan .dialog .dialog-con {
  overflow: hidden;
  max-height: 460px;
  overflow-y: auto;
}
.dialog-wrap.dialog-plan .dialog .plan {
  margin: 0 106px 20px;
}
.dialog-wrap.dialog-plan .dialog .plan h1 {
  font-size: 16px;
  color: #4e5969;
  margin: 30px 0 10px;
}
.dialog-wrap.dialog-plan .dialog .plan p {
  font-size: 14px;
  color: #4e5969;
  margin-bottom: 8px;
}
.dialog-wrap.dialog-course {
  display: none;
}
.dialog-wrap.dialog-course .dialog {
  width: 1310px;
}
.dialog-wrap.dialog-course .dialog .j-search-con {
  width: 200px;
}
.dialog-wrap.dialog-course .dialog .z-list {
  overflow: hidden;
  padding: 0 30px;
  max-height: 400px;
  overflow-y: auto;
  cursor: pointer;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course {
  border: 1px solid #e1ebff;
  padding: 10px 20px;
  margin-bottom: 10px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-course-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-course-title h3 {
  font-size: 16px;
  color: #1d2129;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-course-title span {
  font-size: 14px;
  color: #4d88ff;
  padding-left: 13px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-mes {
  color: #1d2129;
  font-size: 14px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 3px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-mes p {
  margin-right: 24px;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-mes p span {
  color: #4e5969;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course.active {
  background: #e1ebff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
}
.dialog-wrap.dialog-course .dialog .z-list .z-course.z-full {
  cursor: not-allowed;
}
.dialog-wrap.dialog-mes {
  display: none;
}
.dialog-wrap.dialog-mes .dialog {
  width: 370px;
}
.dialog-wrap.dialog-mes .dialog-con {
  padding: 0 30px;
}
.dialog-wrap.dialog-mes .dialog-con h3 {
  font-size: 16px;
  color: #1d2129;
  margin-top: 28px;
  margin-bottom: 16px;
}
.dialog-wrap.dialog-mes .dialog-con p {
  font-size: 14px;
  color: #1d2129;
  display: flex;
  margin-bottom: 16px;
}
.dialog-wrap.dialog-mes .dialog-con p span {
  width: 76px;
  flex-shrink: 0;
}
#tipsBox {
  position: fixed;
  left: 50%;
  top: 50%;
  z-index: 99999;
  transform: translateX(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  display: none;
}
