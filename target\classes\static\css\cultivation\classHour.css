.dialog-wrap {
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  font-family: "PingFang SC";
}
.dialog-wrap .dialog {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  border-radius: 10px;
  overflow: hidden;
}
.dialog-wrap .dialog-btn {
  height: 70px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  border-top: 1px solid #E5E6EB;
  background: #FFF;
}
.dialog-wrap .dialog-btn button.sure {
  width: 88px;
  height: 36px;
  outline: none;
  border: none;
  border-radius: 18px;
  background: #4D88FF;
  box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
  color: #ffffff;
  margin-right: 30px;
  cursor: pointer;
}
.dialog-wrap .dialog-btn button.cancel {
  width: 88px;
  height: 36px;
  outline: none;
  border: none;
  border-radius: 18px;
  border: 1px solid #c9cdd4;
  color: #4e5969;
  margin-right: 16px;
  cursor: pointer;
  background-color: #fff;
}
.dialog-wrap.dialog-hour .dialog {
  width: 820px;
}
.dialog-wrap.dialog-hour .dialog .dialog-con {
  overflow: hidden;
  max-height: 500px;
  overflow-y: auto;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .term {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-bottom: 32px;
  width: 660px;
  margin: 40px auto;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .term h3 {
  color: #1D2129;
  text-align: right;
  font-family: "PingFang SC";
  line-height: 34px;
  margin-right: 14px;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .term input {
  border-radius: 4px;
  border: 1px solid #E5E6EB;
  background: #F7F8FA;
  width: 240px;
  height: 34px;
  padding: 0 10px;
  box-sizing: border-box;
  font-size: 14px;
  cursor: not-allowed;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .term input:disabled {
  color: #C9CDD4;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .hour-list {
  width: 660px;
  margin: 40px auto 0;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .hour-list .title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .hour-list .title h3 {
  font-size: 16px;
  color: #4080FF;
  font-weight: 500;
  margin-right: 10px;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .hour-list .title span {
  height: 22px;
  width: 22px;
  background: url('../../images/cultivation/arrow-icon.png') no-repeat center;
  background-size: 12px;
  cursor: pointer;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .hour-list .title span.slideUp {
  transform: rotate(-90deg);
}
.dialog-wrap.dialog-hour .dialog .dialog-con .list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12px;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .list li {
  padding: 20px 32px 20px 48px;
  border-radius: 4px;
  background: #F6F7FE;
  position: relative;
  margin-right: 20px;
  margin-bottom: 20px;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .list li:nth-child(2n) {
  margin-right: 0;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .list li span {
  width: 32px;
  height: 32px;
  border-radius: 0px 8px 8px 0px;
  background: #E1EBFF;
  display: block;
  font-size: 16px;
  text-align: center;
  line-height: 32px;
  position: absolute;
  left: -4px;
  top: 16px;
  color: #4D88FF;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .list li span::before {
  content: "";
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 4px;
  height: 3px;
  background: url('../../images/cultivation/arrow-bottom.png') no-repeat center;
  background-size: 4px;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .list li dl {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  min-width: 240px;
  width: 84px;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .list li dl:last-child {
  margin-bottom: 0;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .list li dl dt {
  color: #1D2129;
  font-size: 14px;
  width: 84px;
}
.dialog-wrap.dialog-hour .dialog .dialog-con .list li dl dd {
  font-size: 14px;
  color: #4E5969;
}
