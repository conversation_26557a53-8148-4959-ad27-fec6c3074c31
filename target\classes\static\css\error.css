@charset "utf-8";
/* CSS Document */
*{margin:0;padding:0;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;-webkit-appearance:none;}/*-webkit-appearance:radio;*/
body{font:14px/1.5 微软雅黑;background:#fff;color:#333;-webkit-tap-highlight-color: transparent;}
ul{list-style-type:none;}
input,select,textarea{vertical-align:middle; font:14px 微软雅黑; color:#333;}
a{text-decoration:none;color:#333;}
table{border-spacing:0px; border-collapse:collapse;width:100%; border:0px;margin:0;padding:0;}
img{border:0px;}
em{font-style:normal;}
.clearfix{overflow:hidden;zoom:1;}
.clear{clear:both; height:0px; font-size:0px; line-height:0px; overflow:hidden;}
.fl{float:left;}
.fr{float:right;}


.tipsOne{width:100%;margin:0 auto;position:absolute;left:50%;top:50%;transform: translate(-50%, -60%);}
.tipsOne dl{width:360px;margin:0 auto}
.tipsOne dl dt{width:360px;height:252px;margin:0 auto}
.tipsOne dl dt img{display:block;width:100%;height:100%}
.tipsOne dd{color: #8A8B99;font-size: 16px;margin-top:20px;text-align:center;}
.tipsOne .infoCon{width:360px;margin:0 auto;margin-top:20px;}
.tipsOne .infoCon p{color: #8A8B99;font-size:16px;line-height:24px;padding-left:70px;}

@media screen and (max-width: 750px) {
.tipsOne dl{width:100%;margin:0 auto}
.tipsOne dl dt{width:220px;height:154px;margin:0 auto}
.tipsOne dl dt img{display:block;width:100%;height:100%}
.tipsOne dd{color: #8A8B99;font-size: 16px;margin-top:15px;text-align:center;}
.tipsOne .infoCon{width:100%;margin:0 auto;margin-top:20px;}
.tipsOne .infoCon p{font-size:16px;color:#999;line-height:24px;padding-left:70px;}
}

