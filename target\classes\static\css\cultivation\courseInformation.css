.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}
.flex {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}
.transforms {
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}
.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
body {
    background-color: #fff;
    padding: 0;
    font-size: 14px;
    color: #4e5969;
    font-family: PingFang SC;
}
.hide {
    display: none !important;
}
input::-webkit-input-placeholder {
    font-size: 14px;
    color: #ACB4BF;
}
.sel-item {
    display: flex;
    align-items: center;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-right: 0;
    margin-bottom: 15px;
}
.sel-item .sel-title {
    color: #1D2129;
    font-size: 14px;
    width: 98px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}
.sel-item .sel-title span {
    display: inline-block;
    width: 14px;
    color: #F76560;
}
.sel-item .sel-title i {
    width: 16px;
    height: 16px;
    margin-left: 6px;
    background: url(../images/gloss-icon.png) no-repeat center;
    cursor: pointer;
    position: relative;
}
.sel-item .sel-title i.cur em {
    display: block;
}
.sel-item .sel-title i em {
    display: none;
    position: absolute;
    left: -86px;
    top: 27px;
    z-index: 999;
    border-radius: 8px;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    padding: 14px 24px;
    font-size: 14px;
    color: #4E5969;
    line-height: 20px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    height: 48px;
    white-space: nowrap;
    padding-left: 33px;
}
.sel-item .sel-title i em:before {
    content: '';
    position: absolute;
    left: 24px;
    top: 22px;
    width: 4px;
    height: 4px;
    background-color: #4D88FF;
}
.sel-item .sel-title i em:after {
    content: '';
    position: absolute;
    left: 88px;
    top: -5px;
    width: 12px;
    height: 6px;
    background: url(../images/tringle-icon.png) no-repeat center;
}
.sel {
    width: 390px;
    margin-right: 40px;
    height: 34px;
    line-height: 34px;
}
.sel em {
    float: left;
    font-size: 14px;
    color: #474C59;
}
.sel .radio-box {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 34px;
}
.sel .radio-box span {
    font-size: 14px;
    color: #4E5969;
    padding-left: 24px;
    background: url(../images/radio-icon.png) no-repeat left center;
    margin-right: 24px;
    cursor: pointer;
}
.sel .radio-box span.cur {
    background: url(../images/radio-cur-icon.png) no-repeat left center;
}
.sel .select-input {
    height: 34px;
    border-radius: 4px;
    border: 1px solid #D4D6D9;
    background-color: #fff;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    cursor: pointer;
}
.sel .select-input input {
    border: none;
    outline: none;
    background-color: transparent;
    width: 100%;
    height: 100%;
    font-size: 14px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-left: 13px;
    color: #1D2129;
    display: block;
}
.sel .select-input i {
    position: absolute;
    top: 11px;
    right: 11px;
    width: 12px;
    height: 12px;
    background: url(../../images/cultivation/icon-arrow1.png) no-repeat center;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}
.sel .select-input .name {
    font-size: 14px;
    color: #ACB4BF;
    padding-left: 13px;
    line-height: 32px;
    width: 86%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sel .select-input .name.ckd {
    color: #131B26;
}
.sel .select-input.clicked i {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
}
.sel .select-input.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
}
.sel .select-input .select-dropdown {
    width: 100%;
    left: -1px;
    margin: 5px 0;
    padding: 6px 0;
    background-color: #fff;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
    border-radius: 8px;
    position: absolute;
    transform: translate(0, -50px);
    -webkit-transform: translate(0, -50px);
    z-index: 900;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
    z-index: -1;
}
.sel .select-input .select-dropdown .search {
    margin: 8px;
    height: 36px;
    box-sizing: border-box;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    display: flex;
    align-items: center;
}
.sel .select-input .select-dropdown .search input {
    border: none;
    flex: 1;
    background: transparent;
    height: 36px;
    line-height: 36px;
    padding-left: 14px;
    font-size: 14px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    float: left;
}
.sel .select-input .select-dropdown .search input::placeholder {
    color: #8F97A8;
}
.sel .select-input .select-dropdown .search span {
    cursor: pointer;
    float: left;
    width: 16px;
    height: 16px;
    background: url(../../images/cultivation/search-icon.png) no-repeat center;
    margin: 9px;
}
.sel .select-input .select-dropdown .all-selects {
    color: #4E5969;
    font-size: 14px;
    height: 40px;
    line-height: 40px;
    margin: 0 20px;
    padding-left: 24px;
    background: url(../images/check-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .all-selects.cur {
    background: url(../images/checked-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .dropdown-lists {
    padding: 0 0 6px;
    max-height: 148px;
    overflow: auto;
}
.sel .select-input .select-dropdown .dropdown-lists li {
    margin: 0;
    line-height: normal;
    line-height: 40px;
    padding: 0 20px;
    clear: both;
    color: #4E5969;
    font-size: 14px !important;
    list-style: none;
    cursor: pointer;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.sel .select-input .select-dropdown .dropdown-lists li span {
    display: block;
    padding-left: 27px;
    background: url(../images/check-icon.png) no-repeat left center;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
}
.sel .select-input .select-dropdown .dropdown-lists li:hover {
    background: #F5F7FA;
}
.sel .select-input .select-dropdown .dropdown-lists li.cur {
    background: #E1EBFF;
}
.sel .select-input .select-dropdown .dropdown-lists li.cur span {
    background: url(../images/checked-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .dropdown-list li {
    margin: 0;
    line-height: normal;
    padding: 0 16px;
    clear: both;
    color: #4E5969;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
}
.sel .select-input .select-dropdown .dropdown-list li:hover {
    background: #E1EBFF;
}
::-webkit-scrollbar {
    width: 6px;
    height: 10px;
}
::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: #d9d9d9;
}
::-webkit-scrollbar-track {
    border-radius: 6px;
}
#courseInformation {
    width: 580px;
    padding-bottom: 61px;
}
#courseInformation .popup-con {
    padding: 0;
}
#courseInformation .popup-con .content {
    padding: 40px;
}
#courseInformation .bottom {
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
    padding: 0 30px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}
#courseInformation .bottom .exam-cancle {
    border: 1px solid #E5E6EB;
    padding: 0 30px;
    height: 36px;
    font-size: 14px;
    border-radius: 18px;
    color: #4E5969;
    margin-right: 16px;
    line-height: 34px;
    background-color: #fff;
    cursor: pointer;
}
#courseInformation .bottom .exam-sure {
    color: #fff;
    background: #4d88ff;
    box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
    border: 1px solid #4d88ff;
    padding: 0 30px;
    height: 36px;
    font-size: 14px;
    line-height: 34px;
    border-radius: 18px;
    display: block;
    cursor: pointer;
}