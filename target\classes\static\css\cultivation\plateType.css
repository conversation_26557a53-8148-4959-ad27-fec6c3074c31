.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

body {
    background-color: #f7f8fa;
}

.flex {
    display: flex;
    align-items: center;
}

.opt-btn {
    color: #4c85fa;
    cursor: pointer;
    margin-right: 16px;
}

.opt-btn:last-child {
    margin-right: 0;
}

.opt-btn.color-red {
    color: #F76560;
}

.opt-btn.color-yellow {
    color: #f88030;
}

.plate-level li {
    display: block;
    border-bottom: 1px solid #e8ebf1;
    padding: 0 16px;
}

.plate-level li:last-child {
    border-bottom: none;
}

.main {
    margin: 0 auto;
    border-radius: 8px;
    min-height: calc(100vh);
    min-width: 1000px;
    max-width: 1660px;
    background-color: #ffffff;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    overflow: hidden;
}

.main .m-top {
    width: 100%;
    height: 57px;
    border-bottom: 1px solid #e8eaf1;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 30px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.main .m-top .back {
    width: 60px;
    height: 24px;
    background: url(../images/back-icon.png) no-repeat left center;
    cursor: pointer;
    margin-right: 10px;
}

.main .m-top .title {
    font-size: 16px;
    color: #1d2129;
    padding-left: 9px;
    position: relative;
}

.main .m-top .title::after {
    content: "";
    width: 3px;
    height: 16px;
    background-color: #4d88ff;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 3px;
}

.main .table-box {
    margin: 0 32px;
}

.main .tab-nav {
    border-bottom: 1px solid #ddd;
    display: flex;
    align-items: center;
    padding: 0 32px;
}

.main .tab-nav li {
    line-height: 60px;
    font-size: 15px;
    margin-right: 40px;
    color: #272727;
    cursor: pointer;
}

.main .tab-nav li:hover {
    color: #4c85fa;
}

.main .tab-nav li.active {
    color: #4c85fa;
    font-weight: bold;
    position: relative;
}

.main .tab-nav li.active::after {
    content: "";
    width: 100%;
    height: 2px;
    background-color: #4c85fa;
    position: absolute;
    bottom: 0;
    left: 0;
}

.main .tab-box .tab-box-con {
    display: none;
}

.main .tab-box .tab-box-con:first-child {
    display: block;
}

.main .tab-box .tab-box-con iframe {
    display: block;
    border: none;
    width: 100%;
    height: calc(100vh - 61px);
}

.main .table-opt {
    margin: 16px 32px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.main .table-opt button {
    min-width: 90px;
    float: right;
}

.main .layui-inline {
    margin-bottom: 16px;
    margin-right: 16px;
}

div[lay-id=plateTime] .layui-table-fixed-r .layui-table-body .layui-table-cell {
    height: 34px;
}

.cell-blue {
    background-color: #4c85fa;
    color: #fff;
}

.layui-btn-disabled, .layui-btn-disabled:active, .layui-btn-disabled:hover {
    border-color: #8fb2fa !important;
    background-color: #8fb2fa !important;
    color: #fff !important;
    cursor: not-allowed !important;
    opacity: 1;
}

.festivals-tips {
    padding: 10px;
    background: url(../../images/cultivation/tips-icon.png) no-repeat center;
    background-size: 14px;
    cursor: pointer;
    position: relative;
}

#tipsBox {
    padding: 10px 16px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.7);
    position: fixed;
    z-index: 99999999;
    border-radius: 6px;
    display: none;
}

#tipsBox::after {
    content: "";
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -6px;
    border-top: 6px solid rgba(0, 0, 0, 0.7);
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
}

.dialog .dialog-con .layui-form .layui-form-label {
    display: flex;
    align-items: center;
}

.dialog .dialog-con .layui-form .layui-form-label span {
    width: 16px;
    height: 16px;
    display: inline-block;
    background: url(../../images/cultivation/tips-icon2.png) no-repeat center;
    cursor: pointer;
    position: relative;
    margin-left: 4px;
}

.dialog .dialog-con .layui-form .layui-form-label span em {
    display: none;
    position: absolute;
    left: -14px;
    top: -49px;
    width: auto;
    height: 38px;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.6);
    padding: 8px 12px;
    box-sizing: border-box;
    line-height: 22px;
    font-size: 14px;
    color: #ffffff;
    white-space: nowrap;
    z-index: 999;
}

.dialog .dialog-con .layui-form .layui-form-label span em:after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 16px;
    border-top: 6px solid rgba(0, 0, 0, 0.6);
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
}

.dialog .dialog-con .layui-form .layui-form-label span:hover em {
    display: block;
}
