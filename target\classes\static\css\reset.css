@charset "UTF-8";
body,form,textarea,select,option,ol,ul,li,h1,h2,h3,h4,h5,h6,p,th,td,dl,dt,dd,menu,blockquote,fieldset,label,i,em,header,footer,section,
legend,button,input,hr,pre,div,input,span,p,a{margin: 0;padding: 0;-webkit-tap-highlight-color:rgba(0,0,0,0);}
h1,h2,h3,h4,h5,h6{ font-weight:normal; font-size:100%;}
html{font-size: 62.5%;}
body{font:12px/1.5 "微软雅黑","Hiragino Sans GB","Helvetica Neue",Helvetica,STHeiTi,"Microsoft Yahei",sans-serif;color:#333;}
li{list-style-type:none;}
input,select,textarea{vertical-align:middle;  color:#333; outline:none;-webkit-tap-highlight-color:rgba(0,0,0,0);}
a{text-decoration:none;color:#333;}
img{border:0px;}
i,em{font-style:normal;}
b{ font-weight:normal;}
table{border-spacing:0px; border-collapse:collapse;width:100%; border:0px;}
input[type="button"]:hover{cursor: pointer;}
.hide{display: none;}
.clearfix{overflow:hidden;}
.clear{clear:both; height:0px; font-size:0px; line-height:0px; overflow:hidden;}
.column_wrapper{clear: both;*zoom:1;}
.column_wrapper:after {
    clear:both;
    content:".";
    display:block;
    font-size:0;
    height:0;
    line-height:0;
    visibility:hidden;
}
button{
  outline: none;
}

.fl{float:left;}
.fr{float:right;}
/*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
.scrollbar::-webkit-scrollbar{
    width: 5px;/*对应的是垂直滚动条的宽度*/
    height: 5px;/*对应的是水平滚动条的高度*/
    background-color: #f5f5f5;
}
/*定义滚动条的轨道，内阴影及圆角*/
.scrollbar::-webkit-scrollbar-track{
    /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
    border-radius: 10px;
    background-color: transparent;
}
/*定义滑块，内阴影及圆角*/
.scrollbar::-webkit-scrollbar-thumb{
    /*width: 10px;*/
    height: 20px;
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
    background-color: #D9DDE1;
}

