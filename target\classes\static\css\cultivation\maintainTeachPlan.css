body {
    background-color: #f6f7f9;
}

.layui-table-cell {
    padding: 0 10px;
    line-height: 38px;
}

.layui-table-header thead tr {
    background: #f1f3f6;
}

.layui-table-view .layui-table th {
    font-weight: normal;
    color: #86909c;
    font-size: 14px;
}

.layui-table-body tr:nth-child(2n) {
    background: #fafbfc;
}

.layui-table-view .layui-table td {
    color: #4e5969;
    height: 38px;
}

.layui-table-view .layui-table td,
.layui-table-view .layui-table th {
    border-color: #e8ebf3;
}

.delBtn {
    color: #f76560;
}

.delBtn:hover {
    color: #f76560;
    cursor: pointer;
}

.replaceBtn {
    color: #4d88ff;
    margin-right: 16px;
}

.replaceBtn:hover {
    color: #4d88ff;
    cursor: pointer;
}

.layui-week {
    background-color: #e1ebff !important;
    border-color: #cbd9f6 !important;
}

.layui-tr-total td {
    background-color: #f1f3f6 !important;
}

.layui-tr-total td:first-child {
    color: #4e5969 !important;
    font-weight: 900;
}

.layui-tr-total td.layui-total {
    background-color: #b8d3ff !important;
    border-color: #cbd9f6 !important;
}

.layui-tr-total td select {
    display: none !important;
}

.layui-table-view .layui-table td[data-edit]:hover:after {
    border: 1px solid #4d88ff;
}

.layui-table-edit {
    text-align: center !important;
}

.layui-table-edit:focus {
    border-color: #4d88ff !important;
}

.layui-table-view select[lay-ignore] {
    width: 90px;
    height: 30px;
}

.layui-table-total tr td:first-child {
    color: #4e5969 !important;
    font-weight: 900;
}

.container {
    background: #ffffff;
    border-radius: 8px;
    margin: 20px;
    overflow: hidden;
}

.container .top {
    width: 100%;
    height: 60px;
    border-bottom: 1px solid #e8ebf1;
    position: relative;
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
    align-items: center;
}

.container .top .title {
    padding-left: 30px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 36px;
}

.container .top .title .back {
    padding-left: 22px;
    background: url(../../images/cultivation/back.png) no-repeat left center;
    background-size: 16px;
    color: #7d92b3;
    font-size: 14px;
    cursor: pointer;
    margin-right: 16px;
}

.container .top .title .levelone {
    padding-left: 9px;
    position: relative;
    color: #1d2129;
    font-size: 16px;
    margin-right: 6px;
}

.container .top .title .levelone:after {
    content: "";
    position: absolute;
    left: 0;
    top: 2px;
    width: 3px;
    height: 16px;
    background: #4d88ff;
    border-radius: 2px;
}

.container .top .title .icon {
    width: 12px;
    height: 12px;
    background: url(../../images/cultivation/arrow-right.png) no-repeat center;
    background-size: 12px;
    margin-right: 6px;
}

.container .top .title .leveltwo {
    color: #1d2129;
    font-weight: 700;
    font-size: 16px;
}

.container .top .btn {
    position: absolute;
    top: 17px;
    right: 28px;
    width: 116px;
    height: 36px;
    text-align: center;
    line-height: 36px;
    cursor: pointer;
    font-size: 14px;
    color: #ffffff;
    background: #4d88ff;
    box-shadow: 0px 0px 10px #4d88ff;
    border-radius: 4px;
}

.container .top h4 {
    position: relative;
    color: #1d2129;
    font-size: 16px;
    margin-left: 30px;
    padding-left: 9px;
    font-weight: bold;
}

.container .top h4::after {
    content: "";
    width: 3px;
    height: 16px;
    background: #4d88ff;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 4px;
}

.container #tabBox {
    margin: 0 30px 16px;
    overflow: hidden;
    position: relative;
}

.container #tabBox .amountTo {
    position: absolute;
    left: 0;
    bottom: 1px;
    line-height: 46px;
    width: 300px;
    background-color: #f2f2f2;
    box-shadow: 1px 0 8px rgba(0, 0, 0, 0.08);
}

.container #tabBox .amountTo span {
    display: block;
    float: right;
    margin-right: 80px;
}

.container .txt-blue {
    color: #4D88FF;
}

.container .txt-more {
    display: block;
}

.container .txt-more:hover {
    color: #6581BA;
}

.container .tab-link {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url('../images/link.png') no-repeat center;
    background-size: 20px;
    margin-top: 9px;
    cursor: pointer;
}

.container .major-opt {
    margin: 0 30px;
    overflow: hidden;
}

.container .major-opt .major-mes {
    display: flex;
    align-items: center;
    color: #1d2129;
    font-size: 16px;
    line-height: 60px;
}

.container .major-opt .major-mes span {
    margin-right: 40px;
}

.container .major-opt .major-button {
    display: flex;
    align-items: center;
    float: right;
    margin-bottom: 20px;
}

.container .major-opt .major-button button {
    border: 1px solid #4d88ff;
    box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
    border-radius: 4px;
    height: 36px;
    background: #ffffff;
    padding: 0 20px;
    color: #4d88ff;
    font-size: 14px;
    margin-left: 12px;
    cursor: pointer;
    white-space: nowrap;
}

.container .major-opt .major-button button.releaseBtn {
    background-color: #4d88ff;
    color: #ffffff;
}

.container .major-opt .major-button button.releaseBtn.disabledBtn {
    background-color: #C9CDD4;
    border-color: #C9CDD4;
    cursor: not-allowed;
}

.container .remark h1 {
    font-size: 14px;
    margin-bottom: 10px;
}

.container .remark .intro {
    display: block;
    width: 100%;
    padding: 10px 24px;
    font-size: 14px;
    box-sizing: border-box;
    background-color: #ebf1fd;
    color: #818181;
}

.container .remark .intro span {
    width: 16px;
    height: 16px;
    display: inline-block;
    background: url("../images/tips-icon.png") no-repeat left center;
    background-size: 14px;
    vertical-align: text-top;
    padding-right: 4px;
}

.layui-form-checkbox[lay-skin="primary"] i:hover {
    border-color: #4d88ff !important;
}

.layui-form-checked[lay-skin="primary"] i {
    border-color: #4d88ff !important;
    background-color: #4d88ff;
}

.layui-layer {
    border-radius: 10px;
}

.dialog {
    width: 1104px;
    border-radius: 10px;
    background-color: #ffffff;
    overflow: hidden;
}

.dialog .dialog-title {
    height: 54px;
    border-bottom: 1px solid #e5e6eb;
    color: #1d2129;
    font-size: 16px;
    line-height: 54px;
    text-indent: 30px;
    position: relative;
}

.dialog .dialog-title .close-btn {
    width: 20px;
    height: 20px;
    background: url('../../images/cultivation/close1.png') no-repeat center;
    background-size: 20px;
    position: absolute;
    right: 24px;
    top: 17px;
    cursor: pointer;
}

.dialog .dialog-con {
    padding: 0 100px;
    overflow: hidden;
    box-sizing: border-box;
}

.dialog .dialog-con .layui-form {
    margin: 30px 0 24px;
}

.dialog .dialog-con .layui-form .layui-form-label {
    width: 56px;
    padding: 0;
    line-height: 34px;
    padding-right: 8px;
}

.dialog .dialog-con .layui-form .layui-input-inline {
    margin-right: 17px;
}

.dialog .dialog-con .layui-form .layui-input-inline .layui-input {
    height: 34px;
    line-height: 34px;
    width: 120px;
}

.dialog .dialog-con .layui-form .layui-input-inline .layui-form-select {
    width: 100px;
}

.dialog .dialog-con .layui-form .layui-input-inline .layui-form-select .layui-input {
    width: 100px;
}

.dialog .dialog-con .layui-form .layui-btn {
    width: 64px;
    height: 34px;
    line-height: 32px;
    background: #4d88ff;
    border-radius: 6px;
}

.dialog .dialog-con .layui-form .layui-btn.layui-btn-primary {
    background-color: unset;
    border: 1px solid #4d88ff;
    color: #4d88ff;
}

.dialog .dialog-con .layui-form .layui-btn.layui-btn-primary:hover {
    border-color: #4d88ff;
}

.dialog .dialog-con .layui-table-page {
    text-align: right;
}

.dialog .dialog-con .layui-table-view select[lay-ignore] {
    height: 20px;
}

.dialog .dialog-con .layui-table-page .layui-laypage input:focus,
.dialog .dialog-con .layui-table-page .layui-laypage select:focus {
    border-color: #4d88ff !important;
}

.dialog .dialog-con .layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: #4d88ff !important;
}

.dialog .dialog-con .layui-table-page .layui-laypage a:hover,
.dialog .dialog-con .layui-table-page .layui-laypage span.layui-laypage-curr:hover {
    color: #4d88ff !important;
}

.dialog .dialog-con .layui-form-select dl dd.layui-this {
    color: #4d88ff !important;
}

.dialog .dialog-footer {
    height: 70px;
    border-top: 1px solid #e5e6eb;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.dialog .dialog-footer button {
    width: 88px;
    height: 34px;
    border: 1px solid #c9cdd4;
    border-radius: 18px;
    color: #4e5969;
    font-size: 14px;
    background-color: #ffffff;
    cursor: pointer;
}

.dialog .dialog-footer button:last-child {
    background: #4d88ff;
    border-color: #4d88ff;
    box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
    color: #ffffff;
    margin: 0 30px 0 16px;
}

#selTextbook {
    width: 1322px;
    display: none;
}

#selTextbook .tips {
    width: 100%;
    color: #6581BA;
    line-height: 50px;
    text-align: center;
    background: #E1EBFF;
}

#selTextbook .dialog-con {
    padding: 0 60px;
}

#selTextbook .dialog-con .layui-form .layui-input-inline .layui-input {
    width: 240px;
}

#selTextbook .dialog-con .z-check {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #6581BA;
    position: absolute;
    left: 27px;
    bottom: 8px;
}

#selTextbook .dialog-con .z-check .check {
    width: 28px;
    height: 28px;
    cursor: pointer;
    margin-right: 6px;
    background: url(../../images/cultivation/check.png) no-repeat center;
    background-size: 28px;
}

#selTextbook .dialog-con .z-check .check.checked {
    background: url(../../images/cultivation/check1.png) no-repeat center;
}

#selTextbook .dialog-con .selCourse {
    left: 160px;
    color: #8F97A8;
    line-height: 43px;
}

#textbookTips {
    background-color: #4E5969;
    border-radius: 8px;
    width: 265px;
    box-sizing: border-box;
    color: #ffffff;
    position: fixed;
    display: none;
    z-index: 9999;
    padding: 0 2px;
}

#textbookTips .textbook-con-wrap {
    max-height: 240px;
    overflow-y: auto;
    margin: 8px 0;
    padding: 8px 30px;
}

#textbookTips::after {
    content: "";
    border-bottom: 8px solid #4E5969;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    position: absolute;
    top: -8px;
    left: 80px;
}

#textbookTips .textbook-item {
    display: flex;
    align-items: center;
    line-height: 20px;
    margin-bottom: 4px;
}

#textbookTips .textbook-item:last-child {
    margin-bottom: 0;
}

#textbookTips .textbook-item h5 {
    width: 70px;
}

#textbookTips .textbook-con {
    margin-bottom: 8px;
}

#textbookTips .textbook-con:last-child {
    margin-bottom: 0;
}

#textbookTips.textbookBot::after {
    content: "";
    border-top: 8px solid #4E5969;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: unset;
    position: absolute;
    bottom: -8px;
    left: 80px;
    top: unset;
}

#importTeachProcess {
    width: 563px;
    display: none;
}

#importTeachProcess .dialog-title {
    display: flex;
    justify-content: space-between;
}

#importTeachProcess .dialog-title .opt-right {
    display: flex;
    align-items: center;
}

#importTeachProcess .dialog-title .opt-right .btn-export {
    font-size: 14px;
    color: #4D88FF;
    padding-left: 20px;
    background: url('../images/export.png') no-repeat left center;
    cursor: pointer;
    margin-right: 20px;
    text-indent: 0;
}

#importTeachProcess .dialog-title .opt-right .close-btn {
    width: 16px;
    height: 16px;
    background: url('../images/close.png') no-repeat center;
    background-size: 16px;
    margin-right: 30px;
    cursor: pointer;
    position: unset;
}

#importTeachProcess .dialog-con {
    padding: 30px;
    line-height: 22px;
    font-size: 14px;
}

#importTeachProcess .dialog-con h5 {
    color: #1D2129;
}

#importTeachProcess .dialog-con p {
    color: #4E5969;
}

#importTeachProcess .dialog-con p a {
    color: #4D88FF;
    cursor: pointer;
}

#importTeachProcess .dialog-con button {
    border-radius: 20px;
    margin: 24px auto 10px;
    box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
    background-color: #4D88FF;
    padding: 0 30px;
    display: block;
}

#importTeachProcessFile {
    width: 560px;
    display: none;
}

#importTeachProcessFile .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#importTeachProcessFile .dialog-title .close-btn {
    width: 16px;
    height: 16px;
    background: url('../images/close.png') no-repeat center;
    background-size: 16px;
    margin-right: 30px;
    cursor: pointer;
}

#importTeachProcessFile .dialog-con {
    padding: 40px 100px;
}

#importTeachProcessFile .dialog-con .loding {
    display: block;
    margin: 0 auto 25px;
    width: 48px;
    opacity: 0.7;
}

#importTeachProcessFile .dialog-con .importTips {
    font-size: 16px;
    color: #1D2129;
    line-height: 24px;
    text-align: center;
}

#importTeachProcessFile .dialog-con .importTips.importTipsSuccess p {
    text-align: center;
}

#importTeachProcessFile .dialog-con .importTips.importTipsSuccess p:last-child {
    display: none;
}

#importTeachProcessFile .dialog-con .importTips.importTipsSuccess p:first-child span.error {
    display: none;
}

#importTeachProcessFile .dialog-con .importTips.importTipsError {
    padding-left: 32px;
}

#importTeachProcessFile .dialog-con .importTips.importTipsError p:first-child .icon {
    background: url('../images/error-icon.png') no-repeat left top;
    background-size: 24px;
}

#importTeachProcessFile .dialog-con .importTips.importTipsError p:first-child span.error {
    display: unset;
}

#importTeachProcessFile .dialog-con .importTips.importTipsError p:last-child {
    display: unset;
}

#importTeachProcessFile .dialog-con .importTips p {
    white-space: nowrap;
}

#importTeachProcessFile .dialog-con .importTips p:last-child {
    margin-top: 2px;
}

#importTeachProcessFile .dialog-con .importTips p:first-child {
    display: flex;
    align-items: center;
    justify-content: center;
}

#importTeachProcessFile .dialog-con .importTips p:first-child .icon {
    width: 24px;
    height: 24px;
    background: url('../images/success-icon.png') no-repeat center;
    background-size: 24px;
    margin-right: 8px;
    flex-shrink: 0;
}

#importTeachProcessFile .dialog-con .importTips p em {
    padding: 0 4px;
}

#importTeachProcessFile .dialog-con .importTips p .green {
    color: #3EB35A;
}

#importTeachProcessFile .dialog-con .importTips p .yellow {
    color: #FFB026;
}

#importResult {
    width: 560px;
    display: none;
}

#importResult .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#importResult .dialog-title .close-btn {
    width: 16px;
    height: 16px;
    background: url('../images/close.png') no-repeat center;
    background-size: 16px;
    margin-right: 30px;
    cursor: pointer;
}

#importResult .dialog-con {
    padding: 40px 100px;
}

#importResult .dialog-con .importIcon {
    width: 48px;
    height: 48px;
    margin: 0 auto 24px;
    background: url('../images/success-icon.png') no-repeat center;
}

#importResult .dialog-con p {
    font-size: 16px;
    color: #1D2129;
    line-height: 24px;
    text-align: center;
    white-space: nowrap;
}

#importResult .dialog-con p:last-child {
    margin-top: 2px;
}

#importResult .dialog-con p em {
    padding: 0 4px;
}

#importResult .dialog-con p .green {
    color: #3EB35A;
}

#importResult .dialog-con p .yellow {
    color: #FFB026;
}

#importResult .dialog-footer a {
    width: 158px;
    height: 36px;
    border: 1px solid #c9cdd4;
    border-radius: 18px;
    color: #4e5969;
    font-size: 14px;
    background-color: #ffffff;
    cursor: pointer;
    text-align: center;
    line-height: 36px;
}

#importResult.importSuccess .dialog-con .importIcon {
    background: url('../images/success-icon.png') no-repeat center;
}

#importResult.importSuccess .dialog-con .importTips p:first-child .error {
    display: none;
}

#importResult.importSuccess .dialog-con .importTips p:last-child {
    display: none;
}

#importResult.importSuccess .dialog-footer a {
    display: none;
}

#importResult.importError .dialog-con .importIcon {
    background: url('../images/error-icon.png') no-repeat center;
}

#importResult.importError .dialog-con .importTips p:first-child .error {
    display: unset;
}

#importResult.importError .dialog-con .importTips p:last-child {
    display: unset;
}

#importResult.importError .dialog-footer a {
    display: unset;
}

#updateTeachDialog {
    width: 540px;
    display: none;
    overflow: unset;
}

#updateTeachDialog .dialog-con {
    padding: 30px 100px 100px;
    overflow: unset;
}

#updateTeachDialog .dialog-con .item {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

#updateTeachDialog .dialog-con .item .label {
    color: #1D2129;
    font-size: 14px;
    margin-right: 14px;
}

#updateTeachDialog .dialog-con .j-search-con {
    display: flex;
    align-items: center;
    position: relative;
    width: 240px;
    cursor: pointer;
}

#updateTeachDialog .dialog-con .j-search-con .j-select-year {
    left: 0;
}

#updateTeachDialog .dialog-con .j-search-con input {
    width: 100%;
    height: 34px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    padding: 0 20px 0 10px;
    box-sizing: border-box;
    font-size: 14px;
    cursor: pointer;
}

#updateTeachDialog .dialog-con .j-search-con input::placeholder {
    color: #86909C;
}

#updateTeachDialog .dialog-con .j-search-con .j-arrow {
    width: 10px;
    height: 10px;
    background: url(../images/down-icon.png) no-repeat center;
    position: absolute;
    right: 12px;
    top: 12px;
}

#updateTeachDialog .dialog-con .j-search-con .j-arrow.j-arrow-slide {
    transform: rotate(180deg);
}

#updateTeachDialog .dialog-con .j-search-con .j-select-year {
    position: absolute;
    top: 40px;
    left: -1px;
    z-index: 9;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}

#updateTeachDialog .dialog-con .j-search-con .j-select-year.slideShow {
    display: block;
}

#updateTeachDialog .dialog-con .j-search-con .j-select-year .search {
    height: 36px;
    background: #F5F7FA;
    border-radius: 18px;
    margin: 11px 10px;
}

#updateTeachDialog .dialog-con .j-search-con .j-select-year .search input {
    border: none;
    width: 176px;
    background: transparent;
    height: 36px;
    line-height: 36px;
    padding-left: 14px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    float: left;
}

#updateTeachDialog .dialog-con .j-search-con .j-select-year .search span {
    cursor: pointer;
    float: left;
    width: 16px;
    height: 16px;
    background: url(../images/search-icon.png) no-repeat center;
    margin-top: 10px;
}

#updateTeachDialog .dialog-con .j-search-con .j-select-year .all-selects {
    line-height: 17px;
    margin-bottom: 4px;
    height: 17px;
    padding: 0 14px;
    font-size: 12px;
    color: #6B89B3;
    cursor: pointer;
    user-select: none;
}

#updateTeachDialog .dialog-con .j-search-con .j-select-year ul {
    overflow: hidden;
    max-height: 200px;
    overflow-y: auto;
}

#updateTeachDialog .dialog-con .j-search-con .j-select-year ul li {
    line-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    text-indent: 16px;
    cursor: pointer;
    font-size: 14px;
    color: #4E5969;
    box-sizing: border-box;
    padding-right: 30px;
    background-color: #ffffff;
    background-image: url("../images/check-icon.png");
    background-repeat: no-repeat;
    background-position: 96% center;
}

#updateTeachDialog .dialog-con .j-search-con .j-select-year ul li:hover {
    background-color: #E1EBFF;
    color: #4D88FF;
    font-weight: 500;
}

#updateTeachDialog .dialog-con .j-search-con .j-select-year ul li.active {
    background-color: #E1EBFF;
    background-image: url("../images/check-cur.png");
    color: #4D88FF;
    font-weight: 500;
}

#updateTeachDialog .dialog-con .j-search-con.single-box .j-select-year ul li {
    background-image: url("../images/radio-icon.png");
}

#updateTeachDialog .dialog-con .j-search-con.single-box .j-select-year ul li.active {
    background-image: url("../images/radio-cur-icon.png");
}

#addCourse {
    width: 1297px;
    display: none;
}

#addCourse .dialog-con {
    padding: 0 80px;
}

#addCourse .dialog-con .layui-input-block {
    margin-left: 0;
}

#addCourse .dialog-con .form-course {
    display: flex;
    align-items: center;
}

#addCourse .dialog-con .form-course .layui-inline-wrap {
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

#addCourse .dialog-con .courseList {
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
}

#addCourse .dialog-con .courseList .layui-form {
    margin: 4px 0 31px;
}

#addCourse .dialog-con .courseList #courseList,
#addCourse .dialog-con .courseList .layui-table-view {
    width: 1137px;
    border: unset;
}

#addCourse .dialog-con .courseList #courseList .layui-table-box,
#addCourse .dialog-con .courseList .layui-table-view .layui-table-box {
    border: 1px solid #e8ebf3;
}

#addCourse .dialog-con .courseList .layui-table-page {
    text-align: center;
    border-top: unset;
    padding: 0;
    margin-top: 20px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span {
    border: 1px solid #E5E6EB;
    border-radius: 0;
    line-height: 32px;
    margin-bottom: 0;
    width: 41px;
    height: 32px;
    box-sizing: border-box;
    font-size: 14px;
    color: #4E5969;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a .layui-laypage-em,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span .layui-laypage-em {
    border-radius: 0;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-prev,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-prev {
    width: auto;
    border-radius: 2px 0 0 2px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-next,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-next {
    width: auto;
    border-radius: 0 2px 2px 0;
    margin-right: 16px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-skip,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-skip {
    color: #86909C;
    margin-right: 16px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-skip input,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-skip input {
    width: 41px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #E5E6EB;
    margin: 0 8px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-skip .layui-laypage-btn,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-skip .layui-laypage-btn {
    border-radius: 2px;
    width: 60px;
    height: 32px;
    color: #4E5969;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-count,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-count {
    margin-right: 16px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-limits select,
#addCourse .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-limits select {
    width: 104px;
    height: 30px;
    color: #4E5969;
    padding: 0;
    text-align: center;
    border-radius: 2px;
}

#addCourse .dialog-con .courseList .layui-table-page .layui-laypage .layui-laypage-next ~ * {
    border: none;
    width: auto;
}

#addCourse .dialog-con .courseList .selCourse {
    position: absolute;
    left: 0;
    bottom: 15px;
    color: #86909C;
    font-size: 14px;
    line-height: 40px;
    display: flex;
    align-items: center;
}

#addCourse .dialog-con .courseList .selCourse em {
    color: #4d88ff;
    padding: 0 4px;
}

#addCourse .dialog-con .courseList .selCourse .z-check {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #4E5969;
    margin-right: 16px;
}

#addCourse .dialog-con .courseList .selCourse .z-check .check {
    width: 28px;
    height: 28px;
    cursor: pointer;
    margin-right: 6px;
    background: url(../../images/cultivation/check.png) no-repeat center;
    background-size: 28px;
}

#addCourse .dialog-con .courseList .selCourse .z-check .check.checked {
    background: url(../../images/cultivation/check1.png) no-repeat center;
}

#addCourse .dialog-con .courseList .refresh {
    font-size: 14px;
    color: #4D88FF;
    padding-left: 20px;
    background: url('../../images/cultivation/refresh.png') no-repeat left center;
    background-size: 16px;
    position: absolute;
    right: 0;
    bottom: 15px;
    line-height: 40px;
    cursor: pointer;
}

#addCourse .form-course {
    display: flex;
    flex-wrap: wrap;
    margin: 14px 0 0;
}

#addCourse .form-course .layui-inline {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    margin-right: 24px;
}

#addCourse .form-course .layui-inline:last-child .layui-form-label {
    width: 70px;
    text-align: left;
}

#addCourse .form-course .layui-inline .layui-input-inline {
    margin-right: 0;
}

#addCourse .form-course .layui-inline .layui-input-inline .layui-input {
    width: 240px;
    border-radius: 4px;
}

#addCourse .form-course .layui-inline .layui-input-inline .layui-input::placeholder {
    color: #86909C;
}

#addCourse .form-course .layui-inline .layui-input-inline .layui-form-select {
    width: 100%;
}

#addCourse .form-course .layui-form-label {
    width: auto;
    white-space: nowrap;
    padding-right: 14px;
}

#addCourse .form-course .layui-inline-button {
    display: block;
    position: relative;
    margin-right: 0;
    width: 120px;
}

#addCourse .form-course .layui-inline-button::after {
    content: "";
    width: 1px;
    height: 84px;
    background-color: #E8EBF3;
    position: absolute;
    top: 0;
    left: 0;
}

#addCourse .form-course .layui-inline-button .layui-btn {
    width: 96px;
    height: 34px;
    margin-left: 24px;
}

#addCourse .form-course .layui-inline-button .layui-btn:first-child {
    margin-bottom: 16px;
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
}

#releaseDialog,
#timeDialog{
    width: 520px;
    display: none;
}

#releaseDialog .dialog-con,
#timeDialog .dialog-con{
    padding: 40px 80px;
}

#releaseDialog .dialog-con img,
#timeDialog .dialog-con img{
    display: block;
    width: 48px;
    height: 48px;
    margin: 0 auto 20px;
}

#releaseDialog .dialog-con p,
#timeDialog .dialog-con p{
    text-align: center;
    color: #1D2129;
    font-size: 16px;
}

#releaseDialog .dialog-con p:last-child,
#timeDialog .dialog-con p:last-child{
    margin-top: 4px;
}

#setTipSuccessDialog {
    width: 560px;
    overflow: hidden;
    display: none;
}

#setTipSuccessDialog .dialog-con {
    padding: 40px 100px;
}

#setTipSuccessDialog .dialog-con img {
    display: block;
    width: 48px;
    height: 48px;
    margin: 0 auto 20px;
}

#setTipSuccessDialog .dialog-con h5 {
    color: #1D2129;
    text-align: center;
    font-size: 16px;
    margin-bottom: 4px;
}

#setTipSuccessDialog .dialog-con p {
    color: #4E5969;
    text-align: center;
    font-size: 16px;
}

#setTipSuccessDialog .dialog-con #tipSuccessBtn {
    width: 88px;
    height: 34px;
    border-radius: 18px;
    background: #4D88FF;
    box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
    color: #ffffff;
    outline: none;
    display: block;
    margin: 32px auto 0;
    border: none;
    cursor: pointer;
}

#batchEditDialog {
    width: 520px;
    display: none;
    overflow: unset;
}

#batchEditDialog .dialog-con {
    padding: 40px 80px;
    overflow: unset;
}

#batchEditDialog .dialog-con .item {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

#batchEditDialog .dialog-con .item:last-child {
    margin-bottom: 0;
}

#batchEditDialog .dialog-con .item .label {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #1D2129;
    width: 106px;
    margin-right: 14px;
}

#batchEditDialog .dialog-con .item .label span {
    width: 16px;
    height: 16px;
    background: url('../images/tips.png') no-repeat center;
    margin-left: 6px;
    cursor: pointer;
}

#loadingDialog {
    width: 520px;
    padding: 40px 80px;
    box-sizing: border-box;
    position: relative;
    display: none;
}

#loadingDialog .close-btn {
    width: 16px;
    height: 16px;
    background: url('../images/close.png') no-repeat center;
    background-size: 16px;
    cursor: pointer;
    position: absolute;
    right: 16px;
    top: 16px;
}

#loadingDialog img {
    width: 48px;
    display: block;
    margin: 0 auto 24px;
    animation: rotate 2s linear infinite;
}

#loadingDialog p {
    color: #1D2129;
    font-size: 16px;
    text-align: center;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.layui-layer-page .layui-layer-content {
    overflow: unset;
}

.layui-form-checked.layui-checkbox-disabled:hover {
    border-color: #eee !important;
}

.dialog .dialog-con .layui-form-checked.layui-checkbox-disabled i {
    border-color: #eee !important;
}

.dialog .dialog-con .layui-form-checked.layui-checkbox-disabled i:hover {
    border-color: #eee !important;
}

.layui-layer-tips {
    width: auto !important;
    max-width: 500px;
    margin-left: -32px;
}

.layui-layer-tips .layui-layer-content {
    background-color: rgba(32, 32, 32, 0.8);
    border-radius: 6px;
    box-shadow: unset;
    font-size: 14px;
}

.layui-layer-tips i.layui-layer-TipsB,
.layui-layer-tips i.layui-layer-TipsT {
    left: 34px;
    border-right-color: rgba(32, 32, 32, 0.8);
    transform: rotate(-90deg);
    bottom: -11px;
    border-width: 6px;
}

.container .major-opt .major-button .course-statistics {
    flex: 1;
    display: flex;
    align-items: center;
    margin-top: 14px;
}
.container .major-opt .major-button .course-statistics li {
    margin-right: 24px;
}