body {
    background-color: #F7F8FA;
}

.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.flex {
    display: flex;
    align-items: center;
}

.j-search-con {
    display: flex;
    align-items: center;
    position: relative;
    width: 240px;
    cursor: pointer;
}

.j-search-con .j-select-year {
    left: 0;
}

.j-search-con input {
    width: 100%;
    height: 34px;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    padding: 0 20px 0 10px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    font-size: 14px;
    cursor: pointer;
}

.j-search-con input::placeholder {
    color: #86909C;
}

.j-search-con .j-arrow {
    width: 10px;
    height: 10px;
    background: url(../../images/examination/down-icon.png) no-repeat center;
    position: absolute;
    right: 12px;
    top: 12px;
}

.j-search-con .j-arrow.j-arrow-slide {
    transform: rotate(180deg);
}

.j-search-con .j-select-year {
    position: absolute;
    top: 40px;
    left: -1px;
    z-index: 9;
    width: 100%;
    min-width: 200px;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}

.j-search-con .j-select-year.slideShow {
    display: block;
}

.j-search-con .j-select-year .search {
    height: 36px;
    background: #F5F7FA;
    border-radius: 18px;
    margin: 11px 10px;
}

.j-search-con .j-select-year .search input {
    border: none;
    width: 176px;
    background: transparent;
    height: 36px;
    line-height: 36px;
    padding-left: 14px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    float: left;
}

.j-search-con .j-select-year .search span {
    cursor: pointer;
    float: left;
    width: 16px;
    height: 16px;
    background: url(../../images/examination/search-icon.png) no-repeat center;
    margin-top: 10px;
}

.j-search-con .j-select-year .all-selects {
    line-height: 17px;
    margin-bottom: 4px;
    height: 17px;
    padding: 0 14px;
    font-size: 12px;
    color: #6B89B3;
    cursor: pointer;
    user-select: none;
}

.j-search-con .j-select-year ul {
    overflow: hidden;
    max-height: 200px;
    overflow-y: auto;
}

.j-search-con .j-select-year ul li {
    line-height: 40px;
    text-align: left;
    text-indent: 16px;
    cursor: pointer;
    font-size: 14px;
    color: #4E5969;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-right: 30px;
    background-color: #ffffff;
    background-image: url("../../images/examination/check-icon.png");
    background-repeat: no-repeat;
    background-position: 96% center;
}

.j-search-con .j-select-year ul li:hover {
    background-color: #E1EBFF;
    color: #4D88FF;
    font-weight: 500;
}

.j-search-con .j-select-year ul li.active {
    background-color: #E1EBFF;
    background-image: url("../../images/examination/check-cur.png");
    color: #4D88FF;
    font-weight: 500;
}

.j-search-con.single-box .j-select-year ul li {
    background-image: url("../../images/examination/radio-icon.png");
}

.j-search-con.single-box .j-select-year ul li.active {
    background-image: url("../../images/examination/radio-cur-icon.png");
}

.z-main {
    /*max-width: 1660px;*/
    margin: 0 auto 20px;
    background-color: #ffffff;
    overflow: hidden;
    min-height: calc(100vh - 28px);
}

.z-main .z-title {
    padding: 29px 0 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #E8EBF1;
}

.z-main .z-title h3 {
    font-size: 16px;
    line-height: 22px;
    color: #1D2129;
    padding-left: 9px;
    position: relative;
    margin-left: 30px;
}

.z-main .z-title h3::after {
    content: "";
    width: 3px;
    height: 16px;
    background: #4D88FF;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 3px;
}

.z-main .z-title span {
    color: #86909C;
    font-size: 14px;
    margin-left: 16px;
    margin-top: 2px;
}

.z-main .z-con {
    display: flex;
    overflow: hidden;
    position: relative;
}

.z-main .z-con .z-menu {
    width: 342px;
    border-right: 1px solid #E8EBF1;
    overflow: hidden;
}

.z-main .z-con .z-menu .z-nav {
    height: 36px;
    border-radius: 4px;
    overflow: hidden;
    color: #1D2129;
    font-size: 16px;
    margin: 29px 30px 24px;
}

.z-main .z-con .z-menu .z-nav ul {
    display: flex;
    align-items: center;
    height: 36px;
}

.z-main .z-con .z-menu .z-nav ul li {
    flex: 1;
    text-align: center;
    line-height: 36px;
    cursor: pointer;
    background: #F2F3F5;
}

.z-main .z-con .z-menu .z-nav ul li:first-child {
    margin-right: 2px;
}

.z-main .z-con .z-menu .z-nav ul li.active {
    background: #4D88FF;
    color: #FFFFFF;
}

.z-main .z-con .z-menu .z-search {
    display: flex;
    align-items: center;
    margin: 0 30px 24px;
    position: relative
}

.z-main .z-con .z-menu .z-search .teacher-name {
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    width: 282px;
    height: 36px;
    padding: 0 10px 0 30px;
    box-sizing: border-box;
    /*margin-left: 8px;*/
    font-size: 14px;
    background: url('../../images/examination/search-icon.png') no-repeat 10px center;
}

.z-main .z-con .z-menu .z-search:hover span{
    width: 20px;
    height: 20px;
    border-radius: 10px;
    position: absolute;
    right: 10px;
    background: #999999 url("../../images/examination/close.png") no-repeat center;
    cursor: pointer;
}

.z-main .z-con .z-menu .z-search .teacher-name::placeholder {
    color: #86909C;
}

.z-main .z-con .z-menu .z-box {
    overflow: hidden;
}

.z-main .z-con .z-menu .z-box .title {
    margin: 0 30px;
    background: #F1F3F6;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 4px;
    color: #86909C;
    font-size: 14px;
}

.z-main .z-con .z-menu .z-box .con-list {
    margin-left: 30px;
    overflow: hidden;
    overflow-y: auto;
    height: calc(100vh - 280px);
}

.z-main .z-con .z-menu .z-box .con-list ul {
    margin-right: 30px;
    width: 282px;
}

.z-main .z-con .z-menu .z-box .con-list ul li {
    height: 48px;
    line-height: 48px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 29.5px;
    box-sizing: border-box;
}

.z-main .z-con .z-menu .z-box .con-list ul li h3 {
    flex: 1;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap
}

.z-main .z-con .z-menu .z-box .con-list ul li span {
    text-align: right;
    min-width: 50px;
    position: relative;
}

.z-main .z-con .z-menu .z-box .con-list ul li span::after {
    content: "";
    width: 2px;
    height: 16px;
    background-color: #4D88FF;
    position: absolute;
    left: 0;
    top: 16px;
}

.z-main .z-con .z-menu .z-box .con-list ul li:nth-child(2n) {
    background: #FAFBFC;
}

.z-main .z-con .z-menu .z-box .con-list ul li.active {
    background: #E1EBFF;
    border-radius: 4px;
}

.z-main .z-con .z-menu .z-box .con-list ul li.active h3 {
    font-weight: bold;
}

.z-main .z-con .z-menu .z-box .con-list ul li.active span {
    color: #4D88FF;
}

.z-main .z-con .z-table {
    flex: 1;
    overflow: hidden;
    margin: 16px 30px 20px;
}

.z-main .z-con .z-table .z-btn {
    display: flex;
    justify-content: flex-end;
}

.z-main .z-con .z-table .z-btn span {
    border: 1px solid #4D88FF;
    box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
    border-radius: 4px;
    height: 36px;
    line-height: 36px;
    background: #4D88FF;
    padding: 0 20px;
    color: #ffffff;
    font-size: 14px;
    margin-left: 12px;
    cursor: pointer;
    margin-bottom: 16px;
}

.z-main .z-con .z-table .z-btn span:hover {
    border: 1px solid #409eff;
    background: #409eff;
}

.z-main .z-con .z-table .z-btn #clearData {
    border: 1px solid #F33131;
    background: #F33131;
}

.z-main .z-con .z-table .z-btn #clearData:hover {
    border: 1px solid #f56c6c;
    background: #f56c6c;
}

.z-main .z-con .z-table .t-title {
    margin-top: 8px;
    margin-bottom: 4px;
}

.z-main .z-con .z-table .t-room {
    margin-bottom: 8px;
    line-height: 28px;
    /* text-overflow: ellipsis; */
    /* overflow: hidden; */
    /* white-space: nowrap; */
    width: 94%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px
}

.z-main .z-con .z-table .t-room h3 {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    justify-content: space-between;
}

.z-main .z-con .z-table .t-room:hover {
    color: #4D88FF;
    background: #E1EBFF;
    border-radius: 15px;
    position: relative;
    box-sizing: border-box;
}

.z-main .z-con .z-table .t-room .close {
    width: 14px;
    height: 14px;
    background: url('../../images/examination/icon-del.png') no-repeat center;
    background-size: 14px;
    cursor: pointer;
    display: none;
}

.z-main .z-con .z-table .t-room:hover .close {
    display: block;
}

.z-main .z-con .z-table .t-class span {
    display: block;
}

.z-main .z-con .z-table .t-teacher {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.z-main .z-con .z-table .t-teacher .teacher {
    margin-bottom: 8px;
    line-height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    padding-right: 20px;
    position: relative;
}

.z-main .z-con .z-table .t-teacher .teacher h4 {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    justify-content: space-between;
}

.z-main .z-con .z-table .t-teacher .teacher:last-child {
    margin-right: 0;
}

.z-main .z-con .z-table .t-teacher .teacher:hover {

    color: #4D88FF;
    background: #E1EBFF;
    border-radius: 15px;

}

.z-main .z-con .z-table .t-teacher .teacher .close {
    position: absolute;
    top: 7px;
    right: 0;
    width: 14px;
    height: 14px;
    background: url('../../images/examination/icon-del.png') no-repeat center;
    background-size: 14px;
    cursor: pointer;
    display: none;
}

.z-main .z-con .z-table .t-teacher .teacher:hover .close {
    display: block;
}

.z-main .z-con .z-table .blue {
    color: #4D88FF;
}

.layui-table-view .layui-table td {
    height: 109px;
}

.layui-table-cell {
    font-size: 14px;
    padding: 0;
    height: auto;
    overflow: visible;
    text-overflow: inherit;
    white-space: normal;
    word-break: break-all;
    color: #4E5969;
    overflow: hidden;
}

.layui-table-cell .t-con {
    overflow: hidden;
    width: 100%;
}

#slideMenu {
    position: absolute;
    left: 288px;
    bottom: 8px;
    width: 24px;
    height: 24px;
    background: url('../../images/examination/fold.png') no-repeat center;
    background-size: 24px;
    cursor: pointer;
    z-index: 99;
}

.layui-table thead tr,
.layui-table-header {
    background: #F1F3F6;
}

table tbody tr:nth-child(even) {
    background: #FAFBFC;
}

.layui-table-view .layui-table td[lay-event="setCell"] .layui-table-cell {
    display: flex;
    align-items: center;
}

.layui-table-view .layui-table td[lay-event="setCell"].gray {
    background: #F2F3F5;
}

.layui-table-view .layui-table td[lay-event="setCell"].ban {
    background-size: 30px 30px; /* 控制条纹的大小 */
    background-color: #FFFFFF;
    background-image: linear-gradient(-45deg, rgba(201, 201,201, .2) 25%, transparent 25%,
    transparent 50%, rgba(201, 201, 201, .2) 50%, rgba(201, 201, 201, .2) 75%,
    transparent 75%, transparent);
}

.layui-table-view .layui-table td[lay-event="setCell"].optCell .layui-table-cell {
    border: 1px solid #4D88FF !important;
    box-sizing: border-box;
    box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
    height: 100%;
}

.layui-table-view .layui-table td {
    padding: 0;
}

