.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.flex {
  display: flex;
  align-items: center;
}
body {
  background-color: #f7f8fa;
}
.main {
  margin: 20px auto;
  border-radius: 8px;
  min-height: calc(100vh - 40px);
  min-width: 1000px;
  max-width: 1660px;
  background-color: #ffffff;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  overflow: hidden;
}
.main .m-top {
  width: 100%;
  height: 57px;
  border-bottom: 1px solid #e8eaf1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top .back {
  width: 60px;
  height: 24px;
  background: url(../images/back-icon.png) no-repeat left center;
  cursor: pointer;
  margin-right: 10px;
}
.main .m-top .title {
  font-size: 16px;
  color: #1d2129;
  padding-left: 9px;
  position: relative;
}
.main .m-top .title::after {
  content: "";
  width: 3px;
  height: 16px;
  background-color: #4d88ff;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 3px;
}
.main .item {
  padding: 0 30px;
}
.main .item .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 21px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .item .i-top h3 {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #6581BA;
  position: relative;
  display: block;
  margin-right: 24px;
}
.main .item .i-top h3:after {
  content: "";
  position: absolute;
  left: 0;
  top: 1px;
  background: #6581BA;
  border-radius: 2px;
  width: 3px;
  height: 18px;
}
.main .item .i-top .arrow {
  position: relative;
  font-weight: 400;
  width: 12px;
  height: 12px;
  cursor: pointer;
  background: url(../../images/cultivation/arrow-icon.png) no-repeat right center;
  transform: rotate(-80deg);
}
.main .item .i-top .arrow.slide {
  transform: rotate(0);
}
.main .item .i-top-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.main .item .i-top-flex .i-title {
  display: flex;
  align-items: center;
}
.main .item .i-top-flex .i-right .i-progress {
  font-size: 14px;
  color: #1D2129;
  margin-right: 24px;
}
.main .item .i-top-flex .i-right .i-progress .progress {
  position: relative;
  width: 166px;
  height: 3px;
  border-radius: 2px;
  background-color: #E5E6EB;
  margin-left: 14px;
  margin-right: 8px;
}
.main .item .i-top-flex .i-right .i-progress .progress .progress-con {
  height: 3px;
  background-color: #4D88FF;
  border-radius: 2px;
}
.main .item .i-top-flex .i-right .refresh {
  color: #4d88ff;
  padding-left: 20px;
  background: url('../../images/cultivation/refresh.png') no-repeat left center;
  background-size: 16px;
  cursor: pointer;
}
.main .item .stu-mes {
  display: flex;
  align-items: center;
  background: url('../../images/cultivation/bg.png') no-repeat center;
  background-size: cover;
  padding: 24px 32px;
  border-top: 4px solid #4D88FF;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 58px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .item .stu-mes .photo {
  border-radius: 4px;
  background: #FFF;
  width: 144px;
  height: 184px;
  margin-right: 32px;
}
.main .item .stu-mes .photo img {
  display: block;
  width: 120px;
  margin: 11px;
  border-radius: 4px;
}
.main .item .stu-mes .stu-info {
  color: #1D2129;
  flex: 1;
}
.main .item .stu-mes .stu-info .stu-name {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.main .item .stu-mes .stu-info .stu-name h3 {
  font-size: 16px;
  margin-right: 8px;
}
.main .item .stu-mes .stu-info .stu-name span {
  color: #4E5969;
  font-size: 14px;
}
.main .item .stu-mes .stu-info .stu-info-con {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 32px;
}
.main .item .stu-mes .stu-info .stu-info-con li {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-right: 24px;
}
.main .item .stu-mes .stu-info .stu-info-con li .item-con {
  color: #4E5969;
}
.main .item .stu-mes .stu-info .stu-achievement {
  display: flex;
  align-items: center;
  width: 600px;
  justify-content: space-between;
  border-radius: 4px;
  background: #FFF;
  padding: 20px 32px;
  box-sizing: border-box;
}
.main .item .stu-mes .stu-info .stu-achievement li {
  text-align: center;
}
.main .item .stu-mes .stu-info .stu-achievement li .ach-con {
  color: #4D88FF;
  font-variant-numeric: lining-nums tabular-nums;
  font-family: FZCuHeiB-B03;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}
.main .item .stu-mes .stu-info .stu-achievement li .ach-con span {
  font-family: "PingFang SC";
  font-size: 13px;
  font-weight: 400;
  padding-left: 4px;
}
.main .item .stu-mes .stu-info .stu-achievement li .ach-type {
  font-size: 14px;
  color: #4E5969;
}
.main .j-table {
  margin-bottom: 24px;
}
.main .status {
  padding: 0 10px;
  color: #ffffff;
  border-radius: 4px;
  line-height: 20px;
  display: inline-block;
  font-size: 12px;
}
.main .status.status1 {
  background-color: #C9CDD4;
}
.main .status.status2 {
  background-color: #FFB026;
}
.main .status.status3 {
  background-color: #4D88FF;
}
.main .status.status4 {
  background-color: #3EB35A;
}
.layui-table-view .layui-table td[data-field="teacherList"] .layui-table-cell {
  overflow: unset;
}
.layui-table-cell {
  height: auto;
  padding: 0 16px;
}
.layui-table-view tr.color-red {
  color: #F76560;
}
.layui-table-view .layui-table th {
  font-weight: normal;
  color: #6581BA;
}
.layui-table-view .layui-table tr th > div {
  margin: 0;
}
.layui-table-page .layui-laypage a,
.layui-table-page .layui-laypage span {
  height: 32px;
  line-height: 33px;
  font-size: 14px;
}
.layui-table-page select {
  height: 26px;
  line-height: 26px;
}
.layui-table-page .layui-laypage a,
.layui-table-page .layui-laypage span.layui-laypage-curr {
  width: 40px;
  height: 32px;
  line-height: 33px;
  padding: 0;
}
.layui-table-page .layui-laypage .layui-laypage-prev,
.layui-table-page .layui-laypage .layui-laypage-next {
  padding: 0 10px;
}
.layui-table-page .layui-laypage a {
  border: 1px solid #E5E6EB;
  border-radius: 0;
}
.layui-table-column {
  padding: 6px 30px;
  height: 50px;
}
.layui-table-page .layui-laypage .layui-laypage-curr {
  border: 1px solid #4c85fa;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  border-radius: 0;
}
/* .layui-laypage .layui-laypage-curr .layui-laypage-em {
    background: #4c85fa;
    border-radius: 4px;
} */
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #4c85fa !important;
}
.layui-laypage a:hover {
  color: #4c85fa;
}
.layui-table-page .layui-laypage span.layui-laypage-skip {
  margin-left: 16px;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-table-page .layui-laypage button,
.layui-table-page .layui-laypage input {
  border: 1px solid #e2e2e2;
  background-color: transparent;
  height: 32px;
  margin-top: 0;
}
/* .layui-laypage-skip input {
    background: #ffffff;
    border: 1px solid #e7eaf1;
    color: #4d4d4d;
    border-radius: 2px;
}
 */
.layui-laypage button {
  background: #f5f8fb;
  border: 1px solid #e7eaf1;
  color: #595959;
  border-radius: 2px;
}
.layui-table-page {
  text-align: center;
}
.select-input {
    width: 200px;
    height: 34px;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    cursor: pointer;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
}
.select-input em {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 10px;
    height: 10px;
    background: url(../../images/cultivation/icon-arrow1.png) no-repeat center;
    background-size: 10px;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}
.select-input .name {
    font-size: 14px;
    color: #86909C;
    padding-left: 10px;
    width: 200px;
    height: 32px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    line-height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.select-input .name.ckd {
    color: #1D2129;
}
.select-input.clicked em {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    background: url(../../images/cultivation/icon-arrow1.png) no-repeat center;
    background-size: 10px;
}
.select-input.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
}
.select-input .select-dropdown {
    width: inherit;
    left: -1px;
    margin: 5px 0;
    padding: 6px 0;
    background-color: #fff;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
    border-radius: 8px;
    position: absolute;
    transform: translate(0, -50px);
    -webkit-transform: translate(0, -50px);
    z-index: 900;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
    z-index: -1;
}
.select-input .select-dropdown .dropdown-list {
    max-height: 320px;
    overflow: auto;
}
.select-input .select-dropdown .dropdown-list li {
    margin: 0;
    line-height: normal;
    padding: 0 16px;
    clear: both;
    color: #131B26;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
}
.select-input .select-dropdown .dropdown-list li span {
    display: block;
    padding-left: 24px;
    background: url(../../images/cultivation/check-icon.png) no-repeat left center;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}
.select-input .select-dropdown .dropdown-list li:hover {
    background-color: #F5F7FA;
}
.select-input .select-dropdown .dropdown-list li.cur {
    color: #616EE6;
}
.select-input .select-dropdown .dropdown-list li.cur span {
    background: url(../../images/cultivation/check-cur.png) no-repeat left center;
}
.select-input .select-dropdown .confirm {
    line-height: 30px;
    text-align: right;
    cursor: pointer;
    padding: 0 16px;
    color: #4D88FF;
}
.select-input .select-search {
    height: 36px;
    border-bottom: 1px solid #dddddd;
}
.select-input .select-search input {
    height: 30px;
    padding-left: 22px;
    box-sizing: border-box;
    background: url('../../images/cultivation/icon-search.png') no-repeat left center;
    background-size: 18px;
    display: block;
    width: 94%;
    margin: 0 auto;
    border: none;
    font-size: 14px;
    outline: none;
}
.select-input .select-search input:focus {
    background: url('../../images/cultivation/icon-search-focus1.png') no-repeat left center;
}
.select-input .select-search input::placeholder {
    color: #999999;
}
::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 6px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 6px;
}
::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 6px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.005);
    background: #e1dfdf;
}
::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    border-radius: 6px;
    background: #FFFFFF;
}
.filter-wrapper {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
}
.filter-wrapper .lable {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    margin-right: 24px;
    margin-bottom: 16px;
}
.filter-wrapper .lable .lable-name {
    color: #1d2129;
    margin-right: 16px;
    line-height: 34px;
}
.filter-wrapper .lable .btn {
    width: 88px;
    text-align: center;
    line-height: 32px;
    height: 32px;
    border-radius: 4px;
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
    background-color: #4d88ff;
    color: #ffffff;
    border: 1px solid #4d88ff;
    cursor: pointer;
}
