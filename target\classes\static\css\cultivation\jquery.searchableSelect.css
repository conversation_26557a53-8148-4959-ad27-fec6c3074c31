/*
Author: <PERSON>
E-mail: <EMAIL>
Date: 2014-11-05
*/

.searchable-select-hide {
  display: none;
}

.searchable-select {
  display: inline-block;
	width:178px;
  font-size: 14px;
  line-height: 1.428571429;
  color: #555;
  vertical-align: top;
  position: relative;
  outline: none;
  cursor:pointer;
}
.lcEnter_th .searchable-select{ width:140px}

.searchable-select-holder{
  /*padding: 0 20px 0 8px;*/
  background-color: #fff;
  background-image: none;
  border: 1px solid #E1E2E5;
  border-radius: 4px;
  height: 32px;
  line-height:32px;
  -webkit-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
  overflow:hidden; white-space:nowrap; text-overflow:ellipsis;
  padding:0 26px 0 10px;
  vertical-align: top;
  border: 1px solid #E1E2E5;
  border-radius: 4px;
  font-size: 14px;
  color: #d0d3d9;
}

.searchable-select-holder:hover{border:1px solid #CCD9F3;}

.searchable-select-fus{
	box-shadow:0px 0px 4px 0px #75a3ff; border:1px solid #CCD9F3;
}

.searchable-select-caret {
  position: absolute;
  width: 10px;
  height: 10px;
  background:url(../../images/cultivation/icon_input_sel.png) no-repeat;
  top:50%;
  right:14px;
  margin-top:-5px;
}

.searchable-select-dropdown {
  position: absolute;
  background-color: #fff;
  border: 1px solid #E1E2E5;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  padding: 4px;
  top:36px;
  left: 0;
  right: 0;
  z-index:100;
  
}

.searchable-select-input {
  margin-top: 5px;
  border: 1px solid #E1E2E5;
  outline: none;
  padding: 4px;
  width: 100%;
  box-sizing: border-box;
  width: 100%;
}

.searchable-scroll {
  margin-top: 4px;
  position: relative;
}

.searchable-scroll.has-privious {
  /*padding-top: 16px;*/
}

.searchable-scroll.has-next {
  /*padding-bottom: 16px;*/
}

.searchable-has-privious {
  top: 0;
}

.searchable-has-next {
  bottom: 0;
}

.searchable-has-privious, .searchable-has-next {
  height: 16px;
  left: 0;
  right: 0;
  position: absolute;
  text-align: center;
  z-index: 10;
  background-color: white;
  line-height: 8px;
  cursor: pointer;
  display:none;
}

.searchable-select-items {
  max-height: 180px;
  overflow-y: scroll;
  position: relative;
}

.searchable-select-items::-webkit-scrollbar {
  display: none;
}

.searchable-select-item {
  padding: 5px 5px;
  cursor: pointer;
  min-height: 30px;
  box-sizing: border-box;
    transition: all 1s ease 0s;
}

.searchable-select-item.hover {

  background: #555;
  color: white;
}

.searchable-select-item.selected {
  background: #5287FF;
  color: white;
}