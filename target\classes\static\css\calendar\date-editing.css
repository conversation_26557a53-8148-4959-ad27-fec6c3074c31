.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
input[type=search] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
  font-family: inherit;
  font-size: 100%;
}
input::-webkit-search-decoration,
input::-webkit-search-cancel-button {
  display: none;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #86909C;
  font-size: 0.14rem;
}
.hide {
  display: none;
}
body {
  background: #F5F5F8;
}
.hide {
  display: none;
}
.suspend-classes {
  display: none;
}
.sup-lesson-time {
  display: none;
}
.sup-lesson-range {
  display: none;
}
.main {
  overflow: hidden;
  margin-top: 0.64rem;
  overflow-y: auto;
  z-index: 10;
  padding-bottom: 1.04rem;
  background: #F5F5F8;
}
.main .m-top {
  background-color: #fff;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}
.main .m-top .lable {
  width: 100%;
  height: 0.48rem;
  position: relative;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0 0.15rem;
}
.main .m-top .lable .name {
  font-weight: 400;
  font-size: 0.14rem;
  color: #4E5969;
  flex-shrink: 0;
}
.main .m-top .lable .input input {
  outline: none;
  border: none;
  background: transparent;
  height: 0.48rem;
  display: block;
  text-align: right;
  font-size: 0.14rem;
  color: #4E5969;
}
.main .m-top .lable:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  border-bottom: 1px solid #EBEBEB;
  transform: scaleY(0.5);
  -webkit-transform: scaleY(0.5);
  -moz-transform: scaleY(0.5);
  -o-transform: scaleY(0.5);
}
.main .m-top .lable:last-child:after {
  display: none;
}
.main .m-con {
  padding: 0.15rem 0.15rem 0;
}
.main .m-con ul {
  width: 100%;
  overflow: hidden;
}
.main .m-con ul li {
  width: 137.1%;
  margin-bottom: 0.15rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  zoom: 1;
}
.main .m-con ul li.cur .c-con .radio:after {
  background: url(../images/check-cur-icon.png) no-repeat left center;
  background-size: 0.16rem;
}
.main .m-con ul li:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.main .m-con ul li.swipeleft {
  transform: translateX(-1.28rem);
  -webkit-transform: translateX(-1.28rem);
}
.main .m-con ul li .c-con {
  background: #FFFFFF;
  box-shadow: 0px 0px 0.05rem rgba(0, 0, 0, 0.1);
  border-radius: 0.04rem;
  min-height: 1.46rem;
  padding: 0.14rem 0.12rem;
  width: 73%;
  float: left;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-con ul li .c-con .radio {
  padding-left: 0.28rem;
  position: relative;
  font-weight: 400;
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #1D2129;
  margin-bottom: 0.1rem;
}
.main .m-con ul li .c-con .radio:after {
  content: '';
  position: absolute;
  left: 0;
  top: 0.02rem;
  width: 0.16rem;
  height: 0.16rem;
  background: url(../images/check-icons.png) no-repeat left center;
  background-size: 0.16rem;
}
.main .m-con ul li .c-con .c-box {
  overflow: hidden;
}
.main .m-con ul li .c-con .c-box .lable {
  float: left;
  width: 50%;
  font-weight: 400;
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #4E5969;
  margin-bottom: 0.04rem;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .m-con ul li .c-con .c-box .lable .name {
  font-weight: 400;
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #4E5969;
  flex-shrink: 0;
}
.main .m-con ul li .c-con .c-box .lable .detail {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 400;
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #86909C;
}
.main .m-con ul li .operate {
  width: 27%;
  min-height: 1.46rem;
  float: left;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  padding-left: 0.12rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-con ul li .operate .edit {
  width: 0.52rem;
  height: 0.52rem;
  background: #4D88FF;
  box-shadow: 0px 0px 0.08rem rgba(87, 169, 250, 0.3);
  border-radius: 50%;
  font-size: 0.14rem;
  color: #FFFFFF;
  text-align: center;
  line-height: 0.52rem;
}
.main .m-con ul li .operate .delete {
  width: 0.52rem;
  height: 0.52rem;
  background: #F76560;
  box-shadow: 0px 0px 0.08rem rgba(87, 169, 250, 0.3);
  border-radius: 50%;
  margin-left: 0.12rem;
  font-size: 0.14rem;
  color: #FFFFFF;
  text-align: center;
  line-height: 0.52rem;
}
.school-bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1.04rem;
  background-color: #fff;
  box-shadow: 0px -0.08rem 0.12rem -0.04rem rgba(184, 184, 210, 0.2);
}
.school-bottom .handle {
  width: 100%;
  height: 0.7rem;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0 0.16rem;
  position: relative;
}
.school-bottom .handle .all-select {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.school-bottom .handle .all-select span {
  padding-left: 0.32rem;
  background: url(../images/radio-icon.png) no-repeat left center;
  background-size: 0.24rem;
  font-size: 0.16rem;
  color: #1D2129;
}
.school-bottom .handle .all-select span.cur {
  background: url(../images/radio-cur-icon.png) no-repeat left center;
  background-size: 0.24rem;
}
.school-bottom .handle .all-select .nus {
  font-size: 0.16rem;
  color: #1D2129;
  margin-left: 0.08rem;
}
.school-bottom .handle .btns {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-end;
}
.school-bottom .handle .btns span.cancle {
  width: 0.8rem;
  height: 0.4rem;
  background: #FFFFFF;
  border: 0.01rem solid #C9CDD4;
  border-radius: 0.2rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  text-align: center;
  line-height: 0.38rem;
  font-size: 0.14rem;
  color: #4E5969;
}
.school-bottom .handle .btns span.confirm {
  width: 0.8rem;
  height: 0.4rem;
  background: #4D88FF;
  border-radius: 0.2rem;
  text-align: center;
  line-height: 0.4rem;
  font-size: 0.14rem;
  color: #FFFFFF;
  margin-left: 0.2rem;
}
.shows {
  display: block !important;
}
.settingMask {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1009;
}
.settingModal {
  position: absolute;
  right: 0.1rem;
  top: 45px;
  width: 1.05rem;
  background: #ffffff;
  box-shadow: 0px 0px 0.03rem 0px rgba(153, 153, 153, 0.6);
  border-radius: 0.08rem;
}
.settingModal .settingList {
  border-radius: 0.04rem;
  overflow: hidden;
  padding-left: 0;
}
.settingModal .listItem {
  text-align: center;
  height: 0.44rem;
  line-height: 0.44rem;
  font-size: 0.16rem;
  color: #333333;
}
.settingModal:after {
  position: absolute;
  content: '';
  top: -0.09rem;
  right: 0.07rem;
  width: 0.2rem;
  height: 0.1rem;
  background: url(../images/trishadow.png) center center / 0.2rem 0.1rem no-repeat;
}
.settingModal .listItem.color1 {
  color: #333333;
}
.settingModal .listItem.color2 {
  color: #F76560;
}
.settingModal .listItem.color3 {
  color: #4D88FF;
}
