.main.style1 .top .weeks .w-name span {
  color: #3EB35A;
}
.main.style1 .top .weeks .w-con .wc-top .all {
  font-size: 14px;
  color: #3EB35A;
  cursor: pointer;
}
.main.style1 .top .weeks .w-con .wc-top .cancle {
  font-size: 14px;
  color: #3EB35A;
  cursor: pointer;
}
.main.style1 .top .weeks .w-con .wc-main ul li.cur {
  background-color: #3EB35A;
}
.main.style1 .top .step-box .step.active:after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -18px;
  height: 2px;
  background-color: #3EB35A;
}
.main.style1 .top .step-box .step.active em {
  background-color: #3EB35A;
  color: #fff;
}
.main.style1 .top .oprate .mode-switch .switc-con .switch.switch-open {
  background: #3EB35A;
}
.main.style1 .top .oprate .btn-list .t-schedule span {
  display: block;
  margin-right: 4px;
  color: #3EB35A;
}
.main.style1 .top .oprate .btn-list .t-schedule.qxfb span {
  display: block;
  margin-right: 4px;
  color: #f76560;
}
.main.style1 .top .oprate .btn-list .t-schedule.qxfb em {
  color: #f76560;
}
.main.style1 .top .oprate .btn-list .t-schedule em {
  font-size: 14px;
  color: #3EB35A;
}
.main.style1 .top .oprate .btn-list .t-schedule .select-dropdown {
  display: none;
  position: absolute;
  right: 0;
  top: 36px;
  background: #FFF;
  border-radius: 4px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  width: 144px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  color: #3EB35A;
  z-index: 999;
}
.main.style1 .top .oprate .btn-list .t-schedule:hover .select-dropdown {
  display: block;
}
.main.style1 .top .oprate .btn-list .t-schedule .select-dropdown ul li span {
  color: #3EB35A;
  display: block;
  margin-right: 4px;
}
.main.style1 .top .oprate .btn-list .t-schedule .select-dropdown ul li em {
  font-size: 14px;
  color: #3EB35A;
}
.main.style1 .con .c-left .cl-con .tab ul li.cur {
  background-color: #3EB35A;
  color: #fff;
  border-radius: 18px;
}
.main.style1 .con .c-left .shake-hands span {
  display: inline-block;
  font-size: 12px;
  color: #3EB35A;
  position: relative;
}
.main.style1 .con .c-left .schedule-list ul li .manageName:hover {
  background: #B2F0B7 !important;
  color: #1d2129;
  border-radius: 4px;
  color: #3EB35A;
}
.main.style1 .con .c-left .schedule-list ul li .manageName:hover .arrowRight {
  color: #3EB35A;
}
.main.style1 .con .c-left .schedule-list ul li .manageName.cur:hover .arrowRight {
  color: #fff;
}
.main.style1 .con .c-left .schedule-list ul li .manageName.cur {
  border-radius: 4px;
  background: #3EB35A !important;
  color: #ffffff;
}
.main.style1 .con .c-left .schedule-list ul li.level1:nth-child(2n) .manageName.cur {
  background: #3EB35A !important;
}
.main.style1 .con .c-left .schedule-list ul li.level1:nth-child(2n) .level2 .manageName.cur {
  background: #3EB35A !important;
}
.main.style1 .con .c-left .schedule-list ul li.level1:nth-child(2n) .level2:nth-child(2n) .manageName.cur {
  background: #3EB35A !important;
}
.main.style1 .con .c-left .schedule-list ul li.level1:nth-child(2n+1) .manageName.cur {
  background: #3EB35A !important;
}
.main.style1 .con .c-left .schedule-list ul li.level1:nth-child(2n+1) .manageName.cur:hover {
  background: #3EB35A !important;
  color: #fff;
}
.main.style1 .con .c-left .schedule-list ul li.level1:nth-child(2n+1) .level2 .manageName.cur {
  background: #3EB35A !important;
}
.main.style1 .con .c-left .schedule-list ul li.level1:nth-child(2n+1) .level2:nth-child(2n+1) .manageName.cur {
  background: #3EB35A !important;
}
.main.style1 .con .c-right .c-homeWrap .gauge-outfit {
  margin-bottom: 4px;
  border-radius: 4px;
  background: #3EB35A;
  width: 100%;
  height: 44px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 8px 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: space-between;
  align-items: flex-start;
}
.main.style1 .con .c-right .c-homeWrap .oprate .switchs .switc-con .switch.switch-open em {
  position: absolute;
  top: 2px;
  right: 2px;
  left: auto;
  background-color: #3EB35A;
}
.main.style1 .con .c-right .c-homeWrap .oprate .switchs .switc-con .switch.switch-open span {
  position: absolute;
  top: 1px;
  left: 3px;
  color: #3EB35A;
}
.main.style1 .con .c-right .c-homeWrap .oprate .btn-list .rice .select-dropdown ul li span {
  color: #3EB35A;
  display: block;
  margin-right: 4px;
}
.main.style1 .con .c-right .c-homeWrap .oprate .btn-list .rice .select-dropdown ul li:hover em {
  color: #3EB35A;
}
.main.style1 .con .c-right .c-table .tbody.m-tbody.activeing .active {
  background-color: #3EB35A;
  border-color: #3EB35A;
}
.main.style1 .con .c-right .c-table .tbody ul li .select-area:hover {
  border: 1px solid #fff;
}
.main.style1 .con .c-right .c-table .tbody ul li .select-area .oprate span {
  font-size: 16px;
  height: 16px;
  line-height: 16px;
  color: #3EB35A;
  margin-left: 4px;
  cursor: pointer;
  position: relative;
}
.main.style1 .con .c-right .c-side .gauge-outfit {
  margin-bottom: 4px;
  border-radius: 4px;
  background: #3EB35A;
  width: 100%;
  height: 44px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 8px 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: space-between;
  align-items: flex-start;
}
.main.style1 .con .c-right .c-side .gauge-outfit .title .sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #B2F0B7;
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .oprate .sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #B2F0B7;
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li:hover {
  background-color: #B2F0B7;
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .oprate .btn-list .switchs .switc-con .switch.switch-open em {
  position: absolute;
  top: 2px;
  right: 2px;
  left: auto;
  background-color: #3EB35A;
}
.main.style1 .con .c-right .c-side .oprate .btn-list .switchs .switc-con .switch.switch-open span {
  position: absolute;
  top: 1px;
  left: 3px;
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .oprate .btn-list .rice .select-dropdown ul li span {
  color: #3EB35A;
  display: block;
  margin-right: 4px;
}
.main.style1 .con .c-right .c-side .oprate .btn-list .rice .select-dropdown ul li:hover em {
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color3 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color1 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color2 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color4 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color5 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color6 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color7 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color8 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color9 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color10 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color11 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color12 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color13 .grades {
  color: #fff;
}
.main.style1 .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area .grades {
  font-size: 14px;
  width: 100%;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .c-table .tbody ul li .select-area .icon-btn span {
  color: #3EB35A;
  cursor: pointer;
  margin: 0 1px;
}
.main.style1 .con .c-right .c-side .c-table .tbody ul.active li .select-area .name {
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .c-table .tbody ul.active li .select-area .teacher {
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .c-table .tbody ul.active li .select-area .weeks {
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .c-table .tbody ul.active li .select-area .classroom {
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .c-table .tbody ul.active li .select-area .total-hours {
  color: #3EB35A;
}
.main.style1 .con .c-right .c-table .thead ul li {
  background: #D2EFD1;
}
.main.style1 .con .c-right .c-table .tbody ul li:first-child {
  background: #D2EFD1;
}
.main.style1 .con .c-right .c-side .c-table .tbody.course-manage ul li:first-child span {
  background: #D2EFD1;
}
.main.style1 .top .weeks .w-name .detail {
  color: #3EB35A;
}
.main.style1 .top .weeks .w-con .wc-main ul li:hover {
  background-color: #D2EFD1;
}
.main.style1 .top .weeks .w-con .wc-main ul li.cur:hover {
  background-color: #3EB35A;
  color: #fff;
}
.main.style1 .top .oprate .btn-list .t-schedule .select-dropdown ul li:hover {
  background-color: #D2EFD1;
}
.main.style1 .con .c-left .schedule-list ul li .manageName:hover {
  background: #D2EFD1 !important;
  color: #1d2129;
  border-radius: 4px;
  color: #3EB35A;
}
.main.style1 .con .c-left .schedule-list ul li.level1:nth-child(2n) .level2:nth-child(2n) .manageName:hover {
  background: #D2EFD1 !important;
}
.main.style1 .con .c-left .schedule-list ul li.level1:nth-child(2n) .level2 .manageName:hover {
  background: #D2EFD1 !important;
}
.main.style1 .con .c-left .schedule-list ul li.level1:nth-child(2n) .level2:nth-child(2n) .manageName.cur:hover {
  background: #3EB35A !important;
  color: #fff;
}
.main.style1 .con .c-left .schedule-list ul li.level1:nth-child(2n) .level2 .manageName.cur:hover {
  background: #3EB35A !important;
  color: #fff;
}
.main.style1 .con .c-left .schedule-list ul li .manageName.cur:hover {
  color: #fff;
}
.main.style1 .con .c-right {
  flex: 1;
  height: calc(100vh - 100px);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 20px 30px;
  border-radius: 0px 0px 8px 0px;
  background: #A6E1B1;
}
.main.style1 .con .c-right .c-homeWrap .oprate .btn-list .rice .select-dropdown ul li:hover {
  background-color: #D2EFD1;
}
.main.style1 .con .c-right .c-side .gauge-outfit .title .sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #D2EFD1;
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .oprate .sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #D2EFD1;
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li:hover {
  background-color: #D2EFD1;
  color: #3EB35A;
}
.main.style1 .con .c-right .c-side .oprate .btn-list .rice .select-dropdown ul li:hover {
  background-color: #D2EFD1;
}
.main.style1 .con .c-right .c-side .c-table .scroll {
  overflow: auto;
  max-height: 250px;
  margin-bottom: 12px;
  background: transparent;
}
.main.style1 .con .c-right .c-side .c-table .tbody.course-manage ul li:first-child {
  min-width: 30px;
  flex: 0 0 30px;
  flex-shrink: 0;
  z-index: 996;
  background: #D2EFD1;
  border-radius: 0;
}
.main.style1 .con .c-right .c-side .c-table .tbody ul.active li .select-area {
  background-color: #B2F0B7;
  border-color: #B2F0B7;
}
.main.style1 .con .c-right .c-table .tbody ul.carve-up {
  height: 4px;
  background: url(../../../images/basic/roomReserve/white-line-bg.png) repeat-x;
}
.main.style1 .con .c-right .c-table .tbody ul li .select-area:hover {
  border: 1px solid #e9fde8;
}
.main.style1 .con .c-right .c-table .tbody ul li .select-area {
  background-color: #e9fde8;
  border-color: #e9fde8;
}
.main.style1 .con .c-right .c-table .tbody ul li .select-area .tage:after {
  background: linear-gradient(90deg, rgba(233, 253, 232, 0.1), #e9fde8 50%);
}
.main.style1 .con .c-right .c-table .tbody ul li .select-area .oprate:after {
  background: linear-gradient(90deg, rgba(233, 253, 232, 0.1), #e9fde8 50%);
}
.main.style1 .con .c-right .c-table .tbody.m-tbody .disabled {
  background-color: #e5e6eb;
  border-color: #e5e6eb;
}
.main.style1 .con .c-right .c-table .tbody.m-tbody .disabled:hover {
  border-color: #e5e6eb;
}
.style1 .popup .bottom .exam-sure {
  background-color: #3eb35a;
  border-color: #3eb35a;
  box-shadow: 0px 0px 8px 0px rgba(62, 179, 90, 0.3);
}
.style1 #editPoups .popup-con .tab ul li.cur {
  background-color: #3eb35a;
}
.style1 xm-select .xm-body .xm-option .xm-option-icon {
  border-color: #3eb35a !important;
}
.style1 xm-select .xm-label .xm-label-block {
  background-color: #3eb35a !important;
}
.style1 xm-select .xm-body .selected .xm-option-icon {
  color: #3eb35a !important;
}
.style1 .layui-input:focus {
  border-color: #3eb35a !important;
}
.style1 .layui-form-select dl dd.layui-this {
  background-color: #D2EFD1;
  color: #3EB35A;
}
.style1 #editPoups .popup-con .regional-switch .lable .info-list ul li .oprate .edit {
  background: #3EB35A;
}
.style1 #editPoups .popup-con .regional-switch .lable .l-form .add span {
  color: #3EB35A;
  background: url(../../../images/basic/roomReserve/add-hy-icons.png) no-repeat left center;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate span {
  position: relative;
  z-index: 99;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate:after {
  content: "";
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
  margin-top: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.4), #ffffff 50%);
  z-index: 1;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage .em-list {
  position: relative;
  z-index: 99;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage:after {
  content: "";
  position: absolute;
  right: 0;
  width: 170%;
  height: 90%;
  margin-top: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), #ffffff 50%);
  z-index: 1;
}
.main.style1 .con .c-right .c-table .tbody.m-tbody .disabled .oprate {
  display: none !important;
}
.main .con .c-right .c-table .tbody.m-tbody .disabled .oprate {
  display: none !important;
}
.main .con .c-right .c-table .tbody ul li .select-area {
  padding: 7px !important;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage {
  min-height: 30px;
}
.main .con .c-right .c-table .tbody ul li .select-area.active .tage:after {
  background: linear-gradient(90deg, rgba(77, 136, 255, 0.1), #4d88ff 50%);
}
.main .con .c-right .c-table .tbody.m-tbody .lock-in .oprate:after {
  background: linear-gradient(90deg, rgba(241, 243, 246, 0.1), #f1f3f6 50%);
}
.main .con .c-right .c-table .tbody.m-tbody .lock-in .tage:after {
  background: linear-gradient(90deg, rgba(241, 243, 246, 0.1), #f1f3f6 50%);
}
.main .con .c-right .c-table .tbody ul li .select-area.active .oprate:after {
  background: linear-gradient(90deg, rgba(77, 136, 255, 0.1), #4d88ff 50%);
}
.main.style1 .con .c-right .c-table .tbody ul li .select-area.active .tage:after {
  background: linear-gradient(90deg, rgba(62, 179, 90, 0.1), #3eb35a 50%);
}
.main.style1 .con .c-right .c-table .tbody ul li .select-area.active .oprate:after {
  background: linear-gradient(90deg, rgba(62, 179, 90, 0.1), #3eb35a 50%);
}
.main .con .c-right .c-table .tbody ul li .select-area.active .oprate span {
  color: #fff !important;
}
.main.style1 .con .c-right .c-table .tbody.m-tbody .lock-in {
  background-color: #f1f3f6;
  border-color: #F1F3F6;
  cursor: default;
}
.main.style1 .con .c-right .c-table .tbody.m-tbody .lock-in:hover {
  border-color: #F1F3F6;
}
.main.style1 .con .c-right .c-table .tbody.m-tbody .lock-in .oprate:after {
  background: linear-gradient(90deg, rgba(241, 243, 246, 0.1), #f1f3f6 50%);
}
.main.style1 .con .c-right .c-table .tbody.m-tbody .lock-in .tage:after {
  background: linear-gradient(90deg, rgba(241, 243, 246, 0.1), #f1f3f6 50%);
}
/* 更新课表样式 2024.6.27 */
.style1 #editPoups .popup-con .regional-switch .lable .info-list ul li:hover {
  background-color: #d2efd1;
}
.style1 #updateSchedule .popup-con .radio-box ul li.cur h4:before {
  background-color: #3eb35a;
}
.style1 #updateSchedule .popup-con .tips {
  background-color: #d2efd1;
}
.style1 #updateSchedule .popup-con .tips span {
  background: url(../../../images/basic/roomReserve/tips-green.png) no-repeat left center;
}
#updateSchedule {
  width: 560px;
  display: none;
}
#updateSchedule .popup-con {
  padding: 0;
}
#updateSchedule .popup-con .tips {
  width: 100%;
  height: 50px;
  background-color: #e1ebff;
  box-sizing: border-box;
  padding: 0 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#updateSchedule .popup-con .tips span {
  background: url(../../../images/basic/roomReserve/tips-blue.png) no-repeat left center;
  padding-left: 28px;
  font-size: 14px;
  color: #1d2129;
}
#updateSchedule .popup-con .radio-box {
  padding: 40px 80px;
}
#updateSchedule .popup-con .radio-box ul li {
  cursor: pointer;
}
#updateSchedule .popup-con .radio-box ul li:first-child {
  margin-bottom: 24px;
}
#updateSchedule .popup-con .radio-box ul li.cur h4:before {
  content: '';
  position: absolute;
  left: 3px;
  top: 5px;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  background-color: #4d88ff;
}
#updateSchedule .popup-con .radio-box ul li h4 {
  padding-left: 24px;
  font-size: 14px;
  color: #4e5969;
  line-height: 20px;
  margin-bottom: 6px;
  position: relative;
}
#updateSchedule .popup-con .radio-box ul li h4:after {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  border-radius: 50%;
  border: 1px solid #c9cdd4;
  width: 16px;
  height: 16px;
  box-sizing: border-box;
}
#updateSchedule .popup-con .radio-box ul li p {
  color: #86909c;
  font-size: 12px;
  line-height: 21px;
}
#courseReport {
  display: none;
  width: 1000px;
}
#courseReport .reportPage {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  background-color: #fff;
  position: relative;
  margin-top: 24px;
}
#courseReport .reportPage .count-total {
  line-height: 32px;
  color: #86909C;
}
#courseReport .reportPage .count-total span {
  padding: 0 4px;
  color: #4D88FF;
}
#courseReport .reportPage .pageCon {
  text-align: center;
}
#courseReport .reportPage .table-refresh {
  color: #4D88FF;
  font-size: 14px;
  padding-left: 20px;
  background: url('../../../images/basic/roomReserve/refresh.png') no-repeat left center;
  background-size: 16px;
  line-height: 32px;
  cursor: pointer;
}
#courseReport .layui-table tr {
  height: 36px;
}
#courseReport .layui-table-cell {
  color: #4E5969;
  font-size: 14px;
}
#courseReport .layui-table-view .layui-table {
  width: 100%;
}
#courseReport .layui-table-view .layui-table tr th > div {
  height: 36px;
  line-height: 36px;
  color: #6581BA;
}
#courseReport .layui-table-view .layui-table tr.layui-table-hover {
  background-color: #E1EBFF;
}
#courseReport .layui-laypage a,
#courseReport .layui-laypage span {
  line-height: 30px;
  height: 30px;
  border-color: #E5E6EB;
  font-size: 14px;
  color: #4E5969;
  padding: 0 16px;
  margin-bottom: 0;
}
#courseReport .layui-laypage a:hover,
#courseReport .layui-laypage span:hover {
  color: #4C88FF;
}
#courseReport .layui-disabled {
  background-color: unset !important;
  color: #C9CDD4 !important;
}
#courseReport .layui-disabled:hover {
  color: #C9CDD4 !important;
}
#courseReport .layui-laypage input {
  height: 32px;
  border-radius: 2px;
  font-size: 14px;
}
#courseReport .layui-laypage input:focus {
  border-color: #4C88FF !important;
}
#courseReport .layui-laypage {
  margin: 0;
}
#courseReport .layui-laypage button {
  padding: 0 16px;
  height: 32px;
  margin-left: 16px;
  color: #4E5969;
}
#courseReport .layui-laypage .layui-laypage-skip {
  height: 32px;
  line-height: 32px;
  margin: 0 16px;
  font-size: 14px;
  padding: 0;
}
#courseReport .layui-laypage .layui-laypage-limits {
  margin-left: 0;
  padding: 0;
  line-height: 32px;
  height: 32px;
}
#courseReport .layui-laypage input:focus,
#courseReport .layui-laypage select:focus {
  border-color: #4d88ff !important;
}
#courseReport .layui-laypage select {
  color: #4E5969;
  height: 24px;
  font-size: 14px;
}
#courseReport .title .close {
  width: 20px;
  height: 20px;
  background: url('../../../images/basic/roomReserve/close-icon.png') no-repeat center;
  background-size: 20px;
}
#courseReport .popup-con {
  padding: 40px 60px;
}
#courseReport .popup-con span {
  display: inline-block;
  cursor: pointer;
}
#courseReport .popup-con .detail {
  color: #4C88FF;
  margin-right: 15px;
}
#courseReport .popup-con .delete {
  color: #F76560;
}
/* 更新课表样式 2024.6.27 */
