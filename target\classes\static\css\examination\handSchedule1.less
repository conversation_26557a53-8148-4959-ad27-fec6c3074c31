body {
    background-color: #F7F8FA;
}

.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.flex {
    display: flex;
    align-items: center;
}

.layui-table-view .layui-table tr th>div {
    color: #4E5969;
    font-weight: bold;
}

.layui-table-filter i {
    color: #4E5969;
}

.layui-table-view .layui-table td {
    padding: 10px 16px;
}

.z-main {

    margin: 20px;
    background-color: #ffffff;
    overflow: hidden;
    min-height: calc(~"100vh - 68px");
    border-radius: 6px;

    .z-title {
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #E8EBF1;

        .z-title-con {
            display: flex;
            align-items: center;


            h3 {
                font-size: 16px;
                line-height: 22px;
                color: #1D2129;
                padding-left: 9px;
                position: relative;
                margin-left: 30px;

                &::after {
                    content: "";
                    width: 3px;
                    height: 16px;
                    background: #4D88FF;
                    border-radius: 2px;
                    position: absolute;
                    left: 0;
                    top: 3px;
                }
            }

            span {
                font-size: 14px;
                color: #86909C;
                font-size: 14px;
                margin-left: 16px;
                margin-top: 2px;
            }
        }

        .z-btn {
            display: flex;
            align-items: center;

            #clearData {
                height: 36px;
                line-height: 34px;
                padding: 0 20px;
                font-size: 14px;
                margin-right: 16px;
                cursor: pointer;
                border-radius: 4px;
                border: 1px solid #F76560;
                color: #F76560;
                background: #FFF;
                box-shadow: 0px 0px 10px 0px rgba(247, 101, 96, 0.20);

            }

            #save {
                cursor: pointer;
                height: 36px;
                line-height: 34px;
                margin-right: 30px;
                border-radius: 4px;
                background: #4D88FF;
                border: 1px solid #4D88FF;
                padding: 0 20px;
                font-size: 14px;
                color: #fff;
                box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.30);
                width: 96px;
            }
        }

    }

    .z-con {
        display: flex;
        overflow: hidden;
        position: relative;

        .z-menu {
            width: 268px;
            border-right: 1px solid #E8EBF1;
            // overflow: hidden;

            .z-nav {
                border-radius: 4px;
                overflow: hidden;
                color: #1D2129;
                font-size: 14px;
                margin: 24px 30px;


                ul {
                    .flex;
                    height: 26px;
                    background-color: #F1F3F6;
                    padding: 4px;

                    li {
                        flex: 1;
                        text-align: center;
                        line-height: 26px;
                        cursor: pointer;
                        background: #F2F3F5;

                        &.active {
                            background: #fff;
                            color: #4D88FF;
                            border-radius: 4px;
                            box-shadow: 0px 0px 4px 0px rgba(77, 136, 255, 0.15);
                        }
                    }
                }
            }

            .z-search {
                .flex;
                margin: 0 30px 24px;

                .search-icon {
                    width: 36px;
                    height: 34px;
                    background: #E1EBFF url('/css/examination/images/icon-search.png') no-repeat center;
                    background-size: 16px;
                    border-radius: 4px;
                    margin-left: 8px;
                }

                .search-box {
                    border: 1px solid #E5E6EB;
                    border-radius: 4px;
                    width: 164px;
                    height: 36px;
                    padding: 0 10px 0 30px;
                    box-sizing: border-box;
                    font-size: 14px;
                    background: url('/css/examination/images/search-icon.png') no-repeat 10px center;

                    &::placeholder {
                        color: #86909C;
                    }

                }

                .j-search-con.single-box .j-select-year ul li.active {
                    background-image: url('/css/examination/images/check2.png');
                    background-size: 16px;
                    background-color: unset;
                    color: #4E5969;
                    background-position: 90% center;
                }

                .j-search-con.single-box .j-select-year ul li:hover {
                    background-color: #E1EBFF;
                    color: #4D88FF;
                }
            }

            .z-box {
                // overflow: hidden;
                width: 100%;

                .title {
                    margin: 0 30px 8px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    color: #86909C;
                    height: 20px;
                    overflow: hidden;
                }

                .con-list {
                    margin-left: 30px;
                    overflow: hidden;
                    overflow-y: auto;
                    height: calc(~"100vh - 328px");

                    ul {
                        margin-right: 30px;
                        width: 208px;

                        li {
                            .item {
                                height: 48px;
                                font-size: 14px;
                                .flex;
                                justify-content: space-between;
                                padding: 0 16px;
                                box-sizing: border-box;
                                cursor: pointer;
                                color: #1D2129;

                                &.slide {
                                    .arrow {
                                        transform: rotate(0);
                                    }
                                }

                                .arrow {
                                    width: 16px;
                                    height: 16px;
                                    margin-right: 8px;
                                    color: #4E5969;
                                    transform: rotate(-90deg);
                                }

                                h3 {
                                    flex: 1;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                    font-weight: 400;
                                }




                                span.remaining {
                                    margin-left: 8px;
                                }

                                span.check {
                                    width: 16px;
                                    height: 16px;
                                    margin-left: 8px;
                                    border: 1px solid #C9CDD4;
                                    border-radius: 3px;
                                    // background: url('/css/examination/images/check1-icon.png') no-repeat center;
                                    background-size: 16px;
                                    box-sizing: border-box;
                                    display: none;

                                    &.check-half {
                                        border: 1px solid #4D88FF;
                                        background: #4D88FF url('/css/examination/images/check1.png') no-repeat center;
                                        background-size: 10px;
                                    }

                                    &.checked {
                                        border: 1px solid #4D88FF;
                                        background: #4D88FF url('/css/examination/images/check.png') no-repeat center;
                                        background-size: 10px;
                                    }
                                }

                                &:hover {
                                    background-color: #E1EBFF;
                                    color: #4D88FF;
                                    border-radius: 4px;

                                    .arrow {
                                        color: #4D88FF;
                                    }

                                }

                                &.parentActive {
                                    background: #FAFBFC;
                                    border-radius: 4px;
                                }

                                &.active {
                                    background: #4D88FF;
                                    border-radius: 4px;
                                    color: #fff;

                                    .arrow {
                                        color: #fff;
                                    }

                                    h3 {
                                        font-weight: bold;
                                    }

                                    span.check {
                                        border: 1px solid #fff;

                                        &.checked {
                                            border: 1px solid #fff;
                                        }

                                    }

                                }
                            }
                        }
                    }
                }

                .con-list>ul>li>ul {
                    display: none;
                }
            }
        }

        .z-batch {
            display: flex;
            align-items: center;
            height: 56px;
            border-top: 1px dashed #E5E6EB;
            margin: 0 30px;
            justify-content: space-between;
            overflow: hidden;

            .z-batch-wrap {
                display: flex;
                align-items: center;
                color: #1D2129;
                font-size: 14px;

                .switch {
                    margin-left: 14px;
                    position: relative;
                    width: 28px;
                    height: 14px;
                    background-color: #e4e5ea;
                    border-radius: 3px;
                    position: relative;
                    cursor: pointer;


                    span {
                        display: block;
                        background-color: #ffffff;
                        width: 12px;
                        height: 10px;
                        border-radius: 2px;
                        position: absolute;
                        left: 2px;
                        top: 2px;
                        transition: left 0.2s linear;
                    }

                    &.switch-on {
                        background-color: #4d88ff;

                        span {
                            left: 14px;
                        }
                    }
                }

            }

            .slideLeft {
                width: 24px;
                height: 24px;
                background: #dbeaff url(/css/examination/images/fold.png) no-repeat center;
                background-size: 16px;
                transform: rotate(180deg);
                cursor: pointer;
            }
        }

        .z-table {
            flex: 1;
            overflow: hidden;
            padding: 24px 30px;
            height: calc(~"100vh - 150px");
            overflow-y: auto;

            .layui-table-view {
                border-radius: 8px;
            }


            .form-search {
                padding-bottom: 52px;
                position: relative;
                display: flex;
                flex-wrap: wrap;

                &.slideDown {
                    padding-right: 68px;

                    #formSlide {
                        span.arrow {
                            transform: rotate(0deg);
                        }
                    }

                }

                .layui-inline {
                    width: 25%;
                    margin-bottom: 16px;
                    display: flex;
                    align-items: center;

                    .layui-form-label {
                        padding: 7px 15px 7px 0;
                    }

                    .layui-input-inline {
                        flex: 1;
                        margin-right: 24px;
                    }


                    .j-search-con {
                        width: 100%;

                        .j-select-year {
                            min-width: 200px;
                        }
                    }


                    &:last-child {
                        margin-right: 0;
                    }

                    &:nth-child(4n) {
                        .layui-input-inline {
                            margin-right: 0;
                        }
                    }
                }



                .search-btn {
                    width: auto;
                    padding: 0 16px;
                    height: 34px;
                    line-height: 32px;
                    background: #4d88ff;
                    border-radius: 4px;
                    text-align: center;
                    color: #fff;
                    display: inline-block;
                    font-size: 14px;
                    box-sizing: border-box;
                    cursor: pointer;

                    &.clear {
                        background-color: unset;
                        border: 1px solid #4d88ff;
                        color: #4d88ff;
                        margin-right: 16px;
                    }
                }
            }

            #formSlide {
                position: absolute;
                bottom: 75px;
                right: 0;
                color: #4D88FF;
                font-size: 14px;
                display: flex;
                align-items: center;
                cursor: pointer;

                span.arrow {
                    width: 8px;
                    height: 8px;
                    background: url('/css/examination/images/arrow.png') no-repeat right center;
                    background-size: 8px;
                    margin-left: 4px;
                    transform: rotate(180deg);
                }


            }

            .z-opt {
                display: flex;
                align-items: center;
                color: #1D2129;
                font-size: 14px;
                position: absolute;
                bottom: 24px;
                right: 0;

                .z-export {
                    color: #207af9;
                    margin-right: 16px;
                    padding-left: 20px;
                    background: url('/css/examination/images/export.png') no-repeat left center;
                    background-size: 16px;
                    cursor: pointer;
                }
            }

            .z-batch-course {
                display: flex;
                align-items: center;
                color: #1D2129;
                font-size: 14px;


                .switch {
                    margin-left: 14px;
                    position: relative;
                    width: 28px;
                    height: 14px;
                    background-color: #e4e5ea;
                    border-radius: 3px;
                    position: relative;
                    cursor: pointer;


                    span {
                        display: block;
                        background-color: #ffffff;
                        width: 12px;
                        height: 10px;
                        border-radius: 2px;
                        position: absolute;
                        left: 2px;
                        top: 2px;
                        transition: left 0.2s linear;
                    }

                    &.switch-on {
                        background-color: #4d88ff;

                        span {
                            left: 14px;
                        }
                    }
                }

            }

            .z-btn {
                display: flex;
                justify-content: flex-end;
            }

            .t-room {
                line-height: 18px;
                font-size: 13px;
            }

            .t-class {
                span {
                    display: block;
                }
            }

            .t-title {
                font-size: 14px;
                line-height: 20px;
                overflow: hidden;

                // font-weight: 500;
            }

            .t-capacity {
                font-size: 13px;
                line-height: 18px;
            }

            .t-teacher {
                display: flex;
                justify-content: center;
                flex-wrap: wrap;

                .teacher {
                    margin-right: 8px;
                    line-height: 18px;
                    font-size: 13px;

                    &:last-child {
                        margin-right: 0;
                    }
                }
            }

            .t-check {
                width: 16px;
                height: 16px;
                border: 1px solid #C9CDD4;
                border-radius: 3px;
                background-size: 16px;
                box-sizing: border-box;
                position: absolute;
                left: 5px;
                top: 5px;
                box-shadow: 11px 5px 10px 0px #fff;
                display: none;

                &.checked {
                    border: 1px solid #4D88FF;
                    background: #4d88ff url(/css/examination/images/check.png) no-repeat center;
                    background-size: 10px;
                }
            }

            .blue {
                color: #4D88FF;
            }

            .t-con {
                padding: 8px 16px;
                // min-height: 72px;

            }

            td {
                position: relative;

                .layui-table-cell {
                    // height: 100%;
                    min-height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .t-status {
                    position: absolute;
                    right: 5px;
                    top: 5px;
                    border-radius: 2px;
                    background: #3EB35A;
                    padding: 0 4px;
                    line-height: 16px;
                    box-shadow: -11px 2px 10px 0px rgba(255, 255, 255, 1);

                    span {
                        font-size: 12px;
                        color: #FFF;
                        transform: scale(0.83);
                        display: inline-block;
                    }

                    &.t-status1 {
                        background: #FFB026;
                    }

                    &.t-status2 {
                        background: #FF96B9;
                    }
                }
            }


            .layui-table-fixed-l {

                .t-class {
                    flex-direction: column;
                    height: 84px;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    overflow: hidden;
                    // display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    text-align: center;
                    justify-content: center;

                }

                td[lay-event="setCell"]:hover {
                    position: relative;

                    .layui-table-cell {
                        position: absolute;
                        left: 0;
                        top: 0;
                        height: auto;
                        z-index: 9;
                        background-color: #fff;
                        border: 1px solid #4D88FF;
                        box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.30);

                        .t-class {
                            height: unset !important;
                            height: auto;
                            margin: 8px 0;
                            -webkit-line-clamp: unset;
                        }
                    }
                }
            }

            .layui-table-main td:hover {
                position: relative;
                width: 192px !important;

                .layui-table-cell {
                    position: absolute;
                    left: 0;
                    top: 0;
                    height: auto;
                    z-index: 9;
                    background-color: #fff;
                    border: 1px solid #4D88FF;
                    box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.30);

                    .t-con {
                        .t-teacher {
                            margin-top: 6px;

                            .teacher {
                                padding: 0 14px;
                                color: #4D88FF;
                                background: #E1EBFF;
                                border-radius: 12px;
                                position: relative;
                                margin-right: 12px;
                                line-height: 34px;
                                margin-bottom: 6px;

                                .close {
                                    width: 14px;
                                    height: 14px;
                                    background: url('/css/examination/images/icon-del.png') no-repeat center;
                                    background-size: 14px;
                                    cursor: pointer;
                                    position: absolute;
                                    right: -4px;
                                    top: 0;
                                }
                            }
                        }

                        .t-room {
                            padding: 0 16px;
                            color: #4D88FF;
                            background: #E1EBFF;
                            border-radius: 12px;
                            position: relative;
                            display: inline-block;
                            line-height: 34px;


                            .close {
                                width: 14px;
                                height: 14px;
                                background: url('/css/examination/images/icon-del.png') no-repeat center;
                                background-size: 14px;
                                cursor: pointer;
                                position: absolute;
                                right: -4px;
                                top: 0;
                            }
                        }
                    }


                }
            }

        }


    }

}

.j-search-con.single-box .j-select-year ul li[value=''] {
    background-image: unset;
    opacity: 0.8;
}


@media screen and (max-width: 1500px) {
    .z-main .z-con .z-table .form-search .j-search-con {
        width: 220px;
    }
}

@media screen and (max-width: 1300px) {
    .z-main .z-con .z-table .form-search .j-search-con {
        width: 170px;
    }
}

@media screen and (max-width: 1000px) {
    .z-main .z-con .z-table .form-search .j-search-con {
        width: 160px;
    }
}

.layui-table-view .layui-table td {
    height: 76px;
}

.layui-table-cell {
    font-size: 14px;
    padding: 0;
    height: auto;
    overflow: visible;
    text-overflow: inherit;
    white-space: normal;
    word-break: break-all;
    color: #4E5969;
    overflow: hidden;

    .t-con {
        overflow: hidden;
        width: 100%;
    }
}

#slideMenu {
    position: fixed;
    z-index: 99;
    left: -32px;
    width: 32px;
    height: 28px;
    background: #DBEAFF url('/css/examination/images/fold.png') no-repeat center;
    background-size: 16px;
    cursor: pointer;
    transition: all 0.2s linear;
    bottom: 180px;
    border-radius: 0px 16px 16px 0px;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.10);


    &:hover {
        width: 40px;
        background: #DBEAFF url('/css/examination/images/fold.png') no-repeat 16px center;
        background-size: 16px;
    }

    &.expand {
        left: 0;
    }
}

.layui-table thead tr,
.layui-table-header {
    background: #F1F3F6;
}

table tbody tr:nth-child(even) {
    background: #FAFBFC;
}

.layui-table-view .layui-table td[lay-event="setCell"] .layui-table-cell {
    display: flex;
    align-items: center;
}

.layui-table-view .layui-table td[lay-event="setCell"].gray .t-con .t-teacher .teacher.height-light {
    color: #4D88FF;
}
//.layui-table-view .layui-table td[lay-event="setCell"].gray .layui-table-cell>div {
//    // background: #F2F3F5;
//    display: none !important;
//}

.layui-table-view .layui-table td[lay-event="setCell"].optCell .layui-table-cell {
    border: 1px solid #4D88FF !important;
    box-sizing: border-box;
    /* 蓝色投影 */
    box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
    height: auto;
}

/* .layui-table-view .layui-table td[data-field="class"] .layui-table-cell {
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-height: unset;
} */

.layui-table-view .layui-table tr th>div {
    color: #4E5969;
    font-weight: bold;
}

.layui-table-filter i {
    color: #4E5969;
}

.layui-table-view .layui-table td {
    padding: 0;
}

.layui-table tbody tr:hover,
.layui-table-hover {
    background-color: #fff;
}

.layui-table td,
.layui-table th {
    min-height: auto;
    height: 36px;
    line-height: 36px;
}