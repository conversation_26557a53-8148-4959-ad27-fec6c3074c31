.marker {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.dialog {
  border-radius: 10px;
  background-color: #ffffff;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.dialog .tips {
  color: #4e5969;
  margin: 11px 30px 33px 30px;
  padding-left: 18px;
  background: url("../images/tips.png") no-repeat left center;
  background-size: 14px;
  font-size: 12px;
}
.dialog .tips a {
  color: #4D88FF;
  padding-left: 10px;
}
.dialog .dialog-title {
  border-bottom: 1px solid #e5e6eb;
  height: 56px;
  line-height: 56px;
  color: #1d2129;
  font-size: 16px;
  text-indent: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.dialog .dialog-title span {
  width: 24px;
  height: 24px;
  background: url("../images/close.png") no-repeat center;
  background-size: 24px;
  margin-right: 23px;
  cursor: pointer;
}
.dialog .z-search {
  margin-top: 30px;
}
.dialog .z-search .layui-form {
  display: flex;
  flex-wrap: wrap;
}
.dialog .z-search .layui-form .layui-form-item {
  margin-right: 32px;
}
.dialog .z-search .layui-form .layui-form-label {
  width: 60px;
}
.dialog .z-search .layui-form .layui-input-block {
  margin-left: 75px;
}
.dialog .z-search .z-btn {
  width: 68px;
  height: 36px;
  border-radius: 4px;
  background: #4d88ff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  outline: none;
  border: none;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
}
.dialog .z-search .clear {
  background: url("../images/icon_clear.png") no-repeat center;
  width: 36px;
  height: 36px;
  cursor: pointer;
  margin-left: 16px;
}
.dialog .z-tab-search {
  overflow: hidden;
  margin-bottom: 30px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.dialog .z-tab-search ul {
  display: flex;
  align-items: center;
  margin-right: 26px;
}
.dialog .z-tab-search ul li {
  margin-left: 20px;
  padding-left: 24px;
  color: #86909c;
  font-size: 14px;
  cursor: pointer;
  background: url("../images/radio1.png") no-repeat left center;
}
.dialog .z-tab-search ul li.active {
  background: url("../images/radio2.png") no-repeat left center;
}
.dialog .z-tab-search input {
  display: block;
  float: right;
  width: 220px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #c9cdd4;
  padding: 0 28px 0 12px;
  box-sizing: border-box;
}
.dialog .z-tab-search input::placeholder {
  font-size: 14px;
  color: #86909c;
}
.dialog .z-tab-search img {
  display: block;
  position: absolute;
  right: 12px;
  top: 9px;
}
.dialog .dialog-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 70px;
  border-top: 1px solid #e5e6eb;
  padding-right: 30px;
}
.dialog .dialog-btn button {
  width: 88px;
  height: 36px;
  font-size: 14px;
  border-radius: 18px;
  cursor: pointer;
}
.dialog .dialog-btn button.pu-cancel {
  border: 1px solid #c9cdd4;
  color: #4e5969;
  background-color: #fff;
  margin-right: 16px;
}
.dialog .dialog-btn button.pu-sure {
  color: #fff;
  background: #4d88ff;
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  border: 1px solid #4d88ff;
}
.dialog .dialog-con {
  padding: 0 30px;
  box-sizing: border-box;
}
.dialog .dialog-con .z-table {
  margin-bottom: 30px;
}
#selStu {
  width: 1098px;
  display: none;
}
#selStu .j-search-con {
  width: 190px;
}
#selStu .z-relation {
  display: flex;
  align-items: center;
  margin-top: 26px;
}
#selStu .z-relation h3 {
  font-size: 14px;
  color: #1d2129;
}
#selStu .z-relation .layui-tips {
  background: url(../images/tips.png) no-repeat center;
  background-size: 16px;
  width: 16px;
  height: 16px;
  margin: 0;
  cursor: pointer;
}
#selStu .z-relation ul {
  display: flex;
  align-items: center;
  margin-right: 26px;
}
#selStu .z-relation ul li {
  margin-left: 20px;
  padding-left: 24px;
  color: #86909c;
  font-size: 14px;
  cursor: pointer;
  background: url("../images/radio1.png") no-repeat left center;
}
#selStu .z-relation ul li.active {
  background: url("../images/radio2.png") no-repeat left center;
}
#selStu .z-table {
  position: relative;
}
#selStu .z-table .z-check {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #666768;
  position: absolute;
  left: 24px;
  bottom: 8px;
}
#selStu .z-table .z-check .check {
  width: 28px;
  height: 28px;
  cursor: pointer;
  margin-right: 6px;
  background: url(../images/check.png) no-repeat center;
  background-size: 28px;
}
#selStu .z-table .z-check .check.checked {
  background: url(../images/check1.png) no-repeat center;
}
#selStu .z-table .z-has-check {
  position: absolute;
  left: 164px;
  bottom: 13px;
  font-size: 13px;
  color: #666768;
}
#resultData {
  min-width: 562px;
  max-width: 949px;
}
#resultData .data-nav {
  display: flex;
  align-items: center;
  width: 310px;
  height: 36px;
  border-radius: 4px;
  font-size: 14px;
  margin: 30px 0 24px 60px;
  overflow: hidden;
}
#resultData .data-nav li {
  width: 50%;
  line-height: 36px;
  background-color: #F1F3F6;
  color: #4E5969;
  text-align: center;
  cursor: pointer;
}
#resultData .data-nav li:last-child {
  margin-left: 2px;
}
#resultData .data-nav li.active {
  background-color: #4D88FF;
  color: #fff;
}
#resultData .layui-table-view .layui-table th {
  color: #86909C;
}
#resultData .layui-table-view .layui-table td {
  color: #4E5969;
}
#resultData .dialog-con1 {
  padding: 0;
}
#resultData .dialog-con1 .result-form .layui-form-label {
  width: 56px;
  padding: 7px 15px 7px 0;
}
#resultData .dialog-con1 .result-form .layui-input-block {
  margin-left: 71px;
  display: flex;
  align-items: center;
}
#resultData .dialog-con1 .result-form .layui-input-block .j-search-con {
  width: 240px;
}
#resultData .dialog-con1 .result-form .layui-input-block .sel-stu {
  font-size: 14px;
  color: #4C88FF;
  margin-left: 30px;
  margin-right: 16px;
  cursor: pointer;
}
#resultData .dialog-con1 .result-form .layui-input-block .sel-del {
  color: #F76560;
  cursor: pointer;
}
#resultData .dialog-con1 .data-course {
  margin-left: 60px;
  min-height: 185px;
}
#resultData .dialog-con1 .data-course.courseScroll {
  max-height: 440px;
  overflow-y: auto;
}
#resultData .dialog-con1 .data-course .failTable {
  margin-right: 60px;
  margin-bottom: 30px;
  overflow: hidden;
  display: none;
}
#resultData .dialog-con1 .data-course .failTable .tips {
  color: #8F97A8;
  font-size: 14px;
  padding-left: 22px;
  background: url('../images/tips.png') no-repeat left center;
  background-size: 16px;
  margin: 24px 0 24px 0;
}
#resultData .dialog-con1 .data-upload {
  min-height: 185px;
  margin: 0 34px 20px 30px;
  display: none;
}
#resultData .dialog-con1 .data-upload .layui-upload-drag {
  background-color: #f2f3f5;
  padding: 30px 0;
}
#resultData .dialog-con1 .data-upload .layui-upload-drag img {
  width: 14px;
  margin-bottom: 24px;
}
#resultData .dialog-con1 .data-upload .layui-upload-drag .intro {
  color: #1d2129;
  line-height: 22px;
  margin-bottom: 4px;
}
#resultData .dialog-con1 .data-upload .layui-upload-drag .intro1 {
  color: #86909c;
  line-height: 22px;
  font-size: 12px;
}
#resultData .dialog-con1 .data-upload .layui-upload-drag #uploadExcel {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-top: 10px;
}
#resultData .dialog-con1 .data-upload .layui-upload-drag #uploadExcel img {
  margin: 0 4px 0 0;
  width: 24px;
}
#resultData .dialog-con1 .add-course-btn {
  display: flex;
  align-items: center;
  width: 94px;
  line-height: 34px;
  font-size: 14px;
  color: #4D88FF;
  cursor: pointer;
  margin-bottom: 30px;
}
#resultData .dialog-con1 .add-course-btn img {
  margin-right: 8px;
}
#resultData .dialog-con1 .addTips {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 10px 20px 10px 40px;
  font-size: 14px;
  color: #1D2129;
  background-color: #fff;
  display: none;
}
#resultData .dialog-con1 .addTips.tip-success {
  background: url('../images/success.png') no-repeat 20px center;
  background-size: 16px;
}
#resultData .dialog-con1 .addTips.tip-error {
  background: url('../images/export-fail.png') no-repeat 20px center;
  background-size: 16px;
}
.loading {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  color: #1D2129;
  animation: rotate 1.7s linear infinite;
  border-radius: 50%;
  overflow: hidden;
  display: none;
}
@keyframes rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
#resultData {
  width: 578px;
}
.elective-list {
  width: 469px;
}
.elective-list .el-item {
  border-radius: 4px;
  border: 1px solid #e5e6eb;
  padding: 12px;
  margin-bottom: 24px;
  padding-right: 32px;
  position: relative;
}
.elective-list .el-item:first-child .delete {
  display: none;
}
.elective-list .el-item .delete {
  position: absolute;
  top: 10px;
  right: 16px;
  cursor: pointer;
  width: 16px;
  height: 16px;
  background: url(../images/delete-icon.png) no-repeat center;
}
.elective-list .el-item.clicked .student .txt {
  color: #4d88ff;
  cursor: pointer;
}
.elective-list .el-item.clicked .student .txt span {
  cursor: pointer;
}
.elective-list .el-item .course {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 7px;
  box-sizing: border-box;
}
.elective-list .el-item .course .name {
  font-size: 14px;
  color: #1d2129;
  line-height: 20px;
  width: 98px;
  flex-shrink: 0;
}
.elective-list .el-item .course .txt {
  flex: 1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  color: #4d88ff;
  cursor: pointer;
}
.elective-list .el-item .student {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.elective-list .el-item .student .name {
  font-size: 14px;
  color: #1d2129;
  line-height: 20px;
  width: 98px;
  flex-shrink: 0;
}
.elective-list .el-item .student .txt {
  flex: 1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  cursor: default;
  color: #b8d3ff;
}
.elective-list .el-item .student .txt span {
  flex: 1;
  width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: default;
}
.elective-list .el-item .student .txt i {
  flex-shrink: 0;
  display: none;
}
#selCourse {
  width: 1200px;
}
#selCourse .j-search-con {
  width: 178px;
}
.layui-laypage {
  text-align: right;
}
.layui-laypage select {
  padding: 0 26px;
}
