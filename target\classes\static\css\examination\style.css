@charset "utf-8";
/* CSS Document */

.bodyColor{ background:#F3F3F6;}

.zy_box{ padding:20px;}
.zy_main{ min-height:100px; background:#FFFFFF; border-radius:8px;}

.zy_bntBlue{ width:88px; height:30px; line-height:30px; background:#4C88FF; border:solid #4C88FF 1px; font-size:14px; color:#ffffff; border-radius:4px; display:block; text-align:center; box-shadow: 0px 2px 8px 0px rgba(39,111,255,0.3);}
.zy_bntWhite{ width:88px; height:30px; line-height:30px; background:#FFFFFF; border:solid #4C88FF 1px; font-size:14px; color:#4C88FF; border-radius:4px; display:block; text-align:center; box-shadow: 0px 2px 8px 0px rgba(39,111,255,0.3);}

.zy_title{ margin-bottom:20px; padding:0 30px; height:56px; border-bottom:solid #E8EAF1 1px;}
.zy_title_h2{ height:56px; line-height:56px; font-size:16px; color:#6581BA;}
.zy_title_h2:before{ content:''; width:3px; height:16px; background:#4D88FF; margin:-3px 8px 0 0; display:inline-block; vertical-align:middle;}
.zy_title .zy_bntBlue{ margin:12px 0 0 30px;}
.zy_title .zy_bntWhite{ margin:12px 0 0 30px;}

.zy_dates{ margin-bottom:20px; padding:0 30px;}
.zy_dates li{ margin-right:36px; height:20px; line-height:20px; font-size:14px; color:#484F5D; display:inline-block; vertical-align:top;}

.dataSel{ display:inline-block; width:198px; height:30px; line-height:30px; border:solid #D5D9E2 1px; font-size:14px; border-radius:4px; position:relative; text-align:left;}
.dataSel:hover,.dataSel_blue{ border-color:#4C88FF;}
.dataSel_name{ display:block; padding:0 20px 0 9px; height:32px; color:#8F97A8; position:relative; cursor:pointer; overflow:hidden; white-space:nowrap; text-overflow:ellipsis;}
.dataSel_name:after{ content:''; display:block; width:12px; height:12px; background:url(/images/examination/select_arrow.png) no-repeat; position:absolute; right:10px; top:50%; margin-top:-6px;
	-webkit-transform:rotate(0deg); -moz-transform:rotate(0deg); -ms-transform:rotate(0deg); -o-transform:rotate(0deg);
	-webkit-transition:all .2s linear; -moz-transition:all .2s linear; -ms-transition:all .2s linear; -o-transition:all .2s linear; transition:all .2s linear;
}
.dataSel_arrow:after{-webkit-transform:rotate(180deg); -moz-transform:rotate(180deg); -ms-transform:rotate(180deg); -o-transform:rotate(180deg);}
.dataSel_con{ width:100%; max-height:360px; overflow:auto; border:solid #e7e9f0 1px; background:#FFFFFF; position:absolute; left:-1px; top:110%; z-index:999; display:none; box-shadow:rgba(153,153,153,.2) 0 1px 6px;}
.dataSel_con li{ padding:8px 9px; line-height:20px; color:#333333; cursor:pointer;}
.dataSel_con li:hover,.dataSel_con li.active{ background:#F4F7FC; color:#4C88FF;}

.xaScreen{ padding:0 30px;}
.xaScreen .xaScreen_row{ display:table; margin:0 30px 20px 0;}
.xaScreen .xaScreen_name{ min-width:56px; padding-right:16px; line-height:32px; font-size:14px; color:#666666; display:inline-block; vertical-align:top; text-align:right;}
.xaScreen .dataSel{ vertical-align:top;}
.xaScreen .xaScreen_input{ display:inline-block; width:180px; height:20px; line-height:20px; padding:5px 9px; border:solid #D5D9E2 1px; font-size:14px; border-radius:4px; vertical-align:top;}
.xaScreen .xaScreen_input:hover,.xaScreen .xaScreen_input:focus{ border-color:#4C88FF;}
.xaScreen .xaScreen_text{ display:inline-block; padding:0 16px; line-height:34px; font-size:14px; color:#333333; vertical-align:top;}
.xaScreen .xaScreen_bnt{ width:80px; height:32px; line-height:32px; background:#4C88FF; font-size:14px; color:#ffffff; border-radius:4px; display:block; text-align:center; box-shadow: 0px 2px 8px 0px rgba(39,111,255,0.3);}
.xaScreen .xaScreen_add{ width:100px; height:30px; line-height:30px; border:solid #4C88FF 1px; font-size:14px; color:#4C88FF; border-radius:4px; display:block; text-align:center; box-shadow: 0px 2px 8px 0px rgba(39,111,255,0.3);}
.xaScreen .xaScreen_add:before{ content:''; width:12px; height:12px; margin:-3px 6px 0 0; background:url(/images/examination/ico-zy-add.png) no-repeat; display:inline-block; vertical-align:middle;}
.xaScreen .xaScreen_excel{ width:104px; height:30px; line-height:30px; border:solid #4C88FF 1px; font-size:14px; color:#4C88FF; border-radius:4px; display:block; text-align:center; box-shadow: 0px 2px 8px 0px rgba(39,111,255,0.3);}
.xaScreen .xaScreen_excel:before{ content:''; width:12px; height:14px; margin:-3px 6px 0 0; background:url(/images/examination/ico-zy-excel.png) no-repeat; display:inline-block; vertical-align:middle;}

.xaCheck{ width:14px; height:14px; background:url(/images/examination/xaCheck.png) no-repeat; display:inline-block; vertical-align:middle; overflow:hidden; cursor:pointer;}
.xaChecked{ background-position:0 -20px;}
.xaCheck input{ width:14px; height:14px; filter:alpha(opacity=0); opacity:0; cursor:pointer; display:block;}

.selCheck{ width:14px; height:14px; overflow:hidden; position:relative; display:inline-block; vertical-align:middle;}
.selCheck input{ width:14px; height:14px; filter:alpha(opacity=0); opacity:0; cursor:pointer; display:block; position:absolute; left:0; top:0; z-index:2;}
.selCheck input+i{ width:14px; height:14px; background:url(/images/examination/xaCheck.png) no-repeat; overflow:hidden; position:absolute; left:0; top:0; z-index:1;}
.selCheck input:checked+i{ background-position:0 -20px;}

.xaTable_box{ padding:0 30px 30px;}
.xaTable_container{ position:relative;}
.xaTable_container .colorBlue{ color:#4C88FF;}
.xaTable_container .colorRed{ color:#FF5E5E;}
.xaTable_container .colorGreen{ color:#2DBE62;}
.xaTable_container .colorGray{ color:#666666;}
.xaTable_container .colorGraye5{ color:#e5e5e5;}
.xaTable_container .colorBlue,.xaTable_container .colorRed,.xaTable_container .colorGraye5{ margin-right:10px;}
.xaTable_container img{ width:58px; height:28px; display:block;}
.xaTable_container .tr_bj_color{ background:#eaf1ff !important;}
.layui-table td, .layui-table th, .layui-table-col-set, .layui-table-fixed-r, .layui-table-grid-down, .layui-table-header, .layui-table-page, .layui-table-tips-main, .layui-table-tool, .layui-table-total, .layui-table-view, .layui-table[lay-skin=line], .layui-table[lay-skin=row]{ border-color:#E8EBF1;}
.layui-table tbody tr:hover, .layui-table thead tr, .layui-table-click, .layui-table-header, .layui-table-hover, .layui-table-mend, .layui-table-patch, .layui-table-tool, .layui-table-total, .layui-table-total tr, .layui-table[lay-even] tr:nth-child(even){ background-color:#F1F3F6;}
.layui-table-view .layui-table td, .layui-table-view .layui-table th{ padding:7px 0}
.layui-table-view .layui-table th{ color:#6581BA;}
.layui-table-view .layui-table td{ color:#333333;}
.layui-table-view .layui-form-checkbox[lay-skin=primary] i{ width:14px; height:14px; overflow:hidden; border:0;}
.layui-form-checkbox[lay-skin=primary] i{ background:url(/images/examination/xaCheck.png) no-repeat; -webkit-transition:0s linear !important; transition:0s linear !important;}
.layui-form-checked[lay-skin=primary] i{ background:url(/images/examination/xaCheck.png) no-repeat 0 -20px;}
.layui-btn-disabled[lay-skin=primary] i{ background:#cccccc;}
.layui-form-disabled[lay-skin=primary] i{ background:#cccccc;}
.layui-table-page{ height:58px; padding:15px 15px 0; text-align:right;}
.layui-table, .layui-table-view{ margin:0;}
.layui-laypage .layui-laypage-curr .layui-laypage-em{ background:#4C88FF;}
/*.layui-icon-cols:before{ content:''; width:16px; height:16px; display:block; background:url(/images/examination/ico_cols.png) no-repeat center center;}*/
/*.layui-icon-export:before{ content:''; width:16px; height:16px; display:block; background:url(/images/examination/ico_export.png) no-repeat center center;}*/
.layui-laypage a, .layui-laypage span{ color:#666666;}
.layui-table-body .layui-none{ min-height:600px; background:url(/images/examination/zanwu.png) no-repeat center center; text-indent:-999999999px; overflow:hidden;}

.xaTable_left{ height:26px; line-height:26px; font-size:14px; position:absolute; left:22px; bottom:16px;}
.xaTable_all{ font-size:14px; color:#4C88FF; cursor:pointer;}
.xaTable_all .xaCheck,.xaTable_all .selCheck{ margin:0 8px 3px 0;}
.xaTable_all label{ cursor:pointer; margin-right:30px;}
.xaTable_left .colorBlue,.xaTable_left .colorRed{ cursor:pointer; margin-right:30px;}

.zy_steps{ height:24px; padding:24px 0; margin-bottom:20px; text-align:center;}
.zy_steps_ul{ height:24px; display:inline-block; vertical-align:middle;}
.zy_steps_ul li{ position:relative; height:24px; line-height:24px; font-size:16px; color:#999999; float:left; text-align:left;}
.zy_steps_ul li+li{ margin-left:164px;}
.zy_steps_ul li+li:before{ content:''; display:block; width:132px; height:2px; background:#DCDCDC; position:absolute; left:-148px; top:50%; margin-top:-1px;}
.zy_steps_ul li .spanOne,.zy_steps_ul li .spanTwo,.zy_steps_ul li .spanThree{ width:22px; height:22px; line-height:22px; margin:-3px 16px 0 0; font-size:16px; color:#999999; border:solid #999999 1px; border-radius:50%; display:inline-block; vertical-align:middle; text-align:center;}
.zy_steps_ul li+li.active:before{ background:#4C88FF;}
.zy_steps_ul li.active{ color:#4C88FF;}
.zy_steps_ul li.active .spanOne,.zy_steps_ul li.active .spanTwo,.zy_steps_ul li.active .spanThree{ background:#4C88FF; border-color:#4C88FF; color:#FFFFFF;}
.zy_steps_ul li+li.li_check:before{ background:#4C88FF;}
.zy_steps_ul li.li_check{ color:#333333;}
.zy_steps_ul li.li_check .spanOne,.zy_steps_ul li.li_check .spanTwo,.zy_steps_ul li.li_check .spanThree{ background:#ffffff url(/images/examination/ico-zy-steps.png) no-repeat center center; border-color:#4C88FF; color:#FFFFFF; font-size:0; text-indent:-999999999px;}

.zy_tab{ padding:0 30px; margin-bottom:20px; height:34px;}
.zy_tab_ul{ padding:2px; height:30px; background:#F1F3F6; border-radius:8px; float:left;}
.zy_tab_ul li{ width:90px; height:30px; line-height:30px; font-size:14px; color:#8F97A8; float:left; text-align:center;}
.zy_tab_ul li a{ color:#8F97A8; display:block;}
.zy_tab_ul li.active{ background:#FFFFFF; color:#6581BA;}
.zy_tab_ul li.active a{ color:#6581BA;}
.zy_tab_ul li{}
.zy_tab_ul li{}


/*pop*/
.popOverflow{ overflow:hidden;}
.maskDiv{ opacity:0; visibility:hidden; -webkit-transition:opacity 0.3s 0s, visibility 0s 0.3s; transition:opacity 0.3s 0s, visibility 0s 0.3s;}
.maskDiv.maskFadeOut{ opacity:1; visibility:visible; -webkit-transition:opacity 0.3s 0s, visibility 0s 0s; transition:opacity 0.3s 0s, visibility 0s 0s;}
.maskDiv{width:100%;height:100%;position:fixed;top:0;left:0;background:rgba(16,26,41,0.76);z-index:300}
.wid440{width:440px;}
.wid460{width:460px;}
.wid640{width:640px;}
.wid840{width:840px;}
.popDiv{min-height:100px;background:#FFFFFF;border-radius:10px;position:fixed;}
.popHead{padding:0 30px;line-height:60px;height:60px;background:#FFFFFF; border-bottom:solid #f2f2f2 1px; border-radius:10px 10px 0 0;}
.popTitle{ font-size:18px; color:#131B26; font-weight:500;}
.popClose{display:block;width:18px;height:18px; margin-top:21px; background:url(/images/examination/popClose.png) no-repeat;}
.popBottom{padding:17px 30px;height:36px;background:#fff; border-top:solid #f2f2f2 1px; border-radius:0 0 10px 10px;}
.popBottom .bntLinear,.popBottom .bntWhite{ margin-left:30px;}
.bntLinear{ padding:0 16px; min-width:60px; height:36px; line-height:36px; background:#4C88FF; font-size:14px; color:#FFFFFF; font-weight:500; border-radius:18px; box-shadow: 0px 2px 8px 0px rgba(39,111,255,0.3000); text-align:center;}
.bntWhite{ padding:0 16px; min-width:60px; height:34px; line-height:34px; border:solid #4C88FF 1px; font-size:14px; color:#4C88FF; font-weight:500; border-radius:20px; text-align:center;}

.popTime{ padding:30px 0; min-height:100px;}
.popTime .popTime_item{ padding-left:106px; min-height:32px; position:relative;}
.popTime .popTime_item+.popTime_item{ margin-top:12px;}
.popTime .popTime_name{ position:absolute; left:0; top:0; width:86px; height:32px; line-height:32px; padding-right:20px; font-size:14px; color:#666666; text-align:right;}
.popTime .popTime_text{ padding:4px 0; min-height:24px; line-height:24px; font-size:14px; color:#333333;}
.popTime .popTime_input{ display:inline-block; width:304px; height:20px; line-height:20px; padding:5px 9px; border:solid #D5D9E2 1px; background:url(/images/examination/ico-input-time.png) no-repeat right center; font-size:14px; border-radius:4px; vertical-align:top;}
.popTime .popTime_input:focus{ border-color:#4C88FF;}









