.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}

.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}

.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}

.tellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

body {
  padding: 40px;
  font: 14px Helvetica Neue, Helvetica, PingFang SC, Tahoma, Arial, sans-serif;
}

.con {
  .table-box {
    border-left: 1px solid #E8EBF1;
    border-top: 1px solid #E8EBF1;

    .t-header {
      background-color: #F1F3F6;
      height: 52px;
      border-bottom: 1px solid #E8EBF1;
      line-height: 52px;
      text-align: center;
      color: #6581BA;
      font-size: 14px;
      font-weight: 600;
      .flex;

      div {
        border-right: 1px solid #E8EBF1;
      }

      .checkbox {
        width: 50px;
        flex-shrink: 0;
        height: 52px;
        background: url(../examination/images/checkbox-bg.png) no-repeat center;
        background-size: 18px;
        cursor: pointer;

        &.checked {
          background: url(../examination/images/checkbox-cur-bg.png) no-repeat center;
          background-size: 18px;
        }

        &.half {
          background: url(../examination/images/checkbox-half-cur-bg.png) no-repeat center;
          background-size: 18px;
        }
      }

      .handle {
        width: 110px;
        flex-shrink: 0;
      }

      .kcmc,
      .njnj,
      .kkxqxq,
      .kkyxyx,
      .jxbzc {
        flex: 1;
        .tellipsis;

      }
    }

    .t-body {
      border-bottom: 1px solid #E8EBF1;

      .detail-content {
        border-bottom: 1px solid #E8EBF1;
        background-color: #edf2fd;

      }

      .item,
      .detail {
        height: 52px;
        border-bottom: 1px solid #E8EBF1;
        line-height: 52px;
        text-align: center;
        color: #333333;
        font-size: 14px;
        font-weight: normal;

        &:last-child {
          border-bottom: none;
        }

        .flex;

        div {
          border-right: 1px solid #E8EBF1;
        }

        .checkbox {
          width: 50px;
          flex-shrink: 0;
          height: 52px;
          background: url(../examination/images/checkbox-bg.png) no-repeat center;
          background-size: 18px;
          cursor: pointer;

          &.checked {
            background: url(../examination/images/checkbox-cur-bg.png) no-repeat center;
            background-size: 18px;
          }

          &.half {
            background: url(../examination/images/checkbox-half-cur-bg.png) no-repeat center;
            background-size: 18px;
          }

        }

        .handle {
          width: 110px;
          height: 52px;
          flex-shrink: 0;
          box-sizing: border-box;
          padding-left: 20px;
          .flex;
          justify-content: flex-start;

          .add {
            color: #4C88FF;
            margin: 0 4px;
            cursor: pointer;
          }

          .switch {
            color: #4C88FF;
            margin: 0 4px;
            cursor: pointer;
          }
        }

        .kcmc,
        .njnj,
        .kkxqxq,
        .kkyxyx,
        .jxbzc {
          flex: 1;
          .tellipsis;

        }
      }

    }



  }
}



.all-select {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;

  .check {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;

    &.checked {
      i {
        background: url(../examination/images/checkbox-cur-bg.png) no-repeat center;
        background-size: 100%;
      }
    }

    &.half {
      i {
        background: url(../examination/images/checkbox-half-cur-bg.png) no-repeat center;
        background-size: 100%;
      }
    }

    i {
      width: 18px;
      height: 18px;
      background: url(../examination/images/checkbox-bg.png) no-repeat center;
      background-size: 100%;
    }

    span {
      font-size: 14px;
      color: #193b68;
      margin-left: 12px;
    }

  }

  .total {
    color: rgba(0, 0, 0, 0.5);
    font-size: 14px;
    line-height: 18px;
  }
}


#coursePage {
  text-align: right;
  .borDer;
}

.layui-laypage {
  margin: 16px 0;
}

.layui-laypage a,
.layui-laypage span,
.layui-laypage input,
.layui-laypage button,
.layui-laypage select {
  border: none;
}

.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  background: #F1F3F6;
  border-radius: 4px;
  color: #484F5D;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #3381ff;
  border-radius: 4px;
}

.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #3381ff !important;
}

.layui-laypage a:hover {
  color: #3381ff;
}

.layui-laypage-prev {
  background-color: transparent !important;
}

.layui-laypage-next {
  background-color: transparent !important;
}

.layui-laypage-spr {
  background-color: transparent !important;
}

.layui-laypage-skip {
  background-color: transparent !important;
}

.layui-laypage-count {
  background-color: transparent !important;
}

.layui-laypage-skip input {
  border: 1px solid #e2e2e2 !important;
  background-color: transparent !important;
}

.layui-laypage select {
  border: 1px solid #e2e2e2 !important;
}

.layui-laypage button:hover {
  color: #4A7CFE;
}