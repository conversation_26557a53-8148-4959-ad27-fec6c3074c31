.fixedHead {
    position: fixed;
    top: 0;
    width: 100%;
    height: 44px;
    text-align: center;
    background-color: #FFFFFF;
    z-index: 1;
}

.fixedHead .leftHead {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 45px;
    z-index: 1;
}

.fixedHead .back {
    float: left;
    width: 40px;
    height: 44px;
    background: url(../../../images/examination/headback.png) center center/24px 24px no-repeat;
}

.fixedHead .centerHead {
    position: relative;
    width: 50%;
    height: 44px;
    margin: 0 auto;
    line-height: 44px;
    font-size: 18px;
    color: #000000;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.fixedHead .rightHead {
    position: absolute;
    right: 4px;
    bottom: 0;
    height: 44px;
    z-index: 1;
}

.fixedHead .rightHead .gray {
    color: #999999;
}

.fixedHead .textbtn {
    height: 44px;
    line-height: 44px;
    text-align: center;
    width: 1.1rem;
    font-size: 15px;
    color: #0999FF;
    z-index: 1;
}

.fixedHead .icon {
    height: 44px;
    padding: 10px 8px;
    margin-left: 12px;
}

.fixedHead .rightHead img {
    display: block;
    width: 24px;
    height: 24px;
}

body.h5 {
    padding-top: 44px;
}

body.h5 .bottomMask .bottomModal .bottomMain {
    max-height: calc(100vh - 4.3rem)
}

body.h5 .schoolInputBox .searchschoolList {
    top: calc(2.1rem + 44px)
}

/*iphone678状态栏*/
.ioswrapMax .fixedHead {
    padding-top: 20px !important;
    height: 64px;
}

body.ioswrapMax {
    padding-top: 88px;
}

body.h5.ioswrapMax .schoolInputBox .searchschoolList {
    top: calc(2.1rem + 64px)
}

/*iphone678plus状态栏*/
.iospluswrapMax .fixedHead {
    padding-top: 30px !important;
    height: 74px;
}

body.h5.iospluswrapMax .schoolInputBox .searchschoolList {
    top: calc(2.1rem + 74px)
}

/*iphoneX状态栏*/
.iosxwrapMax .fixedHead {
    padding-top: 44px !important;
    height: 88px;
}

body.iosxwrapMax {
    /* padding-top: 88px; */
}

body.h5.iosxwrapMax .schoolInputBox .searchschoolList {
    top: calc(2.1rem + 88px)
}

.toastTips {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: auto;
    height: 1rem;
    padding: 0.2rem 0.32rem;
    font-size: 0.3rem;
    color: #FFFFFF;
    text-align: center;
    line-height: 0.6rem;
    white-space: nowrap;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0.12rem;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.4);
    z-index: 100;
}