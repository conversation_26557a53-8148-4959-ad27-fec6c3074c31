body {
  background-color: #f7f8fa;
}
/* .layui-table-view {
    border: none;
    border-top: 1px solid #E8EBF1;
} */
.layui-table tr {
  height: 36px;
}
.layui-table-cell {
  color: #4E5969;
  font-size: 14px;
}
.layui-table-view .layui-table {
  width: 100%;
}
.layui-table-view .layui-table tr th > div {
  height: 36px;
  line-height: 36px;
  color: #6581BA;
}
.layui-table-view .layui-table tr.layui-table-hover {
  background-color: #E1EBFF;
}
.layui-table-body.layui-table-main {
  max-height: calc(100vh - 506px);
}
.course-list .layui-table-body.layui-table-main {
  max-height: calc(100vh - 257px);
}
/* .layui-table-view .layui-table th:first-child,
.layui-table-view .layui-table td:first-child {
    position: relative;

    &::after {
        content: "";
        width: 1px;
        height: 100%;
        background-color: #E8EBF1;
        position: absolute;
        left: 0;
        top: 0;
    }
}

.layui-table-view .layui-table th:last-child,
.layui-table-view .layui-table td:last-child {
    position: relative;

    &::after {
        content: "";
        width: 1px;
        height: 100%;
        background-color: #E8EBF1;
        position: absolute;
        right: 0;
        top: 0;
    }
} */
.layui-layer-tips {
  margin-top: 0 !important;
}
.layui-laypage a,
.layui-laypage span {
  line-height: 30px;
  height: 30px;
  border-color: #E5E6EB;
  font-size: 14px;
  color: #4E5969;
  padding: 0 16px;
  margin-bottom: 0;
}
.layui-laypage a:hover,
.layui-laypage span:hover {
  color: #4C88FF;
}
.layui-disabled {
  background-color: unset !important;
  color: #C9CDD4 !important;
}
.layui-disabled:hover {
  color: #C9CDD4 !important;
}
.layui-laypage input {
  height: 32px;
  border-radius: 2px;
}
.layui-laypage input:focus {
  border-color: #4C88FF !important;
}
.layui-laypage button {
  padding: 0 16px;
  height: 32px;
  margin-left: 16px;
  color: #4E5969;
}
.layui-laypage .layui-laypage-skip {
  height: 32px;
  line-height: 32px;
  margin: 0 16px;
  font-size: 14px;
}
.layui-laypage .layui-laypage-limits {
  margin-left: 0;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #4d88ff !important;
}
.layui-laypage select {
  color: #4E5969;
}
.bg-green {
  display: inline-block;
  padding: 0 10px;
  line-height: 20px;
  border-radius: 4px;
  background-color: #3EB35A;
  color: #fff;
  font-size: 12px;
}
.bg-orange {
  display: inline-block;
  padding: 0 10px;
  line-height: 20px;
  border-radius: 4px;
  background-color: #FFB026;
  color: #fff;
  font-size: 12px;
}
.font-blue {
  color: #4C88FF;
  cursor: pointer;
}
.font-blue:hover {
  color: #4C88FF;
}
.font-gray {
  color: #C9CDD4;
}
.main {
  margin: 20px 30px;
  background-color: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 40px);
  min-width: 1000px;
  overflow: hidden;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  position: relative;
}
.main .m-top {
  width: 100%;
  height: 60px;
  border-bottom: 1px solid #E8EBF1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top .title {
  font-size: 16px;
  color: #1d2129;
  padding-left: 9px;
  position: relative;
  font-weight: 500;
}
.main .m-top .title::after {
  content: "";
  width: 3px;
  height: 16px;
  background-color: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}
.main .m-top .back {
  cursor: pointer;
  margin-right: 16px;
  padding-left: 22px;
  background: url(../../../images/basic/roomReserve/back.png) no-repeat left center;
  background-size: 16px;
  color: #7d92b2;
  font-size: 14px;
}
.main .m-top ul {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 3px;
}
.main .m-top ul::after {
  content: "";
  width: 3px;
  height: 16px;
  background-color: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.main .m-top ul li {
  padding: 0 16px 0 6px;
  font-size: 16px;
  background: url(../../../images/basic/roomReserve/right-icon.png) no-repeat right 5px;
  background-size: 14px;
}
.main .m-top ul li a {
  color: #4E5969;
}
.main .m-top ul li:last-child {
  background: unset;
}
.main .m-top ul li:last-child a {
  color: #1D2129;
  font-weight: 500;
}
.main .main-item {
  overflow: hidden;
}
.main .main-item:nth-child(2) {
  margin: 0 30px;
}
.main .main-item:nth-child(3) .item-title,
.main .main-item:nth-child(3) .pk-nav {
  margin: 0 30px;
}
.main .main-item .item-title {
  padding: 24px 0 24px 7px;
  color: #6581BA;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  position: relative;
}
.main .main-item .item-title::after {
  content: "";
  position: absolute;
  left: 0;
  width: 3px;
  height: 18px;
  border-radius: 2px;
  background-color: #6581BA;
}
.main .main-item ul.item-task {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 32px;
}
.main .main-item ul.item-task li {
  color: #4E5969;
  font-size: 14px;
  margin-right: 24px;
  display: flex;
  align-items: center;
}
.main .main-item ul.item-task li h5 {
  color: #1D2129;
}
.main .main-item .pk-nav {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 20px !important;
}
.main .main-item .pk-nav li {
  margin-right: 60px;
  cursor: pointer;
  padding-bottom: 19px;
}
.main .main-item .pk-nav li h4 {
  font-size: 16px;
  color: #86909C;
  margin-bottom: 4px;
}
.main .main-item .pk-nav li span {
  display: block;
  color: #9fa3a9;
  font-size: 12px;
}
.main .main-item .pk-nav li.active {
  position: relative;
}
.main .main-item .pk-nav li.active::after {
  content: "";
  width: 100%;
  height: 3px;
  background-color: #4D88FF;
  position: absolute;
  left: 0;
  bottom: 0;
  border-radius: 3px 3px 0 0;
}
.main .main-item .pk-nav li.active h4 {
  color: #1D2129;
}
.main .main-item .jp-rule {
  margin: 0 30px;
}
.main .main-item .pk-box-wrap {
  overflow: hidden;
}
.main .main-item .pk-box-wrap .pk-box {
  overflow: hidden;
  position: relative;
  height: calc(100vh - 372px);
  display: none;
}
.main .main-item .tip-rule {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  margin: 0 30px 20px;
}
.main .main-item .tip-rule .tip {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6581BA;
}
.main .main-item .tip-rule .tip span {
  background: url('../../../images/basic/roomReserve/icon-tips.png') no-repeat center;
  background-size: 20px;
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
.main .main-item .tip-rule a {
  color: #4D88FF;
  cursor: pointer;
}
.main .main-item .course-list {
  padding: 0 30px;
  max-height: calc(100vh - 430px);
  overflow-y: auto;
}
.main .main-item .course-list li {
  color: #1D2129;
  line-height: 30px;
  padding: 16px;
  border-bottom: 1px solid #E8EBF3;
}
.main .jpPage {
  position: absolute;
  bottom: 0;
  height: 72px;
  display: flex;
  align-items: center;
  padding: 0 30px;
  border-top: 1px solid #E5E6EB;
  width: 100%;
  background-color: #fff;
}
.main .jpPage .count-total {
  position: absolute;
  left: 30px;
  line-height: 72px;
  color: #86909C;
}
.main .jpPage .count-total span {
  padding: 0 4px;
}
.main .jpPage .pageCon {
  width: calc(100% -  60px);
  text-align: center;
}
.main .course-list {
  margin: 24px 30px;
}
