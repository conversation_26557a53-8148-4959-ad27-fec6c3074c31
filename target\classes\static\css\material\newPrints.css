.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.hide {
  display: none !important;
}
.lab-box table tbody td {
  padding: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
.lab-box .table table tbody td {
  position: relative;
}
.lab-box .table table tbody td .state {
  position: absolute;
  top: 0;
  right: 0;
  border-radius: 0px 0px 0px 8px;
  background: transparent;
  padding: 0 8px;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
  color: black;
}
.lab-box .table {
  margin: 0;
}
@page {
  size: A4;
}
table {
  border-collapse: collapse;
}
table tr td {
  border-right: 2px solid black;
  empty-cells: show;
}
