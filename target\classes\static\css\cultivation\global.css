/* CSS Document */
body, form, body,form,textarea,select,option,ol,ul,li,h1,h2,h3,h4,h5,h6,p,th,td,dl,dt,dd,menu,blockquote,fieldset,label,i,em,header,footer,section,
legend,button,input,hr,pre,div,input,span,p,a{margin: 0;padding: 0;-webkit-tap-highlight-color:rgba(0,0,0,0);}
h1,h2,h3,h4,h5,h6{ font-weight:normal; font-size:100%;}
html{font-size: 62.5%;}
body, input, pre, select, textarea {
  font-size: 12px;
  line-height: 1.5;
  font-family: "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", 微软雅黑, Arial, sans-serif;
  color:#333;
}
li{list-style-type:none;}
input,select,textarea{vertical-align:middle;  color:#333; outline:none;-webkit-tap-highlight-color:rgba(0,0,0,0);}
a{text-decoration:none;color:#333;}
img{border:0;}
i,em{font-style:normal;}
b{ font-weight:normal;}
table{border-spacing:0; border-collapse:collapse;width:100%; border:0;}
.clearfix{overflow:hidden;}
.clear{clear:both; height:0; font-size:0; line-height:0; overflow:hidden;}
ul, ol, img {  border: 0px;  }
li {  list-style-type: none;  }
input,
select,
textarea {  vertical-align: middle;  outline: none;  }
textarea {  resize: none;  }
a {  text-decoration: none;  -webkit-tap-highlight-color: transparent;  -webkit-appearance: none;  }
input[type="button"]:hover {  cursor: pointer;  }
div:focus {  outline: none;  }
.clear {  clear: both;  }
.clearfix:after {  content: "";  display: block;  clear: both;  }
.fl{
  float: left;
}
.fr{
  float: right;
}
body .teacher_layer{
  border-radius: 10px;
}
body .teacher_layer .layui-layer-title{
  border-radius: 10px 10px 0 0;
  line-height: 60px;
  height: 60px;
  font-size: 18px;
  color: #242933;
  font-weight: bold;
  background: #FFFFFF;
  border-bottom: solid #F2F2F2 1px;
  padding: 0 30px;
  background: #fff;
}
body .teacher_layer .layui-layer-close1 {
  display: inline-block;
  width: 18px;
  height: 18px;
  background: url(../../images/cultivation/popClose.png) no-repeat;
  margin-top: 4px;
}
body .teacher_layer .layui-layer-btn{
  padding-top: 17px;
}
body .teacher_layer .layui-layer-btn .layui-layer-btn0{
  background: #4C88FF;
  border: 1px solid #4C88FF;
  border-radius: 20px;
  display: inline-block;
  width: 70px;
  height: 34px;
  text-align: center;
  line-height: 34px;
  font-size: 14px;
  color: #FFFFFF;
}
body .teacher_layer .layui-layer-btn .layui-layer-btn1{
  background: #FFFFFF;
  border: 1px solid #94C1FF;
  border-radius: 20px;
  display: inline-block;
  width: 70px;
  height: 34px;
  text-align: center;
  line-height: 34px;
  font-size: 14px;
  color: #4C88FF;
}
.layui-form .layui-table-cell input[type=checkbox], .layui-form .layui-table-cell input[type=radio] {
  display: none!important;
}
::-webkit-scrollbar {
    width: 8px;
    height: 8px
}

::-webkit-scrollbar-thumb {
    background: #dedfe0
}

::-webkit-scrollbar-thumb,::-webkit-scrollbar-track {
    border-radius: 8px;
    -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,.2)
}

::-webkit-scrollbar-track {
    background: #f9f9fa
}

