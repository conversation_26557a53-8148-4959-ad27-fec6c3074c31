* {
  font-family: <PERSON><PERSON><PERSON> SC;
}
.bottomLine {
  background: -webkit-linear-gradient(top, transparent 50%, #ebebeb 50%) center
    bottom no-repeat;
  background: -moz-linear-gradient(top, transparent 50%, #ebebeb 50%) center
    bottom no-repeat;
  background: -ms-linear-gradient(top, transparent 50%, #ebebeb 50%) center
    bottom no-repeat;
  background-size: 100% 1px;
}
.tophead {
  width: 100%;
  height: auto;
  text-align: center;
  background-color: #ffffff;
  z-index: 1;
}

.tophead .head {
  position: relative;
}

.tophead .leftHead {
  position: absolute;
  left: 0.16rem;
  bottom: 0;

  height: 0.88rem;
  z-index: 1;
  display: flex;
  align-items: center;
}

.tophead .back {
  float: left;
  width: 0.48rem;
  height: 0.48rem;
  background: url(../images/headback.png) center center/24px 24px no-repeat;
  background-size: 0.48rem;
}

.tophead .centerHead {
  position: relative;
}

.tophead .rightHead {
  position: absolute;
  right: 0.2rem;
  bottom: 0.2rem;
  z-index: 1;
  .menu {
    width: 0.48rem;
    height: 0.48rem;
    background: url("../images/menu.png") no-repeat center;
    background-size: 0.4rem;
  }
  .menu-list {
    width: 1.5rem;
    position: absolute;
    right: 0.02rem;
    top: 0.6rem;
    box-shadow: 0 0 0.1rem 0 rgba(0, 0, 0, 0.2);
    background-color: #ffffff;
    border-radius: 0.06rem;
    display: none;
    &::after {
      content: "";
      width: 0.2rem;
      height: 0.1rem;
      background: url("../images/arrow.png") no-repeat center;
      position: absolute;
      top: 0;
      right: 0;
      top: -0.09rem;
      right: 0.12rem;
      background-size: 0.16rem;
    }
    li {
      line-height: 0.54rem;
      font-size: 0.24rem;

      &:first-child {
        border-bottom: 0.005rem solid #f7f7f7;
        border-radius: 0.06rem 0.06rem 0 0;
      }
      &.active {
        background: #e1ebff;
      }
    }
  }
}

.tophead .completeDelete {
  display: none;
  position: absolute;
  right: 0;
  bottom: 0;
  height: 45px;
  line-height: 45px;
  text-align: center;
  width: 1.1rem;
  font-size: 15px;
  color: #0999ff;
  z-index: 1;
}

.tophead .rightHead .scan {
  display: none;
  float: left;
  width: 24px;
  height: 24px;
  background: url(../images/headback.png) center center/24px 24px no-repeat;
}

.tophead .rightHead .setting {
  float: left;
  width: 40px;
  height: 100%;
  background: url(../images/setting.png) center center/24px 24px no-repeat;
}

.search {
  padding: 0.2rem 0.3rem 0.16rem;
  box-sizing: border-box;
}

.search .search-text {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.28rem;
  height: 0.6rem;
  line-height: 0.6rem;
  color: #c9cdd4;
  background: #f5f6f8;
  border-radius: 0.3rem;
}

.search .search-text img {
  width: 0.36rem;
  margin-right: 0.1rem;
}

.search input {
  height: 0.6rem;
  background: #f5f6f8;
  border-radius: 0.3rem;
  font-size: 0.28rem;
  width: 100%;
  display: none;
  padding: 0 0.3rem;
  box-sizing: border-box;
}

.batchSel {
  height: 0.88rem;
  font-size: 0.3rem;
  line-height: 0.88rem;
  background: #ffffff;
  box-shadow: 0 0.02rem 0.16rem rgba(13, 13, 14, 0.08);
  color: #595969;
  padding: 0 0.45rem;
  box-sizing: border-box;
  position: relative;
  z-index: 99;

  display: flex;
  align-items: center;
}

.batchSel h3 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 600;
}

.batchSel h3 em {
  font-style: normal;
}

.batchSel span {
  width: 0.14rem;
  height: 0.08rem;
  flex-shrink: 0;
  background: url("../images/tringle-icon.png") no-repeat center;
  background-size: 0.14rem;
  margin-left: 0.08rem;
}

.selectBox {
  height: 0.88rem;
  text-align: center;
}

.selectBox .selectWeek {
  font-size: 0.36rem;
  line-height: 0.88rem;
  color: #000000;
  height: 100%;
  width: 50%;
  margin: 0 auto;
}

.selectBox .selectWeek img {
  width: 12px;
  height: 12px;
  margin-left: 6px;
  transition: all 0.2s;
}

.selectBox .selectList {
  display: none;
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
}

.selectBox .selectList ul {
  width: 100%;
  padding: 0.22rem 0.2rem 0.12rem;
  position: relative;
  background-color: #ffffff;
  overflow: hidden;
}

.selectBox .selectList li {
  float: left;
  width: 25%;
  text-align: center;
  padding: 0.08rem;
}

.selectBox .selectList li p {
  background-color: #f5f6f8;
  border-radius: 0.08rem;
  height: 0.72rem;
  font-size: 0.26rem;
  line-height: 0.72rem;
  color: #333333;
}

.selectBox .selectList li p.active {
  color: #0099ff;
}

.selectBox.active .selectWeek img {
  transform: rotate(180deg);
}

.selectBox.active .selectList {
  display: block;
}
.tophead .leftHead .select-week {
  float: left;
}
.tophead .leftHead .select-week i {
  font-style: normal;
}
.tophead .leftHead .select-week span {
  font-size: 0.32rem;
  color: #ff9a2e;
  float: left;
  line-height: 0.88rem;
}
.tophead .leftHead .select-week em {
  float: left;
  margin-left: 6px;
  width: 7px;
  height: 45px;
  background: url(../images/tringle-icon.png) no-repeat center;
  background-size: 7px 4px;
}

.z-table {
  .thead {
    overflow: hidden;
    border-bottom: 0.005rem solid #e5e6eb;
    position: relative;
    padding: 0 0.1rem;

    ul {
      height: 1.12rem;
      display: flex;
      align-items: center;
      li {
        flex: 1;
        color: #86909c;
        font-size: 0.3rem;
        &:first-child {
          width: 1.07rem;
        }
        span {
          display: block;
          text-align: center;
        }
      }
    }
  }
  .tbody {
    overflow: hidden;
    overflow-y: auto;

    -webkit-overflow-scrolling: touch;
    padding: 0 0.1rem;
    ul {
      display: flex;
      align-items: center;
      border-bottom: 0.01px dashed #e5e6eb;
      &:last-child {
        margin-bottom: 0.2rem;
        border-bottom: none;
      }

      li {
        height: 1.56rem;
        flex: 1;
        border-radius: 0.04rem;
        background-color: #f7f8fa;
        margin: 0.04rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #86909c;
        font-size: 0.28rem;
        cursor: not-allowed;
        span {
          display: block;
          text-align: center;
          width: 100%;
          &.section {
            margin-top: 0.16rem;
          }
          &.time {
            color: #86909c;
          }
        }
        &:first-child {
          display: unset;
          width: 1.07rem;
          flex: 1.5;
          background: none;
        }
        &:last-child {
          margin-right: 0;
        }
        &.selCourse {
          cursor: pointer;
          background-color: #e1ebff;
          color: #4d88ff;

          text-align: center;
          &.letter {
            writing-mode: tb-rl;
            letter-spacing: 0.1rem;
          }
        }
      }
    }
  }
}
#selCourseList {
  overflow: hidden;
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch;
}
#list {
  overflow: hidden;
}
#list {
  .z-search-course {
    margin: 0.28rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    input {
      display: block;
      flex: 1;
      height: 0.6rem;
      border-radius: 0.08rem;
      border: 1px solid #ddd;
      padding: 0 0.2rem;
      box-sizing: border-box;
      font-size: 0.26rem;
      &::placeholder {
        color: #999;
      }
    }
    .z-search-con {
      display: flex;
      align-items: center;
    }
    h3 {
      font-size: 0.3rem;
      margin-left: 0.3rem;
      flex: 1;
      color: #4d88ff;
    }
    img {
      width: 0.28rem;
      height: 0.28rem;
    }
  }
  .z-list {
    overflow: hidden;
    padding: 0 0.3rem;
    cursor: pointer;
    .z-course {
      padding: 0.2rem;
      margin-bottom: 0.24rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 0.2rem;
      border: 0.01rem solid #e1ebff;
      background: #fff;
      box-shadow: 0px 0px 0.08rem 0px rgba(0, 0, 0, 0.1);
      .z-course-mes {
        flex: 1;
        p {
          font-size: 0.3rem;
          color: #4e5969;
          &:last-child {
            span {
              padding-right: 0.24rem;
            }
          }
        }
      }
      .z-btn {
        flex-shrink: 0;

        span {
          display: block;
        }
        .btn-detail {
          border-radius: 36px;
          background: #4d88ff;
          width: 1.02rem;
          height: 0.48rem;
          text-align: center;
          line-height: 0.46rem;
          color: #ffffff;
          display: none;
          font-size: 0.26rem;
          margin-bottom: 0.2rem;
        }
        .btn-exit {
          display: none;
        }
        .btn-exit,
        .btn-sel {
          width: 1.02rem;
          height: 0.48rem;
          border: 1px solid #4d88ff;
          box-sizing: border-box;
          color: #4d88ff;
          background-color: #fff;
          border-radius: 36px;
          font-size: 14px;
          text-align: center;
          line-height: 0.46rem;
          font-size: 0.26rem;
        }
      }
      .z-course-title {
        display: flex;
        align-items: center;
        margin-bottom: 0.16rem;
        h3 {
          font-size: 0.32rem;
          color: #1d2129;
        }
        span {
          font-size: 0.3rem;
          color: #4d88ff;
          padding-left: 0.2rem;
        }
      }

      .z-mes {
        color: #1d2129;
        font-size: 14px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: 6px;
        p {
          color: #4e5969;
          font-size: 0.3rem;
          span {
            margin-right: 0.24rem;
          }
        }
      }
      &.active {
        background: #e1ebff;
        .z-btn {
          .btn-detail {
            display: block;
          }
        }
      }
    }
  }
}
.dialog-wrap {
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;

  .dialog {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: #ffffff;
    border-radius: 0.1rem;
    overflow: hidden;
  }
  .dialog-title {
    font-size: 16px;
    color: #1d2129;
    height: 56px;
    line-height: 56px;
    border-bottom: 1px solid #e5e6eb;
    text-indent: 30px;
  }
  .dialog-btn {
    height: 0.88rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 0.01rem solid #ebebec;
    .sure {
      width: 50%;
      height: 0.88rem;
      outline: none;
      border: none;
      background-color: #ffffff;
      color: #4d88ff;
      border-left: 0.01rem solid #ebebec;
      font-size: 0.32rem;
    }
    .cancel {
      width: 50%;
      height: 0.88rem;
      outline: none;
      border: none;
      background-color: #ffffff;
      color: #4d88ff;
      font-size: 0.32rem;
    }
  }
  &.dialog-plan {
    display: none;
    .dialog {
      width: 5.86rem;
      .dialog-con {
        overflow: hidden;
        max-height: 5.6rem;
        overflow-y: auto;
      }
      .plan {
        margin: 0 0.6rem 0.2rem;
        h1 {
          font-size: 0.3rem;
          color: #4e5969;
          margin: 0.3rem 0 0.1rem;
        }
        p {
          font-size: 0.26rem;
          color: #7a8aa1;
          margin-bottom: 0.08rem;
        }
      }
      .dialog-btn {
        height: 0.88rem;
        border-top: 0.01rem solid #e5e6eb;
        text-align: center;
        align-items: center;
        justify-content: center;
        color: #4d88ff;
        font-size: 0.32rem;
      }
    }
  }
  &.dialog-course {
    display: none;
    .dialog {
      width: 7.29rem;
      .z-tab-search {
        // width: 200px;
        margin: 0.41rem 0.3rem 0.2rem;
        border-radius: 0.3rem;
        background: #f5f6f8;
        height: 0.6rem;
        input {
          display: block;
          width: 100%;
          height: 0.6rem;
          border: none;
          background: none;
          text-align: center;
          padding: 0 0.3rem;
          box-sizing: border-box;
          font-size: 0.28rem;
          display: none;
        }
        .z-search {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #c9cdd4;
          font-size: 0.28rem;
          height: 0.6rem;
          img {
            margin-right: 0.1rem;
            width: 0.3rem;
          }
        }
      }
      .z-list {
        overflow: hidden;
        padding: 0 0.3rem;
        max-height: 7rem;
        overflow-y: auto;
        cursor: pointer;
        .z-course {
          background: #f8f8f8;
          padding: 0.2rem 0.33rem;
          margin-bottom: 10px;
          border-radius: 0.2rem;
          border: 0.01rem solid #e1ebff;
          background: #fff;
          box-shadow: 0px 0px 0.08rem 0px rgba(0, 0, 0, 0.1);
          .z-course-title {
            display: flex;
            align-items: center;
            margin-bottom: 0.16rem;
            h3 {
              font-size: 0.32rem;
              color: #1d2129;
            }
            span {
              color: #4d88ff;
              font-size: 0.3rem;
              padding-left: 0.2rem;
            }
          }
          p {
            color: #4e5969;
            font-size: 0.3rem;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 0.14rem;
            span {
              margin-right: 0.3rem;
              span {
                color: #4e5969;
              }
            }
          }
          &.active {
            background: #e1ebff;
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
          }
          &.z-full {
            cursor: not-allowed;
          }
        }
      }
    }
  }
  &.dialog-mes {
    display: none;
    .dialog {
      width: 6.2rem;
    }
    .dialog-con {
      padding: 0 0.48rem;

      h3 {
        font-size: 0.3rem;
        color: #1d2129;
        margin-top: 0.28rem;
        margin-bottom: 0.26rem;
      }
      p {
        font-size: 0.26rem;
        color: #1d2129;
        display: flex;
        margin-bottom: 0.26rem;
        span {
          width: 1.5rem;
          flex-shrink: 0;
        }
        &:nth-child(2) {
          span {
            width: 2rem;
          }
        }
      }
    }
  }
}
.week-dialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  background: rgba(0, 0, 0, 0.2);
  display: none;
}
.week-dialog .w-con {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  transform: translateY(100%);
  -webkit-transform: translateY(100%);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.week-dialog .w-con.active {
  transform: translateY(0);
  -webkit-transform: translateY(0);
}
.week-dialog .w-con .w-head {
  background: #ffffff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 0.32rem 0.32rem 0 0;
  width: 100%;
  height: 0.9rem;
}
.week-dialog .w-con .w-head h3 {
  text-align: center;
  line-height: 0.9rem;
  color: #333333;
  font-weight: 400;
  font-size: 16px;
}
.week-dialog .w-con .w-box {
  width: 100%;
  height: auto;
  background-color: #fff;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding: 15px 35px;
}
.week-dialog .w-con .w-box ul {
  overflow: hidden;
}
.week-dialog .w-con .w-box ul li {
  float: left;
  width: 13.33%;
  margin-right: 4%;
  height: 0.68rem;
  background: #f1f3f6;
  border-radius: 0.08rem;
  margin-bottom: 12px;
  font-size: 13px;
  color: #4e5969;
  text-align: center;
  line-height: 0.68rem;
}
.week-dialog .w-con .w-box ul li.cur {
  background: #4d88ff;
  color: #fff;
}
.week-dialog .w-con .w-box ul li:nth-child(6n) {
  margin-right: 0;
}

#tipsBox {
  position: fixed;
  left: 50%;
  top: 50%;
  z-index: 99999;
  transform: translateX(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 0.2rem 0.3rem;
  border-radius: 0.08rem;
  font-size: 0.3rem;
  display: none;
}
