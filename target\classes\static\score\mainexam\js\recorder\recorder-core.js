/*
录音
https://github.com/xiangyuecn/Recorder
src: recorder-core.js
*/
!function(w){"use strict";var y=function(){},P=function(e){return new t(e)};P.LM="2023-07-01 20:46";var I="Recorder",S="getUserMedia",U="srcSampleRate",N="sampleRate",L="catch";P.<PERSON>=function(){var e=P.Stream;if(e){var t=e.getTracks&&e.getTracks()||e.audioTracks||[],r=t[0];if(r){var n=r.readyState;return"live"==n||n==r.LIVE}}return!1},P.BufferSize=4096,P.Destroy=function(){for(var e in z(I+" Destroy"),M(),r)r[e]()};var r={};P.BindDestroy=function(e,t){r[e]=t},P.Support=function(){var e=navigator.mediaDevices||{};return e[S]||(e=navigator)[S]||(e[S]=e.webkitGetUserMedia||e.mozGetUserMedia||e.msGetUserMedia),!!e[S]&&(P.Scope=e,!!P.GetContext())},P.GetContext=function(e){var t=w.AudioContext;if(t||(t=w.webkitAudioContext),!t)return null;var r=P.Ctx;if(r&&"closed"!=r.state||(r=P.Ctx=new t,P.NewCtxs=P.NewCtxs||[],P.BindDestroy("Ctx",function(){var e=P.Ctx;e&&e.close&&(e.close(),P.Ctx=0);var t=P.NewCtxs;P.NewCtxs=[];for(var r=0;r<t.length;r++)t[r].close()})),e&&r.close)try{r=new t,P.NewCtxs.push(r)}catch(e){z("GetContext tryNew异常",1,e)}return r},P.CloseNewCtx=function(e){if(e&&e!=P.Ctx){e.close&&e.close();for(var t=P.NewCtxs||[],r=t.length,n=0;n<t.length;n++)if(t[n]==e){t.splice(n,1);break}z("剩"+r+"-1="+t.length+"个GetContext未close",t.length?3:0)}};var b=function(e){var t=e.state,r="ctx.state="+t;return"suspended"==t&&(r+="（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）"),r},A="ConnectEnableWebM";P[A]=!0;var T="ConnectEnableWorklet";P[T]=!1;var x=function(e,c){var f=e.BufferSize||P.BufferSize,u=e.Stream,l=u._RC||u._c||P.GetContext(!0);u._c=l;var v,r,h,i=function(e){var t=u._m=l.createMediaStreamSource(u),r=l.destination,n="createMediaStreamDestination";l[n]&&(r=u._d=l[n]()),t.connect(e),e.connect(r)},p="",g=u._call,d=function(e){for(var t in g){for(var r=e.length,n=new Int16Array(r),a=0,o=0;o<r;o++){var i=Math.max(-1,Math.min(1,e[o]));i=i<0?32768*i:32767*i,n[o]=i,a+=Math.abs(i)}for(var s in g)g[s](n,a);return}},m="ScriptProcessor",C="audioWorklet",s=I+" "+C,_="RecProc",y="MediaRecorder",S=y+".WebM.PCM",b=l.createScriptProcessor||l.createJavaScriptNode,x="。由于"+C+"内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启"+C+"。",M=function(){r=u.isWorklet=!1,n(u),z("Connect采用老的"+m+"，"+(P[T]?"但已":"可")+"设置"+I+"."+T+"=true尝试启用"+C+p+x,3);var e=u._p=b.call(l,f,1,1);i(e),e.onaudioprocess=function(e){var t=e.inputBuffer.getChannelData(0);d(t)}},k=function(){v=u.isWebM=!1,D(u),r=u.isWorklet=!b||P[T];var t=w.AudioWorkletNode;if(r&&l[C]&&t){var n=function(){return r&&u._na},a=u._na=function(){""!==h&&(clearTimeout(h),h=setTimeout(function(){h=0,n()&&(z(C+"未返回任何音频，恢复使用"+m,3),b&&M())},500))},o=function(){if(n()){var e=u._n=new t(l,_,{processorOptions:{bufferSize:f}});i(e),e.port.onmessage=function(e){h&&(clearTimeout(h),h=""),n()?d(e.data.val):r||z(C+"多余回调",3)},z("Connect采用"+C+"，设置"+I+"."+T+"=false可恢复老式"+m+p+x,3)}};l.resume()[g&&"finally"](function(){if(n())if(l[_])o();else{var e,t,r=(t="class "+_+" extends AudioWorkletProcessor{",t+="constructor "+(e=function(e){return e.toString().replace(/^function|DEL_/g,"").replace(/\$RA/g,s)})(function(e){DEL_super(e);var t=this,r=e.processorOptions.bufferSize;t.bufferSize=r,t.buffer=new Float32Array(2*r),t.pos=0,t.port.onmessage=function(e){e.data.kill&&(t.kill=!0,$C.log("$RA kill call"))},$C.log("$RA .ctor call",e)}),t+="process "+e(function(e,t,r){var n=this,a=n.bufferSize,o=n.buffer,i=n.pos;if((e=(e[0]||[])[0]||[]).length){o.set(e,i);var s=~~((i+=e.length)/a)*a;if(s){this.port.postMessage({val:o.slice(0,s)});var c=o.subarray(s,i);(o=new Float32Array(2*a)).set(c),i=c.length,n.buffer=o}n.pos=i}return!n.kill}),t=(t+='}try{registerProcessor("'+_+'", '+_+')}catch(e){$C.error("'+s+'注册失败",e)}').replace(/\$C\./g,"console."),"data:text/javascript;base64,"+btoa(unescape(encodeURIComponent(t))));l[C].addModule(r).then(function(e){n()&&(l[_]=1,o(),h&&a())})[L](function(e){z(C+".addModule失败",1,e),n()&&M()})}})}else M()};!function(){var e=w[y],t="ondataavailable",r="audio/webm; codecs=pcm";v=u.isWebM=P[A];var n=e&&t in e.prototype&&e.isTypeSupported(r);if(p=n?"":"（此浏览器不支持"+S+"）",!c||!v||!n)return k();var a=function(){return v&&u._ra},o=(u._ra=function(){""!==h&&(clearTimeout(h),h=setTimeout(function(){a()&&(z(y+"未返回任何音频，降级使用"+C,3),k())},500))},Object.assign({mimeType:r},P.ConnectWebMOptions)),i=u._r=new e(u,o),s=u._rd={sampleRate:l[N]};i[t]=function(e){var t=new FileReader;t.onloadend=function(){if(a()){var e=R(new Uint8Array(t.result),s);if(!e)return;if(-1==e)return void k();h&&(clearTimeout(h),h=""),d(e)}else v||z(y+"多余回调",3)},t.readAsArrayBuffer(e.data)},i.start(~~(f/48)),z("Connect采用"+S+"，设置"+I+"."+A+"=false可恢复使用"+C+"或老式"+m)}()},n=function(e){e._na=null,e._n&&(e._n.port.postMessage({kill:!0}),e._n.disconnect(),e._n=null)},D=function(e){e._ra=null,e._r&&(e._r.stop(),e._r=null)},M=function(e){var t=(e=e||P)==P,r=e.Stream;r&&(r._m&&(r._m.disconnect(),r._m=null),!r._RC&&r._c&&P.CloseNewCtx(r._c),r._RC=null,r._c=null,r._d&&(a(r._d.stream),r._d=null),r._p&&(r._p.disconnect(),r._p.onaudioprocess=r._p=null),n(r),D(r),t&&a(r)),e.Stream=0},a=P.StopS_=function(e){for(var t=e.getTracks&&e.getTracks()||e.audioTracks||[],r=0;r<t.length;r++){var n=t[r];n.stop&&n.stop()}e.stop&&e.stop()};P.SampleData=function(e,t,r,n,a){var o="SampleData";n||(n={});var i=n.index||0,s=n.offset||0,c=n.filter;if(c&&c.fn&&c.sr!=t&&(c=null,z(o+"的filter采样率变了，重设滤波",3)),!c){var f=3*t/4<r?0:r/2*3/4;c={fn:f?P.IIRFilter(!0,t,f):0}}c.sr=t;var u=c.fn,l=n.frameNext||[];a||(a={});var v=a.frameSize||1;a.frameType&&(v="mp3"==a.frameType?1152:1);var h=e.length;h+1<i&&z(o+"似乎传入了未重置chunk "+i+">"+h,3);for(var p=0,g=i;g<h;g++)p+=e[g].length;p=Math.max(0,p-Math.floor(s));var d=t/r;1<d?p=Math.floor(p/d):(d=1,r=t),p+=l.length;for(var m=new Int16Array(p),C=0,g=0;g<l.length;g++)m[C]=l[g],C++;for(;i<h;i++){for(var _=e[i],g=s,y=_.length,S=u&&u.Embed,b=0,x=0,M=0,k=0,w=0,I=0;w<y;w++,I++)if(I<y&&(S?(M=_[I],k=S.b0*M+S.b1*S.x1+S.b0*S.x2-S.a1*S.y1-S.a2*S.y2,S.x2=S.x1,S.x1=M,S.y2=S.y1,S.y1=k):k=u?u(_[I]):_[I]),b=x,x=k,0!=I){var L=Math.floor(g);if(w==L){var A=Math.ceil(g),T=g-L,D=b,R=A<y?x:D;m[C]=D+(R-D)*T,C++,g+=d}}else w--;s=Math.max(0,g-y)}l=null;var O=m.length%v;if(0<O){var F=2*(m.length-O);l=new Int16Array(m.buffer.slice(F)),m=new Int16Array(m.buffer.slice(0,F))}return{index:i,offset:s,filter:c,frameNext:l,sampleRate:r,data:m}},P.IIRFilter=function(e,t,r){var n=2*Math.PI*r/t,a=Math.sin(n),o=Math.cos(n),i=a/2,s=1+i,c=-2*o/s,f=(1-i)/s;if(e)var u=(1-o)/2/s,l=(1-o)/s;else var u=(1+o)/2/s,l=-(1+o)/s;var v=0,h=0,p=0,g=0,d=0,m=function(e){return p=u*e+l*v+u*h-c*g-f*d,h=v,v=e,d=g,g=p};return m.Embed={x1:0,x2:0,y1:0,y2:0,b0:u,b1:l,a1:c,a2:f},m},P.PowerLevel=function(e,t){var r=e/t||0;return r<1251?Math.round(r/1250*10):Math.round(Math.min(100,Math.max(0,100*(1+Math.log(r/1e4)/Math.log(10)))))},P.PowerDBFS=function(e){var t=Math.max(.1,e||0);return t=Math.min(t,32767),t=20*Math.log(t/32767)/Math.log(10),Math.max(-100,Math.round(t))},P.CLog=function(e,t){var r=new Date,n=("0"+r.getMinutes()).substr(-2)+":"+("0"+r.getSeconds()).substr(-2)+"."+("00"+r.getMilliseconds()).substr(-3),a=this&&this.envIn&&this.envCheck&&this.id,o=["["+n+" "+I+(a?":"+a:"")+"]"+e],i=arguments,s=w.console||{},c=2,f=s.log;for("number"==typeof t?f=1==t?s.error:3==t?s.warn:f:c=1;c<i.length;c++)o.push(i[c]);u?f&&f("[IsLoser]"+o[0],1<o.length?o:""):f.apply(s,o)};var z=function(){P.CLog.apply(this,arguments)},u=!0;try{u=!console.log.apply}catch(e){}var o=0;function t(e){this.id=++o,i();var t={type:"mp3",bitRate:16,sampleRate:16e3,onProcess:y};for(var r in e)t[r]=e[r];this.set=t,this._S=9,this.Sync={O:9,C:9}}P.Sync={O:9,C:9},P.prototype=t.prototype={CLog:z,_streamStore:function(){return this.set.sourceStream?this:P},_streamCtx:function(){var e=this._streamStore().Stream;return e&&e._c},open:function(e,r){var n=this,a=n.set,o=n._streamStore(),i=0;e=e||y;var s=function(e,t){t=!!t,n.CLog("录音open失败："+e+",isUserNotAllow:"+t,1),i&&P.CloseNewCtx(i),r&&r(e,t)},c=function(){n.CLog("open ok id:"+n.id),e(),n._SO=0},f=o.Sync,u=++f.O,l=f.C;n._O=n._O_=u,n._SO=n._S;var t=n.envCheck({envName:"H5",canProcess:!0});if(t)s("不能录音："+t);else if(a.sourceStream){if(!P.GetContext())return void s("不支持此浏览器从流中获取录音");M(o);var v=n.Stream=a.sourceStream;v._RC=a.runningContext,v._call={};try{x(o)}catch(e){return M(o),void s("从流中打开录音失败："+e.message)}c()}else{var h=function(e,t){try{w.top.a}catch(e){return void s('无权录音(跨域，请尝试给iframe添加麦克风访问策略，如allow="camera;microphone")')}/Permission|Allow/i.test(e)?s("用户拒绝了录音权限",!0):!1===w.isSecureContext?s("浏览器禁止不安全页面录音，可开启https解决"):/Found/i.test(e)?s(t+"，无可用麦克风"):s(t)};if(P.IsOpen())c();else if(P.Support()){var p=a.runningContext;p||(p=i=P.GetContext(!0));var g=function(t){setTimeout(function(){t._call={};var e=P.Stream;e&&(M(),t._call=e._call),(P.Stream=t)._c=p,t._RC=a.runningContext,function(){if(l!=f.C||!n._O){var e="open被取消";return u==f.O?n.close():e="open被中断",s(e),!0}}()||(P.IsOpen()?(e&&n.CLog("发现同时多次调用open",1),x(o,1),c()):s("录音功能无效：无音频流"))},100)},d=function(e){var t=e.name||e.message||e.code+":"+e;n.CLog("请求录音权限错误",1,e),h(t,"无法录音："+t)},m=a.audioTrackSet||{};m[N]=p[N];var C={audio:m};try{var _=P.Scope[S](C,g,d)}catch(e){n.CLog(S,3,e),C={audio:!0},_=P.Scope[S](C,g,d)}n.CLog(S+"("+JSON.stringify(C)+") "+b(p)+"，一般默认会降噪和回声消除，移动端可能会降低系统播放音量，请参阅文档中audioTrackSet配置"),_&&_.then&&_.then(g)[L](d)}else h("","此浏览器不支持录音")}},close:function(e){e=e||y;var t=this,r=t._streamStore();t._stop();var n=r.Sync;if(t._O=0,t._O_!=n.O)return t.CLog("close被忽略（因为同时open了多个rec，只有最后一个会真正close）",3),void e();n.C++,M(r),t.CLog("close"),e()},mock:function(e,t){var r=this;return r._stop(),r.isMock=1,r.mockEnvInfo=null,r.buffers=[e],r.recSize=e.length,r[U]=t,r},envCheck:function(e){var t,r=this.set,n="CPU_BE";if(t||P[n]||!w.Int8Array||new Int8Array(new Int32Array([1]).buffer)[0]||(i(n),t="不支持CPU_BE架构"),!t){var a=r.type;this[a+"_envCheck"]?t=this[a+"_envCheck"](e,r):r.takeoffEncodeChunk&&(t=a+"类型"+(this[a]?"":"(未加载编码器)")+"不支持设置takeoffEncodeChunk")}return t||""},envStart:function(e,t){var r=this,n=r.set;r.isMock=e?1:0,r.mockEnvInfo=e,r.buffers=[],r.recSize=0,r.envInLast=0,r.envInFirst=0,r.envInFix=0,r.envInFixTs=[];var a=n[N];if(t<a?n[N]=t:a=0,r[U]=t,r.CLog(U+": "+t+" set."+N+": "+n[N]+(a?" 忽略"+a:""),a?3:0),r.engineCtx=0,r[n.type+"_start"]){var o=r.engineCtx=r[n.type+"_start"](n);o&&(o.pcmDatas=[],o.pcmSize=0)}},envResume:function(){this.envInFixTs=[]},envIn:function(e,t){var a=this,o=a.set,i=a.engineCtx,r=a[U],n=e.length,s=P.PowerLevel(t,n),c=a.buffers,f=c.length;c.push(e);var u=c,l=f,v=Date.now(),h=Math.round(n/r*1e3);a.envInLast=v,1==a.buffers.length&&(a.envInFirst=v-h);var p=a.envInFixTs;p.splice(0,0,{t:v,d:h});for(var g=v,d=0,m=0;m<p.length;m++){var C=p[m];if(3e3<v-C.t){p.length=m;break}g=C.t,d+=C.d}var _=p[1],y=v-g;if(y/3<y-d&&(_&&1e3<y||6<=p.length)){var S=v-_.t-h;if(h/5<S){var b=!o.disableEnvInFix;if(a.CLog("["+v+"]"+(b?"":"未")+"补偿"+S+"ms",3),a.envInFix+=S,b){var x=new Int16Array(S*r/1e3);n+=x.length,c.push(x)}}}var M=a.recSize,k=n,w=M+k;if(a.recSize=w,i){var I=P.SampleData(c,r,o[N],i.chunkInfo);i.chunkInfo=I,w=(M=i.pcmSize)+(k=I.data.length),i.pcmSize=w,c=i.pcmDatas,f=c.length,c.push(I.data),r=I[N]}var L=Math.round(w/r*1e3),A=c.length,T=u.length,D=function(){for(var e=R?0:-k,t=null==c[0],r=f;r<A;r++){var n=c[r];null==n?t=1:(e+=n.length,i&&n.length&&a[o.type+"_encode"](i,n))}if(t&&i)for(r=l,u[0]&&(r=0);r<T;r++)u[r]=null;t&&(e=R?k:0,c[0]=null),i?i.pcmSize+=e:a.recSize+=e},R=0,O="rec.set.onProcess";try{R=o.onProcess(c,s,L,r,f,D)}catch(e){console.error(O+"回调出错是不允许的，需保证不会抛异常",e)}var F=Date.now()-v;if(10<F&&1e3<a.envInFirst-v&&a.CLog(O+"低性能，耗时"+F+"ms",3),!0===R){var z=0;for(m=f;m<A;m++)null==c[m]?z=1:c[m]=new Int16Array(0);z?a.CLog("未进入异步前不能清除buffers",3):i?i.pcmSize-=k:a.recSize-=k}else D()},start:function(){var t=this,e=1;if(t.set.sourceStream?t.Stream||(e=0):P.IsOpen()||(e=0),e){var r=t._streamCtx();if(t.CLog("start 开始录音 "+b(r)),t._stop(),t.state=3,t.envStart(null,r[N]),t._SO&&t._SO+1!=t._S)t.CLog("start被中断",3);else{t._SO=0;var n=function(){3==t.state&&(t.state=1,t.resume())};if("suspended"==r.state){var a="AudioContext resume: ";t.CLog(a+"wait..."),r.resume().then(function(){t.CLog(a+r.state),n()})[L](function(e){t.CLog(a+r.state+" 可能无法录音："+e.message,1,e),n()})}else n()}}else t.CLog("未open",1)},pause:function(){var e=this;e.state&&(e.state=2,e.CLog("pause"),delete e._streamStore().Stream._call[e.id])},resume:function(){var e,r=this;if(r.state){r.state=1,r.CLog("resume"),r.envResume();var t=r._streamStore().Stream;t._call[r.id]=function(e,t){1==r.state&&r.envIn(e,t)},(e=t)._na&&e._na(),e._ra&&e._ra()}},_stop:function(e){var t=this,r=t.set;t.isMock||t._S++,t.state&&(t.pause(),t.state=0),!e&&t[r.type+"_stop"]&&(t[r.type+"_stop"](t.engineCtx),t.engineCtx=0)},stop:function(r,t,e){var n,a=this,o=a.set,i=a.envInLast-a.envInFirst,s=i&&a.buffers.length;a.CLog("stop 和start时差"+(i?i+"ms 补偿"+a.envInFix+"ms envIn:"+s+" fps:"+(s/i*1e3).toFixed(1):"-")+" LM:"+P.LM);var c=function(){a._stop(),e&&a.close()},f=function(e){a.CLog("结束录音失败："+e,1),t&&t(e),c()},u=function(e,t){if(a.CLog("结束录音 编码花"+(Date.now()-n)+"ms 音频时长"+t+"ms 文件大小"+e.size+"b"),o.takeoffEncodeChunk)a.CLog("启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据",3);else if(e.size<Math.max(100,t/2))return void f("生成的"+o.type+"无效");r&&r(e,t),c()};if(!a.isMock){var l=3==a.state;if(!a.state||l)return void f("未开始录音"+(l?"，开始录音前无用户交互导致AudioContext未运行":""));a._stop(!0)}var v=a.recSize;if(v)if(a.buffers[0])if(a[o.type]){if(a.isMock){var h=a.envCheck(a.mockEnvInfo||{envName:"mock",canProcess:!1});if(h)return void f("录音错误："+h)}var p=a.engineCtx;if(a[o.type+"_complete"]&&p){var g=Math.round(p.pcmSize/o[N]*1e3);return n=Date.now(),void a[o.type+"_complete"](p,function(e){u(e,g)},f)}n=Date.now();var d=P.SampleData(a.buffers,a[U],o[N]);o[N]=d[N];var m=d.data;g=Math.round(m.length/o[N]*1e3),a.CLog("采样"+v+"->"+m.length+" 花:"+(Date.now()-n)+"ms"),setTimeout(function(){n=Date.now(),a[o.type](m,function(e){u(e,g)},function(e){f(e)})})}else f("未加载"+o.type+"编码器");else f("音频buffers被释放");else f("未采集到录音")}},w[I]&&(z("重复引入"+I,3),w[I].Destroy()),w[I]=P;var R=function(e,t){t.pos||(t.pos=[0],t.tracks={},t.bytes=[]);var r=t.tracks,n=[t.pos[0]],a=function(){t.pos[0]=n[0]},o=t.bytes.length,i=new Uint8Array(o+e.length);if(i.set(t.bytes),i.set(e,o),t.bytes=i,!t._ht){if(E(i,n),W(i,n),!O(E(i,n),[24,83,128,103]))return;for(E(i,n);n[0]<i.length;){var s=E(i,n),c=W(i,n),f=[0],u=0;if(!c)return;if(O(s,[22,84,174,107])){for(;f[0]<c.length;){var l=E(c,f),v=W(c,f),h=[0],p={channels:0,sampleRate:0};if(O(l,[174]))for(;h[0]<v.length;){var g=E(v,h),d=W(v,h),m=[0];if(O(g,[215])){var C=F(d);p.number=C,r[C]=p}else if(O(g,[131])){var C=F(d);1==C?p.type="video":2==C?(p.type="audio",u||(t.track0=p),p.idx=u++):p.type="Type-"+C}else if(O(g,[134])){for(var _="",y=0;y<d.length;y++)_+=String.fromCharCode(d[y]);p.codec=_}else if(O(g,[225]))for(;m[0]<d.length;){var S=E(d,m),b=W(d,m);if(O(S,[181])){var C=0,x=new Uint8Array(b.reverse()).buffer;4==b.length?C=new Float32Array(x)[0]:8==b.length?C=new Float64Array(x)[0]:z("WebM Track !Float",1,b),p[N]=Math.round(C)}else O(S,[98,100])?p.bitDepth=F(b):O(S,[159])&&(p.channels=F(b))}}}t._ht=1,z("WebM Tracks",r),a();break}}}var M=t.track0;if(M){if(16==M.bitDepth&&/FLOAT/i.test(M.codec)&&(M.bitDepth=32,z("WebM 16改32位",3)),M[N]!=t[N]||32!=M.bitDepth||M.channels<1||!/(\b|_)PCM\b/i.test(M.codec))return t.bytes=[],t.bad||z("WebM Track非预期",3,t),-(t.bad=1);for(var k=[],w=0;n[0]<i.length;){var l=E(i,n),v=W(i,n);if(!v)break;if(O(l,[163])){var I=15&v[0],p=r[I];if(p){if(0===p.idx){for(var L=new Uint8Array(v.length-4),y=4;y<v.length;y++)L[y-4]=v[y];k.push(L),w+=L.length}}else z("WebM !Track"+I,1,r)}a()}if(w){var A=new Uint8Array(i.length-t.pos[0]);A.set(i.subarray(t.pos[0])),t.bytes=A,t.pos[0]=0;for(var L=new Uint8Array(w),y=0,T=0;y<k.length;y++)L.set(k[y],T),T+=k[y].length;var x=new Float32Array(L.buffer);if(1<M.channels){for(var D=[],y=0;y<x.length;)D.push(x[y]),y+=M.channels;x=new Float32Array(D)}return x}}},O=function(e,t){if(!e||e.length!=t.length)return!1;if(1==e.length)return e[0]==t[0];for(var r=0;r<e.length;r++)if(e[r]!=t[r])return!1;return!0},F=function(e){for(var t="",r=0;r<e.length;r++){var n=e[r];t+=(n<16?"0":"")+n.toString(16)}return parseInt(t,16)||0},E=function(e,t,r){var n=t[0];if(!(n>=e.length)){var a=e[n],o=("0000000"+a.toString(2)).substr(-8),i=/^(0*1)(\d*)$/.exec(o);if(i){var s=i[1].length,c=[];if(!(n+s>e.length)){for(var f=0;f<s;f++)c[f]=e[n],n++;return r&&(c[0]=parseInt(i[2]||"0",2)),t[0]=n,c}}}},W=function(e,t){var r=E(e,t,1);if(r){var n=F(r),a=t[0],o=[];if(n<2147483647){if(a+n>e.length)return;for(var i=0;i<n;i++)o[i]=e[a],a++}return t[0]=a,o}};P.TrafficImgUrl="//ia.51.la/go1?id=20469973&pvFlag=1";var i=P.Traffic=function(e){e=e?"/"+I+"/Report/"+e:"";var t=P.TrafficImgUrl;if(t){var r=P.Traffic,n=/^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href)||[],a=n[1]||"http://file/",o=(n[0]||a)+e;if(0==t.indexOf("//")&&(t=/^https:/i.test(o)?"https:"+t:"http:"+t),e&&(t=t+"&cu="+encodeURIComponent(a+e)),!r[o]){r[o]=1;var i=new Image;i.src=t,z("Traffic Analysis Image: "+(e||I+".TrafficImgUrl="+P.TrafficImgUrl))}}}}(window),"function"==typeof define&&define.amd&&define(function(){return Recorder}),"object"==typeof module&&module.exports&&(module.exports=Recorder);