.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}

.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}

textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}

textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}

input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

textarea:-ms-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}

.clearfixs {
  zoom: 1;
}

.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#scheduleHead thead tr th {
  position: relative;
}

#scheduleHead thead tr th .weeks {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 0px 0.08rem 0.08rem 0px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
}

#scheduleHead thead tr th .weeks span {
  width: 0.34rem;
  margin: 0 auto;
  line-height: 0.22rem;
  font-size: 10px;
  color: #008ae6;
  text-align: center;
  transform: scale(0.9);
  -webkit-transform: scale(0.9);
}

#scheduleHead thead tr th .weeks span em {
  font-weight: 500;
  font-size: 14px;
  line-height: 0.39rem;
  text-align: center;
  color: #008ae6;
  font-style: normal;
}

#scheduleTable tbody .clickable.cur {
  border: 0.03rem solid #0099ff;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  border-radius: 0.08rem;
}

#scheduleTable tbody .clickable.active {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  background: #0094ff;
  box-shadow: 0px 0.02rem 0.17rem rgba(211, 211, 211, 0.5);
  border-radius: 0.08rem;
}

#scheduleTable tbody .clickable.disabled {
  background: #f0f0f0;
  border-radius: 0.08rem;
}

#scheduleTable tbody .disabled.cur {
  background: #f0f0f0;
  border-radius: 0.08rem;
}

#scheduleTable tbody .disabled.cur .courseName {
  color: #a3a3a3;
}

#scheduleTable tbody .disabled.cur .courseLoc {
  color: #a3a3a3;
}

#scheduleTable tbody .disabled.cur .teacher {
  color: #a3a3a3;
}

table .tddiv.borders {
  border: 0.03rem solid #0099ff;
  border-radius: 0.08rem;
}

.settingMask.shows {
  display: block;
}

.ios-select-widget-box header.iosselect-header a {
  color: #0099ff;
}

.layer:after {
  content: "至";
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0);
  -webkit-transform: translate(-50%, 0);
  top: 149px;
  width: 40px;
  height: 35px;
  color: rgba(36, 36, 37, 0.6);
  font-size: 0.24rem;
  line-height: 35px;
  text-align: center;
}

.ios-select-widget-box ul li.at {
  font-size: 15px;
}

.ios-select-widget-box ul li.side1 {
  font-size: 14px;
}

.ios-select-widget-box ul li.side2 {
  font-size: 13px;
}

.ios-select-widget-box ul li {
  font-size: 12px;
}

.ios-select-widget-box .cover-area1 {
  height: 40px;
  background-color: rgba(36, 36, 37, 0.02);
  border: none;
}

.ios-select-widget-box .cover-area2 {
  border: none;
}

.two-level-box .iosselect-box {
  padding-left: 0 !important;
}

.one-level-box .layer:after {
  display: none;
}

.two-level-box .one-level-contain:after {
  display: none;
}

table td {
  border-right: none;
}

table .col0 {
  background: #ffffff;
}

table tr td:first-child {
  border-right: none;
  border-bottom: dashed 1px #f3f4f6;
}

#scheduleHead {
  padding: 0 0.24rem;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}

.table,
.tables {
  padding: 0 0.24rem;
}

table thead tr th {
  background-color: #fff;
}

#scheduleHead thead tr th .weeks span {
  color: #4e5969;
}

#scheduleHead thead tr th .weeks span em {
  color: #4e5969;
}

table .tddiv.color1 {
  background: #e1ebff;
  border-radius: 0.08rem;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  flex-wrap: wrap;
}

table td[rowspan] p {
  color: #86909c;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  padding: 0 0.08rem;
}

.tophead .rightHead .search {
  float: left;
  width: 40px;
  height: 100%;
  background: url(../../../images/examination/search-icon.png) center center/24px 24px no-repeat;
  background-size: .48rem;
}

.tophead .rightHead .share {
  float: left;
  width: .48rem;
  height: .48rem;
  background: url(../../../images/examination/share-icon.png) center center/24px 24px no-repeat;
  background-size: .48rem;
}

.tophead .rightHead .menu {
  float: left;
  width: .48rem;
  height: .48rem;
  background: url(../../../images/examination/menu.png) center center/24px 24px no-repeat;
  background-size: .48rem;
}

.tophead .leftHead .select-week {
  float: left;
}

.tophead .leftHead .select-week i {
  font-style: normal;
}

.tophead .leftHead .select-week span {
  font-size: 16px;
  color: #ff9a2e;
  float: left;
  line-height: 45px;
}

.tophead .leftHead .select-week em {
  float: left;
  margin-left: 6px;
  width: 7px;
  height: 45px;
  background: url(../../../images/examination/tringle-icon.png) no-repeat center;
  background-size: 7px 4px;
}

.week-dialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  background: rgba(0, 0, 0, 0.2);
  display: none;
}

.week-dialog .w-con {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  transform: translateY(100%);
  -webkit-transform: translateY(100%);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}

.batchDetail {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  background: rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding-top: 2rem;
  padding-bottom: 2rem;
  padding: 2rem 0.24rem 0.24rem;
  display: none;
}

.batchCon {
  position: fixed;
  background-color: #ffffff;
  width: 6.08rem;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 0.2rem;
}

.batchClose {
  position: absolute;
  width: .32rem;
  height: .32rem;
  background: url('../../../images/examination/close.png') no-repeat center;
  background-size: .32rem;
  z-index: 99;
  right: -0.32rem;
  top: 0;
}

.batchCon .item-list {
  margin: 0.56rem 0.64rem 0.3rem;
}

.batchCon .item-list .item {
   display: flex;
  line-height: 0.54rem;
  font-size: 0.3rem;
  margin-bottom: 0.16rem;
  overflow: hidden;
}

.batchCon .item-list .item h3 {
  width: 1.82rem;
  color: #4e5969;
}

.batchCon .item-list .item div {
  color: #86909c;
  float: left;
  width: 2.98rem;
}

#nameList {
  height: 0.88rem;
  color: #3d5cff;
  font-size: 0.32rem;
  line-height: 0.88rem;
  text-align: center;
  text-align: center;
  position: relative;
  width: 100%;
  display: block;
  font-family: "ABeeZee";
}

#nameList::after {
  content: "";
  width: 100%;
  height: 1px;
  background-color: #e5e6eb;
  position: absolute;
  left: 0;
  top: 0;
  transform: scaleY(0.5);
}

.listItem {
  justify-content: space-between;
}

.listItem .mul-choice {
  display: block;
  width: 0.48rem;
  height: 0.48rem;
  margin-right: 0.2rem;
  background-image: url(../../../images/examination/cheched.png);
  background-size: 0.48rem auto;
  background-position: 0 0;
}

.listItem.active .mul-choice {
  background-position: 0 -0.48rem;
}

.list .itemCon {
  overflow: hidden;
  min-height: 1rem;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}

.hislocationList .listItem {
  min-height: 1rem;
}

.list .itemCon {
  min-height: 1rem;
}

.hislocationList .listItem .itemRight img {
  width: 0.28rem;
  height: 0.28rem;
}

.list .itemCon h1 {
  font-size: 0.32rem;
  color: #333333;
  position: relative;
  padding-left: 0.32rem;
}

.hislocationList {
  padding-left: 0.3rem;
}

.list .itemRight {
  flex: 1;
  height: 100%;
  padding-right: 0.3rem;
  max-width: 5rem;
}

.list .itemRight p.ycenter,
.list .itemRight #selectLoc {
  word-break: break-all;

  height: 100%;
  font-size: 0.3rem;
  color: #999999;
  padding: 0.3rem 0;
  line-height: 0.4rem;
  justify-content: flex-end;
  text-align: right;
}

.list .itemRight img {
  margin-left: 0.1rem;
}

.list .itemCon h1 span {
  position: absolute;
  left: 0;
  top: 0;
  color: #f53f3f;
  margin-right: 0.08rem;
  font-weight: bold;
}

.recall-bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  padding-bottom: 0.88rem;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding: 0 0.3rem 0.88rem;
}

.recall-bottom .refresh {
  width: 3.25rem;
  height: 0.87rem;
  background: #ffffff;
  border: 0.03rem solid #c9cdd4;
  border-radius: 0.2rem;
  font-size: 0.3rem;
  line-height: 0.87rem;
  color: #4e5969;
  text-align: center;
}

.recall-bottom .search {
  width: 3.25rem;
  text-align: center;
  height: 0.87rem;
  background: #4d88ff;
  border-radius: 0.2rem;
  font-size: 0.3rem;
  color: #ffffff;
}

.ios-select-widget-box.olay > div {
  background-color: transparent;
}

.ios-select-widget-box header.iosselect-header {
  background-color: #fff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 0.32rem 0.32rem 0px 0px;
}

.ios-select-widget-box.olay {
  background: rgba(0, 0, 0, 0.2);
}

.choosedepartment-dialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  background: rgba(0, 0, 0, 0.2);
  display: none;
}

.choosedepartment-dialog .w-con {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  transform: translateY(100%);
  -webkit-transform: translateY(100%);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}

.choosedepartment-dialog .w-con.active {
  transform: translateY(0);
  -webkit-transform: translateY(0);
}

.choosedepartment-dialog .w-con .w-head {
  background: #ffffff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 0.32rem 0.32rem 0 0;
  width: 100%;
  height: 0.9rem;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding: 0 0.3rem;
}

.choosedepartment-dialog .w-con .w-head .cancle {
  font-size: 15px;
  color: #4d88ff;
  width: 2.2rem;
}

.choosedepartment-dialog .w-con .w-head .name {
  color: #333333;
  font-size: 16px;
}

.choosedepartment-dialog .w-con .w-head .btns {
  font-size: 15px;
  color: #4d88ff;
  width: 2.2rem;
  text-align: right;
}

.choosedepartment-dialog .w-con .w-head .btns span {
  display: inline-block;
  margin-left: 0.24rem;
}

.choosedepartment-dialog .w-con .w-head .btns span.acllSelect {
  margin-left: 0;
}

.choosedepartment-dialog .w-con .w-box {
  width: 100%;
  height: auto;
  background-color: #fff;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding: 0.2rem 0.3rem;
}

.search-inputs {
  width: 100%;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  margin-bottom: 0.2rem;
}

.search-inputs .s-con {
  background: #f5f6f8;
  border-radius: 0.3rem;
  height: 0.6rem;
  display: flex;
  align-items: center;
  position: relative;
  color: #333333;
  font-size: 14px;
  overflow: hidden;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}

.search-inputs .s-con img {
  width: 0.36rem;
  height: 0.36rem;
  margin-right: 0.1rem;
  margin-left: 0.2rem;
}

.search-inputs .s-con input {
  display: block;
  flex: 1;
  height: 0.6rem;
  border: none;
  background: none;
  font-size: 0.28rem;
  font-family: auto;
}

.search-inputs .s-con input::placeholder {
  color: #b3b3b3;
  font-size: 0.28rem;
}

.search-inputs .s-con .m-close {
  overflow: hidden;
  background: url(../../../images/examination/cancle-icon.png) no-repeat center;
  background-size: .28rem .28rem;
  margin-right: .2rem;
  width: .28rem;
  height: .28rem;
  display: none;
}

.choosedepartment-dialog .w-con .w-box ul {
  overflow-y: auto;
  max-height: 4.8rem;
  -webkit-overflow-scrolling: touch;
}

.choosedepartment-dialog .w-con .w-box ul li {
  height: 0.96rem;
  line-height: 0.96rem;
  font-size: 16px;
  color: #4e5969;
  padding-left: 0.72rem;
  position: relative;
}

.choosedepartment-dialog .w-con .w-box ul li:after {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 0.48rem;
  height: 0.48rem;
  margin-top: -0.24rem;
  background: url(../../../images/examination/tick-icon.png) no-repeat center;
  background-size: 0.48rem;
}

.choosedepartment-dialog .w-con .w-box ul li.cur:after {
  background: url(../../../images/examination/tick-cur-icon.png) no-repeat center;
  background-size: 0.48rem;
}

.list .itemRight p.ycenter .color1 {
  color: #4e5969;
}

.studentCount {
  overflow: hidden;
  margin: 0.16rem 0.3rem 0;
  font-size: 0.24rem;
  line-height: 0.43rem;
  color: #999999;
}

.studentCount span {
  padding-right: 0.32rem;
}

.teacherList {
  overflow: hidden;

}

.teacherList .teacher {
  height: 1.32rem;
  display: flex;
  align-items: center;
}

.teacherList .teacher img {
  width: 0.8rem;
  height: 0.8rem;
  border-radius: 0.08rem;
  overflow: hidden;
  margin: 0 0.2rem 0 0.3rem;
}

.teacherList .teacher h3 {
  flex: 1;
  font-size: 0.32rem;
  color: #333333;
  margin-right: 0.3rem;
  height: 0.36rem;
}

.teacherList .teacher .arrow {
  width: .16rem;
  height: .28rem;
  background: url('../../../images/examination/arrow-icon.png') no-repeat center;
  background-size: .16rem;
  margin-right: .3rem;
}

.teacherMes {
  flex: 1;
}

.teacherMes h3 {
  flex: 1;
  font-size: 0.32rem;
  color: #333333;
  margin-right: 0.3rem;
  line-height: 0.44rem;
  margin-bottom: 0.04rem;
}

.teacherMes span {
  padding: 0 0.2rem;
  height: 0.4rem;
  line-height: 0.4rem;
  display: inline-block;
  font-size: 0.24rem;
  background: #eaf4ff;
  border-radius: 0.04rem;
}

.teacherMes span.warning {
  background: #FFF7E8;
  color: #FFB026;
}

.teacherMes span.cheat {
  background: #FFECE8;
  color: #F76560;
}

.teacherMes span.missExam {
  background: #EAF4FF;
  color: #4D88FF;
}

.teacherMes span.deferredExam {
  background: #e3e5f7;
  color: #7b87f8;
}

.teacherMes span.normal {
  background: rgb(225, 243, 216);
  color: #67C23A;
}

.teacherList .teacher .notes {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
  font-size: 0.26rem;
  border: 1px solid #cccccc;
  border-radius: 24px;
  width: 1.02rem;
  height: 0.48rem;
  margin-right: 0.3rem;
}
.teacherList .teacher .cancel {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
  font-size: 0.26rem;
  border: 1px solid #cccccc;
  border-radius: 24px;
  width: 1.02rem;
  height: 0.48rem;
  margin-right: 0.3rem;
  text-align: center;
  line-height: 0.48rem;
}
.teacherList .teacher .notes span {
  width: .14rem;
  height: .14rem;
  background: url('../../../images/examination/add.png') no-repeat center;
  background-size: .14rem;
  margin-right: 0.06rem;
}

.notesList {
  width: 1.44rem;
  position: absolute;
  right: 0.36rem;
}

.notesList .arrowTop {
  width: .28rem;
  height: .18rem;
  background: url('../../../images/examination/arrow-top.png') no-repeat center;
  background-size: .28rem;
  margin-bottom: -.02rem;
  margin-left: .96rem;
  position: relative;
  z-index: 9;
}

.notesList .arrowBotton {
  width: .28rem;
  height: .18rem;
  background: url('../../../images/examination/arrow-top.png') no-repeat center;
  background-size: .28rem;
  margin-top: -.04rem;
  margin-left: .96rem;
  transform: rotate(180deg);
  position: relative;
  z-index: 9;
  display: none;
}


.notesList ul {
  overflow: hidden;
  background-color: #FFFFFF;
  border-radius: 0.08rem;
  box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0.24);
  position: relative;
}

.notesList ul li {
  font-size: .32rem;
  height: .88rem;
  text-align: center;
  line-height: .88rem;
  box-shadow: inset 0 -1px 0 #EBEBEB;
}

.notesList ul li::after {
  box-shadow: unset;
}
.scheduleWrap .scheduleTable {
  display: none;
}
.scheduleWrap .scheduleTable:first-child {
  display: block;
}
.week-table {
  position: relative;
}
.week-table .tips {
  position: absolute;
  left: 0;
  top: 0;
  width: 0.65rem;
  padding: 0 0.1rem;
  height: 1.16rem;
  background: #ebf4ff;
  border-radius: 0 0.16rem 0.16rem 0;
  font-size: 0.16rem;
  color: #1479ff;
  margin-right: 0.16rem;
  line-height: 1.2;

  z-index: 9;
  display: flex;
  align-items: center;
}
