body {
  background-color: #F7F8FA;
  padding: 20px;
  font-size: 14px;
  color: #4E5969;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main {
  min-height: calc(100vh);
  background: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
}
.main .top {
  width: 100%;
  height: 60px;
  border-bottom: 1px solid #E8EBF1;
  position: relative;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  align-items: center;
}
.main .top .title {
  padding-left: 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 36px;
}
.main .top .title .back {
  padding-left: 22px;
  background: url(../../images/basic/back-icon.png) no-repeat left center;
  background-size: 16px;
  color: #7D92B3;
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
}
.main .top .title .levelone {
  padding-left: 9px;
  position: relative;
  color: #6581BA;
  font-size: 16px;
  margin-right: 6px;
}
.main .top .title .levelone:after {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
}
.main .top .title .icon {
  width: 12px;
  height: 12px;
  background: url(../../images/basic/arrow-right.png) no-repeat center;
  background-size: 12px;
  margin-right: 6px;
}
.main .top .title .leveltwo {
  color: #1D2129;
  font-weight: 700;
  font-size: 16px;
}
.main .top .btn {
  position: absolute;
  top: 17px;
  right: 28px;
  width: 116px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 14px;
  color: #FFFFFF;
  background: #4D88FF;
  box-shadow: 0px 0px 10px #4D88FF;
  border-radius: 4px;
}
.main .top h4 {
  position: relative;
  color: #6581BA;
  font-size: 16px;
  margin-left: 30px;
  padding-left: 9px;
}
.main .top h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}
.main .form-con {
  display: flex;
  font-size: 14px;
  padding: 20px 30px 20px;
  position: relative;
}
.main .form-con .form-btn {
  width: 166px;
  position: relative;
}
.main .form-con .form-btn::after {
  content: "";
  width: 1px;
  height: 84px;
  background: #E8EBF1;
  position: absolute;
  left: 0px;
  top: 0px;
}
.main .form-con .form-btn .btn {
  width: 104px;
  height: 34px;
  background: #ffffff;
  border-radius: 4px;
  display: block;
  cursor: pointer;
}
.main .form-con .form-btn .btn.btn-search {
  background: #4D88FF;
  border: 1px solid #4D88FF;
  color: #ffffff;
  margin-bottom: 16px;
}
.main .form-con .form-btn .btn.btn-reset {
  border: 1px solid #4D88FF;
  color: #4D88FF;
}
.main .form-con .sel-box {
  flex: 1;
}
.main .form-con .sel-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.main .form-con .sel-row .sel-item {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 32px;
}
.main .form-con .sel-row .sel-item .sel-title {
  color: #1D2129;
  font-size: 14px;
  width: 84px;
}
.main .form-con .sel-row .sel-item .sel-title span {
  display: inline-block;
  width: 14px;
  color: #F76560;
}
.sel {
  float: left;
  margin-bottom: 0;
}
.sel .select-input {
  margin-left: 0;
  margin-right: 0;
}
.sel .select-input .select-dropdown {
  position: absolute;
  left: -1px;
  top: 33px;
}
.sel .select-input .name {
  text-align: left;
}
.sel {
  flex: 1;
  width: 0;
  margin-right: 40px;
  height: 34px;
  line-height: 34px;
}
.sel em {
  float: left;
  font-size: 14px;
  color: #474C59;
}
.sel .select-input {
  height: 34px;
  border-radius: 4px;
  border: 1px solid #D4D6D9;
  background-color: #fff;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
}
.sel .select-input i {
  position: absolute;
  top: 11px;
  right: 11px;
  width: 12px;
  height: 12px;
  background: url(../../images/basic/arrow-icon.png) no-repeat center;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.sel .select-input .name {
  font-size: 14px;
  color: #ACB4BF;
  padding-left: 13px;
  line-height: 32px;
  width: 86%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.sel .select-input .name.ckd {
  color: #131B26;
}
.sel .select-input.clicked i {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.sel .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
}
.sel .select-input .select-dropdown {
  width: 100%;
  left: -1px;
  margin: 5px 0;
  padding: 6px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: absolute;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
.sel .select-input .select-dropdown .search {
  margin: 8px;
  height: 36px;
  box-sizing: border-box;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.sel .select-input .select-dropdown .search input {
  border: none;
  flex: 1;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  font-size: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  float: left;
}
.sel .select-input .select-dropdown .search input::placeholder {
  color: #8F97A8;
}
.sel .select-input .select-dropdown .search span {
  cursor: pointer;
  float: left;
  width: 16px;
  height: 16px;
  background: url(../../images/basic/search-icon.png) no-repeat center;
  margin: 9px;
}
.sel .select-input .select-dropdown .all-selects {
  color: #4E5969;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  margin: 0 20px;
  padding-left: 24px;
  background: url(../../images/basic/check-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .all-selects.cur {
  background: url(../../images/basic/checked-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .dropdown-lists {
  padding: 0 0 6px;
  max-height: 240px;
  overflow: auto;
}
.sel .select-input .select-dropdown .dropdown-lists li {
  margin: 0;
  line-height: normal;
  line-height: 40px;
  padding: 0 20px;
  clear: both;
  color: #4E5969;
  font-size: 14px !important;
  list-style: none;
  cursor: pointer;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.sel .select-input .select-dropdown .dropdown-lists li span {
  display: block;
  padding-left: 27px;
  background: url(../../images/basic/check-icon.png) no-repeat left center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}
.sel .select-input .select-dropdown .dropdown-lists li:hover {
  background: #F5F7FA;
}
.sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #E1EBFF;
}
.sel .select-input .select-dropdown .dropdown-lists li.cur span {
  background: url(../../images/basic/checked-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .dropdown-list li {
  margin: 0;
  line-height: normal;
  padding: 0 16px;
  clear: both;
  color: #4E5969;
  font-size: 14px !important;
  white-space: nowrap;
  list-style: none;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
.sel .select-input .select-dropdown .dropdown-list li:hover {
  background: #E1EBFF;
}
.sel.times .sel-time {
  width: 104px;
  height: 34px;
}
.sel.times .sel-time span {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 18px;
  height: 18px;
  background: url(../../images/basic/time-icon.png) no-repeat center;
}
.table {
  display: none;
  overflow: hidden;
  margin: 0 30px 30px 30px;
}
.table table {
  overflow: hidden;
  table-layout: fixed;
}
.table table tr {
  height: 80px;
}
.table table tr td {
  text-align: center;
}
.table table tr td p:first-child {
  margin-bottom: 6px;
}
.table table thead tr {
  background: #F1F3F6;
}
.table table thead td {
  border: 1px solid #E8EBF1;
  color: #86909C;
  font-size: 14px;
}
.table table tbody td {
  font-size: 14px;
  border: 1px solid #E8EBF1;
  color: #4E5969;
}
.table table tbody td span {
  padding-right: 16px;
}
.table table tbody td a {
  color: #4080FF;
  cursor: pointer;
}
.table table tbody td.idle {
  background: #E1EBFF;
}
.tableDetail {
  margin: 20px 30px;
  min-height: calc(100vh - 220px);
}
.tableDetail table tr {
  height: 36px;
}
.tableDetail table tr td {
  border: 1px solid #E8EBF1;
  font-size: 14px;
  text-align: center;
}
.tableDetail table thead {
  background: #F1F3F6;
}
.tableDetail table thead tr td {
  color: #86909C;
}
.tableDetail table tbody tr td {
  color: #4E5969;
}
#coursePage {
  text-align: center;
  margin-bottom: 30px;
}
.layui-laypage a,
.layui-laypage span,
.layui-laypage input,
.layui-laypage button,
.layui-laypage select {
  border: none;
}
.layui-laypage button {
  background: #6AA1FF;
  color: #ffffff;
  border: 4px;
}
.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  border-radius: 4px;
  color: #86909C;
  font-size: 14px;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #6AA1FF;
  border-radius: 4px;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #6AA1FF !important;
}
.layui-laypage a:hover {
  color: #6AA1FF;
}
.layui-laypage-prev {
  background-color: transparent !important;
}
.layui-laypage-next {
  background-color: transparent !important;
}
.layui-laypage-spr {
  background-color: transparent !important;
}
.layui-laypage-skip {
  background-color: transparent !important;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-laypage-skip input {
  border: 1px solid #e2e2e2 !important;
  background-color: transparent !important;
}
.layui-laypage-next {
  border: 1px solid #86909C !important;
}
.layui-laypage-prev {
  border: 1px solid #86909C !important;
}
.layui-disabled,
.layui-disabled:hover {
  border: 1px solid #d2d2d2 !important;
}
.btns{
  margin-bottom:20px;

  padding-left:16px;
  justify-content: end;
  height:20px;
}
.btns .resetting{
  background: url(../../images/basic/refresh-icons.png) no-repeat left center;
  font-size:14px;
  color:#4E5969;
  padding-left:24px;
  cursor:pointer;
  height: 34px;
  font-size:14px;
  color:#4D88FF;
  line-height: 34px;
  margin-bottom:16px;
}
.btns .export-record{
  background: url(../../images/basic/upload-icons.png) no-repeat left center;
  font-size:14px;
  color:#4E5969;
  padding-left:24px;
  cursor:pointer;
  height: 34px;
  font-size:14px;
  color:#4D88FF;
  line-height: 34px;
}
.main .form-con .form-btn{
  width: 136px;
}
.main .form-con .form-btn{
  display:flex;
  display:-webkit-flex;
  align-items: flex-end;
  justify-content: flex-start;
  flex-wrap: wrap;
  flex-direction: column;
}

.main .form-con{
  padding-bottom:8px;
}