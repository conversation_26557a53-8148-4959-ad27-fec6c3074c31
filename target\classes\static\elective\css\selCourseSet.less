body {
  background-color: #f7f8fa;
}
.z-main {
  max-width: 1660px;
  margin: 8px 20px 0;
  background-color: #ffffff;
  overflow: hidden;
  min-height: calc(~"100vh - 8px");

  .z-title {
    padding: 29px 0 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e8ebf1;
    justify-content: space-between;

    h3 {
      font-size: 16px;
      line-height: 22px;
      color: #1d2129;
      padding-left: 9px;
      position: relative;
      margin-left: 30px;

      &::after {
        content: "";
        width: 3px;
        height: 16px;
        background: #4d88ff;
        border-radius: 2px;
        position: absolute;
        left: 0;
        top: 3px;
      }
    }

    span {
      font-size: 14px;
      color: #86909c;
      font-size: 14px;
      margin-left: 16px;
      margin-top: 2px;
    }
    #saveBth {
      width: 96px;
      height: 36px;
      border-radius: 4px;
      background: #4d88ff;
      box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
      margin-right: 20px;
      color: #fff;
      text-align: center;
      line-height: 36px;
      cursor: pointer;
    }
  }
  .z-tab {
    overflow: hidden;
    margin: 0 30px;

    ul {
      display: flex;
      align-items: center;
      height: 60px;
      line-height: 60px;

      li {
        margin-right: 62px;
        color: #86909c;
        font-size: 14px;
        cursor: pointer;

        &.active {
          position: relative;
          color: #1d2129;
          font-size: 16px;

          &::after {
            content: "";
            width: 100%;
            height: 3px;
            background: #4d88ff;
            border-radius: 3px 3px 0 0;
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
          }
        }
      }
    }
  }
  .z-search {
    margin-top: 30px;

    .layui-form {
      display: flex;
      flex-wrap: wrap;

      .layui-form-item {
        margin-right: 32px;
      }

      .layui-form-label {
        width: 60px;
      }

      .layui-input-block {
        margin-left: 75px;
      }
    }
    .z-btn {
      width: 68px;
      height: 36px;
      border-radius: 4px;
      background: #4d88ff;
      box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
      outline: none;
      border: none;
      color: #ffffff;
      font-size: 14px;
      cursor: pointer;
    }
    .clear {
      background: url("../images/icon_clear.png") no-repeat center;
      width: 36px;
      height: 36px;
      cursor: pointer;
      margin-left: 16px;
    }
  }

  .z-box {
    margin: 0 30px;
    .box-common {
      .z-tab-search {
        overflow: hidden;
        margin-bottom: 18px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        ul {
          display: flex;
          align-items: center;
          margin-right: 26px;
          li {
            margin-left: 20px;
            padding-left: 24px;
            color: #86909c;
            font-size: 14px;
            cursor: pointer;
            background: url("../images/radio1.png") no-repeat left center;
            &.active {
              background: url("../images/radio2.png") no-repeat left center;
            }
          }
        }
        input {
          display: block;
          float: right;
          width: 220px;
          height: 32px;
          border-radius: 4px;
          border: 1px solid #c9cdd4;
          padding: 0 28px 0 12px;
          box-sizing: border-box;
          &::placeholder {
            font-size: 14px;
            color: #86909c;
          }
        }
        img {
          display: block;
          position: absolute;
          right: 12px;
          top: 9px;
        }
      }
      &.box-limit {
        margin-top: 53px;
        .item {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          margin-bottom: 44px;
          h3 {
            font-size: 16px;
            color: #1d2129;
            width: 192px;
            margin-right: 14px;
          }
          .tips {
            background: url("../images/tips.png") no-repeat center;
            background-size: 16px;
            width: 16px;
            height: 16px;
            margin-right: 34px;
          }

          .switch {
            width: 28px;
            height: 14px;
            border-radius: 3px;
            background-color: #e0dfdf;
            position: relative;
            margin-right: 10px;
            cursor: pointer;
            span {
              position: absolute;
              display: block;
              width: 12px;
              height: 10px;
              top: 2px;
              left: 2px;
              transition: all linear 100ms;
              border-radius: 2px;
              background-color: #fff;
            }
            &.active {
              span {
                left: 14px;
              }
              background-color: #6aa1ff;
            }
          }
          .witchState {
            font-size: 14px;
            color: #86909c;
            margin-right: 58px;
          }
          .tip-mes {
            color: #4e5969;
            font-size: 14px;
            padding-left: 9px;
            position: relative;
            &::after {
              content: "";
              width: 4px;
              height: 4px;
              border-radius: 2px;
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              background-color: #4d88ff;
            }
          }
          .tip-mes1 {
            font-size: 16px;
            color: #1d2129;
          }
          h4 {
            font-size: 16px;
            color: #1d2129;
            width: 106px;
          }
          input {
            width: 121px;
            height: 34px;
            border-radius: 4px;
            border: 1px solid #e5e6eb;
            background: #fff;
            padding: 0 6px;
            margin-right: 15px;
            &::placeholder {
              color: #86909c;
            }
          }
        }
      }
    }
    .box-stu {
      .z-search {
        .layui-form .layui-form-item {
          margin-right: 26px;
        }
        .tips {
          display: flex;
          align-items: center;
          height: 30px;
          color: #8f97a8;
          span {
            width: 16px;
            height: 16px;
            cursor: pointer;
            background: url("../images/tips.png") no-repeat center;
            margin-right: 6px;
          }
          p {
            display: none;
          }
        }
      }
    }
    .oprate-table {
      color: #4d88ff;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        margin-right: 10px;
        cursor: pointer;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
.z-btns {
  display: flex;
  align-items: center;
  margin-top: 9px;
  margin-bottom: 8px;

  .addRule {
    background: #4d88ff;
    box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
    border-radius: 4px;
    padding: 0 14px;
    height: 36px;
    font-size: 14px;
    color: #ffffff;
    line-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    float: right;
    img {
      margin-right: 7px;
    }
  }
  .importStu {
    width: 114px;
    height: 36px;
    border-radius: 6px;
    border: 1px solid #4d88ff;
    background: #fff;
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
    text-align: center;
    line-height: 34px;
    color: #4d88ff;
    margin-left: 16px;
    box-sizing: border-box;
    cursor: pointer;
  }
  .del {
    width: 74px;
    height: 36px;
    border-radius: 6px;
    border: 1px solid #4d88ff;
    background: #fff;
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
    text-align: center;
    line-height: 34px;
    color: #4d88ff;
    margin-left: 16px;
    box-sizing: border-box;
    cursor: pointer;
  }
}
.dialog {
  // overflow: hidden;
  border-radius: 10px;
  background-color: #ffffff;
  .tips {
    color: #4e5969;
    margin: 11px 30px 33px 30px;
    padding-left: 20px;
    background: url("../images/tips.png") no-repeat left center;
    background-size: 16px;
    // margin: 0 30px;
    a {
      color: #4d88ff;
      padding-left: 10px;
    }
  }
  .dialog-title {
    border-bottom: 1px solid #e5e6eb;
    height: 56px;
    line-height: 56px;
    color: #1d2129;
    font-size: 16px;
    text-indent: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      width: 24px;
      height: 24px;
      background: url("../images/close.png") no-repeat center;
      background-size: 24px;
      margin-right: 23px;
      cursor: pointer;
    }
  }
  .z-search {
    margin-top: 30px;

    .layui-form {
      display: flex;
      flex-wrap: wrap;

      .layui-form-item {
        margin-right: 32px;
      }

      .layui-form-label {
        width: 60px;
      }

      .layui-input-block {
        margin-left: 75px;
      }
    }
    .z-btn {
      width: 68px;
      height: 36px;
      border-radius: 4px;
      background: #4d88ff;
      box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
      outline: none;
      border: none;
      color: #ffffff;
      font-size: 14px;
      cursor: pointer;
    }
    .clear {
      background: url("../images/icon_clear.png") no-repeat center;
      width: 36px;
      height: 36px;
      cursor: pointer;
      margin-left: 16px;
    }
  }
  .z-tab-search {
    overflow: hidden;
    margin-bottom: 30px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    ul {
      display: flex;
      align-items: center;
      margin-right: 26px;
      li {
        margin-left: 20px;
        padding-left: 24px;
        color: #86909c;
        font-size: 14px;
        cursor: pointer;
        background: url("../images/radio1.png") no-repeat left center;
        &.active {
          background: url("../images/radio2.png") no-repeat left center;
        }
      }
    }
    input {
      display: block;
      float: right;
      width: 220px;
      height: 32px;
      border-radius: 4px;
      border: 1px solid #c9cdd4;
      padding: 0 28px 0 12px;
      box-sizing: border-box;
      &::placeholder {
        font-size: 14px;
        color: #86909c;
      }
    }
    img {
      display: block;
      position: absolute;
      right: 12px;
      top: 9px;
    }
  }
  .dialog-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 70px;
    border-top: 1px solid #e5e6eb;
    padding-right: 30px;

    button {
      width: 88px;
      height: 36px;
      font-size: 14px;
      border-radius: 18px;
      cursor: pointer;

      &.pu-cancel {
        border: 1px solid #c9cdd4;
        color: #4e5969;
        background-color: #fff;
        margin-right: 16px;
      }

      &.pu-sure {
        color: #fff;
        background: #4d88ff;
        box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
        border: 1px solid #4d88ff;
      }
    }
  }

  .dialog-con {
    padding: 0 30px;
    box-sizing: border-box;
    .z-table {
      margin-bottom: 30px;
    }
  }
}
.dialogCheck {
  width: 600px;
  display: none;
  .dialog-con {
    overflow: hidden;
    max-height: 600px;
    overflow-y: auto;
    padding: 30px 60px;
    dl {
      display: flex;
      line-height: 1.5;
      margin-bottom: 8px;
      font-size: 14px;
      dt {
        flex-shrink: 0;
        color: #666666;
      }
      dd {
        flex: 1;
      }
    }
  }
}
.layui-layer {
  border-radius: 10px !important;
  overflow: unset;
}
.layui-layer-title {
  border-radius: 10px 10px 0 0 !important;
}
#selCourse {
  width: 1098px;
  display: none;
  height: 756px;
  .j-search-con {
    width: 190px;
  }
}

#selStu {
  width: 1098px;
  display: none;
  // height: 756px;
  .j-search-con {
    width: 190px;
  }
  .z-relation {
    display: flex;
    align-items: center;
    margin-top: 26px;
    h3 {
      font-size: 14px;
      color: #1d2129;
    }
    .layui-tips {
      background: url(../images/tips.png) no-repeat center;
      background-size: 16px;
      width: 16px;
      height: 16px;
      margin: 0;
      cursor: pointer;
    }
    ul {
      display: flex;
      align-items: center;
      margin-right: 26px;
      li {
        margin-left: 20px;
        padding-left: 24px;
        color: #86909c;
        font-size: 14px;
        cursor: pointer;
        background: url("../images/radio1.png") no-repeat left center;
        &.active {
          background: url("../images/radio2.png") no-repeat left center;
        }
      }
    }
  }
  .z-table {
    position: relative;
    .z-check {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #666768;
      position: absolute;
      left: 24px;
      bottom: 8px;
      .check {
        width: 28px;
        height: 28px;
        cursor: pointer;
        margin-right: 6px;
        background: url(../images/check.png) no-repeat center;
        background-size: 28px;
        &.checked {
          background: url(../images/check1.png) no-repeat center;
        }
      }
    }
  }
}
#selRelation {
  width: 1780px;
  height: 811px;
  display: none;
  .dialog-con {
    background: #f8f8f8;
    padding: 32px 41px 17px;
    .z-search {
      height: 52px;
      padding: 0 40px;
      margin-top: 0;
      background-color: #ffffff;
      border-bottom: 1px solid #e8e8e8;
      display: flex;
      align-items: center;
      .layui-form-item {
        margin-bottom: 0;
      }
    }
    .selItem {
      display: flex;
      background-color: #ffffff;
      .item {
        width: 50%;
        margin: 20px 0;
        &:first-child {
          border-right: 1px solid #ebebeb;
        }
        .item-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 50px;
          margin: 0 20px;
          border-bottom: 1px solid #e8ebf1;
          .title {
            font-size: 16px;
            color: #1d2129;
            position: relative;
            line-height: 50px;
            &::after {
              content: "";
              width: 60px;
              height: 3px;
              position: absolute;
              left: 0;
              bottom: 0;
              background-color: #4d88ff;
            }
          }
          .z-tab-search {
            margin-bottom: 0;
          }
        }
        .z-search {
          height: auto;
          overflow: hidden;
          margin-top: 27px;
          padding: 0 24px;
          border: none;
          .layui-form-item {
            margin-bottom: 18px;
            margin-right: 16px;
          }
          .layui-input-block {
            width: 175px;
            .j-search-con {
              width: 175px;
            }
          }
        }
        .z-btns {
          margin-left: 24px;
        }
        .z-table {
          margin: 0 24px;
        }
      }
    }
  }
}
#selScore {
  width: 747px;
  display: none;
  .layui-form {
    // overflow: hidden;
    margin: 30px 100px;
  }
  .layui-form-label {
    width: 56px;
  }
  .layui-input {
    border-color: #e5e6eb;
    &::placeholder {
      color: #86909c;
    }
  }
  .layui-input-block {
    width: 476px;
    margin-left: 72px;
    .j-search-con {
      width: 476px;
      .j-select-year .search input {
        width: 93%;
      }
    }
    &.set-score {
      span {
        line-height: 30px;
        padding-left: 14px;
      }
    }
  }
}
#selScoreGates {
  width: 819px;
  display: none;
  .layui-form {
    // margin: 30px 100px;
    .title {
      font-size: 14px;
      color: #1d2129;
      padding: 5px 0 25px;
    }
    .layui-form-label {
      width: 56px;
    }
    .layui-input-block {
      margin-left: 72px;
      .j-search-con {
        .j-select-year .search input {
          width: 87%;
        }
      }
      &.set-score {
        span {
          line-height: 30px;
          padding-left: 14px;
        }
      }
    }
    .form-item {
      display: flex;
      flex-wrap: wrap;
      .layui-form-item {
        width: 50%;
        margin-bottom: 33px;
        &:nth-child(2n) {
          padding-left: 30px;
          box-sizing: border-box;
        }
      }

      .layui-input {
        border-color: #e5e6eb;
        &::placeholder {
          color: #86909c;
        }
      }
    }
    .ask-score {
      position: relative;
      .ask-item {
        display: flex;
        align-items: center;
        margin-bottom: 36px;
        margin-left: 80px;
        input[type="number"] {
          width: 84px;
          height: 34px;
          border: 1px solid #e5e6eb;
          padding: 0 10px;
          box-sizing: border-box;
          border-radius: 4px;
          &::placeholder {
            color: #86909c;
          }
        }
        .range-text {
          font-size: 14px;
          margin-right: 20px;
          margin-left: 8px;
        }
        img {
          margin: 0 6px;
        }
        .add-range {
          width: 34px;
          height: 34px;
          background: url("../images/add-icon1.png") no-repeat center;
          background-size: 14px;
          margin-left: 10px;
          cursor: pointer;
        }
        .range-del {
          width: 34px;
          height: 34px;
          background: url("../images/del-icon.png") no-repeat center;
          background-size: 18px;
          margin-left: 10px;
          cursor: pointer;
        }
        .j-search-con .j-select-year ul li {
          background: unset;
          padding-right: unset;
        }
      }
      .j-search-con.single-box .j-select-year ul li {
        background: unset;
        padding: unset;
        text-align: center;
        text-indent: 0;
      }
      .j-search-con.single-box .j-select-year ul li.active {
        background-color: #e1ebff;
      }
      .condition {
        position: absolute;
        left: 0;
        top: 2px;
        background: url("../images/relation.png") no-repeat right center;
        width: 70px;
        height: 100px;
        z-index: 999;
        .j-search-con {
          width: 60px;
          .j-arrow.j-arrow-slide {
            transform: rotate(-90deg);
          }
        }
      }
    }
  }
}

#courseCheck {
  width: 747px;
  display: none;
  .layui-form {
    // overflow: hidden;
    margin: 30px 82px;
  }
  .layui-form-label {
    width: 90px;
  }
  .layui-input {
    border-color: #e5e6eb;
    &::placeholder {
      color: #86909c;
    }
  }
  .layui-input-block {
    width: 476px;
    margin-left: 106px;
    .j-search-con {
      width: 476px;
    }
    &.set-score {
      span {
        line-height: 30px;
        padding-left: 14px;
      }
    }
  }
}
#courseLimit {
  width: 747px;
  display: none;
  .layui-form {
    // overflow: hidden;
    margin: 30px 100px;
  }
  .layui-form-label {
    width: 56px;
  }
  .layui-input {
    border-color: #e5e6eb;
    &::placeholder {
      color: #86909c;
    }
  }
  .layui-input-block {
    width: 476px;
    margin-left: 72px;
    .j-search-con {
      width: 476px;
    }
    &.set-score {
      span {
        line-height: 30px;
        padding-left: 14px;
      }
    }
  }
}
#termCourse {
  width: 450px;
  display: none;
  .dialog-con {
    display: flex;
    align-items: center;
    padding: 0 0 30px 29px;
    .exchange {
      width: 30px;
      height: 30px;
      background: url("../images/exchage.png") no-repeat center;
      flex-shrink: 0;
    }
    /*  .j-search-con {
      width: 179px;
    } */
    .layui-form {
      background-color: #ffffff;
      padding: 14px 14px 0 14px;
      &.layui-form-left {
        .layui-form-label {
          width: 92px;
        }
        .layui-input-block {
          margin-left: 107px;
        }
      }
      &.layui-form-right {
        .layui-input {
          border-color: #e5e6eb;
          cursor: no-drop;
          &::placeholder {
            color: #86909c;
          }
        }
        .layui-form-label {
          width: 121px;
        }
        .layui-input-block {
          margin-left: 136px;
        }
      }
    }
  }
}

#marshalling {
  width: 383px;
  display: none;

  .layui-form-label {
    width: 70px;
  }
  .layui-input-block {
    margin-left: 86px;
  }
  .selCount {
    input[type="number"] {
      width: 100px;
      height: 34px;
      border: 1px solid #e5e6eb;
      padding: 0 10px;
      box-sizing: border-box;
      border-radius: 4px;
      &::placeholder {
        color: #86909c;
      }
    }

    img {
      margin: 0 6px;
    }
  }
}
#importStu {
  width: 800px;
  .dialog-con {
    margin: 30px 0;
    .layui-upload-drag {
      background-color: #f2f3f5;
      padding: 30px 0;

      img {
        width: 14px;
        margin-bottom: 24px;
      }
      .intro {
        color: #1d2129;
        line-height: 20px;
      }
      .intro1 {
        color: #86909c;
        line-height: 20px;
      }
    }
  }
}
.layui-form-radio * {
  color: #86909c;
}
.layui-layer-page .layui-layer-content {
  overflow: unset !important;
}
