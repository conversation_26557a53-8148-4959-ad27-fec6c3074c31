.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.transforms {
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}

textarea::-webkit-input-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

textarea:-moz-placeholder {
    font-size: 12px;
    color: #bcbcc5;
}

input[type=search]::-webkit-search-cancel-button {
    -webkit-appearance: none;
}

textarea:-ms-input-placeholder {
    font-size: 12px;
}

.clearfixs {
    zoom: 1;
}

.clearfixs:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.hide {
    display: none !important;
}

input::-webkit-input-placeholder {
    color: #86909C;
    font-size: 14px;
    font-weight: 300;
}

body {
    background-color: #F7F8FA;
}

.main {
    margin: 20px;
    background-color: #fff;
    border-radius: 8px;
    padding-bottom: 20px;
    min-height: calc(100vh - 60px);
    min-width: 1300px;
}

.main .j-title {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 60px;
    border-bottom: 1px solid #E8EBF1;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding: 0 30px;
}

.main .j-title .back {
    padding-left: 22px;
    float: left;
    background: url(../../images/timetable/back-icon.png) no-repeat left center;
    background-size: 16px;
    font-weight: 400;
    font-size: 14px;
    line-height: 60px;
    color: #7D92B3;
    margin-right: 16px;
    cursor: pointer;
}

.main .j-title p {
    position: absolute;
    left: 119px;
    top: 0;
    height: 60px;
    line-height: 60px;
    color: #C9CDD4;
    font-weight: normal;
}

.main .j-title h4 {
    float: left;
    position: relative;
    font-size: 16px;
    color: #6581BA;
    margin-left: 30px;
    padding-left: 9px;
    font-weight: 400;
}

.main .j-title h4::after {
    content: "";
    width: 3px;
    height: 16px;
    background: #4C85FA;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 4px;
}

.main .j-title .j-btns {
    margin-right: 30px;
}

.main .j-title .j-btns button {
    width: 92px;
    height: 36px;
    font-size: 14px;
    border-radius: 6px;
    outline: none;
    cursor: pointer;
}

.main .j-title .j-btns button.btn-cancel {
    border: 1px solid #4C88FF;
    box-shadow: 0px 0px 8px rgba(39, 111, 255, 0.31);
    color: #4C88FF;
    background-color: #fff;
    margin-right: 14px;
}

.main .j-title .j-btns button.btn-complate {
    background: #4C88FF;
    box-shadow: 0px 0px 8px rgba(39, 111, 255, 0.31);
    border: 1px solid #4C88FF;
    color: #fff;
}

.main .cons {
    padding: 0 30px;
    position: relative;
}

.main .cons .lab-top {
    display: flex;
    display: -webkit-flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin-bottom: 20px;
    padding: 0 10px;
    padding-top: 20px;
}

.main .cons .lab-top h2 {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #1D2129;
}

.main .cons .lab-top .names {
    width: auto;
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    color: #1D2129;
    white-space: nowrap;
    margin-right: 20px;
}

.main .cons .lab-top .switch {
    background: #DADFE6;
    border-radius: 4px;
    width: 28px;
    height: 14px;
    position: relative;
    margin-top: 3px;
    cursor: pointer;
}

.main .cons .lab-top .switch span {
    position: absolute;
    left: 2px;
    top: 2px;
    display: block;
    width: 12px;
    height: 10px;
    border-radius: 2px;
    background: #ffffff;
    transition: all 200ms linear;
}

.main .cons .lab-top .switch.active {
    background: #4D88FF;
}

.main .cons .lab-top .switch.active span {
    left: 14px;
}

.main .cons .lab-top .tit {
    font-size: 14px;
    line-height: 20px;
    color: #86909C;
    margin-left: 10px;
}

.main .cons .btn-list {
    width: 100%;
    height: 30px;
    margin-bottom: 20px;
    margin-top: 20px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 10px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.main .cons .btn-list .edit {
    width: 120px;
    height: 30px;
    background: #4D88FF;
    box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
    border-radius: 6px;
    text-align: center;
    line-height: 30px;
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
}

.main .cons .btn-list .add {
    width: 64px;
    height: 30px;
    background: #4D88FF;
    box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
    border-radius: 6px;
    text-align: center;
    line-height: 30px;
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
}

.main .cons .btn-list .tips {
    padding-left: 22px;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #8F97A8;
    background: url(../../images/timetable/ntips-icon.png) no-repeat left center;
}

.main .cons .btn-list .tips i {
    color: #6aa1ff;
}

.no-data {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 88px;
    height: 88px;
    background: url(../../images/timetable/no-data.png) no-repeat center;
    display: none;
}

.main .cons .p-tables {
    margin: 25px 10px;
    height: auto;
    border: 1px solid #E8EBF3;
    border-bottom: none;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    position: relative;
}

.main .cons .p-tables .row {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
    height: 80px;
}

.main .cons .p-tables .row.row-colw {
    background: transparent;
}

.main .cons .p-tables .row.row-colw .lab {
    background-color: #fff;
}

.main .cons .p-tables .row.row-colb {
    background: transparent;
}

.main .cons .p-tables .row.row-colb .lab {
    background: #FAFBFC;
}

.main .cons .p-tables .row.row-colb .lab .time-sel {
    border: 1px solid #FAFBFC;
}

.main .cons .p-tables .row:first-child {
    height: 40px;
    line-height: 40px;
    background: #F1F3F6;
}

.main .cons .p-tables .row:first-child .lab {
    color: #86909C;
    height: 40px;
    line-height: 40px;
}

.main .cons .p-tables .row.break {
    background: #F1F3F6;
    position: relative;
}

.main .cons .p-tables .row.break .delet {
    position: absolute;
    right: -26px;
    top: 0;
    width: 26px;
    height: 80px;
    background: url(../../images/timetable/huishou.png) no-repeat right center;
    cursor: pointer;
    display: none;
}

.main .cons .p-tables .row.break:hover .delet {
    display: block;
}

.main .cons .p-tables .row.break .noborder {
    border-right: none;
}

.main .cons .p-tables .row .lab {
    border-right: 1px solid #E8EBF3;
    text-align: center;
    height: 80px;
    font-size: 14px;
    color: #86909C;
}

.main .cons .p-tables .row .lab .time-sels {
    width: 160px;
    height: 34px;
    border-radius: 4px;
    margin: 23px auto 0;
    font-size: 14px;
    color: #4E5969;
    padding: 0 24px;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.main .cons .p-tables .row .lab .time-sels .t-start {
    display: inline-block;
    height: 18px;
    line-height: 18px;
    margin-top: 7px;
}

.main .cons .p-tables .row .lab .time-sels .sign {
    display: inline-block;
    line-height: 34px;
    margin: 0 9px;
}

.main .cons .p-tables .row .lab .time-sels .t-end {
    display: inline-block;
    height: 18px;
    line-height: 18px;
    margin-top: 7px;
}

.main .cons .p-tables .row .lab .time-sel {
    width: 160px;
    height: 34px;
    border: 1px solid #fff;
    border-radius: 4px;
    margin: 23px auto 0;
    font-size: 14px;
    color: #4E5969;
    padding: 0 21px;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.main .cons .p-tables .row .lab .time-sel:hover {
    border: 1px solid #D4D6D9;
}

.main .cons .p-tables .row .lab .time-sel .t-start {
    display: inline-block;
    height: 18px;
    line-height: 18px;
    margin-top: 7px;
    cursor: pointer;
}

.main .cons .p-tables .row .lab .time-sel .t-start:hover {
    background: rgba(97, 110, 230, 0.2);
}

.main .cons .p-tables .row .lab .time-sel .sign {
    display: inline-block;
    line-height: 32px;
    margin: 0 9px;
}

.main .cons .p-tables .row .lab .time-sel .t-end {
    display: inline-block;
    height: 18px;
    line-height: 18px;
    margin-top: 7px;
    cursor: pointer;
}

.main .cons .p-tables .row .lab .time-sel .t-end:hover {
    background: rgba(97, 110, 230, 0.2);
}

.main .cons .p-tables .row .lab .time-sel.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
}

.main .cons .p-tables .row .lab .time-sel .select-dropdown {
    width: inherit;
    max-height: 200px;
    overflow: auto;
    margin: 5px 0;
    padding: 5px 0;
    background-color: #fff;
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    position: absolute;
    left: 0;
    top: 32px;
    transform: translate(0, -50px);
    -webkit-transform: translate(0, -50px);
    z-index: 900;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
    z-index: -1;
}

.main .cons .p-tables .row .lab .time-sel .select-dropdown .dropdown-list {
    padding: 6px 0;
}

.main .cons .p-tables .row .lab .time-sel .select-dropdown .dropdown-list li {
    margin: 0;
    line-height: normal;
    line-height: 40px;
    padding: 0 20px;
    clear: both;
    color: #131B26;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    text-align: left;
}

.main .cons .p-tables .row .lab .time-sel .select-dropdown .dropdown-list li:hover {
    background: #E1EBFF;
}

.main .cons .p-tables .row .lab .time-sel .select-dropdown .dropdown-list li.cur {
    color: #616EE6;
}

.main .cons .p-tables .row .lab:last-child {
    border-right: none;
}

.main .cons .p-tables .row .lab:first-child {
    width: 145px;
    display: flex;
    display: -webkit-flex;
    justify-content: center;
    align-items: center;
    line-height: 20px;
    position: relative;
}

.main .cons .p-tables .row .lab:first-child span {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.main .cons .p-tables .row .lab:first-child .ckable {
    position: absolute;
    left: 0;
    top: 0;
    background-color: transparent;
    width: 100%;
    height: 100%;
    z-index: 9;
}

.main .cons .p-tables .row .lab:first-child .ckable .up {
    width: 100%;
    height: 50%;
    cursor: pointer;
}

.main .cons .p-tables .row .lab:first-child .ckable .down {
    width: 100%;
    height: 50%;
    cursor: pointer;
}

.main .cons .p-tables .row .lab.fb {
    flex: 1;
}

.main .cons .p-tables .row.linear {
    width: 100%;
    height: 1px;
    background-color: #E8EBF3;
    position: relative;
    display: block !important;
}

.main .cons .p-tables .row.linear .icons {
    display: none;
    position: absolute;
    left: -33px;
    top: -12px;
    width: 33px;
    height: 26px;
    cursor: pointer;
    background: url(../../images/timetable/add-kj.png) no-repeat left center;
}

.main .cons .p-tables .row.linear .icons em {
    display: none;
    position: absolute;
    left: -27px;
    top: -51px;
    width: 80px;
    height: 47px;
    background: url(../../images/timetable/tip-icon.png) no-repeat center;
}

.main .cons .p-tables .row.linear .icons:hover em {
    display: block;
}

.main .cons .p-tables .row.linear.hov {
    background-color: #616EE6;
}

.main .cons .p-tables .row.linear.hov .icons {
    display: block;
}

.main .cons .p-tables .row.linear.hov:after {
    content: "";
    position: absolute;
    left: 0;
    top: -1px;
    width: 100%;
    height: 1px;
    background-color: #616EE6;
}

.main .cons .p-tables .row.linear:hover {
    background-color: #616EE6;
}

.main .cons .p-tables .row.linear:hover .icons {
    display: block;
}

.main .cons .p-tables .row.linear:hover:after {
    content: "";
    position: absolute;
    left: 0;
    top: -1px;
    width: 100%;
    height: 1px;
    background-color: #616EE6;
}

.main .cons .p-tables .row.rev-row .lab .time-sel.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, -244px) !important;
    -webkit-transform: translate(0, -244px) !important;
}

.main .cons .p-tables .row.rev-row .lab .time-sel .select-dropdown {
    transform: translate(0, -274px) !important;
    -webkit-transform: translate(0, -274px) !important;
}

.add-timetable .window {
    width: 412px;
    height: 494px;
}

.add-timetable .window .p-content .lable {
    width: 100%;
    height: 34px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 24px;
}

.add-timetable .window .p-content .lable .name {
    font-weight: 400;
    font-size: 14px;
    line-height: 34px;
    color: #1D2129;
    margin-right: 28px;
    white-space: nowrap;
}

.add-timetable .window .p-content .lable .input {
    background: #FFFFFF;
    border: 1px solid #E1E1E5;
    box-sizing: border-box;
    border-radius: 4px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    width: 240px;
    height: 34px;
}

.add-timetable .window .p-content .lable .input input {
    border: none;
    background: transparent;
    outline: none;
    width: 100%;
    height: 32px;
    line-height: 32px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-left: 10px;
    font-size: 14px;
}

.add-timetable .window .p-content .search-box {
    width: 100%;
    height: 34px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 24px;
}

.add-timetable .window .p-content .search-box .input {
    background: #FFFFFF;
    border: 1px solid #E1E1E5;
    box-sizing: border-box;
    border-radius: 4px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    width: 282px;
    height: 34px;
    border-radius: 4px 0 0 4px;
    border-right: none;
    padding-left: 30px;
    background: url(../../images/timetable/search-icons.png) no-repeat 10px center;
}

.add-timetable .window .p-content .search-box .input input {
    border: none;
    background: transparent;
    outline: none;
    width: 100%;
    height: 32px;
    line-height: 32px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    font-size: 14px;
}

.add-timetable .window .p-content .search-box .btn {
    background: #4D88FF;
    border-radius: 0px 4px 4px 0px;
    width: 70px;
    height: 34px;
    text-align: center;
    line-height: 34px;
    font-size: 14px;
    color: #FFFFFF;
    cursor: pointer;
}

.add-timetable .window .p-content .clas-list .top {
    width: 100%;
    height: 20px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.add-timetable .window .p-content .clas-list .top .name {
    font-size: 14px;
    color: #8F97A8;
}

.add-timetable .window .p-content .clas-list .top .selected {
    font-size: 14px;
    color: #8F97A8;
}

.add-timetable .window .p-content .clas-list .list {
    width: 100%;
    border: 1px solid #E8EBF1;
}

.add-timetable .window .p-content .clas-list .list .l-head {
    width: 100%;
    height: 40px;
    background: #F1F3F6;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    border-bottom: 1px solid #E8EBF1;
}

.add-timetable .window .p-content .clas-list .list .l-head .name {
    font-size: 14px;
    color: #86909C;
}

.add-timetable .window .p-content .clas-list .list .l-head .btn {
    padding-right: 26px;
    background: url(../../images/timetable/check-icon.png) no-repeat right center;
    background-size: 16px;
    cursor: pointer;
    font-size: 14px;
    color: #86909C;
}

.add-timetable .window .p-content .clas-list .list .l-head .btn.cur {
    background: url(../../images/timetable/check-cur.png) no-repeat right center;
    background-size: 16px;
}

.add-timetable .window .p-content .clas-list .list .l-body {
    width: 100%;
    height: 120px;
    overflow: hidden;
}

.add-timetable .window .p-content .clas-list .list .l-body ul li {
    width: 100%;
    height: 40px;
    background: #F1F3F6;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    border-bottom: 1px solid #E8EBF1;
    background-color: #fff;
    cursor: pointer;
}

.add-timetable .window .p-content .clas-list .list .l-body ul li .name {
    font-size: 14px;
    color: #4E5969;
}

.add-timetable .window .p-content .clas-list .list .l-body ul li .btn {
    width: 16px;
    height: 16px;
    background: url(../../images/timetable/check-icon.png) no-repeat center;
    background-size: 16px;
    cursor: pointer;
}

.add-timetable .window .p-content .clas-list .list .l-body ul li.cur .btn {
    width: 16px;
    height: 16px;
    background: url(../../images/timetable/check-cur.png) no-repeat center;
    background-size: 16px;
    cursor: pointer;
}

.add-timetable .window .p-content .clas-list .list .l-body ul li:last-child {
    border: none;
}

.add-timetable .window .p-content .clas-list .list .l-body ul li:nth-child(2n+1) {
    background: #FAFBFC;
}

.addBreak-diag .window {
    width: 512px;
    height: 282px;
}

.addBreak-diag .window .p-content {
    padding: 24px 30px;
    padding-left: 100px;
}

.addBreak-diag .window .p-content .lable {
    margin-bottom: 30px;
}

.addBreak-diag .window .p-content .lable .name {
    float: left;
    width: 66px;
    height: 34px;
    line-height: 34px;
    text-align: left;
    font-size: 14px;
}

.addBreak-diag .window .p-content .lable .sel {
    float: left;
    margin-bottom: 0;
}

.addBreak-diag .window .p-content .lable .sel em {
    font-weight: 400;
    font-size: 14px;
    line-height: 34px;
    color: #1D2129;
    white-space: nowrap;
}

.addBreak-diag .window .p-content .lable .sel .select-input {
    margin-left: 0;
    position: relative;
    width: 80px;
    float: left;
    margin-right: 14px;
}

.addBreak-diag .window .p-content .lable .sel .select-input .select-dropdown {
    position: absolute;
    left: -1px;
    top: 33px;
}

.addBreak-diag .window .p-content .lable .input {
    float: left;
    background: #FFFFFF;
    border: 1px solid #E1E1E5;
    box-sizing: border-box;
    border-radius: 4px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    width: 240px;
    height: 34px;
}

.addBreak-diag .window .p-content .lable .input input {
    border: none;
    background: transparent;
    outline: none;
    width: 100%;
    height: 32px;
    line-height: 32px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-left: 10px;
}

.main .cons .onready-tab {
    border-bottom: 1px solid #E8EBF3;
}

.main .cons .onready-tab.empty-table .row .lab:last-child {
    border-right: 1px solid #E8EBF3;
}

.main .cons .onready-tab .row {
    justify-content: flex-start;
}

.main .cons .onready-tab .row .lab {
    position: relative;
}

.main .cons .onready-tab .row .lab:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 1px;
    background-color: #E8EBF3;
}

.main .cons .onready-tab .row:last-child .lab:after {
    display: none;
}

.layui-table {
    margin: 0 auto;
}

.layui-table,
.layui-table-view {
    margin: 0 auto;
}

.j-table {
    padding: 0 10px;
    min-height: calc(100vh - 281px);
}

.layui-table tbody tr:hover,
.layui-table thead tr,
.layui-table-click,
.layui-table-header,
.layui-table-hover,
.layui-table-mend,
.layui-table-patch,
.layui-table-tool,
.layui-table-total,
.layui-table-total tr,
.layui-table[lay-even] tr:nth-child(even) {
    background: #F1F3F6;
}

.layui-table td,
.layui-table th,
.layui-table-col-set,
.layui-table-fixed-r,
.layui-table-grid-down,
.layui-table-header,
.layui-table-page,
.layui-table-tips-main,
.layui-table-tool,
.layui-table-total,
.layui-table-view,
.layui-table[lay-skin=line],
.layui-table[lay-skin=row] {
    border-color: #E8EBF1;
}

.layui-table-view .layui-table th {
    color: #86909C;
}

.layui-table-cell {
    color: #4E5969;
}

.opt {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
}

.opt .warehouse {
    margin: 0 14px;
    font-size: 14px;
    color: #4080FF;
    cursor: pointer;
}

.opt .delet {
    margin: 0 14px;
    font-size: 14px;
    color: #F76560;
    cursor: default;
}

.opt .delets {
    margin: 0 14px;
    font-size: 14px;
    color: #F76560;
    cursor: pointer;
}

.stuts {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
}

.stuts .edit {
    margin: 0 14px;
    font-size: 14px;
    cursor: pointer;
    color: #4E5969;
}

.stuts .edits {
    margin: 0 14px;
    font-size: 14px;
    cursor: default;
    color: #4E5969;
    padding-right: 20px;
    background: url(../../images/timetable/edits-icons.png) no-repeat right center;
    cursor: pointer;
}

.creteLesson-diag .window {
    width: 354px;
    height: 240px;
}

.creteLesson-diag .window .p-content p {
    font-weight: 400;
    font-size: 14px;
    line-height: 54px;
    text-align: center;
    color: #4E5969;
}

.addschem-diag .window .p-content {
    padding: 30px;
}

.addschem-diag .window .p-content h3 {
    width: 100%;
    height: 22px;
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    color: #4080FF;
    text-align: left;
    margin-bottom: 24px;
}

.addschem-diag .window .p-content .lable {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
}

.addschem-diag .window .p-content .lable.lab-time .sel:nth-child(4n) {
    margin-right: 50px;
}

.addschem-diag .window .p-content .lable.lab-time .sel:nth-child(3n) {
    margin-right: 0;
}

.addschem-diag .window .p-content .lable .sel {
    flex-shrink: 0;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    margin-right: 50px;
    margin-bottom: 24px;
}

.addschem-diag .window .p-content .lable .sel.times .select-input {
    width: 168px;
    margin-right: 0;
}

.addschem-diag .window .p-content .lable .sel:nth-child(4n) {
    margin-right: 0;
}

.addschem-diag .window .p-content .lable .sel em {
    font-weight: 400;
    font-size: 14px;
    line-height: 34px;
    color: #1D2129;
}

.addschem-diag .window .p-content .lable .sel .select-input {
    width: 80px;
    margin-left: 14px;
    margin-right: 14px;
}

.main .cons .btn-list {
    justify-content: space-between;
}

.addschem-diag .window {
    height: auto;
    min-height: 600px;
}

.addschem-diag .window .p-content {
    padding-bottom: 70px;
}

.addschem-diag .window .p-content .lab-morning-time {
    display: none;
}

.addschem-diag .window .p-content .lab-night-time {
    display: none;
}

.main .cons .p-tables .row .lab:first-child {
    background-color: #F1F3F6;
}

.main .cons .p-tables .break .lab .time-sel {
    border: 1px solid #F1F3F6;
}

.main .cons .p-tables .break .lab:last-of-type {
    border-right: none;
}

.main .cons .p-tables .row.break .noborder {
    border-right: none;
}

.main .cons .p-tables .row.break div:nth-last-child(2) {
    border-right: none;
}

/*20230221*/
.cons .c-top .select-session {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 0 24px;
    height: 34px;
}

.cons .c-top .select-session .names {
    font-size: 14px;
    color: #1D2129;
    margin-right: 14px;
}

.cons .c-top .select-session .select-input {
    width: 240px;
}

.cons .c-top .tab {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 20px;
    margin-bottom: 30px;
}

.cons .c-top .tab .name {
    font-size: 14px;
    color: #1D2129;
    margin-right: 14px;
}

.cons .c-top .tab ul {
    overflow: hidden;
}

.cons .c-top .tab ul li {
    float: left;
    cursor: pointer;
    margin-right: 24px;
    padding-left: 24px;
    background: url(../../images/timetable/radio-icon.png) no-repeat left center;
    font-size: 14px;
    color: #86909C;
}

.cons .c-top .tab ul li.cur {
    background: url(../../images/timetable/radio-cur-icon.png) no-repeat left center;
}