body {
  background-color: #F7F8FA;
  font-size: 14px;
  color: #4E5969;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .form-con .sel-item {
  flex: 0 0 20%;
}
.main .form-con .sel-item .sel {
  flex: 1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .form-con .sel-item .sel .select-input {
  flex: 1;
  width: 0;
}
.main .form-con .sel-item .sel.seat {
  flex: 1;
  width: 0;
}
.main .form-con .sel-item .sel.seat .layui-input {
  flex: 1;
  width: 0;
}
.main .form-con .sel-item .sel.seat span {
  width: 32px;
  margin: 0;
  text-align: center;
  flex-shrink: 0;
}
.main .form-con .sel-item .btn {
  flex: 1;
  width: 0;
  max-width: 104px;
}
.main .jpPage {
  position: relative;
  bottom: auto;
}
.main .course-list {
  min-height: calc(100vh - 375px);
}
.layui-table-grid-down {
  display: none;
}
