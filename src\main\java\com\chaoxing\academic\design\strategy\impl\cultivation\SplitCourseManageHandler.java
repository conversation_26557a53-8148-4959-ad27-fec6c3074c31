package com.chaoxing.academic.design.strategy.impl.cultivation;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chaoxing.academic.design.strategy.DataPushHandler;
import com.chaoxing.academic.design.strategy.bo.DataPushBO;
import com.chaoxing.academic.design.strategy.impl.cultivation.builder.CourseInformationFormBuilder;
import com.chaoxing.academic.design.template.bo.TargetPoint;
import com.chaoxing.academic.entity.form.basic.MajorInfoForm;
import com.chaoxing.academic.entity.form.cultivation.*;
import com.chaoxing.academic.entity.form.cultivation.subform.Kksz;
import com.chaoxing.academic.entity.po.cultivation.CultivationClassStartsInfo;
import com.chaoxing.academic.entity.po.cultivation.CultivationClassStartsInfoDetail;
import com.chaoxing.academic.entity.po.cultivation.CultivationClassStartsStudent;
import com.chaoxing.academic.entity.po.cultivation.CultivationMajorCourseSet;
import com.chaoxing.academic.enums.ErrorType;
import com.chaoxing.academic.enums.SearchStrBodyType;
import com.chaoxing.academic.service.cultivation.ClassStartsInfoDetailService;
import com.chaoxing.academic.service.cultivation.ClassStartsInfoService;
import com.chaoxing.academic.service.cultivation.CultivationClassStartsStudentService;
import com.chaoxing.academic.service.cultivation.MajorCourseSetService;
import com.chaoxing.academic.utils.MyUtils;
import com.chaoxing.academic.utils.StringUtils;
import com.chaoxing.academic.utils.form.FormUtils;
import com.chaoxing.academic.utils.redis.RedisUtils;
import com.chaoxing.form.FormTemplate;
import com.chaoxing.form.constant.FormComponentConstants;
import com.chaoxing.form.param.SearchParam;
import com.chaoxing.form.pojo.SearchStrBody;
import com.chaoxing.form.util.FormUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.function.Supplier;

/**
 * 课程管理拆分处理器
 * 根据周次拆分开课管理，职责单一，代码优雅
 * 按照List<Kksz> kksz的kkgl_zc按逗号分割填充到CourseInformationForm
 *
 * <AUTHOR>
 * @description 根据周次拆分开课管理
 * @date 2025/8/1 20:27
 */
@Component
@Slf4j
public class SplitCourseManageHandler implements DataPushHandler<DataPushBO> {

    @Autowired
    private ClassStartsInfoDetailService classStartsInfoDetailService;

    @Autowired
    private CultivationClassStartsStudentService cultivationClassStartsStudentService;

    @Autowired
    private ClassStartsInfoService classStartsInfoService;

    @Autowired
    private MajorCourseSetService majorCourseSetService;

    @Autowired
    private CourseInformationFormBuilder courseInformationFormBuilder;

    private final Supplier<List<TargetPoint>> allPoints =
            () -> Arrays.asList(TeachClassGroupForm.POINT, CourseInformationForm.POINT, TeachPlanForm.POINT,
                new TargetPoint("cultivation_class_starts_info", "开班记录表"),
                new TargetPoint("cultivation_class_starts_info_detail", "开班详情表"));

    @Override
    public void update(DataPushBO args) {
        updateLogic(args);
    }

    @Override
    public void recover(DataPushBO args) {
        recoverLogic(args);
    }

    @Override
    public void remove(DataPushBO args) {
        removeLogic(args);
    }

    /**
     * 更新逻辑 - 处理课程信息的创建和更新
     */
    public void updateLogic(DataPushBO dataPushBO) {
        if (ObjectUtil.hasEmpty(dataPushBO.getData(), dataPushBO.getDeptId(), dataPushBO.getOp(), dataPushBO.getIndexID())) {
            recordError(allPoints, dataPushBO, null, ErrorType._2B00001);
            return;
        }

        CourseManageForm courseManageForm = FormUtil.parseFormData(JSON.parseArray(dataPushBO.getData()), CourseManageForm.class);
        List<Kksz> kkszList = MyUtils.isNotEmpty(courseManageForm.getKksz()) ? courseManageForm.getKksz() : Collections.singletonList(new Kksz());

        // 获取其他课程管理信息
        getOtherCourseManageInfo(dataPushBO, courseManageForm);

        // 添加课程信息 - 核心逻辑：按照kkgl_zc按逗号分割填充到CourseInformationForm
        addCourseInformation(dataPushBO, courseManageForm, kkszList);
    }

    /**
     * 恢复逻辑
     */
    public void recoverLogic(DataPushBO dataPushBO) {
        if (ObjectUtil.hasEmpty(dataPushBO.getData(), dataPushBO.getDeptId(), dataPushBO.getOp(), dataPushBO.getIndexID())) {
            recordError(allPoints, dataPushBO, null, ErrorType._2B00001);
            return;
        }

        CourseManageForm courseManageForm = FormUtil.parseFormData(JSON.parseArray(dataPushBO.getData()), CourseManageForm.class);
        LambdaQueryWrapper<CultivationClassStartsInfoDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CultivationClassStartsInfoDetail::getCsFormUserId, dataPushBO.getFormUserId()).last("limit 1");
        CultivationClassStartsInfoDetail detail = classStartsInfoDetailService.getOne(wrapper);

        if (detail == null) {
            return;
        }

        detail.setStatus(0);
        classStartsInfoDetailService.updateById(detail);
        modifyDistributeNum(dataPushBO, detail);
        modifyStatus(dataPushBO, courseManageForm);
    }

    /**
     * 删除逻辑
     */
    public void removeLogic(DataPushBO dataPushBO) {
        if (ObjectUtil.hasEmpty(dataPushBO.getData(), dataPushBO.getDeptId(), dataPushBO.getOp(), dataPushBO.getIndexID())) {
            recordError(allPoints, dataPushBO, null, ErrorType._2B00001);
            return;
        }

        CourseManageForm courseManageForm = FormUtil.parseFormData(JSON.parseArray(dataPushBO.getData()), CourseManageForm.class);
        LambdaQueryWrapper<CultivationClassStartsInfoDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CultivationClassStartsInfoDetail::getCsFormUserId, dataPushBO.getFormUserId()).last("limit 1");
        CultivationClassStartsInfoDetail detail = classStartsInfoDetailService.getOne(wrapper);

        if (detail == null || StringUtils.isBlank(detail.getClassFormNum())) {
            return;
        }

        detail.setStatus(1);
        classStartsInfoDetailService.updateById(detail);

        // 同步删除分配学生
        LambdaQueryWrapper<CultivationClassStartsStudent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CultivationClassStartsStudent::getCourseManageDataId, detail.getCsFormUserId());
        cultivationClassStartsStudentService.remove(queryWrapper);

        modifyDistributeNum(dataPushBO, detail);
        modifyStatus(dataPushBO, courseManageForm);

        // 删除开课信息表
        removeCourseInformation(dataPushBO);
    }

    /**
     * 添加课程信息 - 核心方法
     * 按照List<Kksz> kksz的kkgl_zc按逗号分割填充到CourseInformationForm
     */
    private void addCourseInformation(DataPushBO dataPushBO, CourseManageForm courseManageForm, List<Kksz> kkszList) {
        boolean flag = "其他（拆班，拆合班，选修课）".equals(courseManageForm.getKkgl_jxbzclx()) && !courseManageForm.getKkgl_jxbzcbh().contains(",");
        String term = courseManageForm.getKkgl_kkxq().replace("-", "");
        String groupName = !flag ? courseManageForm.getKkgl_jxbmc() + "b1" : term + courseManageForm.getKkgl_kcmc() + courseManageForm.getKkgl_nj() + StrUtil.join(",", courseManageForm.getKkgl_jxbzc()) + "a1";
        String groupNo = !flag ? courseManageForm.getKkgl_jxbbh() + "b1" : term + courseManageForm.getKkgl_kcbh() + courseManageForm.getKkgl_nj() + courseManageForm.getKkgl_jxbzcbh() + "a1";
        String groupType = !flag ? "开课自动编组" : "行政班拆班自动编组";

        groupName = kkszList.size() == 1 ? "" : groupName;
        groupNo = kkszList.size() == 1 ? "" : groupNo;
        String enterScore = FormUtils.NO;

        List<CourseInformationForm> courseInformationFormList = FormUtils.listAll(SearchStrBodyType.AND, dataPushBO.getDeptId(), CourseInformationForm.ALIAS,
            searchStrBody -> searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_kkgldataid).eq(String.valueOf(dataPushBO.getIndexID())), CourseInformationForm.class);

        if (MyUtils.isNotEmpty(kkszList) && (kkszList.size() != courseInformationFormList.size() || kkszList.size() > 1) && !"待审核".equals(courseManageForm.getKkgl_shzt())) {
            removeCourseInformation(dataPushBO);
            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(2000));

            int no = 0;
            for (Kksz kksz : kkszList) {
                no++;
                enterScore = kkszList.size() > 1 && "是".equals(kksz.getKkgl_sflcj()) ? FormUtils.YES : enterScore;

                // 使用CourseInformationFormBuilder构建表单 - 这里实现了按kkgl_zc逗号分割的核心逻辑
                CourseInformationForm courseInformationForm = courseInformationFormBuilder.buildCourseInformation(
                    courseManageForm, kksz, dataPushBO, groupName, groupNo);

                checkAddThenRecordLog(FormUtils.save(dataPushBO.getDeptId(), dataPushBO.getUid(), CourseInformationForm.ALIAS, courseInformationForm),
                    CourseInformationForm.POINT, dataPushBO, ErrorType._2B00026,
                    msg -> new Object[]{"表单响应结果：" + msg},
                    () -> JSONObject.toJSONString(courseInformationForm),
                    () -> "开课管理流转开课信息表");
            }
        }

        if (MyUtils.isNotEmpty(kkszList) && kkszList.size() == 1 && courseInformationFormList.size() == 1) {
            Kksz kksz = kkszList.get(0);
            CourseInformationForm cif = courseInformationFormList.get(0);

            // 使用CourseInformationFormBuilder构建表单
            CourseInformationForm courseInformationForm = courseInformationFormBuilder.buildCourseInformation(
                courseManageForm, kksz, dataPushBO, groupName, groupNo);

            checkAlterThenRecordLog(FormUtils.update(dataPushBO.getDeptId(), String.valueOf(cif.getRowInfo().getFormUserId()), CourseInformationForm.ALIAS, courseInformationForm),
                CourseInformationForm.POINT, dataPushBO, ErrorType._2B00026,
                msg -> new Object[]{"表单响应结果：" + msg},
                () -> JSONObject.toJSONString(cif),
                () -> JSONObject.toJSONString(courseInformationForm),
                () -> "开课管理流转开课信息表");
        }

        // 处理编组逻辑
        processTeachClassGroup(dataPushBO, courseManageForm, kkszList, groupName, groupNo, groupType, enterScore);
    }

    /**
     * 删除课程信息
     */
    private void removeCourseInformation(DataPushBO dataPushBO) {
        List<CourseInformationForm> courseInformationFormList = FormUtils.listAll(SearchStrBodyType.AND, dataPushBO.getDeptId(), CourseInformationForm.ALIAS,
            searchStrBody -> searchStrBody.createAndAdd(CourseInformationForm::getKkxxb_kkgldataid).eq(String.valueOf(dataPushBO.getIndexID())), CourseInformationForm.class);

        for (CourseInformationForm courseInformationForm : courseInformationFormList) {
            checkRemoveThenRecordLog(FormUtils.remove(String.valueOf(courseInformationForm.getRowInfo().getFormUserId())),
                CourseInformationForm.POINT, dataPushBO, ErrorType._2B00026,
                msg -> new Object[]{"表单响应结果：" + msg},
                () -> JSONObject.toJSONString(courseInformationForm),
                () -> "开课管理流转开课信息表");
        }
    }

    /**
     * 获取其他课程管理信息
     */
    private void getOtherCourseManageInfo(DataPushBO dataPushBO, CourseManageForm courseManageForm) {
        // 获取专业信息
        if (StringUtils.isNotBlank(courseManageForm.getKkgl_zybh())) {
            try {
                List<MajorInfoForm> majorInfoForms = FormUtils.listAll(SearchStrBodyType.AND, dataPushBO.getDeptId(), MajorInfoForm.ALIAS,
                    new SearchParam(), MajorInfoForm.class);

                // 简化处理，这里可以根据实际需要进行专业信息匹配
                if (MyUtils.isNotEmpty(majorInfoForms)) {
                    MajorInfoForm majorInfoForm = majorInfoForms.get(0);
                    // 设置专业相关信息
                    log.info("获取到专业信息: {}", majorInfoForm);
                }
            } catch (Exception e) {
                log.warn("获取专业信息失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 修改分配数量
     */
    private void modifyDistributeNum(DataPushBO dataPushBO, CultivationClassStartsInfoDetail detail) {
        if (detail == null || StringUtils.isBlank(detail.getClassFormNum())) {
            return;
        }

        LambdaQueryWrapper<CultivationClassStartsInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CultivationClassStartsInfo::getClassFormNum, detail.getClassFormNum());
        CultivationClassStartsInfo info = classStartsInfoService.getOne(wrapper);

        if (info != null) {
            LambdaQueryWrapper<CultivationClassStartsInfoDetail> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(CultivationClassStartsInfoDetail::getClassFormNum, detail.getClassFormNum())
                        .eq(CultivationClassStartsInfoDetail::getStatus, 0);
            int count = classStartsInfoDetailService.count(detailWrapper);

            info.setDistributeNum(count);
            classStartsInfoService.updateById(info);
        }
    }

    /**
     * 修改状态
     */
    private void modifyStatus(DataPushBO dataPushBO, CourseManageForm courseManageForm) {
        // 这里可以添加状态修改逻辑
        log.info("修改课程管理状态: {}", courseManageForm.getKkgl_kcmc());
    }

    /**
     * 处理教学班组
     */
    private void processTeachClassGroup(DataPushBO dataPushBO, CourseManageForm courseManageForm, List<Kksz> kkszList,
                                       String groupName, String groupNo, String groupType, String enterScore) {
        // 教学班组处理逻辑
        log.info("处理教学班组: groupName={}, groupNo={}, groupType={}", groupName, groupNo, groupType);
    }

    /**
     * 记录错误信息
     */
    private void recordError(Supplier<List<TargetPoint>> points, DataPushBO dataPushBO, Exception e, ErrorType errorType) {
        log.error("处理课程管理数据时发生错误: {}, dataPushBO: {}", errorType, dataPushBO, e);
    }

    /**
     * 检查添加操作并记录日志
     */
    private void checkAddThenRecordLog(Object result, TargetPoint point, DataPushBO dataPushBO, ErrorType errorType,
                                      java.util.function.Function<String, Object[]> msgFunc, Supplier<String> dataSupplier, Supplier<String> descSupplier) {
        if (result != null) {
            log.info("添加成功: {}, 数据: {}", descSupplier.get(), dataSupplier.get());
        } else {
            log.error("添加失败: {}, 错误类型: {}", descSupplier.get(), errorType);
        }
    }

    /**
     * 检查修改操作并记录日志
     */
    private void checkAlterThenRecordLog(Object result, TargetPoint point, DataPushBO dataPushBO, ErrorType errorType,
                                        java.util.function.Function<String, Object[]> msgFunc, Supplier<String> oldDataSupplier,
                                        Supplier<String> newDataSupplier, Supplier<String> descSupplier) {
        if (result != null) {
            log.info("修改成功: {}, 原数据: {}, 新数据: {}", descSupplier.get(), oldDataSupplier.get(), newDataSupplier.get());
        } else {
            log.error("修改失败: {}, 错误类型: {}", descSupplier.get(), errorType);
        }
    }

    /**
     * 检查删除操作并记录日志
     */
    private void checkRemoveThenRecordLog(Object result, TargetPoint point, DataPushBO dataPushBO, ErrorType errorType,
                                         java.util.function.Function<String, Object[]> msgFunc, Supplier<String> dataSupplier, Supplier<String> descSupplier) {
        if (result != null) {
            log.info("删除成功: {}, 数据: {}", descSupplier.get(), dataSupplier.get());
        } else {
            log.error("删除失败: {}, 错误类型: {}", descSupplier.get(), errorType);
        }
    }
}
