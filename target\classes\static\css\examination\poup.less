.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}

.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}

.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}

textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: rgba(188, 188, 197, 1);
}

textarea:-moz-placeholder {
  font-size: 12px;
  color: rgba(188, 188, 197, 1);
}

input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

textarea:-ms-input-placeholder {
  font-size: 12px;
  color: rgba(188, 188, 197, 1);
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

.layui-laypage .layui-disabled {
  background-color: transparent !important;
  border: none !important;
}


.clearfixs {
  zoom: 1;
}

.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

input::-webkit-input-placeholder {
  color: #ACB4BF !important;
  font-size: 14px;
}

::-webkit-scrollbar {
  width: 6px;
  height: 10px;
}

::-webkit-scrollbar-thumb {
  border-radius: 6px;

  background: rgba(217, 217, 217, 1);
}

::-webkit-scrollbar-track {
  border-radius: 6px;
}

.popups {
  background: #FFFFFF;
  border-radius: 10px;
  // overflow: hidden;
  display: none;

  .title {
    height: 52px;
    line-height: 52px;
    font-size: 16px;
    font-weight: 400;
    padding: 0 24px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #F2F2F2;

    .name {
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      color: rgba(29, 33, 41, 1);
      text-align: left;
    }

    .close {
      width: 20px;
      height: 20px;
      background: url(../../../../../../../../教务需求记录20240605/考务/images/close-icon.png) no-repeat center;
      cursor: pointer;
    }
  }

  .popup-con {
    padding: 30px;

  }

  .bottom {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 66px;
    border-top: 1px solid #E5E6EB;
    padding: 0 24px;

    div {
      width: 88px;
      height: 34px;
      text-align: center;
      line-height: 32px;
      font-size: 14px;
      border-radius: 18px;
      cursor: pointer;
      .borDer;

      &.cancle {
        border: 1px solid #C9CDD4;
        color: #4E5969;
        background-color: #fff;
        margin-right: 16px;
      }

      &.confirm {
        color: #fff;
        background: #4D88FF;
        box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
        border: 1px solid #4D88FF;
      }
    }
  }
}


.layui-layer-page .layui-layer-content {
  height: auto !important;
}



.exam-popup {
  width: 770px;

  .popup-con {
    padding: 40px 80px;
    .borDer;
    // height: 388px;
    // overflow-y: auto;

    .from-wrapper {
      .lable {
        display: flex;
        display: -webkit-flex;
        align-items: flex-start;
        justify-content: flex-start;

        .name {
          display: flex;
          display: -webkit-flex;
          align-items: center;
          justify-content: flex-start;
          height: 20px;
          width: 92px;
          flex-shrink: 0;

          span {
            font-size: 14px;
            color: #1D2129;
            margin-right: 6px;
          }

          i {
            width: 16px;
            height: 16px;
            display: inline-block;
            background: url(../../../../../../../../教务需求记录20240605/考务/images/feedback.png) no-repeat center;
            cursor: pointer;
            position: relative;

            &:hover {
              em {
                display: block;
              }
            }

            em {
              display: none;
              position: absolute;
              // transform: translate(-14px, -49px);
              // -webkit-transform: translate(-14px, -49px);
              left: -14px;
              top: -49px;
              width: auto;
              height: 38px;
              border-radius: 6px;
              background: #4E5969;
              padding: 8px 12px;
              box-sizing: borDer-box;
              -webkit-box-sizing: borDer-box;
              -moz-box-sizing: borDer-box;
              -ms-box-sizing: borDer-box;
              -o-box-sizing: borDer-box;
              line-height: 22px;
              font-size: 14px;
              color: #ffffff;
              white-space: nowrap;
              z-index: 999;

              &:after {
                content: '';
                position: absolute;
                bottom: -6px;
                left: 16px;
                width: 12px;
                transform: rotate(180deg);
                -webkit-transform: rotate(180deg);
                height: 6px;
                background: url(../../../../../../../../教务需求记录20240605/考务/images/black-tringle-icon.png) no-repeat center;
              }
            }
          }
        }

        .checkbox {
          flex: 1;

          ul {
            overflow: hidden;

            li {
              float: left;
              color: #4e5969;
              width: 125px;
              .borDer;
              .textEls;
              font-size: 14px;
              line-height: 20px;
              margin-bottom: 24px;
              padding-left: 24px;
              background: url(../../../../../../../../教务需求记录20240605/考务/images/check1-icon.png) no-repeat left center;
              background-size: 16px;
              cursor: pointer;


              &.cur {
                background: url(../../../../../../../../教务需求记录20240605/考务/images/checked1-icon.png) no-repeat left center;
                background-size: 16px;
              }
            }
          }
        }

        .textarea {
          border: 1px solid #E5E6EB;
          .borDer;
          border-radius: 4px;
          flex: 1;
          min-height: 132px;
          padding: 6px 9px;

          ul {
            overflow: hidden;

            li {
              float: left;
              background-color: #E1EBFF;
              height: 34px;
              padding: 0 12px;
              border-radius: 12px;
              display: flex;
              display: -webkit-flex;
              align-items: center;
              justify-content: center;
              position: relative;
              margin-right: 12px;
              margin-bottom: 12px;
              cursor: move;

              .delet {
                position: absolute;
                width: 14px;
                height: 15px;
                top: 0;
                right: -4px;
                background: url(../../../../../../../../教务需求记录20240605/考务/images/icon-del.png) no-repeat left center;
                cursor: pointer;
              }

              i {
                font-size: 14px;
                color: #4D88FF;
                margin-right: 4px;
              }

              span {
                font-size: 14px;
                color: #4D88FF;
              }
            }
          }
        }
      }
    }


  }
}



.layui-layer {
  // top: 150px !important;
  border-radius: 10px !important;
}