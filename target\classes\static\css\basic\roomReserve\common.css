.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #ACB4BF;
  font-size: 14px;
}
.hide {
  display: none;
}
::-webkit-scrollbar {
  width: 6px;
  height: 10px;
}
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #d9d9d9;
}
::-webkit-scrollbar-track {
  border-radius: 6px;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.layui-form .layui-form-item {
  margin-bottom: 32px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
.layui-form .layui-form-item .layui-form-label {
  color: #1d2129;
  font-size: 14px;
  font-weight: 400;
  margin-right: 14px;
  height: 34px;
  line-height: 34px;
  width: 70px;
}
.layui-form .layui-form-item .layui-input-block {
  margin-left: 0;
  width: 240px;
}
.layui-input:focus {
  border-color: #4D88FF !important;
}
#editPoups {
  width: 1231px;
}
#editPoups .popup-con .tab {
  height: 36px;
  margin-bottom: 24px;
}
#editPoups .popup-con .tab ul {
  overflow: hidden;
  border-radius: 4px;
  float: left;
}
#editPoups .popup-con .tab ul li {
  float: left;
  cursor: pointer;
  width: 161px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  font-size: 16px;
  color: #1d2129;
  background-color: #F2F3F5;
}
#editPoups .popup-con .tab ul li:first-child {
  margin-right: 2px;
}
#editPoups .popup-con .tab ul li.cur {
  background-color: #4d88ff;
  color: #fff;
}
#editPoups .popup-con .regional-switch .lable {
  display: none;
}
#editPoups .popup-con .regional-switch .lable.active {
  display: block;
}
#editPoups .popup-con .regional-switch .lable .top .layui-form {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#editPoups .popup-con .regional-switch .lable .top .layui-form .layui-form-item {
  margin-bottom: 24px;
  margin-right: 32px;
}
#editPoups .popup-con .regional-switch .lable .rs-main {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
#editPoups .popup-con .regional-switch .lable #tbody-kb {
  height: 410px;
  overflow-y: auto;
}
#editPoups .popup-con .regional-switch .lable .info-list {
  margin-bottom: 6px;
  height: 424px;
  overflow-y: auto;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li {
  margin-bottom: 8px;
  border-radius: 4px;
  background: #F7F8FA;
  padding: 4px 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li:hover {
  background-color: #E1EBFF;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li:hover .item-list {
  border-right: 1px solid #fff;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li .item-list {
  flex: 1;
  width: 0;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  border-right: 1px solid #E8EBF1;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li .item-list .item {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 12px;
  margin-bottom: 12px;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li .item-list .item span {
  width: auto;
  flex-shrink: 0;
  height: 20px;
  line-height: 20px;
  font-size: 16px;
  color: #1d2129;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li .item-list .item .content {
  width: auto;
  line-height: 20px;
  padding-right: 20px;
  color: #4e5969;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li .item-list .item .layui-input {
  display: none;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li .oprate {
  flex-shrink: 0;
  width: 68px;
  padding-left: 18px;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li .oprate .cancle {
  display: none;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li .oprate .editFinish {
  display: none;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li .oprate .edit {
  width: 68px;
  height: 34px;
  border-radius: 18px;
  background: #4D88FF;
  text-align: center;
  line-height: 34px;
  cursor: pointer;
  margin-right: 12px;
  font-size: 14px;
  color: #ffffff;
  margin-bottom: 8px;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li .oprate .delet {
  width: 68px;
  height: 34px;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
  border-radius: 18px;
  border: 1px solid #E5E6EB;
  background: #FFF;
  font-size: 14px;
  color: #4e5969;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable {
  display: block;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable:hover {
  background: #F7F8FA;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .item-list {
  width: 100%;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding-bottom: 12px;
  border-right: 0;
  border-bottom: 1px solid #E8EBF1;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .item-list .item {
  flex: 0 0 48%;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .item-list .item:nth-child(2n+1) {
  padding-right: 4%;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .item-list .item span {
  width: 66px;
  text-align: justify;
  text-align-last: justify;
  /*兼容ie*/
  text-justify: distribute-all-lines;
  white-space: nowrap;
  margin-right: 14px;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .item-list .item span em {
  display: none;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .item-list .item .content {
  display: none;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .item-list .item .layui-input {
  display: block;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .oprate {
  padding-top: 16px;
  padding-bottom: 12px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: flex-end;
  width: 100%;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .oprate .edit {
  display: none;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .oprate .delet {
  display: none;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .oprate .cancle {
  display: block;
  width: 68px;
  height: 34px;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
  border-radius: 18px;
  border: 1px solid #E5E6EB;
  background: #FFF;
  font-size: 14px;
  color: #4e5969;
  margin-right: 12px;
}
#editPoups .popup-con .regional-switch .lable .info-list ul li.editable .oprate .editFinish {
  display: block;
  width: 96px;
  height: 34px;
  border-radius: 18px;
  background: #4D88FF;
  text-align: center;
  line-height: 34px;
  cursor: pointer;
  margin-right: 12px;
  font-size: 14px;
  color: #ffffff;
}
#editPoups .popup-con .regional-switch .lable .l-form {
  width: 324px;
  flex-shrink: 0;
  margin-right: 40px;
}
#editPoups .popup-con .regional-switch .lable .l-form .add {
  height: 22px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#editPoups .popup-con .regional-switch .lable .l-form .add span {
  padding-left: 18px;
  background: url(../../../images/basic/roomReserve/add-icons.png) no-repeat left center;
  font-size: 14px;
  color: #4d88ff;
  cursor: pointer;
}
#editPoups .popup-con .regional-switch .lable .r-table {
  flex: 1;
  width: 0;
}
#editPoups .popup-con .regional-switch .lable .r-table .thead {
  margin-bottom: 4px;
}
#editPoups .popup-con .regional-switch .lable .r-table .thead ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
#editPoups .popup-con .regional-switch .lable .r-table .thead ul li {
  flex: 1;
  color: #4e5969;
  font-size: 14px;
  margin-right: 4px;
  border-radius: 4px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  background-color: #E8EBF3;
}
#editPoups .popup-con .regional-switch .lable .r-table .thead ul li:first-child {
  flex: 0 0 30px;
  flex-shrink: 0;
}
#editPoups .popup-con .regional-switch .lable .r-table .thead ul li:last-child {
  margin-right: 0;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 4px;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul.carve-up {
  height: 4px;
  background: url(../../../images/basic/roomReserve/line-bg.png) repeat-x;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li {
  flex: 1;
  border-radius: 4px;
  background: #F7F8FA;
  margin-right: 4px;
  color: #4e5969;
  font-size: 14px;
  font-weight: 400;
  height: 56px;
  width: 0;
  cursor: pointer;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area {
  width: 100%;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 7px 15px;
  border: 1px solid #F7F8FA;
  position: relative;
  border-radius: 4px;
  height: 56px;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area:hover {
  border: 1px solid #F7F8FA;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area:hover .oprate {
  display: flex;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area:hover .tage {
  display: block;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage {
  display: none;
  position: absolute;
  top: 6px;
  right: 4px;
  line-height: 16px;
  font-size: 10px;
  color: #FF6B6B;
  border-radius: 4px;
  height: 72px;
  top: 4px;
  z-index: 99;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em {
  display: block;
  width: auto;
  height: 16px;
  padding: 0 4px;
  border-radius: 4px;
  line-height: 16px;
  font-size: 12px;
  color: #FFFFFF;
  transform: scale(0.9);
  -webkit-transform: scale(0.9);
  margin-bottom: 2px;
  white-space: nowrap;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em.c1 {
  background: #FA9191;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em.c2 {
  background: #8ED66B;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em.c3 {
  background: #6ECBFA;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em.c4 {
  background: #61b769;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em.c5 {
  background: #53BDF6;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em.c6 {
  background: #527FF3;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em.c7 {
  background: #A67EF0;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em:hover .tips {
  display: block;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em .tips {
  display: none;
  position: absolute;
  z-index: 1999;
  left: 38px;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  padding-left: 4px;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em .tips .tts {
  background: rgba(24, 30, 51, 0.8);
  border-radius: 4px;
  padding: 10px 16px;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  color: #FFFFFF;
  width: 236px;
  height: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  white-space: pre-wrap;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .tage em .tips:after {
  content: '';
  position: absolute;
  left: -4px;
  top: 50%;
  transform: translateY(-50%) rotate(-90deg);
  -webkit-transform: translateY(-50%) rotate(-90deg);
  width: 12px;
  height: 6px;
  background: url(../../../images/basic/roomReserve/black-tringle-icon.png) no-repeat center;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .oprate {
  position: absolute;
  right: 5px;
  bottom: 11px;
  z-index: 99;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  display: none;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .oprate span {
  font-size: 16px;
  height: 16px;
  line-height: 16px;
  color: #4d88ff;
  margin-left: 4px;
  cursor: pointer;
  position: relative;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .oprate span.cur:hover em {
  display: block;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .oprate span em {
  display: none;
  position: absolute;
  right: -22px;
  top: -48px;
  width: auto;
  padding: 8px 12px;
  line-height: 22px;
  border-radius: 6px;
  background: #4E5969;
  text-align: center;
  white-space: nowrap;
  font-size: 14px;
  color: #ffffff;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .oprate span em:after {
  content: '';
  position: absolute;
  bottom: -4px;
  right: 24px;
  width: 12px;
  height: 6px;
  background: url(../../../images/basic/roomReserve/black-tringle-icon.png) no-repeat center;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .name {
  font-weight: 500;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .teacher {
  font-weight: 400;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .teacher.self {
  color: #f76560;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li .select-area .classroom {
  font-weight: 400;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li:first-child {
  flex: 0 0 30px;
  flex-shrink: 0;
  display: flex;
  display: -webkit-flex;
  justify-content: flex-start;
  align-items: center;
  justify-content: center;
  cursor: default;
  background-color: #E8EBF3;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li:first-child span {
  font-size: 14px;
  color: #4e5969;
}
#editPoups .popup-con .regional-switch .lable .r-table .tbody ul li:last-child {
  margin-right: 0;
}
.popup {
  background: #FFFFFF;
  border-radius: 10px;
  display: none;
}
.popup .title {
  height: 56px;
  line-height: 56px;
  font-size: 16px;
  font-weight: 400;
  padding: 0 30px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #E5E6EB;
}
.popup .title .name {
  font-size: 16px;
  color: #1d2129;
  text-align: left;
}
.popup .popup-con {
  padding: 30px;
}
.popup .bottom {
  border-top: 1px solid #E5E6EB;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 0;
  padding: 0 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popup .bottom .exam-cancle {
  border: 1px solid #E5E6EB;
  padding: 0 30px;
  height: 36px;
  font-size: 14px;
  border-radius: 18px;
  color: #4E5969;
  margin-right: 16px;
  background-color: #fff;
}
.popup .bottom .exam-sure {
  color: #fff;
  background: #4d88ff;
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  border: 1px solid #4d88ff;
  padding: 0 30px;
  height: 36px;
  font-size: 14px;
  border-radius: 18px;
  display: block;
}
