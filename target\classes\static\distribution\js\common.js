  // 选择
  $(".j-select").on('click', "ul li", function () {
    $(this).addClass('active').siblings().removeClass('active');
    $(this).parents('.j-select').hide();
})
// 下拉
$(".schoolSel").on('click', function (e) {
    // $(".j-material .j-select-year").removeClass('slideShow');
    // $(".j-material .j-arrow").removeClass('j-arrow-slide');
    let parent = $(this).parent();
    parent.find('.j-arrow').toggleClass('j-arrow-slide');
    parent.find('.j-select-year').toggleClass('slideShow')
    e.stopPropagation()
})
$('.j-select-year').on('click', "ul li", function () {
    $(this).addClass('active').siblings().removeClass('active');
    let txt = $(this).text();
    let parent = $(this).parents('.j-search-con');
    parent.find('.schoolSel').val(txt.trim());
    parent.find('.j-arrow').removeClass('j-arrow-slide');
    $(this).parents('.j-select-year').removeClass("slideShow")
    if ($(this).parent("ul").hasClass("main")){
        var index = $(this).index();
        var cell = $(".jc").find(".cell");
        for (let i=0;i<cell.length;i++){
            var hasClass = $(cell[i]).children(".lable").find("span").hasClass("cur");
            if (hasClass) {
                var gys = $(cell[i]).find(".gys")
                for (let i=0;i<gys.length;i++){
                    $(gys[i]).children("li").eq(index).addClass('active').siblings().removeClass('active');
                }
                $(cell[i]).find('.schoolSel').val(txt.trim());
            }
        }
    }
})

// 点击页面其他地方消失
$(document).on('click', function (e) {
    if ($(e.target).closest('.j-select').length > 0 || $(e.target).closest('.j-select-year')
        .length > 0) {
        // alert('弹出框内部被点击了');
    } else {
        $(".j-select").hide();
        $('.j-select-year').removeClass("slideShow")
    }
})