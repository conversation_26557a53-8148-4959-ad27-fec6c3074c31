<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1, user-scalable=no">
    <title>待选课</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/head.css'"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/courseList.css?v=11'"/>
    <script th:src="${_CPR_}+'/elective/js/responsive-1.0.js'"></script>
</head>

<body>
<header>
    <div class="head-signal"></div>
    <div class="head-con">
        <a href="javascript:void(0)" class="head-back"> </a>
        <h1 class="head-title">学生选课</h1>
<!--        <div class="centerHead">-->
<!--            <div class="selectBox">-->
<!--                <div class="selectWeek"><span class="week" week="1"></span><em></em></div>-->
<!--            </div>-->
<!--        </div>-->
        <div class="head-menu"  style="display: none;">
            <div class="menu" id="menu"></div>
            <ul class="menu-list" id="menuList">
                <li>课表显示</li>
                <li class="active">列表显示</li>
            </ul>
        </div>
    </div>
</header>
<!-- 2025.3.25 start -->
<div class="con-select">
    <ul>
        <li class="course"><span>学期</span> <i></i></li>
        <li class="date"><span>暂无选课计划</span><i></i></li>
    </ul>
</div>
<!-- 2025.3.25 end -->
<div class="search-course">
    <input type="text" id="xxkmc" placeholder="请输入课程名称">
<!--    <div class="search-btn" id="searchBtn">筛选</div>-->
</div>
<div class="handle">
    <div class="btns">
        <div class="btn select">
            <span>已选课程</span>
        </div>
        <div class="btn group">
            <span>编组课程</span>
        </div>
    </div>
    <div class="search-btn" id="searchBtn">筛选</div>
</div>
<div class="main">
<!--    <div class="z-search-course">
        <h1>查看已选课程</h1>
        <div class="switch" id="courseSwitch"><span></span></div>
    </div>-->
    <div class="z-search-list" id="zList">
    </div>
</div>

<!-- 选择学期 -->
<!--<div class="selectWeek-dialog">-->
<!--    <div class="w-con active">-->
<!--        <div class="w-head">-->
<!--            <h3>点击选择学期</h3>-->
<!--        </div>-->
<!--        <div class="w-box">-->
<!--            <ul id="xnxqdiv">-->
<!--                <li class="cur">1</li>-->
<!--            </ul>-->
<!--        </div>-->

<!--    </div>-->
<!--</div>-->
<!-- 详情 -->
<div class="dialog-wrap dialog-mes">
    <div class="dialog">
        <div class="dialog-con">
            <h1>22数据库</h1>
            <div class="item">
                <h3>所属选课计划：</h3><span>2023-2024-1选课</span>
            </div>
            <div class="item">
                <h3>开设课程 ：</h3><span>数据数据库数据库数据库数据库</span>
            </div>
            <div class="item">
                <h3>开课年级 ：</h3><span>2022级</span>
            </div>
            <div class="item">
                <h3>开课院系 ：</h3><span>计算机学院</span>
            </div>
            <div class="item">
                <h3>开课专业 ：</h3><span>计算机专业</span>
            </div>
            <div class="item">
                <h3>课程类型 ：</h3><span>专业选修课</span>
            </div>
            <div class="item">
                <h3>上课时间 ：</h3><span>1-20周，周一，第七节</span>
            </div>
            <div class="item">
                <h3>上课地点 ：</h3><span>南校区</span>
            </div>
            <div class="item">
                <h3>授课教师 ：</h3><span>李四</span>
            </div>
            <div class="item">
                <h3>学分 ：</h3><span>2分</span>
            </div>
            <div class="item">
                <h3>课程容量：</h3><span>50</span>
            </div>
            <div class="item">
                <h3>性别 ：</h3><span>男</span>
            </div>
            <div class="item">
                <h3>编组 ：</h3><span>第5梯队</span>
            </div>
            <div class="item">
                <h3>课程简介：</h3>
                <div class="wrapper">
                    <input id="exp1" class="exp" type="checkbox">
                    <div class="text" id="introText">
                        <label class="btn" for="exp1" style="display: none;"></label>
                        浮动元素是如何定位的
                        两个向左浮动，一个向右浮动。浮动元素是如何定位的
                        两个向左浮动，一个向右浮动。要注意到第要注意到第二个向左浮动的这样浮动，它们会至下一行。
                    </div>
                </div>
            </div>
        </div>
        <div class="dialog-btn">
            <button class="cancel">取消</button>
            <button class="sure" id="exitCourse">确定</button>
        </div>
    </div>
</div>
<!-- 筛选 -->
<div class="list-search-wrap">
    <div class="list-wrap">
        <div class="list" id="searchItem">
<!--            <div class="item">-->
<!--                <h1>选课计划</h1>-->
<!--                <div class="sel-con" data-id="selectPlan" id="jh">请选择</div>-->
<!--            </div>-->
            <div class="item">
                <h1>开课年级</h1>
                <div class="sel-con" data-id="selectGrade" id="xxk_kknj">请选择</div>
            </div>
            <div class="item">
                <h1>开课院系</h1>
                <div class="sel-con" data-id="selectDepart" id="xxk_kkyx">请选择</div>
            </div>
            <div class="item">
                <h1>开课专业</h1>
                <div class="sel-con" data-id="selectMajor" id="xxk_kkzy">请选择</div>
            </div>
            <div class="item">
                <h1>开设课程</h1>
                <div class="sel-con" data-id="selectCourse" id="kskc">请选择</div>
            </div>
            <div class="item">
                <h1>开课校区</h1>
                <div class="sel-con" data-id="selectType" id="xxk_kkxiaoqu">请选择</div>
            </div>
        </div>
        <div class="recall-bottom">
            <button class="refresh">重置</button>
            <button class="search">查询</button>
        </div>
    </div>

</div>
<!-- 选择学期 2025.3.25 -->
<div class="selectWeek-dialog dialog-box">
    <div class="w-con active">
        <div class="w-head">
            <h3>点击选择学期</h3>
        </div>
        <div class="w-box">
            <ul id="xnxqdiv">
            </ul>
        </div>

    </div>
</div>

<!-- 选择选课计划 2025.3.25 -->
<div class="selectCoursePlan-dialog dialog-box" id="selectPlan">
    <div class="w-con active">
        <div class="w-head">
            <h3>点击选择选课计划</h3>
        </div>
        <div class="search-course">
            <input type="search" placeholder="搜索">
        </div>
        <div class="w-box" id="jhdiv">
            <ul>
            </ul>
        </div>

    </div>
</div>
<!-- 选课计划 -->
<!--<div class="choosedepartment-dialog" id="selectPlan">-->
<!--    <div class="w-con">-->
<!--        <div class="w-head">-->
<!--            <div class="cancle">取消</div>-->
<!--            <div class="name">选课计划</div>-->
<!--            <div class="btns">-->
<!--                <span class="save">保存</span>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="w-box">-->
<!--            <div class="search-inputs">-->
<!--                <div class="s-con">-->
<!--                    <img th:src="${_CPR_}+'/elective/images/search1.png'" alt="">-->
<!--                    <input type="text" placeholder="请输入" class="mSearch">-->
<!--                    <span class="m-close" style="display: none;"></span>-->
<!--                </div>-->
<!--            </div>-->
<!--            <ul>-->
<!--                <li class="all">全选</li>-->
<!--            </ul>-->
<!--        </div>-->

<!--    </div>-->
<!--</div>-->
<!-- 开课年级 -->
<div class="choosedepartment-dialog" id="selectGrade">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">开课年级</div>
            <div class="btns">
                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img th:src="${_CPR_}+'/elective/images/search1.png'" alt="">
                    <input type="text" placeholder="请输入" class="mSearch">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
                <li class="all">全选</li>
            </ul>
        </div>

    </div>
</div>
<!-- 开课院系  -->
<div class="choosedepartment-dialog" id="selectDepart">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">开课院系</div>
            <div class="btns">

                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="">
                    <input type="text" placeholder="请输入" class="mSearch">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
                <li class="all">全部</li>
            </ul>
        </div>

    </div>
</div>
<!-- 开课专业 -->
<div class="choosedepartment-dialog" id="selectMajor">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">开课专业</div>
            <div class="btns">

                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="">
                    <input type="text" placeholder="请输入" class="mSearch">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
                <li class="all">全部</li>
            </ul>
        </div>

    </div>
</div>
<!-- 开设课程 -->
<div class="choosedepartment-dialog" id="selectCourse">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">开设课程</div>
            <div class="btns">

                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="">
                    <input type="text" placeholder="请输入" class="mSearch">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
                <li class="all">全部</li>
            </ul>
        </div>

    </div>
</div>
<!-- 选择校区 -->
<div class="choosedepartment-dialog" id="selectType">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">开课校区</div>
            <div class="btns">

                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="">
                    <input type="text" placeholder="请输入" class="mSearch">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
                <li class="all">全部</li>
            </ul>
        </div>

    </div>
</div>
<div id="captcha"></div>
</body>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]] || '';
    var fid = [[${fid}]];
    let r = [[${r}]];
    let academicYear = [[${academicYear}]];
    let dates = [[${dates}]];
    let fidEnc = [[${fidEnc}]];
    let allAcademicYear = [[${allAcademicYear}]];
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/CXJSBridge.js"></script>
<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/app.utils.js"></script>
<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/tools.js"></script>
<script type='text/javascript' src='https://captcha.chaoxing.com/load.min.js?t='></script>
<script th:src="${_CPR_}+'/js/base.js'"></script>
<script th:src="${_CPR_}+'/js/my.util.js'"></script>
<script th:src="${_CPR_}+'/elective/js/headHeight.js'"></script>
<script th:inline="javascript">
    function setHeight(){
        var mainH = $(window).height() - $(".main").offset().top;
        $(".main").height(mainH);
    }
    setTimeout(function () {
        setHeight()
    }, 500);
    layui.use(['layer'], function () {
        var layer = layui.layer;
    })
    jsBridge.postNotification('CLIENT_WEB_BOUNCES', {"forbiddenFlag": 1});
    jsBridge.postNotification('CLIENT_WEB_BOUNCES', {
        "forbiddenFlag": 1
    });
    var xxk_kknj = '';
    var xxk_kkyx = '';
    var xxk_kkzy = '';
    var kskc = '';
    var xxk_kkxiaoqu = '';
    var xxk_kclx = '';
    var jh = "";
    var jc = [[${jc}]];
    var zc = 1;
    var stuOptional;
    var stuSelectCourseCid = [];
    var stuAppluSelectCourseCid = [];
    var finalzx = false;
    var opcid;
    var finaltaskBdid = 0;
    var finaltaskcid = 0;
    var opCourse = 2;
    var captchaIns = null;
    var listtab = 1;
    var yzm = false;
    var jcArr = [];
    var xnxqh = "";
    var bzMap = new Map();
    if (academicYear) {
        xnxqh = academicYear.xnxq_xnxqh;
        if (academicYear.xnxq_xnxqmc) {
            $(".con-select ul li.course").find("span").text(academicYear.xnxq_xnxqmc);
        } else {
            $(".con-select ul li.course").find("span").text(academicYear.xnxq_xnxqh);
        }
    }
    if (allAcademicYear) {
        var html = "";
        for (let i = 0; i < allAcademicYear.length; i++) {
            if (academicYear && academicYear.xnxq_xnxqmc && academicYear.xnxq_xnxqmc == allAcademicYear[i].xnxq_xnxqmc) {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='cur'>" + allAcademicYear[i].xnxq_xnxqmc + "</li>";
            } else if (academicYear && academicYear.xnxq_xnxqh && academicYear.xnxq_xnxqh == allAcademicYear[i].xnxq_xnxqh) {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='cur'>" + allAcademicYear[i].xnxq_xnxqh + "</li>";
            } else if (allAcademicYear[i].xnxq_xnxqmc) {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class=''>" + allAcademicYear[i].xnxq_xnxqmc + "</li>";
            } else {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class=''>" + allAcademicYear[i].xnxq_xnxqh + "</li>";
            }
        }
        $("#xnxqdiv").html(html);
    }
    $(".head-back").on('click', function () {
        AppUtils.closeView();
    })
    U.ajax({
        type: 'post',
        url: "../elective/task/getTask",
        data: {fid: fid,xnxqh:xnxqh},
        dataType: 'json',
        async: false,
        success: function (result) {
            var sxhtml = "";//筛选计划
            var newjh = [[${jh}]];
            for (let i = 0; i < result.data.length; i++) {
                sxhtml +="<li>";
                sxhtml +="<div class='name'>" + result.data[i].xkjhbJhmc + "</div>";
                if ('未结束'==result.data[i].xkjhbXkjhzt){
                    sxhtml +="<div class='state not-finished'>未结束</div>";
                }else {
                    sxhtml +="<div class='state finished'>已结束</div>";
                }
                if (newjh ==''){
                    newjh = result.data[i].xkjhbJhmc;
                }
                sxhtml +="</li>";
            }
            if (newjh!=''){
                $(".con-select ul li.date").find("span").text(newjh);
            }
            $("#selectPlan .w-box ul").append(sxhtml);
        }
    })
    initCXCaptcha({
        captchaId: 'dRgtnuKwnxSvXXOl0btdNZqATWH8Kmjv',
        element: '#captcha',
        mode: 'popup',
        // type: 'iconclick',
        onVerify: function (err, data) {
            /**
             * 第一个参数是err（Error的实例），验证失败才有err对象
             * 第二个参数是data对象，验证成功后的相关信息，data数据结构为key-value，如下：
             * {
             * validate: 'xxxxx' // 二次验证信息
             * }
             **/
            if (err) return; // 当验证失败时，内部会自动refresh方法，无需手动再调用一次
            if (opCourse == 1) {
                electiveCourses(data.validate)
            } else {
                dropCourses(data.validate);
            }
            captchaIns.refresh()
        }
    }, function onload(instance) {
        captchaIns = instance;
    }, function onerror(err) {
    });
    if (r) {
        if (r.code !== 200) {
            layer.msg(r.msg);
        } else {
            for (let i = 0; i < r.data.lessons.length; i++) {
                jcArr[r.data.lessons[i][0].actualNum] = r.data.lessons[i][0].lessonNumName;
            }
            refreshData();
        }
    } else {
        layer.msg("课表数据异常");
    }
    $("#xxkmc").keyup(function (e) {
        if (e.keyCode == 13) {
            optional_courses();
        }
    })

    function refreshData() {
        optionalCourse()
        optional_courses()
    }

    function optionalCourse() {
        U.ajax({
            type: 'post',
            url: "../elective/task/optional/course",
            data: {fid: fid, xnxqh: xnxqh},
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    return false;
                }
                if (!result.data) {
                    $(".dialog-plan").hide();
                    return false;
                }
                stuSelectCourseCid = [];
                stuAppluSelectCourseCid = [];
                stuOptional = result.data;
                if (stuOptional) {
                    for (let z = 0; z < stuOptional.length; z++) {
                        var selected_course = $.parseJSON(stuOptional[z].selectedCourse);
                        for (let y = 0; y < selected_course.length; y++) {
                            if (selected_course[y].zx) {
                                stuAppluSelectCourseCid.push(selected_course[y].cid);
                            } else {
                                stuSelectCourseCid.push(selected_course[y].cid)
                            }
                        }
                    }

                }
            }
        })
    }

    function yzmdropCourses(taskBdid, cid) {
        opCourse = 2;
        finaltaskBdid = taskBdid;
        finaltaskcid = cid;
        if (captchaIns && yzm) {
            captchaIns.popUp();
        } else {
            dropCourses("");
        }
    }

    function yzmelectiveCourses(taskBdid, cid, zx) {
        opCourse = 1;
        finaltaskBdid = taskBdid;
        finaltaskcid = cid;
        finalzx = zx;
        if (captchaIns && yzm) {
            captchaIns.popUp();
        } else {
            electiveCourses("");
        }
    }

    // 查看已选课程
/*    $('#courseSwitch').click(function () {
        $(this).toggleClass('active')
        optional_courses();
    })*/
    $(".handle .btns .btn").click(function () {
        $(this).toggleClass('cur')
        // $(this).addClass("cur").siblings().removeClass("cur");
        optional_courses();
    })

    function optional_courses() {
        var xxk_kknj1 = $("#xxk_kknj").text();
        if (xxk_kknj1 == '请选择') {
            xxk_kknj1 = "";
        }
        var xxk_kkyx1 = $("#xxk_kkyx").text();
        if (xxk_kkyx1 == '请选择') {
            xxk_kkyx1 = "";
        }
        var xxk_kkzy1 = $("#xxk_kkzy").text();
        if (xxk_kkzy1 == '请选择') {
            xxk_kkzy1 = "";
        }
        var kskc1 = $("#kskc").text();
        if (kskc1 == '请选择') {
            kskc1 = "";
        }
        var xxk_kkxiaoqu1 = $("#xxk_kkxiaoqu").text();
        if (xxk_kkxiaoqu1 == '请选择') {
            xxk_kkxiaoqu1 = "";
        }
        var xxk_kclx1 = '';
        var xxk_xxkmc1 = $("#xxkmc").val();
        var jc1 = jc;
        var jh1 = $(".con-select ul li.date").find("span").text();
        // if (jh1 == '请选择') {
        //     jh1 = "";
        // }
        U.ajax({
            type: 'post',
            url: "../elective/task/optional/courses/list",
            data: {
                fid: fid,
                xxk_kknj: xxk_kknj1,
                xxk_kkyx: xxk_kkyx1,
                xxk_kkzy: xxk_kkzy1,
                kskc: kskc1,
                xxk_kkxiaoqu: xxk_kkxiaoqu1,
                xxk_kclx: xxk_kclx1,
                xxk_xxkmc: xxk_xxkmc1,
                jc: jc1,
                jh: jh1,
                xnxqh: xnxqh,
                pageSize: 1000,
                select: $(".handle .btns .select").hasClass('cur'),
                bzsearch: $(".handle .btns .group").hasClass('cur'),
            },
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    layer.msg(result.msg)
                    return false;
                }
                if (!result.data) {
                    return false;
                }
                var html = "";
                for (let i = 0; i < result.data.length; i++) {
                    let d = result.data[i];
                    var bzid = "";
                    if (d.zzxk == 1 && d.bzid) {
                        bzid = d.bzid;
                    }
                    if (bzid != '') {
                        let bzlist = bzMap.get(bzid);
                        if (bzlist && bzlist.length > 0) {
                            var push = true;
                            for (let j = 0; j < bzlist.length; j++) {
                                if (bzlist[j].id == d.id) {
                                    push = false;
                                }
                            }
                            if (push) {
                                bzlist.push(d);
                            }
                        } else {
                            var bznewList = [];
                            bznewList.push(d);
                            bzMap.set(bzid, bznewList);
                        }
                    }
                }
                for (let i = 0; i < result.data.length; i++) {
                    let d = result.data[i];
                    if (d.zzxk == 1 && d.bzid && bzMap.get(d.bzid) != undefined) {
                        continue
                    }
                    if (stuSelectCourseCid.indexOf(d.id) != -1) {
                        html += "<div class='z-course z-course-sel' taskBdid = '" + d.taskBdid + "' id='" + d.id + "'>";
                    } else if (stuAppluSelectCourseCid.indexOf(d.id) != -1) {
                        html += "<div class='z-course z-course-sel' taskBdid = '" + d.taskBdid + "' id='" + d.id + "'>";
                    } else if(d.xxkXxkrl != -1 && (d.selectNum >= d.xxkXxkrl)){
                        html += "<div class='z-course z-course-full' taskBdid = '" + d.taskBdid + "' id='" + d.id + "'>";
                    }else {
                        html += "<div class='z-course' taskBdid = '" + d.taskBdid + "' id='" + d.id + "'>";
                    }
                    html += "<div class='z-course-title'><h1>" + d.xxkXxkmc + "</h1><span class='btn-detail'>详情</span></div>";
                    html += "<div class='course-mes'>";
                    html += " <dl><dt>选课计划：</dt><dd>" + d.taskName + "</dd></dl>";
                    if (d.xxkXxkrl == -1) {
                        html += "<dl><dt>课程余量：</dt><dd><span class='num'>" + d.selectNum + "</span><i>/</i>无上限</dd></dl>";
                    } else {
                        html += "<dl><dt>课程余量：</dt><dd><span class='num'>" + d.selectNum + "</span><i>/</i>" + d.xxkXxkrl + "</dd></dl>";
                    }
                    html += " <dl> <dt>上课时间：</dt><dd>" + getsksjStr(d.xxkSksj) + "</dd></dl>";
                    html += "<dl><dt>课程类型：</dt><dd>" + d.xxkKclx + "</dd></dl>";
                    html += "<dl><dt>学&nbsp;&nbsp;&nbsp;&nbsp;分：</dt><dd>" + d.xxkXxkxf + "分</dd></dl>";
                    html += " <dl><dt>上课地点：</dt><dd>" + d.xxkSkdd + "</dd></dl>";
                    html += "<dl><dt>授课教师：</dt><dd>" + d.xxkSkjs + "</dd></dl>";
                    html += "</div>";
                    html += "<div class='z-btn'>";
                    // html += "<span class='btn btn-detail'>详情</span>";
                    if (d.source == 4) {
                        if (stuSelectCourseCid.indexOf(d.id) != -1) {
                            html += "<span class='btn btn-exit'>退课</span>";
                        } else if (stuAppluSelectCourseCid.indexOf(d.id) != -1) {
                            html += "<span class='btn btn-full'>审核中</span>";
                        } else {
                            if (d.zx ==1){
                                html += "<span class='btn btn-follow'>跟班上课</span>";
                                html += "<span class='btn btn-Self'>自修</span>";
                            }else {
                                html += "<span class='btn btn-follow'  style='width: 100%'>跟班上课</span>";
                            }
                        }
                    } else {
                        html += "<span class='btn btn-exit'>退课</span>";
                        if (d.xxkXxkrl != -1 && (d.selectNum >= d.xxkXxkrl)) {
                            html += "<span class='btn btn-full'>已满</span>";
                        } else {
                            html += "<span class='btn btn-sel'>选课</span>";
                        }
                    }
                    html += "</div>";
                    html += "</div>";
                }
                for (const [key, value] of bzMap) {
                    let d = value[0];
                    var bzkcmc =[];
                    value.forEach(item => {
                        bzkcmc.push(item.xxkXxkmc);
                    });
                    if (stuSelectCourseCid.indexOf(d.id) != -1) {
                        html += "<div class='z-course z-course-sel' taskBdid = '" + d.taskBdid + "' id='" + d.id + "'>";
                    } else {
                        html += "<div class='z-course' taskBdid = '" + d.taskBdid + "' id='" + d.id + "'>";
                    }
                    html += "<div class='z-course-title'><h1>" + d.bzmc + "</h1><span class='btn-detail'>详情</span></div>";
                    html += "<div class='course-mes'>";
                    html += " <dl><dt>课程名称：</dt><dd>" + bzkcmc.join(",") + "</dd></dl>";
                    html += "<dl><dt>课程数：</dt><dd>" + value.length + "</dd></dl>";
                    html += "</div>";
                    html += "<div class='z-btn'>";
                    // html += "<span class='btn btn-detail'>详情</span>";
                    html += "<span class='btn btn-exit'>退课</span>";
                    html += "<span class='btn btn-sel'>选课</span>";
                    html += "</div>";
                    html += "</div>";
                }
                $("#zList").html(html);
            }
        })
    }

    function electiveCourses(validate) {
        var succ = true;
        U.ajax({
            type: 'post',
            url: "../elective/task/elective/courses",
            data: {
                fid: fid,
                taskBdid: finaltaskBdid,
                cid: finaltaskcid,
                zx: finalzx,
                validate: validate,
                captchaIns: !!captchaIns
            },
            dataType: 'json',
            async: false,
            success: function (result) {
                yzm = result.data;
                if (result.code == 119) {
                    var param = {
                        'data': [
                            {
                                'alias': 'xkzxsq_skls',
                                'val': result.supplementary.xkzxsq_skls,
                                'compt': 'contact'
                            }
                            , {
                                'alias': 'xkzxsq_xm',
                                'val': ['' + result.supplementary.xkzxsq_xm + ''],
                                'compt': 'editinput'
                            }
                            , {
                                'alias': 'xkzxsq_xh',
                                'val': ['' + result.supplementary.xkzxsq_xh + ''],
                                'compt': 'editinput'
                            }
                            , {
                                'alias': 'xkzxsq_uid',
                                'val': ['' + result.supplementary.xkzxsq_uid + ''],
                                'compt': 'editinput'
                            }
                            , {
                                'alias': 'xkzxsq_ssnj',
                                'val': ['' + result.supplementary.xkzxsq_ssnj + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_ssyx',
                                'val': ['' + result.supplementary.xkzxsq_ssyx + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_sszy',
                                'val': ['' + result.supplementary.xkzxsq_sszy + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_szbj',
                                'val': ['' + result.supplementary.xkzxsq_szbj + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_szxq',
                                'val': ['' + result.supplementary.xkzxsq_szxq + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_ssxkjh',
                                'val': ['' + result.supplementary.xkzxsq_ssxkjh + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_jxbmc',
                                'val': ['' + result.supplementary.xkzxsq_jxbmc + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_jxbbh',
                                'val': ['' + result.supplementary.xkzxsq_jxbbh + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_kkbm',
                                'val': ['' + result.supplementary.xkzxsq_kkbm + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_xshid',
                                'val': ['' + result.supplementary.xkzxsq_xshid + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_xxkhid',
                                'val': ['' + result.supplementary.xkzxsq_xxkhid + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_xkjhhid',
                                'val': ['' + result.supplementary.xkzxsq_xkjhhid + ''],
                                'compt': 'editinput'
                            }, {
                                'alias': 'xkzxsq_id',
                                'val': ['' + result.supplementary.xkzxsq_id + ''],
                                'compt': 'editinput'
                            }
                        ]
                    }
                    openA("", result.supplementary.url + "&precast=" + encodeURIComponent(JSON.stringify(param)))
                    return
                } else if (result.code !== 200) {
                    layer.msg(result.msg)
                    succ = false;
                    return
                }
                jsBridge.postNotification('CLIENT_REFRESH_STATUS', {'status': '1'});
                if (jc != '') {
                    layer.msg("报名成功")
                    setTimeout(function () {
                        AppUtils.closeView();
                    }, 500);
                    return;
                }
                refreshData()
                layer.msg("报名成功")
            }
        })
        return succ;
    }

    function dropCourses(validate) {
        var succ = true;
        U.ajax({
            type: 'post',
            url: "../elective/task/drop/courses",
            data: {fid: fid, taskBdid: finaltaskBdid, cid: finaltaskcid, validate: validate, captchaIns: !!captchaIns},
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    if ('不在计划时间范围内报名不允许退课' == result.msg) {
                        //姓名 tksqb_xm
                        //学号 tksqb_xh  0009
                        //uid tksqb_uid
                        //所属退课计划 13
                        //退选选修课名称 tksqb_txxxkmc
                        var param = {
                            'data': [
                                {
                                    'alias': 'tksqb_xm',
                                    'val': ['' + result.data.tksqb_xm + ''],
                                    'compt': 'editinput'
                                }
                                , {
                                    'alias': 'tksqb_xh',
                                    'val': ['' + result.data.tksqb_xh + ''],
                                    'compt': 'editinput'
                                }
                                , {
                                    'alias': 'tksqb_uid',
                                    'val': ['' + result.data.tksqb_uid + ''],
                                    'compt': 'editinput'
                                }
                                , {'alias': '13', 'val': ['' + result.data.taskname + ''], 'compt': 'editinput'}
                                , {
                                    'alias': 'tksqb_txxxkmc',
                                    'val': ['' + result.data.tksqb_txxxkmc + ''],
                                    'compt': 'editinput'
                                }
                            ]
                        }
                        // 创建表单
                        var temp_form = document.createElement('form')
                        // 填写表单数据
                        temp_form.action = result.data.url;
                        temp_form.target = '_blank'
                        temp_form.method = 'post'
                        temp_form.style.display = 'none'
                        // 添加参数
                        var opt = document.createElement('textarea')
                        opt.name = 'precast'
                        opt.value = JSON.stringify(param)
                        temp_form.appendChild(opt)
                        document.body.appendChild(temp_form)
                        // 提交数据
                        // temp_form.submit()
                        openA("", result.data.url + "&precast=" + encodeURIComponent(JSON.stringify(param)))
                        return
                    }
                    yzm = result.data;
                    layer.msg(result.msg)
                    succ = false;
                    return
                }
                layer.msg("退课成功")
                yzm = result.data;
                jsBridge.postNotification('CLIENT_REFRESH_STATUS', {'status': '1'});
                refreshData();
            }
        })
        return succ;
    }

    //选择学期
    // $(".selectWeek").click(function () {
    //     $(".selectWeek-dialog").show();
    // });
    //学期切换
    // $(".selectWeek-dialog").on("click", ".w-con .w-box ul li", function () {
    //     $(this).addClass("cur").siblings().removeClass("cur");
    //     $(".selectWeek-dialog").hide();
    //     // $(".selectWeek-dialog .w-con").removeClass("active");
    //     let texts = $(this).text();
    //     $(".tophead .centerHead .selectBox .week").text(texts);
    //     window.location.href = "/elective/task/mobileListV3/index?fidEnc=" + fidEnc + "&xnxqh=" + $(this).attr("data-id");
    // });

    //2025.3.25

    //选择学期
    $(".con-select ul li.course").click(function () {
        $(this).addClass("active").siblings().removeClass("active");
        $(".selectWeek-dialog").show();
    })

    $(".selectWeek-dialog").on("click", ".w-con .w-box ul li", function () {
        $(this).addClass("cur").siblings().removeClass("cur");
        let text = $(this).text();
        $(".con-select ul li.course").find("span").text(text);
        $(".con-select ul li.course").removeClass("active");
        window.location.href = "/elective/task/mobileListV3/index?fidEnc=" + fidEnc + "&xnxqh=" + $(this).attr("data-id");
        $(".selectWeek-dialog").hide();
    })


    //选择选课
    $(".con-select ul li.date").click(function () {
        $(this).addClass("active").siblings().removeClass("active");
        $(".selectCoursePlan-dialog").show();
    })

    $(".selectCoursePlan-dialog").on("click", " .w-con .w-box ul li", function () {
        $(this).addClass("cur").siblings().removeClass("cur");
        let text = $(this).find(".name").text();
        $(".con-select ul li.date").find("span").text(text);
        $(".con-select ul li.date").removeClass("active");
        $(".selectCoursePlan-dialog").hide();
        optional_courses();
    })

    $(".dialog-box").on('click', function(event) {
        console.log($(event.target))
        if (!$(event.target).closest('.w-con').length) {
            // 如果点击的元素不是弹窗或其子元素，则隐藏弹窗
            $(".con-select ul li.course").removeClass("active");
            $(".con-select ul li.date").removeClass("active");
            $('.dialog-box').hide();
        }

    });
    $(".selectCoursePlan-dialog .w-con .search-course input").on("keyup",function(event){
        if (event.keyCode == 13) {
            let val=$(this).val();
            performSearch(val);
        }
        return false;
    })

    function performSearch(val){
        $(".selectCoursePlan-dialog .w-con .w-box ul li").each(function(){
            if($(this).text().indexOf(val)==-1){
                $(this).hide();
            }else{
                $(this).show();
            }
        })
    }

    //2025.3.25 end

    function _jsBridgeReady() {
        jsBridge.postNotification('CLIENT_TOOLBAR_TYPE', {'toolbarType': 0});
    }

    // 切换展示形式
    $("#menu").click(function (e) {
        $(this).next().show();
        e.stopPropagation();
    });
    $("#menuList li").click(function () {
        var index = $(this).index();
        $("#menuList").hide();
        if (index == 0) {
            AppUtils.closeView();
            // window.location.href = "/elective/task/mobileV3/index?fidEnc=" + fidEnc + "&xnxqh=" + xnxqh;
        }
    });
    /* ***************** 选课 **************************** */
    // 选课
    $(".main").on('click', '.z-course .btn ', function () {
        if ($(this).hasClass('btn-sel') || $(this).hasClass('btn-follow')) {
            // 选课
            yzmelectiveCourses($(this).parents(".z-course").attr("taskBdid"), $(this).parents(".z-course").attr("id"), false);
        } else if ($(this).hasClass('btn-Self')) {
            // 选课
            yzmelectiveCourses($(this).parents(".z-course").attr("taskBdid"), $(this).parents(".z-course").attr("id"), true);
        } else if ($(this).hasClass('btn-exit')) {
            // 退课
            yzmdropCourses($(this).parents(".z-course").attr("taskBdid"), $(this).parents(".z-course").attr("id"));
        } else if ($(this).hasClass('btn-detail')) {
            courseDetail($(this).parents(".z-course").attr("id"))
        }

    })

    $(".main").on('click', '.z-course .z-course-title .btn-detail', function () {
        courseDetail($(this).parents(".z-course").attr("id"))
    })


    function courseDetail(cid) {
        opcid = cid;
        for (const [key, value] of bzMap) {
            for (let i = 0; i < value.length; i++) {
                if (value[i].id == cid){
                    var html = "";
                    var dd = value[i];
                    html += "<h1 id='" + dd.id + "'  taskBdid = '" + dd.taskBdid + "'>" + dd.bzmc + "</h1>";
                    html += "<div class='item'><h3>所属选课计划:</h3><span>" + dd.taskName + "</span></div>";
                    for (let j = 0; j < value.length; j++) {
                        var d  = value[j];
                        html += "<div class='item'><h3>课程 "+(j+1)+":</h3><span></span></div>";
                        html += "<div class='item'><h3>开设课程:</h3><span>" + d.xxkXxkmc + "</span></div>";
                        html += "<div class='item'><h3>开课年级:</h3><span>" + d.xxkKknj + "</span></div>";
                        html += "<div class='item'><h3>开课院系:</h3><span>" + d.xxkKkyx + "</span></div>";
                        html += "<div class='item'><h3>开课专业:</h3><span>" + d.xxkKkzy + "</span></div>";
                        html += "<div class='item'><h3>课程类型:</h3><span>" + d.xxkKclx + "</span></div>";
                        html += "<div class='item'><h3>上课时间:</h3><span>" + getsksjStr(d.xxkSksj) + "</span></div>";
                        html += "<div class='item'><h3>上课地点:</h3><span>" + d.xxkSkdd + "</span></div>";
                        html += "<div class='item'><h3>授课教师:</h3><span>" + d.xxkSkjs + "</span></div>";
                        html += "<div class='item'><h3>学分:</h3><span>" + d.xxkXxkxf + "分</span></div>";
                        if (d.xxkXxkrl == -1) {
                            html += "<div class='item'><h3>课程容量:</h3><span>无上限</span></div>";
                        } else {
                            html += "<div class='item'><h3>课程容量:</h3><span>" + d.xxkXxkrl + "</span></div>";
                        }
                        html += "<div class='item'><h3>性别:</h3><span>" + d.xxkKxxsxb + "</span></div>";

                        $(".dialog-mes .dialog .dialog-con").html(html);
                        $(".dialog-mes").show();
                    }
                    return false;
                }
            }
        }
        U.ajax({
            type: 'post',
            url: "../elective/task/course/detail",
            data: {fid: fid, cid: cid},
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    layer.msg(result.msg)
                    return false;
                }
                var html = "";
                var d = result.data;

                html += "<h1 id='" + d.id + "'  taskBdid = '" + d.taskBdid + "'>" + d.xxkXxkmc + "</h1>";
                html += "<div class='item'><h3>所属选课计划 ：</h3><span>" + d.xxkmc + "</span></div>";
                html += "<div class='item'><h3>开设课程 ：</h3><span>" + d.xxkXxkmc + "</span></div>";
                html += "<div class='item'><h3>开课年级 ：</h3><span>" + d.xxkKknj + "</span></div>";
                html += "<div class='item'><h3>开课院系 ：</h3><span>" + d.xxkKkyx + "</span></div>";
                html += "<div class='item'><h3>开课专业 ：</h3><span>" + d.xxkKkzy + "</span></div>";
                html += "<div class='item'><h3>课程类型 ：</h3><span>" + d.xxkKclx + "</span></div>";
                html += "<div class='item'><h3>上课时间 ：</h3><span>" + getsksjStr(d.xxkSksj) + "</span></div>";
                html += "<div class='item'><h3>上课地点 ：</h3><span>" + d.xxkSkdd + "</span></div>";
                html += "<div class='item'><h3>授课教师 ：</h3><span>" + d.xxkSkjs + "</span></div>";
                html += "<div class='item'><h3>学分 ：</h3><span>" + d.xxkXxkxf + "分</span></div>";
                if (d.xxkXxkrl == -1) {
                    html += "<div class='item'><h3>课程容量：</h3><span>无上限</span></div>";
                } else {
                    html += "<div class='item'><h3>课程容量 ：</h3><span>" + d.xxkXxkrl + "</span></div>";
                }
                html += "<div class='item'><h3>性别 ：</h3><span>" + d.xxkKxxsxb + "</span></div>";
                html += "<div class='item'><h3>编组 ：</h3><span>" + d.courseGroupName + "</span></div>";


                if (d.kcjs != '') {
                    html += "<div class='item'>";
                    html += "<h3>课程简介：</h3>";
                    html += "<div class='wrapper'>";
                    html += "<div class='ellipse'>...</div>";
                    html += "<div class='text ellText' id='introText'>";
                    html += "<label class='btn' for='exp1' style=''></label>";
                    html += d.kcjs;
                    html += "</div>";
                    html += "</div>";
                    html += "</div>";
                }
                $(".dialog-mes .dialog .dialog-con").html(html);
                $(".dialog-mes").show();

                if (d.kcjs != '') {
                    // 详情展开收起
                    $('#introText').removeClass('ellText')
                    $('#introText').find('.btn').hide()
                    $('.wrapper .btn').removeClass('collapse')
                    $('.wrapper .ellipse').hide()
                    var introH = $('#introText').height()
                    var lineH = parseFloat($('#introText').css('line-height'))
                    console.log(introH, lineH)
                    if (introH > lineH * 4) {
                        $('#introText').addClass('ellText')
                        $('#introText').find('.btn').show()
                        $('.wrapper .ellipse').show()
                    } else {
                        $('#introText').removeClass('ellText')
                        $('#introText').find('.btn').hide()
                        $('.wrapper .ellipse').hide()
                    }
                }
            }
        })
    }

    // 点击展开收起
    $(".dialog-wrap").on("click", ".wrapper .btn", function () {
        if ($(this).hasClass('collapse')) {
            $(this).removeClass('collapse')
            $(this).parent().addClass('ellText')
            $('.wrapper .ellipse').show()
        } else {
            $(this).addClass('collapse')
            $(this).parent().removeClass('ellText')
            $('.wrapper .ellipse').hide()
        }
    })

    U.ajax({
        type: "POST",
        url: "../elective/getNj",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#selectGrade .w-box ul").append(html);
        }
    });

    U.ajax({
        type: "POST",
        url: "../elective/getYX",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#selectDepart .w-box ul").append(html);
        }
    });

    U.ajax({
        type: "POST",
        url: "../elective/getZY",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#selectMajor .w-box ul").append(html);
        }
    });

    U.ajax({
        type: "POST",
        url: "../elective/getTaskKC",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#selectCourse .w-box ul").append(html);
        }
    });

    U.ajax({
        type: "POST",
        url: "../elective/getXQ",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#selectType .w-box ul").append(html);
        }
    });



    //
    /* ***************** 详情 **************************** */


    $(".dialog-mes .dialog-btn button").click(function () {
        $(".dialog-mes").hide();
    })
    /* ***************** 筛选 **************************** */
    // 筛选
    $("#searchBtn").click(function () {
        $(".list-search-wrap").show()
    })

    // 选择
    var selEle;
    $("#searchItem .sel-con").click(function () {
        selEle = $(this);
        var id = $(this).attr('data-id');
        var dialog = $("#" + id)
        dialog.show();
        dialog.find('.w-con').addClass("active");
    })

    // 检索
    $(".mSearch").on('input', function () {
        let val = $(this).val();
        var nextEle = $(this).parents('.search-inputs').next("ul");
        if (val != "") {
            nextEle.find("li").each(function (i, ele) {
                let txt = $(ele).text();
                if (txt.indexOf(val) >= 0) {
                    $(ele).show();
                } else {
                    $(ele).hide();
                }
            });
            nextEle.find("li.all").hide();
        } else {
            nextEle.find("li").show();
        }
    })
    // 选择
    $(".choosedepartment-dialog").on("click", ".w-con .w-box ul li", function () {
        $(this).toggleClass("cur");
        if ($(this).hasClass('all')) {
            // 全选
            if ($(this).hasClass('cur')) {
                $(this).nextAll().addClass('cur');
            } else {
                $(this).nextAll().removeClass('cur');
            }
        }
        let totalNus = $(this).parents(".choosedepartment-dialog").find(".w-con .w-box ul li").length - 1;
        let currentNus = $(this).parents(".choosedepartment-dialog").find(".w-con .w-box ul li.cur:not(.all)").length;
        if (totalNus == currentNus) {
            $(this).siblings('.all').addClass('cur');
        } else {
            $(this).siblings('.all').removeClass('cur');
        }
    })


    //取消
    $(".choosedepartment-dialog .w-con .w-head .cancle").click(function () {
        $(".choosedepartment-dialog").hide();
        $(".choosedepartment-dialog .w-con").removeClass("active");
    })

    //保存
    $(".choosedepartment-dialog .w-con .w-head .btns .save").click(function () {
        let cStr = [];
        let p = $(this).parents('.choosedepartment-dialog');
        var cur = p.find('li.cur');
        if (cur.length > 0) {
            if (p.find('li.all.cur').length > 0) {
                cStr.push('全部')
            } else {
                cur.each(function () {
                    cStr.push($(this).text())
                })
            }

            selEle.text(cStr.join(',')).addClass("color1");
        } else {
            selEle.text('请选择').removeClass('color1');
        }
        p.hide();
        $("#choosedepartment .w-con").removeClass("active");

    })

    //重置
    $(".recall-bottom .refresh").click(function () {
        $("#searchItem .sel-con").text('请选择').removeClass('color1');
        $('.choosedepartment-dialog li.cur').removeClass('cur');
    })
    //   查询
    $(".recall-bottom .search").click(function () {
        $(".list-search-wrap").hide();
        // 选择内容
        let params = {}
        $("#searchItem .sel-con").each(function () {
            var key = $(this).attr('data-id');
            var txt = $(this).text();
            params[key] = txt;
        })
        optional_courses();
    })

    //[{"xxk_skjc":"3","xxk_skxq":"2","xxk_skzc":"1-5,10"}]
    //获取上课时间
    function getsksjStr(sj) {
        let sjsjon = $.parseJSON(sj);
        var sksjstr = "";
        for (let i = 0; i < sjsjon.length; i++) {
            let split = sjsjon[i].xxk_skzc.split(",");
            for (let j = 0; j < split.length; j++) {
                sksjstr += split[j] + "周、";
            }
            sksjstr += "周" + getxq(sjsjon[i].xxk_skxq) + "、";
            sksjstr += getjc(sjsjon[i].xxk_skjc);
            sksjstr += ";";
        }
        return sksjstr;
    }

    function getjc(xxk_skjc) {
        var skxqstr = "";
        let split = xxk_skjc.split(",");
        for (let i = 0; i < split.length; i++) {
            if (split[i].indexOf("-") != -1) {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                let split1 = split[i].split("-");
                skxqstr += getjcStr(split1[0]) + "-" + getjcStr(split1[1]);
            } else {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                skxqstr += getjcStr(split[i]);
            }
        }
        return skxqstr;
    }

    function getxq(xxk_skxq) {
        var skxqstr = "";
        let split = xxk_skxq.split(",");
        for (let i = 0; i < split.length; i++) {
            if (split[i].indexOf("-") != -1) {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                let split1 = split[i].split("-");
                skxqstr += getxqStr(split1[0]) + "-" + getxqStr(split1[1]);
            } else {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                skxqstr += getxqStr(split[i]);
            }
        }
        return skxqstr;
        // for (let i = 0; i < xxk_skxq.length; i++) {
        //     let split = xxk_skxq[i].split(",");
        //     for (let j = 0; j < split.length; j++) {
        //         skxqstr += split[j] + "周、";
        //     }
        // }
    }

    function getxqStr(xq) {
        if (xq == 1) {
            return "一";
        } else if (xq == 2) {
            return "二";
        } else if (xq == 3) {
            return "三";
        } else if (xq == 4) {
            return "四";
        } else if (xq == 5) {
            return "五";
        } else if (xq == 6) {
            return "六";
        } else if (xq == 7) {
            return "日";
        } else {
            return "";
        }
    }

    function getjcStr(jc) {
        return jcArr[jc];
    }


</script>

</html>