.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#scheduleHead thead tr th {
  position: relative;
}
#scheduleHead thead tr th .weeks {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 0px 0.08rem 0.08rem 0px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
}
#scheduleHead thead tr th .weeks span {
  width: 0.34rem;
  margin: 0 auto;
  line-height: 0.22rem;
  font-size: 10px;
  color: #008AE6;
  text-align: center;
  transform: scale(0.9);
  -webkit-transform: scale(0.9);
}
#scheduleHead thead tr th .weeks span em {
  font-weight: 500;
  font-size: 14px;
  line-height: 0.39rem;
  text-align: center;
  color: #008AE6;
  font-style: normal;
}
#scheduleTable tbody .clickable.cur {
  border: 0.03rem solid #0099FF;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  border-radius: 0.08rem;
}
#scheduleTable tbody .clickable.active {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  background: #0094FF;
  box-shadow: 0px 0.02rem 0.17rem rgba(211, 211, 211, 0.5);
  border-radius: 0.08rem;
}
#scheduleTable tbody .clickable.disabled {
  background: #F0F0F0;
  border-radius: 0.08rem;
}
#scheduleTable tbody .disabled.cur {
  background: #F0F0F0;
  border-radius: 0.08rem;
}
#scheduleTable tbody .disabled.cur .courseName {
  color: #a3a3a3;
}
#scheduleTable tbody .disabled.cur .courseLoc {
  color: #a3a3a3;
}
#scheduleTable tbody .disabled.cur .teacher {
  color: #a3a3a3;
}
table .tddiv.borders {
  border: 0.03rem solid #0099FF;
  border-radius: 0.08rem;
}
.settingMask.shows {
  display: block;
}
.ios-select-widget-box header.iosselect-header a {
  color: #0099ff;
}
.layer:after {
  content: '至';
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0);
  -webkit-transform: translate(-50%, 0);
  top: 149px;
  width: 40px;
  height: 35px;
  color: rgba(36, 36, 37, 0.6);
  font-size: 0.24rem;
  line-height: 35px;
  text-align: center;
}
.ios-select-widget-box ul li.at {
  font-size: 15px;
}
.ios-select-widget-box ul li.side1 {
  font-size: 14px;
}
.ios-select-widget-box ul li.side2 {
  font-size: 13px;
}
.ios-select-widget-box ul li {
  font-size: 12px;
}
.ios-select-widget-box .cover-area1 {
  height: 35px;
  background-color: rgba(36, 36, 37, 0.02);
  border: none;
}
.ios-select-widget-box .cover-area2 {
  border: none;
}
.two-level-box .iosselect-box {
  padding-left: 0 !important;
}
.one-level-box .layer:after {
  display: none;
}
.two-level-box .one-level-contain:after {
  display: none;
}

table td{
  border-right:none;
}
table .col0{
  background: #FFFFFF;
}
table tr td:first-child{
  border-right:none;
  border-bottom: dashed 1px #F3F4F6;
}
#scheduleHead{
  padding:0 0.24rem;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
.table,.tables{
  padding:0 0.24rem;
}
table thead tr th{
  background-color: #fff;
}
#scheduleHead thead tr th .weeks span{
  color: #4E5969;
}
#scheduleHead thead tr th .weeks span em{
  color: #4E5969;
}
table th span:first-child{
  font-weight: normal;
}

table .tddiv.color1{
  background: #E1EBFF;
  border-radius: 0.08rem; 
  display: flex;
  display:-webkit-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  flex-wrap: wrap;
}
table td[rowspan] p{
  color: #86909C;
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;
  text-align: center;
  padding: 0rem 0.06rem;
}

.tophead .rightHead .search{
  float: left;
  width: 40px;
  height: 100%;
  background: url(../../images/basic/mobile-search-icon.png) center center/24px 24px no-repeat;

}

.tophead .leftHead .select-week{
   float: left;
}
.tophead .leftHead .select-week i{
  font-style: normal;
}
.tophead .leftHead .select-week span{
  font-size:16px;
  color: #FF9A2E;
  float:left;
  line-height: 45px;
}
.tophead .leftHead .select-week em{
  float:left;
  margin-left:6px;
  width: 7px;
  height:45px;
  background: url(../../images/basic/mobile-tringle-icon.png) no-repeat center;
  background-size:7px 4px;
}

.week-dialog{
  position: fixed;
  left:0;
  top:0;
  width: 100%;
  height:100%;
  z-index: 999;
  background: rgba(0, 0, 0, 0.2);
  display:none;
}
.week-dialog .w-con{
  position: absolute;
  left:0;
  bottom:0;
  right:0;
  transform: translateY(100%);
  -webkit-transform: translateY(100%);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.week-dialog .w-con.active{
  transform: translateY(0);
  -webkit-transform: translateY(0);
}
.week-dialog .w-con .w-head{
  background: #FFFFFF;
  border-bottom: 1px solid #F2F2F2;
  border-radius: 0.32rem 0.32rem 0 0;
  width: 100%;
  height:0.9rem;
}
.week-dialog .w-con .w-head h3{
  text-align: center;
  line-height: 0.9rem;
  color: #333333;
  font-weight: 400;
  font-size: 16px;
}
.week-dialog .w-con .w-box{
  width: 100%;
  height:auto;
  background-color: #fff;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding:15px 35px;
}
.week-dialog .w-con .w-box ul{
  overflow: hidden;
}
.week-dialog .w-con .w-box ul li{
  float:left;
  width: 13.33%;
  margin-right:4%;
  height: 0.68rem;
  background: #F1F3F6;
  border-radius: 0.08rem;
  margin-bottom:12px;
  font-size:13px;
  color: #4E5969;
  text-align: center;
  line-height: 0.68rem;
}
.week-dialog .w-con .w-box ul li.cur{
  background: #4D88FF;
  color:#fff;
}
.week-dialog .w-con .w-box ul li:nth-child(6n){
  margin-right:0;
}

.scroll-dialog{
  position: fixed;
  left:0;
  top:0;
  width: 100%;
  height:100%;
  z-index: 999;
  background: rgba(0, 0, 0, 0.2);
  /* display:none; */
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding-top:2rem;
  padding-bottom: 2rem;
 
  padding:2rem 0.24rem 0.24rem;
  display:none;

}
.scroll-dialog ul{
  width: 100%;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling:touch;
}
.scroll-dialog ul li{
  background: #FFFFFF;
  box-shadow: 0px 0px 0.08rem rgba(0, 0, 0, 0.1);
  border-radius:0.2rem;
  margin-bottom:0.24rem;
  padding:0.32rem 0.4rem 0.16rem;
}
.scroll-dialog ul li h3{
  font-size: 16px;
  line-height:0.45rem;
  color: #1D2129;
  margin-bottom: 0.16rem;
}
.scroll-dialog ul li .cons{
  overflow: hidden;
}
.scroll-dialog ul li .cons .lab{
  overflow: hidden;
  float:left;
  width: 50%;
  margin-bottom:0.16rem;
}
.scroll-dialog ul li .cons .lab .name{
  font-size:15px;
  line-height: 0.42rem;
  color: #4E5969;
  float:left;
}
.scroll-dialog ul li .cons .lab .texts{
  font-size: 15px;
  line-height: 0.42rem;
  color: #86909C;
  float:left;
  width: 54%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}




.listItem .mul-choice { display: block; width: 0.48rem; height: 0.48rem; margin-right: 0.2rem; background-image: url(../../images/basic/mobile-cheched.png); background-size: 0.48rem auto; background-position: 0 0; }
.listItem.active .mul-choice { background-position: 0 -0.48rem; }
.list .itemCon {
    flex: 1;
    overflow: hidden;
    min-height: 1rem;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
  }
  .hislocationList .listItem{
    min-height: 1rem;
  }
  .list .itemCon{
    min-height: 1rem;
  }
  .hislocationList .listItem .itemRight img{
    width: 0.28rem;
    height: 0.28rem;
  }
  

  .list .itemCon h1 {
    font-size: 0.32rem;
    color: #333333;
    position: relative;
    padding-left:0.32rem;
}
.hislocationList {
  padding-left: 0.3rem;
}
.list .itemRight {
  height: 100%;
  padding-right: 0.3rem;
}
.list .itemRight p.ycenter, .list .itemRight #selectLoc {
  word-break: break-all;
  max-width: 255px;
  height: 100%;
  font-size: 0.3rem;
  color: #999999;
  padding: 0.3rem 0;
  line-height: 0.4rem;
  justify-content: flex-end;
  text-align: right;
}
.list .itemRight img{
  margin-left: 0.1rem;
}
.list .itemCon h1 span{
  position: absolute;
  left:0;
  top:0;
  color:#F53F3F;
  margin-right:0.08rem;
  font-weight: bold;
}

.recall-bottom{
  position: fixed;
  left:0;
  right:0;
  bottom:0;
  width: 100%;
  padding-bottom:0.88rem;
  display: flex;
  display:-webkit-flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding:0 0.3rem 0.88rem;
}
.recall-bottom .refresh{
  width: 3.25rem;
  height: 0.87rem;
  background: #FFFFFF;
  border: 0.03rem solid #C9CDD4;
  border-radius: 0.2rem;
  font-size: 0.3rem;
  line-height: 0.87rem;
  color: #4E5969;
  text-align: center;
}
.recall-bottom .search{
  width: 3.25rem;
  text-align: center;
  height: 0.87rem;
  background: #4D88FF;
  border-radius: 0.2rem;
  font-size: 0.3rem;
  line-height: 0.87rem;
  color: #FFFFFF;
}
.ios-select-widget-box.olay > div{
  background-color: transparent;
}
.ios-select-widget-box header.iosselect-header{
  background-color: #fff;
  border-bottom: 1px solid #F2F2F2;
  border-radius: 0.32rem 0.32rem 0px 0px;
}
.ios-select-widget-box.olay{
  background: rgba(0, 0, 0, 0.2);
}











.choosedepartment-dialog{
  position: fixed;
  left:0;
  top:0;
  width: 100%;
  height:100%;
  z-index: 999;
  background: rgba(0, 0, 0, 0.2);
  display:none;
}
.choosedepartment-dialog .w-con{
  position: absolute;
  left:0;
  bottom:0;
  right:0;
  transform: translateY(100%);
  -webkit-transform: translateY(100%);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.choosedepartment-dialog .w-con.active{
  transform: translateY(0);
  -webkit-transform: translateY(0);
}
.choosedepartment-dialog .w-con .w-head{
  background: #FFFFFF;
  border-bottom: 1px solid #F2F2F2;
  border-radius: 0.32rem 0.32rem 0 0;
  width: 100%;
  height:0.9rem;
  display: flex;
  display:-webkit-flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding:0 0.3rem;

}
.choosedepartment-dialog .w-con .w-head .cancle{
  font-size:15px;
  color: #4D88FF;
  width: 2.2rem;
}
.choosedepartment-dialog .w-con .w-head .name{
  color: #333333;
  font-size: 16px;
}
.choosedepartment-dialog .w-con .w-head .btns{
  font-size:15px;
  color: #4D88FF;
  width: 2.2rem;
  text-align: right;
}
.choosedepartment-dialog .w-con .w-head .btns span{
  display: inline-block;
  margin-left:0.24rem;
}
.choosedepartment-dialog .w-con .w-head .btns span.acllSelect{
  margin-left:0;
}
.choosedepartment-dialog .w-con .w-box{
  width: 100%;
  height:auto;
  background-color: #fff;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding:0.2rem 0.3rem;
}
.search-inputs {
  width: 100%;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  margin-bottom:0.2rem;
}
.search-inputs .s-con {
  background: #F5F6F8;
  border-radius: 0.3rem;
  height: .6rem;
  display: flex;
  align-items: center;
  position: relative;
  color: #333333;
  font-size: 14px;
  overflow: hidden;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.search-inputs .s-con img {
  width: .36rem;
  height: .36rem;
  margin-right: .1rem;
  margin-left: .2rem;
}
.search-inputs .s-con input {
  display: block;
  flex: 1;
  height: .6rem;
  border: none;
  background: none;
  font-size: .28rem;
  font-family: auto;
}
.search-inputs .s-con input::placeholder {
  color: #B3B3B3;
  font-size: .28rem;
}
.search-inputs .s-con .m-close {
  overflow: hidden;
  background: url(../images/cancle-icon.png) no-repeat center;
  background-size: .28rem .28rem;
  margin-right: .2rem;
  width: .28rem;
  height: .28rem;
  display: none;
}
.choosedepartment-dialog .w-con .w-box ul{
  overflow-y: auto;
  max-height:4.8rem;
  -webkit-overflow-scrolling:touch;
}
.choosedepartment-dialog .w-con .w-box ul li{
  height: 0.96rem;
  line-height: 0.96rem;
  font-size:16px;
  color: #4E5969;
  padding-left:0.72rem;
  position: relative;
}
.choosedepartment-dialog .w-con .w-box ul li:after{
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 0.48rem;
  height: 0.48rem;
  margin-top: -0.24rem;
  background: url(../../images/basic/mobile-tick-icon.png) no-repeat center;
  background-size: 0.48rem;
}
.choosedepartment-dialog .w-con .w-box ul li.cur:after {
  background: url(../../images/basic/mobile-tick-cur-icon.png) no-repeat center;
  background-size: 0.48rem;
}

.list .itemRight p.ycenter .color1{
  color: #4E5969;
}