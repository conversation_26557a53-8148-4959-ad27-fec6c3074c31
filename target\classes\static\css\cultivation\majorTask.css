.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.flex {
  display: flex;
  align-items: center;
}
body {
  background-color: #f7f8fa;
}
.main {
  margin: 0 auto;
  border-radius: 8px;
  min-height: calc(100vh);
  min-width: 1000px;
  max-width: 1660px;
  background-color: #ffffff;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  overflow: hidden;
}
.main .opt-btn {
  color: #4c85fa;
  cursor: pointer;
}
.main .m-top {
  width: 100%;
  height: 57px;
  border-bottom: 1px solid #e8eaf1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top .back {
  width: 60px;
  height: 24px;
  background: url(../../images/cultivation/back-icon.png) no-repeat left center;
  cursor: pointer;
  margin-right: 10px;
}
.main .m-top .title {
  font-size: 16px;
  color: #1d2129;
  padding-left: 9px;
  position: relative;
}
.main .m-top .title::after {
  content: "";
  width: 3px;
  height: 16px;
  background-color: #4d88ff;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 3px;
}
.main .search-con {
  margin: 24px 30px 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.main .search-con .layui-inline {
  margin-right: 16px;
  margin-bottom: 16px;
}
.main .table-box {
  position: relative;
  margin: 0 30px 24px;
}
.main .z-check {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6581BA;
  position: absolute;
  left: 16px;
  bottom: 8px;
}
.main .z-check .check {
  width: 28px;
  height: 28px;
  cursor: pointer;
  margin-right: 6px;
  background: url(../../images/cultivation/check-icon.png) no-repeat center;
  background-size: 18px;
}
.main .z-check .check.checked {
  background: url(../../images/cultivation/check-cur.png) no-repeat center;
}
.main .selCourse {
  left: 160px;
  line-height: 43px;
  position: absolute;
  bottom: 3px;
  font-size: 14px;
  line-height: 40px;
  color: #8F97A8;
}
.main .courseList {
  position: relative;
  margin-bottom: 24px;
}
.main .courseList .selCourse {
  position: absolute;
  left: 10px;
  bottom: 0;
  color: #1d2129;
  font-size: 14px;
  line-height: 40px;
}
.main .courseList .selCourse em {
  color: #4d88ff;
  padding: 0 4px;
}
.main .item {
  padding: 0 30px;
}
.main .item .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 24px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .item .i-top h3 {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #6581ba;
  position: relative;
  display: block;
  margin-right: 24px;
}
.main .item .i-top h3:after {
  content: "";
  position: absolute;
  left: 0;
  top: 1px;
  background: #6581ba;
  border-radius: 2px;
  width: 3px;
  height: 18px;
}
.main .item .i-top .arrow {
  position: relative;
  font-weight: 400;
  width: 12px;
  height: 12px;
  cursor: pointer;
  background: url(../../images/cultivation/arrow-icon.png) no-repeat right center;
  transform: rotate(-80deg);
}
.main .item .i-top .arrow.slide {
  transform: rotate(0);
}
.main .tab-nav {
  border-bottom: 1px solid #ddd;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
}
.main .tab-nav li {
  line-height: 50px;
  font-size: 14px;
  margin-right: 32px;
  color: #5f5f5f;
  cursor: pointer;
}
.main .tab-nav li:hover {
  color: #4c85fa;
}
.main .tab-nav li.active {
  color: #4c85fa;
  font-weight: bold;
  position: relative;
}
.main .tab-nav li.active::after {
  content: "";
  width: 100%;
  height: 2px;
  background-color: #4c85fa;
  position: absolute;
  bottom: 0;
  left: 0;
}
.main .tab-box .table-box1 {
  display: none;
}
.main .tab-box .table-box1:first-child {
  display: block;
}
.main div[lay-id=courseList] .layui-table-main {
  max-height: 380px;
  overflow-y: auto;
}
@media screen and (max-width: 1660px) {
  .j-search-con {
    width: 200px;
  }
}
@media screen and (max-width: 1430px) {
  .j-search-con {
    width: 160px;
  }
}
@media screen and (max-width: 1280px) {
  .j-search-con {
    width: 140px;
  }
}
