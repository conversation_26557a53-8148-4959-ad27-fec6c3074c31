body {
  background-color: #F7F8FA;
  padding: 20px;
  font-size: 14px;
  color: #4E5969;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
::selection {
  background: #dde7ff;
  /* 粉红色背景 */
}
::-webkit-scrollbar {
  width: 6px;
  height: 10px;
}
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #d9d9d9;
}
::-webkit-scrollbar-track {
  border-radius: 6px;
}
.main {
  min-height: calc(100vh - 40px);
  overflow: hidden;
}
.main .top {
  width: 100%;
  height: 60px;
  margin-bottom: 10px;
  background: #FFFFFF;
  position: relative;
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 10px;
}
.main .top .titles {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 22px;
  padding-left: 30px;
}
.main .top .titles .back {
  padding-left: 22px;
  background: url(../../../images/basic/roomReserve/back-icon.png) no-repeat left center;
  background-size: 16px;
  color: #7D92B3;
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
}
.main .top .titles .levelone {
  padding-left: 9px;
  position: relative;
  color: #4e5969;
  font-size: 16px;
  margin-right: 6px;
  font-weight: 400;
}
.main .top .titles .levelone:after {
  content: '';
  position: absolute;
  left: 0;
  top: 3px;
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
}
.main .top .titles .icon {
  width: 12px;
  height: 12px;
  background: url(../../../images/basic/roomReserve/right-arrow.png) no-repeat center;
  background-size: 12px;
  margin-right: 6px;
}
.main .top .titles .leveltwo {
  color: #1d2129;
  font-weight: 400;
  font-size: 16px;
}
.main .top h4 {
  position: relative;
  color: #1d2129;
  font-size: 16px;
  margin-left: 30px;
  padding-left: 9px;
}
.main .top h4::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4D88FF;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 4px;
}
.main .top .r-title {
  padding-right: 30px;
  display: flex;
  display: -wekit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .top .r-title .save {
  width: 138px;
  height: 34px;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  background-color: #4d88ff;
  text-align: center;
  line-height: 34px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  border-radius: 4px;
}
.main .m-con {
  width: 100%;
  height: calc(100vh - 110px);
  background-color: #fff;
  border-radius: 10px;
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.main .m-con .rule-document {
  padding: 0 30px;
  width: 655px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  flex-shrink: 0;
}
.main .m-con .rule-document .r-top {
  width: 100%;
  height: 68px;
  border-bottom: 1px solid #e8ebf3;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .m-con .rule-document .r-top h3 {
  font-size: 16px;
  color: #6581ba;
  line-height: 20px;
  padding-left: 7px;
  position: relative;
}
.main .m-con .rule-document .r-top h3:after {
  content: '';
  position: absolute;
  left: 0;
  top: 1;
  width: 3px;
  height: 18px;
  background-color: #6581ba;
  border-radius: 4px;
}
.main .m-con .rule-document .r-con {
  padding: 40px 40px 66px;
  height: calc(100vh - 227px);
  overflow-y: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-con .rule-document .r-con h4,
.main .m-con .rule-document .r-con p {
  font-size: 14px;
  color: #1d2129;
  line-height: 26px;
}
.main .m-con .rule-document .r-con h4 span,
.main .m-con .rule-document .r-con p span {
  padding: 5px 0;
}
.main .m-con .rule-document .r-con h4 span.selected,
.main .m-con .rule-document .r-con p span.selected {
  background-color: #dde7ff;
}
.main .m-con .rule-document .r-con h4 span.warn.selected,
.main .m-con .rule-document .r-con p span.warn.selected {
  background-color: #ffe4ba;
}
.main .m-con .task-details {
  flex: 1;
  padding: 0 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-con .task-details .r-top {
  width: 100%;
  margin-bottom: 24px;
  height: 68px;
  border-bottom: 1px solid #e8ebf3;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .m-con .task-details .r-top h3 {
  font-size: 16px;
  color: #6581ba;
  line-height: 20px;
  padding-left: 7px;
  position: relative;
}
.main .m-con .task-details .r-top h3:after {
  content: '';
  position: absolute;
  left: 0;
  top: 1;
  width: 3px;
  height: 18px;
  background-color: #6581ba;
  border-radius: 4px;
}
.main .m-con .task-details .td-con {
  height: calc(100vh - 227px);
  overflow-y: auto;
}
.main .m-con .task-details .td-con ul li {
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 16px;
}
.main .m-con .task-details .td-con ul li .sort {
  width: 32px;
  flex-shrink: 0;
  height: 22px;
  line-height: 22px;
  font-size: 14px;
  color: #1d2129;
}
.main .m-con .task-details .td-con ul li.warn .sort {
  background: url(../../../images/basic/roomReserve/warn-icon.png) no-repeat left center;
  overflow: hidden;
  text-indent: 99em;
}
.main .m-con .task-details .td-con ul li.warn .t-con .texts {
  background-color: #fff7e8;
  border-top: 4px solid #fff7e8;
  color: #ffb026;
}
.main .m-con .task-details .td-con ul li.active .t-con .texts {
  background-color: #dde7ff;
  border-top: 4px solid #4d88ff;
}
.main .m-con .task-details .td-con ul li.active.warn .t-con .texts {
  background-color: #fff7e8;
  border-top: 4px solid #ffb026;
}
.main .m-con .task-details .td-con ul li .t-con {
  flex: 1;
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding-left: 25px;
  position: relative;
}
.main .m-con .task-details .td-con ul li .t-con .line {
  position: absolute;
  left: 0;
  top: 0;
  width: 10px;
  flex-shrink: 0;
  height: 100%;
  background: url(../../../images/basic/roomReserve/linear.png) repeat-y center;
  background-size: 2px auto ;
}
.main .m-con .task-details .td-con ul li .t-con .line span {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 29px;
  background: #fff url(../../../images/basic/roomReserve/round.png) no-repeat center top;
}
.main .m-con .task-details .td-con ul li .t-con .texts {
  flex: 1;
  background-color: #f6f7fe;
  border-top: 4px solid #f6f7fe;
  border-radius: 4px;
  padding: 16px;
  padding-top: 12px;
  font-size: 14px;
  color: #4e5969;
  cursor: pointer;
}
.main .m-con .handle {
  border-left: 1px solid #e8ebf3;
  border-right: 1px solid #e8ebf3;
  width: 9px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  height: 100%;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
}
.main .m-con .handle span {
  width: 100%;
  height: 40px;
  background: url(../../../images/basic/roomReserve/handle-icon.png) no-repeat center;
  cursor: move;
  background-size: 5px 11px ;
}
