/*table 过滤*/
.layui-table-filter {
    height: 100%;
    cursor: pointer;
    /*  position: absolute;
    right: 15px;
    padding: 0 5px; */
    position: relative !important;
    right: auto;
    padding: 0 12px;
}

.layui-table-filter i {
    font-size: 12px;
    color: #ccc;
}

.layui-table-filter:hover i {
    color: #666;
}

.layui-table-filter.tableFilter-has i {
    color: #1E9FFF;
}

.layui-table-filter-view {
    min-width: 200px;
    background: #FFFFFF;
    border: 1px solid #d2d2d2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12);
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 90000000;
}

.layui-table-filter-box {
    padding: 10px;
    overflow: hidden;
}

.layui-table-filter-box .loading {
    width: 100%;
    height: 100%;
    text-align: center;
    line-height: 150px;
}

.layui-table-filter-box .loading i {
    font-size: 18px;
}

.layui-table-filter-box input.layui-input {
    margin-bottom: 10px;
}

.layui-table-filter-box ul {
    border: 1px solid #eee;
    max-height: 150px;
    overflow: auto;
    margin-bottom: 10px;
    padding: 5px 10px 5px 10px;
}

.layui-table-filter-box ul.layui-laydate-list {
    height: 230px;
    max-height: 230px;
}

.layui-table-filter-box ul li {}

.layui-table-filter-box ul.radio {
    padding: 0px;
    background: #FFFFFF;
    border-radius: 4px 4px 0px 0px;
}

.layui-table-filter-box ul.radio li {
    padding: 0px;
    height: 32px;
    font-size: 14px;
    color: #4E5969;
    line-height: 32px;
}

.layui-table-filter-box ul li .layui-form-radio {
    display: block;
    color: #666;
    margin: 0px;
    padding: 0px;
    transition: .1s linear;
}

.layui-table-filter-box ul li .layui-form-radio div {
    display: block;
    padding: 0 20px;
    height: 32px;
    line-height: 32px;
    color: #4E5969;
}

.layui-table-filter-box ul li .layui-form-radio i {
    display: none;
}

.layui-table-filter-box ul li .layui-form-radio:hover {
    background: #F7F8FA;
}

.layui-table-filter-box ul li .layui-form-radio.layui-form-radioed {
    background: #DDE7FF;
}

.layui-table-filter-box ul li .layui-form-radio.layui-form-radioed div {
    color: #4A7CFE;
}

.layui-table-filter-date {
    margin-bottom: 10px;
}

.layui-table-filter-date .layui-laydate {
    box-shadow: none;
    border: 0;
}

.layui-laydate-header {
    border-bottom: none !important;
}

.layui-layer-tips {
    z-index: 99999;
}


.layui-table-filter-box ul li .layui-form-checkbox i {}

.layui-table-filter-box ul.check {
    padding: 0px;
    background: #FFFFFF;
    border-radius: 4px 4px 0px 0px;
}

.layui-table-filter-box ul.check li {
    padding: 0px;
    height: 32px;
    font-size: 14px;
    color: #4E5969;
    line-height: 32px;
}

.layui-table-filter-box ul.check li .layui-form-checkbox span {
    display: block;
    padding: 0 20px;
    height: 32px;
    line-height: 32px;
    color: #4E5969;
}

.layui-table-filter-box ul li .layui-form-checkbox {
    display: block;
    color: #666;
    margin: 0px;

    transition: .1s linear;
}

.layui-table-filter-box ul li .layui-form-checkbox div {
    display: block;
    padding: 0 20px;
    height: 32px;
    line-height: 32px;
    color: #4E5969;
}

/* .layui-table-filter-box ul li .layui-form-checkbox i {display: none;} */
.layui-table-filter-box ul li:hover {
    background: #F7F8FA;
}

.layui-table-filter-box ul li .layui-form-checkbox.layui-form-checked div {
    color: #4A7CFE;
}

.layui-table-filter-box ul li {
    padding: 3px;
}

.layui-btn-normal {
    background-color: #4D88FF;
}