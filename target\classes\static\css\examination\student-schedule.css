body {
  background-color: #fff;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.hide {
  display: none !important;
}
.layui-form-select dl dd.layui-disabled {
  background: none !important;
}
.layui-form-select dl dd.layui-select-tips {
  display: none !important;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.student-wrapper {
  padding-top: 40px;
}
.student-wrapper .layui-form .layui-form-item .item-radio {
  margin-bottom: 20px;
  position: relative;
}
.student-wrapper .layui-form .layui-form-item .item-con .lable {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 24px;
  margin-bottom: 16px;
}
.student-wrapper .layui-form .layui-form-item .item-con .lable .name {
  color: #1D2129;
  text-align: left;
  width: 260px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.student-wrapper .layui-form .layui-form-item .item-con .lable .name span {
  width: 16px;
  height: 17px;
  background: url(../../images/examination/tips-icon.png) no-repeat right center;
  background-size: 16px;
  display: inline-block;
  vertical-align: top;
  padding-left: 6px;
  cursor: pointer;
}
.student-wrapper .layui-form .layui-form-item .item-con .radio-list .radio {
  display: none;
}
.student-wrapper .layui-form .layui-form-item .item-con .radio-list .radio.cur {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.student-wrapper .add-exam-schedule span {
  cursor: pointer;
  font-size: 14px;
  color: #528BFC;
  display: none;
}
.student-wrapper .select {
  display: none;
  margin-right: 20px;
}
.student-wrapper .select .layui-input {
  height: 28px;
}
.student-wrapper .save-settings {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  margin-top: 100px;
}
.student-wrapper .save-settings span {
  box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
  border-radius: 4px;
  height: 36px;
  line-height: 36px;
  background: #4D88FF;
  padding: 0 20px;
  color: #ffffff;
  font-size: 14px;
  margin-left: 12px;
  cursor: pointer;
  margin-bottom: 16px;
}
.student-wrapper .add-custom {
  display: none;
  font-size: 14px;
  color: #4C88FF;
  cursor: pointer;
}
.student-wrapper .delet {
  display: none;
  font-size: 14px;
  color: #f76560;
  cursor: pointer;
  position: absolute;
  left: 600px;
  top: 2px;
}
.show {
  display: inline-block !important;
}
.layui-radio-disbaled > i {
  color: transparent !important;
}
.layui-disabled {
  background: url(../../images/examination/disabled.png) no-repeat left center;
}
.layui-form-select .layui-edge {
  border: none !important;
  background-color: transparent !important;
  width: 10px;
  height: 10px;
  background: url(../../images/examination/down-icon.png) no-repeat center;
  top: 50%;
  margin-top: -5px;
}
.layui-form-select dl {
  padding: 0 !important;
  border-color: #E5E6EB;
}
