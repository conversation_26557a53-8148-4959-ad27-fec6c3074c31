body {
  background-color: #F7F8FA;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
.hide {
  display: none !important;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  font-size: 14px;
  color: #8F97A8;
}
.hide {
  display: none;
}
.main {
  margin: 20px;
  background-color: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 40px);
  min-width: 1000px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top {
  width: 100%;
  height: 57px;
  border-bottom: 1px solid #E8EAF1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .m-top .back {
  width: 60px;
  height: 24px;
  background: url(../../images/cultivation/back-icon.png) no-repeat left center;
  cursor: pointer;
  margin-right: 10px;
}
.main .m-top .title {
  font-size: 16px;
  color: #6581BA;
}
.main .item {
  padding: 0 30px;
  margin-bottom: 40px;
}
.main .item .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 10px;
  margin-top: 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .item .i-top span {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #484F5D;
  position: relative;
  display: block;
  margin-right: 24px;
}
.main .item .i-top span:after {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  background: #4D88FF;
  border-radius: 1px;
  width: 3px;
  height: 14px;
}
.main .item .i-top .arrow {
  padding-right: 14px;
  position: relative;
  font-weight: 400;
  font-size: 14px;
  color: #4C88FF;
  cursor: pointer;
}
.main .item .i-top .arrow:after {
  content: '';
  position: absolute;
  right: 0;
  top: 5px;
  width: 10px;
  height: 10px;
  background: url(../../images/cultivation/stow-icon.png) no-repeat right center;
}
.main .item .i-con {
  overflow: hidden;
  display: flex;
  display: -webkit-flex;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
}
.main .item .i-con h4.i-title {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #484F5D;
  margin-bottom: 16px;
}
.main .item .i-con .course-inform {
  flex: 1;
}
.main .item .i-con .course-inform ul {
  overflow: hidden;
  background: #F9FAFB;
  margin-right: 24px;
  padding: 20px 24px;
  border-radius: 4px;
  height: 340px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .item .i-con .course-inform ul li {
  width: 50%;
  height: 20px;
  margin-bottom: 16px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  float: left;
}
.main .item .i-con .course-inform ul li .name {
  font-weight: 400;
  font-size: 14px;
  color: #717B91;
  flex-shrink: 0;
}
.main .item .i-con .course-inform ul li .tit {
  color: #484f5d;
  font-weight: 400;
  font-size: 14px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .item .i-con .class-box {
  min-width: 852px;
  flex: 1;
}
.main .item .i-con .class-box .set-con {
  background: #F9F9FB;
  border-radius: 8px;
  padding: 16px 6px 10px 6px;
  margin-bottom: 30px;
}
.main .item .i-con .class-box .set-con .layui-inline {
  margin-right: 120px;
  margin-bottom: 0;
}
.main .item .i-con .class-box .set-con .layui-form-label {
  width: 86px;
  text-align: right;
  font-size: 14px;
  line-height: 34px;
  color: #717B91;
  padding: 0;
  margin-right: 12px;
}
.main .item .i-con .class-box .set-con .layui-input,
.main .item .i-con .class-box .set-con .layui-textarea,
.main .item .i-con .class-box .set-con .layui-select {
  height: 34px;
  font-size: 14px;
}
.main .item .i-con .class-box .set-con .layui-form-select .layui-edge {
  border-top-color: #99B2E6 !important;
  border-radius: 4px;
}
.main .item .i-con .class-box .set-con .layui-input {
  border-color: #D5D9E2;
  border-radius: 4px;
}
.main .item .i-con .class-box .set-con .layui-input::placeholder {
  color: #8F97A8 !important;
}
.main .item .i-con .class-box .set-con p.txt {
  font-size: 14px;
  line-height: 34px;
  color: #484F5D;
  width: 240px;
}
.main .item .i-con .class-box .set-con .layui-form-select {
  width: 240px;
}
.main .item.item-teacher .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 20px;
  margin-top: 30px;
}
.main .item.item-teacher .i-top span {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #484F5D;
  position: relative;
  display: block;
}
.main .item.item-teacher .i-top span:after {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  background: #4D88FF;
  border-radius: 1px;
  width: 3px;
  height: 14px;
}
.main .item.item-teacher .i-search {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.main .item.item-teacher .i-search .i-add {
  width: 110px;
  height: 34px;
  border: 1px solid #4C88FF;
  border-radius: 4px;
  font-size: 14px;
  color: #4C88FF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 40px;
  box-sizing: border-box;
  cursor: pointer;
}
.main .item.item-teacher .i-search .i-add span {
  margin-right: 6px;
  font-size: 24px;
}
.main .item.item-teacher .i-search .layui-inline {
  margin-bottom: 0;
}
.main .item.item-teacher .i-search .layui-form-label {
  width: 57px;
  text-align: right;
  font-size: 14px;
  line-height: 34px;
  color: #717B91;
  padding: 0;
  margin-right: 12px;
}
.main .item.item-teacher .i-search .layui-input {
  border-color: #D5D9E2;
  border-radius: 4px;
  width: 240px;
  height: 34px;
  font-size: 14px;
}
.main .item.item-teacher .i-search .layui-input::placeholder {
  color: #8F97A8 !important;
}
.main .item.item-teacher .i-search .btnSubmit {
  width: 88px;
  height: 34px;
  line-height: 34px;
  padding: 0;
  border: 1px solid #4C88FF;
  border-radius: 4px;
  margin-left: 40px;
  background-color: #fff;
  font-size: 14px;
  color: #4C88FF;
}
.main .item.item-teacher .layui-table tbody tr:nth-child(even) {
  background-color: #FFFFFF;
}
.main .item.item-teacher .layui-table tbody tr:nth-child(even):hover {
  background-color: #edf2fd;
}
.main .itemBtn {
  width: 118px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 6px;
  font-size: 14px;
  color: #FFFFFF;
  background-color: #6A9CFF;
  margin-left: 30px;
  margin-top: 20px;
  cursor: pointer;
}
.layui-table-cell {
  padding: 0 17px;
  height: 30px;
}
.layui-table-cell .tmplOpt {
  display: flex;
  align-items: center;
}
.layui-table-cell .tmplOpt h5 {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.layui-table-cell .tmplOpt span.set {
  color: #4C88FF;
  margin-left: 16px;
  cursor: pointer;
}
.layui-table-cell .layui-input {
  width: 220px;
  background: #FFFFFF;
  border: 1px solid #D5D9E2;
  border-radius: 4px;
}
.layui-table-cell .layui-input:focus {
  border: 1px solid #4C88FF !important;
}
.layui-table-cell .edit {
  display: inline-block;
  color: #4C88FF;
  margin-right: 17px;
  cursor: pointer;
}
.layui-table-cell .delet {
  display: inline-block;
  color: #FF5E5E;
  cursor: pointer;
}
.layui-table-view .layui-table th {
  font-size: 14px;
  color: #6581BA;
  height: 44px;
}
.layui-table-view .layui-table td {
  color: #484F5D;
  font-size: 14px;
  height: 44px;
}
.layui-laypage a,
.layui-laypage span {
  border: none;
}
.layui-laypage a,
.layui-laypage span {
  padding: 0 11px;
  margin: 0 5px;
  background: #F1F3F6;
  border-radius: 4px;
  color: #484F5D;
}
.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background: #4C85FA;
  border-radius: 4px;
}
.layui-laypage input:focus,
.layui-laypage select:focus {
  border-color: #4C85FA !important;
}
.layui-laypage a:hover {
  color: #4C85FA;
}
.layui-laypage-prev {
  background-color: transparent !important;
}
.layui-laypage-next {
  background-color: transparent !important;
}
.layui-laypage-spr {
  background-color: transparent !important;
}
.layui-laypage-skip {
  background-color: transparent !important;
}
.layui-laypage-count {
  background-color: transparent !important;
}
.layui-laypage-skip input {
  border: 1px solid #e2e2e2;
  background-color: transparent;
}
.layui-laypage-skip input {
  background: #FFFFFF;
  border: 1px solid #E7EAF1;
  color: #4D4D4D;
  border-radius: 2px;
}
.layui-laypage button {
  background: #F5F8FB;
  border: 1px solid #E7EAF1;
  color: #595959;
  border-radius: 2px;
}
.layui-table-page > div {
  text-align: right;
}
