body {
    background-color: #f7f8fa;
}

.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.layui-form-radio {
    margin: 0 10px 0 0;
}

.layui-form-radio *,
.layui-form-checkbox * {
    color: #7a828e;
}

.layui-form-radio > i {
    margin-right: 6px;
    font-size: 18px;
}

.layui-form-radioed > i {
    color: #4d88ff;
}

.layui-form-radioed > i,
.layui-form-radio > i:hover {
    color: #4d88ff;
}

.layui-form .layui-form-item {
    margin-bottom: 16px;
}

.layui-form .layui-form-label {
    padding: 4px 0;
    width: 64px;
    text-align: left;
    color: #1d2129;
    display: flex;
    align-items: center;
}

.layui-form .layui-form-label span {
    width: 16px;
    height: 17px;
    background: url(../../../images/cultivation/tips-icon.png) no-repeat right center;
    background-size: 16px;
    display: inline-block;
    vertical-align: middle;
    padding-left: 6px;
    cursor: pointer;
    vertical-align: text-bottom;
}

.layui-form .layui-input-block {
    margin-left: 78px;
}

.layui-form.form-set .layui-form-label {
    width: 112px;
}

.layui-form.form-set .layui-input-block {
    margin-left: 126px;
}

.layui-form.form-set .layui-input-block .layui-input:focus {
    border-color: #e5e6eb !important;
    box-shadow: unset;
}

.layui-form.form-set .layui-input-block.time-block {
    position: relative;
}

.layui-form.form-set .layui-input-block.time-block img {
    position: absolute;
    display: block;
    right: 8px;
    top: 11px;
}

.layui-form.form-set #timeLimit {
    height: 34px;
    border-radius: 4px;
}

.layui-form.form-set #timeLimit::placeholder {
    color: #86909c;
}

.layui-form.teach-set .layui-form-item {
    display: flex;
    align-items: flex-start;
}

.layui-form.teach-set .layui-form-item .layui-form-switch {
    margin-top: 0;
    width: 28px;
    height: 14px;
    min-width: 0;
    border-radius: 3px;
    background-color: #D2D3D8;
    margin-right: 8px;
}

.layui-form.teach-set .layui-form-item .layui-form-switch > i {
    width: 12px;
    height: 10px;
    border-radius: 2px;
    top: 1px;
    left: 1px;
    background-color: #fff;
}

.layui-form.teach-set .layui-form-item .swtichText {
    color: #C9CDD4;
}

.layui-form.teach-set .layui-form-item .layui-form-onswitch {
    border-color: #4C87FD;
    background-color: #4C87FD;
}

.layui-form.teach-set .layui-form-item .layui-form-onswitch > i {
    margin-left: 12px;
}

.layui-form.teach-set .layui-form-item .layui-form-label {
    width: auto;
    min-width: 134px;
}

.layui-form.teach-set .layui-form-item .layui-input-block {
    margin-left: 14px;
}

.layui-form-checkbox[lay-skin=primary] {
    margin-top: 6px !important;
    margin-bottom: 20px;
    margin-right: 9px;
    min-width: 123px;
}

.layui-form-checkbox[lay-skin=primary] > i {
    border-radius: 3px;
    border-color: #c2c2c2;
}

.layui-form-checked[lay-skin=primary] > i {
    border-color: #4D88FF !important;
    background-color: #4D88FF;
    font-weight: bold;
}

.layui-form-checkbox[lay-skin=primary]:hover > i {
    border-color: #4D88FF;
    color: #ffffff;
}

.layui-form-checked,
.layui-form-checked:hover {
    border-color: #4d88ff;
}

.layui-laydate .layui-this,
.layui-laydate .layui-this > div {
    background-color: #4D88FF !important;
}

.layui-laydate-footer span:hover,
.laydate-set-ym span:hover {
    color: #4D88FF !important;
}

.layui-laydate-header i:hover {
    color: #4D88FF !important;
}

.z-main {
    background-color: #ffffff;
    border-radius: 4px;
    margin-bottom: 10px;
}

.z-main .z-title {
    padding: 29px 0 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e8ebf1;
}

.z-main .z-title h3 {
    font-size: 16px;
    line-height: 22px;
    color: #1d2129;
    padding-left: 9px;
    position: relative;
    margin-left: 30px;
}

.z-main .z-title h3::after {
    content: "";
    width: 3px;
    height: 16px;
    background: #4d88ff;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 3px;
}

.z-main .z-title span {
    color: #86909c;
    font-size: 14px;
    margin-left: 16px;
    margin-top: 2px;
}

.z-main .box-con {
    margin: 0 30px 32px;
}

.z-main .box-con .box-title {
    color: #6581ba;
    font-size: 16px;
    line-height: 20px;
    margin: 20px 0 32px;
    padding-left: 7px;
    position: relative;
    display: flex;
    align-items: center;
}

.z-main .box-con .box-title::after {
    content: "";
    width: 3px;
    height: 18px;
    background-color: #6581ba;
    border-radius: 4px;
    position: absolute;
    left: 0;
    top: 1px;
}

.z-main .box-con .box-title span {
    width: 20px;
    height: 20px;
    background: url('../../../images/cultivation/arrow-icon.png') no-repeat center;
    background-size: 12px;
    margin-left: 21px;
    cursor: pointer;
    transform: rotate(180deg);
    transition: transform 0.2s;
}

.z-main .box-con .box-title span.active {
    transform: rotate(0deg);
}

.z-main .box-con .radio-modal:first-child {
    margin-bottom: 24px;
}

.z-main .box-con .radio-modal p {
    color: #7a828e;
    margin-left: 24px;
}

.z-main .box-con .raido-create {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.z-main .box-con .raido-create:first-child {
    margin-bottom: 24px;
}

.z-main .box-con .raido-create .sel-member {
    display: flex;
    align-items: center;
    border-radius: 4px;
    border: 1px dashed #4d88ff;
    background: #fff;
    height: 34px;
    box-sizing: border-box;
    padding: 0 14px;
    color: #4d88ff;
    font-size: 14px;
    margin-right: 24px;
    margin-left: 20px;
    cursor: pointer;
}

.z-main .box-con .raido-create .sel-member img {
    margin-right: 4px;
}

.z-main .box-con .raido-create ul {
    display: flex;
    align-items: center;
}

.z-main .box-con .raido-create ul li {
    height: 34px;
    line-height: 34px;
    padding: 0 16px;
    color: #4d88ff;
    font-size: 14px;
    border: 1px solid #4d88ff;
    border-radius: 30px;
    margin-right: 12px;
    position: relative;
}

.z-main .box-con .raido-create ul li span {
    width: 14px;
    height: 14px;
    background: url("../../../images/cultivation/mooc/icon-del.png") no-repeat center;
    background-size: 14px;
    position: absolute;
    right: -4px;
    top: 0;
    cursor: pointer;
}

.j-search-con {
    display: flex;
    align-items: center;
    position: relative;
    width: 308px;
    cursor: pointer;
}

.j-search-con .j-select-year {
    left: 0;
}

.j-search-con input {
    width: 100%;
    height: 34px;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    padding: 0 20px 0 10px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    font-size: 14px;
    cursor: pointer;
}

.j-search-con input::placeholder {
    color: #86909c;
}

.j-search-con .j-arrow {
    width: 10px;
    height: 10px;
    background: url(../../../images/cultivation/down-icon.png) no-repeat center;
    position: absolute;
    right: 12px;
    top: 12px;
    pointer-events: none;
}

.j-search-con .j-arrow.j-arrow-slide {
    transform: rotate(180deg);
}

.j-search-con .j-select-year {
    position: absolute;
    top: 40px;
    left: -1px;
    z-index: 999;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}

.j-search-con .j-select-year.slideShow {
    display: block;
}

.j-search-con .j-select-year.slideShowTop {
    display: block;
    top: unset;
    bottom: 40px;
}

.j-search-con .j-select-year .search {
    height: 36px;
    background: #f5f7fa;
    border-radius: 18px;
    margin: 11px 10px;
}

.j-search-con .j-select-year .search input {
    border: none;
    width: 84%;
    background: transparent;
    height: 36px;
    line-height: 36px;
    padding-left: 14px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    float: left;
}

.j-search-con .j-select-year .search span {
    cursor: pointer;
    float: left;
    width: 16px;
    height: 16px;
    background: url(../../../images/cultivation/search-icon.png) no-repeat center;
    margin-top: 10px;
}

.j-search-con .j-select-year .all-selects {
    line-height: 17px;
    margin-bottom: 4px;
    height: 17px;
    padding: 0 14px;
    font-size: 12px;
    color: #6b89b3;
    cursor: pointer;
    user-select: none;
}

.j-search-con .j-select-year ul {
    overflow: hidden;
    max-height: 200px;
    overflow-y: auto;
}

.j-search-con .j-select-year ul li {
    line-height: 40px;
    text-align: left;
    text-indent: 16px;
    cursor: pointer;
    font-size: 14px;
    color: #4e5969;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    padding-right: 30px;
    background-color: #ffffff;
    background-image: url("../../../images/cultivation/check-icon.png");
    background-repeat: no-repeat;
    background-position: 96% center;
}

.j-search-con .j-select-year ul li:hover {
    background-color: #e1ebff;
    color: #4d88ff;
    font-weight: 500;
}

.j-search-con .j-select-year ul li.active {
    background-color: #e1ebff;
    background-image: url("../../../images/cultivation/check-cur.png");
    color: #4d88ff;
    font-weight: 500;
}

.j-search-con.single-box .j-select-year ul li {
    background-image: url("../../../images/cultivation/mooc/radio-icon.png");
}

.j-search-con.single-box .j-select-year ul li.active {
    background-image: url("../../../images/cultivation/radio-cur-icon.png");
}

#dialogSel {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
}

#dialogSel .selHead {
    height: 56px;
    font-size: 16px;
    text-indent: 30px;
    line-height: 56px;
    border-bottom: 1px solid #ddd;
}

#dialogSel .selCon {
    display: flex;
    padding: 20px 30px;
}

#dialogSel .fl-person {
    float: left;
    width: 300px;
    margin-right: 6px;
}

#dialogSel .fl-person .tab-search {
    height: 34px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
}

#dialogSel .fl-person .tab-search input {
    float: left;
    width: 240px;
    height: 34px;
    border-radius: 4px 0 0 4px;
    border: 1px solid #fff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding-left: 30px;
    background: url("../../../images/cultivation/icon-search.png") no-repeat 10px center;
    background-size: 16px 16px;
    font-size: 14px;
    color: #4e5969;
    border: 1px solid #ddd;
}

#dialogSel .fl-person .tab-search input::placeholder {
    color: #8f97a8;
}

#dialogSel .fl-person .tab-search span {
    display: block;
    float: left;
    width: 60px;
    height: 34px;
    background: #4d88ff;
    border-radius: 0px 4px 4px 0px;
    color: #ffffff;
    text-align: center;
    line-height: 34px;
    cursor: pointer;
}

#dialogSel .fl-person .kex-box {
    height: 298px;
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 6px;
    overflow: hidden;
    overflow-y: auto;
}

#dialogSel .fl-person .kex-box .moreBoxs {
    width: 100%;
    height: 30px;
    display: none;
}

#dialogSel .fl-person .kex-box .moreBoxs img {
    width: 100px;
    height: 20px;
    display: block;
    margin: 10px auto;
}

#dialogSel .fl-person .kex-box .list .region .dropDown {
    margin-left: 12px;
}

#dialogSel .fl-person .kex-box .list .floor .list .region .dropDown {
    margin-left: 26px;
}

#dialogSel .fl-person .kex-box .list .floor .list .floor .list .region .dropDown {
    margin-left: 38px;
}

#dialogSel .fl-person .kex-box .list .floor .list .floor .list .floor .list .region .dropDown {
    margin-left: 50px;
}

#dialogSel .fl-person .kex-box .list .floor .list .floor .list .floor .list .floor .list .region .dropDown {
    margin-left: 62px;
}

#dialogSel .fl-person .kex-box .list .floor .list .floor .list .floor .list .floor .list .floor .list.region .dropDown {
    margin-left: 74px;
}

#dialogSel .fl-person .kex-box .list .floor .list .region-member .name {
    margin-left: 36px;
}

#dialogSel .fl-person .kex-box .list .floor .list .floor .list .region-member .name {
    margin-left: 50px;
}

#dialogSel .fl-person .kex-box .list .floor .list .floor .list .floor .list .region-member .name {
    margin-left: 64px;
}

#dialogSel .fl-person .kex-box .list .floor .list .floor .list .floor .list .floor .list .region-member .name {
    margin-left: 76px;
}

#dialogSel .fl-person .kex-box .list .floor .list .floor .list .floor .list .floor .list .floor .list .region-member .name {
    margin-left: 88px;
}

#dialogSel .fl-person .kex-box .list {
    overflow: hidden;
}

#dialogSel .fl-person .kex-box .region {
    width: 100%;
    clear: both;
    position: relative;
    min-height: 48px;
    overflow: hidden;
    cursor: pointer;
    border-bottom: 1px solid #eef1f3;
}

#dialogSel .fl-person .kex-box .region.region-file .dropDown {
    float: left;
    z-index: 99;
    width: 16px;
    height: 48px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    transform: rotate(-90deg);
    -webkit-transform: rotate(-90deg);
}

#dialogSel .fl-person .kex-box .region.region-file .dropDown:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -5px;
    width: 0;
    height: 0;
    border-top: 5px solid #4e5969;
    border-right: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 5px solid transparent;
}

#dialogSel .fl-person .kex-box .region.region-file .dropDown.disabled {
    display: none;
}

#dialogSel .fl-person .kex-box .region.region-file .dropDown.cur {
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
}

#dialogSel .fl-person .kex-box .region.region-file .name {
    float: left;
    line-height: 40px;
    font-size: 14px;
    color: #4e5969;
    margin-left: 8px;
}

#dialogSel .fl-person .kex-box .region.region-file .name.division {
    line-height: 48px;
    padding-left: 26px;
    background: url("../../../images/cultivation/mooc/icon-file.png") no-repeat left center;
    background-size: 20px 18px;
}

#dialogSel .fl-person .kex-box .region.region-file .st-add {
    display: none;
    float: right;
    margin-right: 14px;
    height: 41px;
    width: 14px;
    background: url(../../../images/cultivation/mooc/mailAdd.png) no-repeat center;
    background-size: 100%;
}

#dialogSel .fl-person .kex-box .region.region-file .st-add.selected {
    display: block;
    background: url(../../../images/cultivation/mooc/properSle.png) no-repeat center;
}

#dialogSel .fl-person .kex-box .region.region-file:hover .st-add {
    display: block;
}

#dialogSel .fl-person .kex-box .region.region-member .name {
    float: left;
    color: #4e6957;
}

#dialogSel .fl-person .kex-box .region.region-member .name h3 {
    line-height: 28px;
}

#dialogSel .fl-person .kex-box .region.region-member .name p {
    color: #bcbcc5;
    margin-bottom: 4px;
}

#dialogSel .fl-person .kex-box .region.region-member .st-add {
    display: none;
    float: right;
    margin-right: 14px;
    height: 48px;
    width: 14px;
    background: url(../../../images/cultivation/mooc/mailAdd.png) no-repeat center;
    background-size: 100%;
    cursor: pointer;
}

#dialogSel .fl-person .kex-box .region.region-member .st-add.selected {
    display: block;
    background: url(../../../images/cultivation/mooc/properSle.png) no-repeat center;
}

#dialogSel .fl-person .kex-box .region.region-member:hover .st-add {
    display: block;
}

#dialogSel .fl-person .kex-box .list {
    width: 100%;
    height: auto;
}

#dialogSel .fl-person .kex-box .list .floor {
    display: none;
}

#dialogSel .fr-pitch {
    float: right;
    width: 300px;
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 2px;
    overflow: hidden;
    border-radius: 4px;
}

#dialogSel .fr-pitch h2 {
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #ffffff;
    background-color: #4d88ff;
}

#dialogSel .fr-pitch .kex-box {
    width: 100%;
    height: 298px;
    overflow: hidden;
    overflow-y: auto;
}

#dialogSel .fr-pitch .kex-box ul li {
    width: 100%;
    min-height: 48px;
    overflow: hidden;
    border-bottom: 1px solid #eef1f3;
}

#dialogSel .fr-pitch .kex-box ul li .name {
    float: left;
    color: #4e5969;
    padding-left: 20px;
}

#dialogSel .fr-pitch .kex-box ul li .name h3 {
    line-height: 28px;
}

#dialogSel .fr-pitch .kex-box ul li .name p {
    color: #bcbcc5;
    margin-bottom: 4px;
}

#dialogSel .fr-pitch .kex-box ul li .cancl {
    display: none;
    float: right;
    margin-right: 14px;
    height: 48px;
    width: 16px;
    background: url(../../../images/cultivation/mooc/pageCancle.png) no-repeat center;
    background-size: 100%;
    cursor: pointer;
}

#dialogSel .fr-pitch .kex-box ul li:hover .cancl {
    display: block;
}

#dialogSel .selFooter {
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid #ddd;
}

#dialogSel .selFooter button {
    width: 88px;
    height: 36px;
    background: #fff;
    border: 1px solid #c9cdd4;
    border-radius: 18px;
    box-sizing: border-box;
    text-align: center;
    line-height: 34px;
    padding: 0;
    cursor: pointer;
    font-size: 14px;
    color: #4e5969;
}

#dialogSel .selFooter button:last-child {
    background: #4d88ff;
    box-shadow: 0 0 8px rgba(39, 111, 255, 0.31);
    border-radius: 18px;
    border: none;
    color: #fff;
    line-height: 36px;
    margin-left: 16px;
    margin-right: 30px;
}

.popAddRoleGroup {
    width: 508px;
    height: auto;
    overflow: hidden;
    position: fixed;
    z-index: 999;
    background: #ffffff;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    border-radius: 4px;
}

.popAddRoleGroup .pup-title {
    width: 100%;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #333333;
    text-align: center;
    position: relative;
    margin-bottom: 10px;
}

.popAddRoleGroup .pup-title .close {
    position: absolute;
    top: 13px;
    right: 13px;
    width: 14px;
    height: 14px;
    background: url(../../../images/cultivation/mooc/close-icon.png) no-repeat;
    cursor: pointer;
    margin-top: 0;
}

.popAddRoleGroup .popup-con {
    overflow: hidden;
}

.popAddRoleGroup .popup-con .popup-search {
    width: 480px;
    height: 26px;
    background: #f8f8fa;
    border-radius: 4px;
    border: 1px solid #bcbcc5;
    margin: 0 auto;
    position: relative;
}

.popAddRoleGroup .popup-con .popup-search img {
    display: block;
    float: left;
    margin: 5px;
}

.popAddRoleGroup .popup-con .popup-search input {
    display: block;
    float: left;
    width: 412px;
    height: 26px;
    border: none;
    background: none;
}

.popAddRoleGroup .popup-con .popup-search input::placeholder {
    color: #bcbcc5;
}

.popAddRoleGroup .popup-con .popup-search span {
    display: block;
    float: right;
    width: 40px;
    height: 26px;
    background: #ffffff;
    border-radius: 0px 4px 4px 0px;
    border: 1px solid #4a68d1;
    position: absolute;
    right: -1px;
    top: -1px;
    color: #4a68d1;
    text-align: center;
    line-height: 26px;
    cursor: pointer;
}

.popAddRoleGroup .popup-con .popup-tips {
    overflow: hidden;
    width: 480px;
    margin: 0 auto;
    height: 28px;
    line-height: 28px;
}

.popAddRoleGroup .popup-con .popup-tips h6 {
    float: left;
    color: #bcbcc5;
}

.popAddRoleGroup .popup-con .popup-tips div {
    float: right;
    color: #4a68d1;
}

.popAddRoleGroup .popup-con .popup-tips div span {
    padding-left: 2px;
}

.popAddRoleGroup .popup-con .popup-tips div span i {
    color: #85858f;
}

.popAddRoleGroup .popup-con .popup-tips div span em {
    font-weight: normal;
}

.popAddRoleGroup .popup-con .cla-head {
    margin: 0 14px;
    height: 28px;
    background: #eaeaee;
    line-height: 28px;
    font-weight: 500;
    color: #bcbcc5;
    font-size: 12px;
}

.popAddRoleGroup .popup-con .cla-head ul {
    overflow: hidden;
}

.popAddRoleGroup .popup-con .cla-head ul li {
    display: block;
    float: left;
}

.popAddRoleGroup .popup-con .cla-head ul li:last-child {
    width: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.popAddRoleGroup .popup-con .cla-head ul li:last-child span {
    display: block;
    width: 12px;
    height: 12px;
    background: url(../../../images/cultivation/mooc/checkbox.png) no-repeat center;
    background-size: 12px 12px;
    vertical-align: middle;
    margin-left: 8px;
    margin-right: 8px;
}

.popAddRoleGroup .popup-con .cla-head ul li:last-child span.active {
    background: url(../../../images/cultivation/mooc/manage-selectd.png) no-repeat center;
    background-size: 12px 12px;
    border: none;
}

.popAddRoleGroup .popup-con .cla-head ul li:first-child {
    width: 400px;
    text-indent: 10px;
}

.popAddRoleGroup .popup-con .cla-body {
    overflow: hidden;
    margin: 0 14px;
}

.popAddRoleGroup .popup-con .cla-body .mail-level {
    overflow: hidden;
    border-left: 1px solid #eaeaee;
    border-right: 1px solid #eaeaee;
    height: 417px;
    overflow-y: auto;
}

.popAddRoleGroup .popup-con .cla-body .mail-level .mCSB_scrollTools {
    right: -2px;
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul {
    overflow: hidden;
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul ul {
    display: none;
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul li {
    display: block;
    overflow: hidden;
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul li .manageTitle {
    overflow: hidden;
    border-bottom: 1px solid #f9fafb;
    font-size: 14px;
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul li .manageTitle .manageSelect {
    float: left;
    background: url(../../../images/cultivation/mooc/icon-app-sel.png) no-repeat 50px center;
    background-size: 14px;
    width: 64px;
    height: 28px;
    cursor: pointer;
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul li .manageTitle .manageSelect.manageSelected {
    background: url(../../../images/cultivation/mooc/icon-app-sel1.png) no-repeat 50px center;
    background-size: 14px;
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul li .manageTitle .manageName {
    float: left;
    line-height: 28px;
    width: 390px;
    height: 28px;
    overflow: hidden;
    color: #45454c;
    margin-left: 10px;
    cursor: pointer;
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul li .manageTitle .manageName .arrowRight {
    width: 14px;
    height: 14px;
    float: left;
    background: url("../../../images/cultivation/mooc/mail-arrow1.png") no-repeat center;
    background-size: 14px 14px;
    -webkit-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    transform: rotate(-90deg);
    cursor: pointer;
    margin-top: 7px;
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul li .manageTitle .manageName .arrowRight.slideArrow {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul li .manageTitle .manageName h3 {
    float: left;
    font-size: 12px;
    font-weight: 500;
    max-width: 380px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul li.level1 > .manageTitle > .manageName {
    color: #ff8562;
}

.popAddRoleGroup .popup-con .cla-body .mail-level ul li.level2 > .manageTitle > .manageName h3 {
    margin-left: 14px;
    color: #45454c;
}

.popAddRoleGroup .no-data {
    height: 417px;
    text-align: center;
    line-height: 417px;
    font-size: 14px;
    color: #999;
    display: none;
}

.popAddRoleGroup .popup-btn {
    height: 56px;
    border-top: 1px solid #eaeaee;
    display: flex;
    align-items: center;
    justify-content: center;
}

.popAddRoleGroup .popup-btn span {
    width: 88px;
    height: 26px;
    background: #4a68d1;
    border-radius: 4px;
    color: #ffffff;
    font-weight: 500;
    text-align: center;
    line-height: 26px;
    cursor: pointer;
}

.layui-layer {
    background: unset !important;
    box-shadow: unset !important;
}

.layui-form-radio:hover > *,
.layui-form-radioed,
.layui-form-radioed > i {
    color: #4d88ff !important;
}

.layui-layer-tips {
    width: auto !important;
    max-width: 500px;
}

.layui-layer-tips .layui-layer-content {
    background-color: #4E5969;
    border-radius: 6px;
    box-shadow: unset;
    font-size: 14px;
}

.layui-layer-tips i.layui-layer-TipsB,
.layui-layer-tips i.layui-layer-TipsT {
    left: 6px;
    border-right-color: #4E5969;
    transform: rotate(-90deg);
    bottom: -14px;
}

#handleActivated {
    overflow: hidden;
    display: none;
}

#handleActivated .clone-set,
#handleActivated .join-set {
    overflow: hidden;
}

#handleActivated .layui-form-label {
    width: 140px;
    color: #1D2129;
}

#handleActivated .layui-input-block .layui-input-block {
    margin-left: 154px;
    min-height: 28px;
    display: flex;
    align-items: center;
}

#handleActivated .layui-form-switch {
    margin-top: 0;
    width: 18px;
    height: 12px;
    min-width: 0;
    border-radius: 3px;
    background-color: #D2D3D8;
    margin-right: 8px;
}

#handleActivated .layui-form-switch > i {
    width: 12px;
    height: 10px;
    border-radius: 2px;
    top: 1px;
    left: 1px;
    background-color: #fff;
}

#handleActivated .swtichText {
    color: #C9CDD4;
}

#handleActivated .layui-form-onswitch {
    border-color: #4C87FD;
    background-color: #4C87FD;
}

#handleActivated .layui-form-onswitch > i {
    margin-left: 14px;
}

#handleActivated .course-set {
    margin-bottom: 32px;
}

#handleActivated .course-set .layui-form-label {
    color: #4E5969;
    font-weight: 400;
}

#TeachSet .layui-form-switch {
    margin-top: 0;
    width: 18px;
    height: 12px;
    min-width: 0;
    border-radius: 3px;
    background-color: #D2D3D8;
    margin-right: 8px;
}

#TeachSet .layui-form-switch > i {
    width: 12px;
    height: 10px;
    border-radius: 2px;
    top: 1px;
    left: 1px;
    background-color: #fff;
}

#TeachSet .layui-form-onswitch {
    border-color: #4C87FD;
    background-color: #4C87FD;
}

#TeachSet .layui-form-onswitch > i {
    margin-left: 14px;
}

.item-hide {
    display: none;
}

#classHourSet {
    display: flex;
}

#tbSet {
    display: block;
}

#tbSet .tb-title {
    font-size: 16px;
    color: #1D2129;
    margin: 30px 0 24px;
}

#tbSet .layui-form-switch {
    margin-top: 0;
    width: 18px;
    height: 12px;
    min-width: 0;
    border-radius: 3px;
    background-color: #D2D3D8;
    margin-right: 8px;
}

#tbSet .layui-form-switch > i {
    width: 12px;
    height: 10px;
    border-radius: 2px;
    top: 1px;
    left: 1px;
    background-color: #fff;
}

#tbSet .swtichText {
    color: #C9CDD4;
}

#tbSet .layui-form-onswitch {
    border-color: #4C87FD;
    background-color: #4C87FD;
}

#tbSet .layui-form-onswitch > i {
    margin-left: 14px;
}

.layui-form-checkbox[lay-skin=primary] i {
    width: 14px;
    height: 14px;
}

.layui-form-checkbox[lay-skin=primary] span {
    line-height: 15px;
}

.layui-form-checkbox[lay-skin=primary] {
    padding-left: 25px;
}

.z-nav {
    overflow: hidden;
    height: 60px;
    border-bottom: 1px solid #E8EBF1;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.z-nav ul {
    display: flex;
    align-items: center;
    line-height: 60px;
    margin-left: 30px;
}

.z-nav ul li {
    margin-right: 60px;
    color: #86909C;
    font-size: 16px;
    cursor: pointer;
}

.z-nav ul li:first-child {
    display: block;
}

.z-nav ul li.active {
    color: #1D2129;
    position: relative;
    display: block;
}

.z-nav ul li.active::after {
    content: "";
    width: 60px;
    height: 3px;
    background: #4D88FF;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.z-nav #saveSet {
    width: 96px;
    height: 34px;
    background-color: #4D88FF;
    text-align: center;
    color: #fff;
    border-radius: 6px;
    border: 1px solid #4D88FF;
    margin-right: 60px;
    box-shadow: 0 0 6px 0 rgba(77, 136, 255, 0.4);
    cursor: pointer;
}

.z-main-wrap {
    overflow: hidden;
    background-color: #fff;
    height: calc(100vh - 62px);
    overflow-y: auto;
}

.z-main-wrap .z-main {
    display: none;
}

.z-main-wrap .z-main:first-child {
    display: block;
}

#tipDialog {
    width: 560px;
    background-color: #fff;
    border-radius: 10px;
    padding: 40px 100px;
    box-sizing: border-box;
    display: none;
}

#tipDialog img {
    display: block;
    width: 40px;
    height: 40px;
    margin: 0 auto 24px;
}

#tipDialog p {
    text-align: center;
    line-height: 22px;
    font-size: 16px;
    color: #1D2129;
}

#tipDialog button {
    width: 88px;
    height: 34px;
    border-radius: 18px;
    background: #4D88FF;
    box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
    color: #ffffff;
    outline: none;
    display: block;
    margin: 32px auto 0;
    border: none;
    cursor: pointer;
}

.z-main-wrap .z-main .py-table {
    padding: 24px 30px;
    background-color: #fff;
}

.z-main-wrap .z-main .py-table h3 {
    margin-bottom: 24px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
}

.z-main-wrap .z-main .py-table h3 span {
    padding-left: 7px;
    position: relative;
    font-size: 16px;
    color: #6581ba;
    line-height: 20px;
    margin-right: 6px;
}

.z-main-wrap .z-main .py-table h3 span:after {
    content: '';
    position: absolute;
    left: 0;
    top: 1px;
    width: 3px;
    height: 18px;
    background-color: #6581ba;
    border-radius: 4px;
}

.z-main-wrap .z-main .py-table h3 em {
    width: 16px;
    height: 16px;
    background: url(../images/feedback.png) no-repeat center;
    cursor: pointer;
    position: relative;
}

.z-main-wrap .z-main .py-table h3 em:hover .tip {
    display: block;
}

.z-main-wrap .z-main .py-table h3 em .tip {
    display: none;
    position: absolute;
    left: -44px;
    top: 28px;
    padding: 8px 12px;
    border-radius: 6px;
    background: #4E5969;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
    z-index: 99;
}

.z-main-wrap .z-main .py-table h3 em .tip:after {
    content: '';
    position: absolute;
    left: 48px;
    top: -4px;
    width: 8px;
    height: 8px;
    background-color: #4E5969;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
}

.z-main-wrap .z-main .py-table h3 em .tip i {
    color: #FFFFFF;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    display: block;
    white-space: nowrap;
}

.layui-table-view .layui-table tr th > div {
    color: #8A8B99;
    font-weight: 400;
    font-size: 14px;
}
.layui-table thead tr, .layui-table-header {
    background: #F2F4F7;
    color: #8A8B99;
    font-size: 14px;
}
