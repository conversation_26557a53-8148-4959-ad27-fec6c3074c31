.popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
  display: block;
}
.popup .popup-box {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 752px;
  background-color: #FFFFFF;
  border-radius: 10px;
}
.popup .popup-box .pu-title {
  height: 56px;
  font-size: 16px;
  color: #1D2129;
  line-height: 56px;
  text-align: center;
  border-bottom: 1px solid #E5E6EB;
}
.popup .popup-box .pu-con .pu-tips {
  width: 632px;
  height: 70px;
  border: 1px dashed #6581BA;
  border-radius: 4px;
  background: #E1EBFF;
  margin: 20px auto 0;
  font-size: 14px;
  color: #6581BA;
  line-height: 20px;
  padding: 14px;
  box-sizing: border-box;
}
.popup .popup-box .pu-con form {
  margin: 40px 60px;
  font-size: 14px;
}
.popup .popup-box .pu-con form .form-item {
  display: flex;
  align-items: center;
  position: relative;
  margin: 0 80px 24px;
}
.popup .popup-box .pu-con form .form-item h3 {
  width: 70px;
  text-align: left;
  color: #1D2129;
}
.popup .popup-box .pu-con form .form-item .radioCon {
  display: flex;
  align-items: center;
}
.popup .popup-box .pu-con form .form-item .radioCon li {
  color: #4E5969;
  font-size: 14px;
  padding-left: 24px;
  position: relative;
  line-height: 24px;
  margin-right: 24px;
  cursor: pointer;
}
.popup .popup-box .pu-con form .form-item .radioCon li::after {
  content: "";
  width: 16px;
  height: 16px;
  border: 1px solid #C9CDD4;
  background: #F2F3F5;
  position: absolute;
  left: 0;
  top: 4px;
  border-radius: 50%;
  box-sizing: border-box;
}
.popup .popup-box .pu-con form .form-item .radioCon li.active::before {
  content: "";
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #4C85FA;
  position: absolute;
  left: 3px;
  top: 7px;
  z-index: 9;
}
.popup .popup-box .pu-con form .form-item .radioSelCon {
  display: block;
}
.popup .popup-box .pu-con form .form-item .radioSelCon li {
  margin-bottom: 10px;
  margin-right: 0;
}
.popup .popup-box .pu-con form .form-item .radioSelCon li:last-child {
  margin-bottom: 0;
}
.popup .popup-box .pu-con form .form-item input {
  width: 240px;
  height: 34px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 0 10px;
  font-weight: 400;
  font-size: 14px;
}
.popup .popup-box .pu-con form .form-item input::placeholder {
  color: #86909C;
}
.popup .popup-box .pu-con form .form-item .j-arrow {
  width: 10px;
  height: 10px;
  background: url(../images/icon-arrow1.png) no-repeat center;
  position: absolute;
  right: 10px;
  top: 12px;
}
.popup .popup-box .pu-con form .form-item .j-arrow.j-arrow-slide {
  transform: rotate(180deg);
}
.popup .popup-box .pu-con form .form-item .j-select-year {
  position: absolute;
  top: 36px;
  left: 70px;
  z-index: 9;
  width: 240px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #D5D9E2;
  border-radius: 4px;
  display: none;
  background-color: #fff;
  z-index: 99;
}
.popup .popup-box .pu-con form .form-item .j-select-year.slideShow {
  display: block;
}
.popup .popup-box .pu-con form .form-item .j-select-year ul li {
  line-height: 32px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  cursor: pointer;
}
.popup .popup-box .pu-con form .form-item .j-select-year ul li.active,
.popup .popup-box .pu-con form .form-item .j-select-year ul li:hover {
  background: #E1EBFF;
}
.popup .popup-box .pu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70px;
  border-top: 1px solid #E5E6EB;
}
.popup .popup-box .pu-btn button {
  width: 88px;
  height: 36px;
  font-size: 14px;
  border-radius: 18px;
  cursor: pointer;
}
.popup .popup-box .pu-btn button.pu-cancel {
  border: 1px solid #C9CDD4;
  color: #4E5969;
  background-color: #fff;
  margin-right: 16px;
}
.popup .popup-box .pu-btn button.pu-sure {
  color: #fff;
  background: #4C85FA;
  border: 1px solid #4C85FA;
  box-shadow: 0px 0px 10px rgba(90, 177, 118, 0.3);
}
