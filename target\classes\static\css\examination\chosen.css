/* @group Base */
.chosen-container {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 14px;
  zoom: 1;
  *display: inline;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  margin:0 auto;
  width: 200px;
}
.chosen-container * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.chosen-container .chosen-drop {
  position: absolute;
  top: 110%;
  left: -9999px;
  z-index: 1010;
  width: 100%;
  border: 1px solid #e7eaf1;
  background: #fff;
  box-shadow:rgba(153,153,153,.2) 0 1px 6px;
}
.chosen-container.chosen-with-drop .chosen-drop {
  left: 0;border-color:#e7eaf1
}
.chosen-container a {
  cursor: pointer;
}

/* @end */
/* @group Single Chosen */
.chosen-container-single .chosen-single {
  position: relative;
  display: block;
  overflow: hidden;
  padding: 0 0 0 9px;
  height: 32px;
  border: 1px solid #e7e9f0;
  border-radius: 3px;
  background-color: #fff;
  background-clip: padding-box;
  color: #333;
  text-decoration: none;
  white-space: nowrap;
  line-height: 30px;
  font-size: 14px;
}
.chosen-container-single .chosen-default {
  color:#8F97A8;
}
.chosen-container-single .chosen-single span {
  display: block;
  overflow: hidden;
  margin-right: 20px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.chosen-container-single .chosen-single-with-deselect span {
  margin-right: 38px;
}
/*.chosen-container-single .chosen-single abbr {
  position: absolute;
  top: 6px;
  right: 26px;
  display: block;
  width: 12px;
  height: 12px;
  background: url('chosen-sprite.png') -42px 1px no-repeat;
  font-size: 1px;
}*/
/*.chosen-container-single .chosen-single abbr:hover {
  background-position: -42px -10px;
}
.chosen-container-single.chosen-disabled .chosen-single abbr:hover {
  background-position: -42px -10px;
}*/
.chosen-container-single .chosen-single div {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: 20px;
  height: 100%;
}
.chosen-container-single .chosen-single div b {
  display: block;
  width: 12px;
  height: 100%;
  background: url(/images/examination/select_arrow.png) no-repeat center center;
  -webkit-transform:rotate(0deg); -moz-transform:rotate(0deg); -ms-transform:rotate(0deg); -o-transform:rotate(0deg);
	-webkit-transition:all .2s linear; -moz-transition:all .2s linear; -ms-transition:all .2s linear; -o-transition:all .2s linear; transition:all .2s linear;
}
.chosen-container-single .chosen-search {
  position: relative;
  z-index: 1010;
  margin: 0;
  padding: 10px;
  white-space: nowrap;
}
.chosen-container-single .chosen-search input[type="text"] {
  margin: 1px 0;
  padding: 6px 30px 6px 10px;
  width: 100%;
  height:32px;
  line-height:32px;
  outline: 0;
  border: 0;
  background:#f5f8fb url('/images/examination/selSearch.png') no-repeat right center;
  font-size: 1em;
  font-family: sans-serif;
  line-height: normal;
  border-radius: 32px;
}
/*.chosen-container-single .chosen-search input[type="text"]:focus{
  border:1px solid #1bbfd5;
}*/
.chosen-container-single .chosen-drop {
  margin-top: -1px;
  border-radius: 0 0 4px 4px;
  background-clip: padding-box;
}
.chosen-container-single.chosen-container-single-nosearch .chosen-search {
  position: absolute;
  left: -9999px;
}

/* @end */
/* @group Results */
.chosen-container .chosen-results {
  color: #444;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  max-height:216px;
  overflow-y:auto;
}
.chosen-container .chosen-results::-webkit-scrollbar{ width: 8px;}
.chosen-container .chosen-results::-webkit-scrollbar-track{ background-color: #f5f5f5; -webkit-box-shadow:inset 0 0 3px rgba(0,0,0,0.1); border-radius:5px;}
.chosen-container .chosen-results::-webkit-scrollbar-thumb{ background-color: rgba(0, 0, 0, 0.2); border-radius: 5px;}
.chosen-container .chosen-results::-webkit-scrollbar-button{ background-color: #eee; display: none;}
.chosen-container .chosen-results::-webkit-scrollbar-corner{ background-color: black;}

.chosen-container .chosen-results li {
  display: none;
  padding: 8px 10px;
  list-style: none;
  line-height: 20px;
  word-wrap: break-word;
  -webkit-touch-callout: none;
}
.chosen-container .chosen-results li.active-result {
  display: list-item;
  cursor: pointer;
}
.chosen-container .chosen-results li.disabled-result {
  display: list-item;
  color: #ccc;
  cursor: default;
}
.chosen-container .chosen-results li.highlighted {
  background-color: #f5f8fb;
  color:#4C88FF;
}
.chosen-container .chosen-results li.result-selected {
  background-color: #F4F7FC;
  color:#4C88FF;
}
.chosen-container .chosen-results li.no-results {
  color: #777;
  display: list-item;
  background: #f4f4f4;
}
.chosen-container .chosen-results li.group-result {
  display: list-item;
  font-weight: bold;
  cursor: default;
}
.chosen-container .chosen-results li.group-option {
  padding-left: 15px;
}
.chosen-container .chosen-results li em {
  font-style: normal;
  text-decoration: underline;
}

/* @end */
/* @group Multi Chosen */
.chosen-container-multi .chosen-choices {
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0 5px;
  width: 100%;
  height: auto !important;
  height: 1%;
  border: 1px solid #ddd;
  background-color: #fff;
  cursor: text;
}
.chosen-container-multi .chosen-choices li {
  float: left;
  list-style: none;
}
.chosen-container-multi .chosen-choices li.search-field {
  margin: 0;
  padding: 0;
  white-space: nowrap;
}
.chosen-container-multi .chosen-choices li.search-field input[type="text"] {
  margin: 1px 0;
  padding: 0;
  height: 25px;
  outline: 0;
  border: 0 !important;
  background: transparent !important;
  box-shadow: none;
  color: #999;
  font-size: 100%;
  font-family: sans-serif;
  line-height: normal;
  border-radius: 0;
}
.chosen-container-multi .chosen-choices li.search-choice {
  position: relative;
  margin: 3px 5px 3px 0;
  padding: 3px 20px 3px 5px;
  border: 1px solid #aaa;
  max-width: 100%;
  border-radius: 3px;
  background-color: #ddd;
  background-size: 100% 19px;
  background-repeat: repeat-x;
  background-clip: padding-box;
  /*box-shadow: 0 0 2px white inset, 0 1px 0 rgba(0, 0, 0, 0.05);*/
  color: #333;
  line-height: 13px;
  cursor: default;
}
.chosen-container-multi .chosen-choices li.search-choice span {
  word-wrap: break-word;
}
.chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
  position: absolute;
  top: 4px;
  right: 3px;
  display: block;
  width: 12px;
  height: 12px;
  /*background: url('chosen-sprite.png') -42px 1px no-repeat;*/
  font-size: 1px;
}
.chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover {
  background-position: -42px -10px;
}
.chosen-container-multi .chosen-choices li.search-choice-disabled {
  padding-right: 5px;
  border: 1px solid #ddd;
  background-color: #e4e4e4;
  color: #666;
}
.chosen-container-multi .chosen-choices li.search-choice-focus {
  background: #ddd;
}
.chosen-container-multi .chosen-choices li.search-choice-focus .search-choice-close {
  background-position: -42px -10px;
}
.chosen-container-multi .chosen-results {
  margin: 0;
  padding: 0;
}
.chosen-container-multi .chosen-drop .result-selected {
  display: list-item;
  color: #ccc;
  cursor: default;
}

/* @end */
/* @group Active  */
.chosen-container-active .chosen-single {
  border: 1px solid #ddd;
  /*box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);*/
}
.chosen-container-active.chosen-with-drop .chosen-single {
  border: 1px solid #ddd;
  box-shadow: 0 1px 0 #fff inset;
  border-color:#4C88FF
}
.chosen-container-active.chosen-with-drop .chosen-single div {
  border-left: none;
  background: transparent;
}
.chosen-container-active.chosen-with-drop .chosen-single div b {
  -webkit-transform:rotate(180deg); -moz-transform:rotate(180deg); -ms-transform:rotate(180deg); -o-transform:rotate(180deg);
}
.chosen-container-active .chosen-choices {
  border: 1px solid #ddd;
  /*box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);*/
}
.chosen-container-active .chosen-choices li.search-field input[type="text"] {
  color: #222 !important;
}

/* @end */
/* @group Disabled Support */
.chosen-disabled {
  opacity: 0.5 !important;
  cursor: default;
}
.chosen-disabled .chosen-single {
  cursor: default;
}
.chosen-disabled .chosen-choices .search-choice .search-choice-close {
  cursor: default;
}

/* @end */
/* @group Right to Left */
.chosen-rtl {
  text-align: right;
}
.chosen-rtl .chosen-single {
  overflow: visible;
  padding: 0 8px 0 0;
}
.chosen-rtl .chosen-single span {
  margin-right: 0;
  margin-left: 26px;
  direction: rtl;
}
.chosen-rtl .chosen-single-with-deselect span {
  margin-left: 38px;
}
.chosen-rtl .chosen-single div {
  right: auto;
  left: 3px;
}
.chosen-rtl .chosen-single abbr {
  right: auto;
  left: 26px;
}
.chosen-rtl .chosen-choices li {
  float: right;
}
.chosen-rtl .chosen-choices li.search-field input[type="text"] {
  direction: rtl;
}
.chosen-rtl .chosen-choices li.search-choice {
  margin: 3px 5px 3px 0;
  padding: 3px 5px 3px 19px;
}
.chosen-rtl .chosen-choices li.search-choice .search-choice-close {
  right: auto;
  left: 4px;
}
.chosen-rtl.chosen-container-single-nosearch .chosen-search,
.chosen-rtl .chosen-drop {
  left: 9999px;
}
.chosen-rtl.chosen-container-single .chosen-results {
  margin: 0 0 4px 4px;
  padding: 0 4px 0 0;
}
.chosen-rtl .chosen-results li.group-option {
  padding-right: 15px;
  padding-left: 0;
}
.chosen-rtl.chosen-container-active.chosen-with-drop .chosen-single div {
  border-right: none;
}
.chosen-rtl .chosen-search input[type="text"] {
  padding: 4px 5px 4px 20px;
  background: white url('/images/examination/chosen-sprite.png') no-repeat -30px -20px;
  background: url('/images/examination/chosen-sprite.png') no-repeat -30px -20px;
  direction: rtl;
}
/*.chosen-rtl.chosen-container-single .chosen-single div b {
  background-position: 6px 2px;
}
.chosen-rtl.chosen-container-single.chosen-with-drop .chosen-single div b {
  background-position: -12px 2px;
}*/

/* @end */
/* @group Retina compatibility */
@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 144dpi) {
  .chosen-rtl .chosen-search input[type="text"],
  .chosen-container-single .chosen-single abbr,
  .chosen-container-single .chosen-single div b,
  .chosen-container-single .chosen-search input[type="text"],
  .chosen-container-multi .chosen-choices .search-choice .search-choice-close,
  .chosen-container .chosen-results-scroll-down span,
  .chosen-container .chosen-results-scroll-up span {
    background-image: url('/images/examination/<EMAIL>') !important;
    background-size: 52px 37px !important;
    background-repeat: no-repeat !important;

  }
}
/* @end */
