.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.flex {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
body {
  background-color: #F7F8FA;
  padding: 20px;
  font-size: 14px;
  color: #4e5969;
}
.hide {
  display: none !important;
}
.main {
  width: calc(100vw - 40px);
  min-height: calc(100vh - 40px);
  background: #FFFFFF;
  border-radius: 8px;
  position: relative;
}
.main .top {
  width: 100%;
  height: 60px;
  border-bottom: 1px solid #E8EBF1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0 30px;
  justify-content: space-between;
}
.main .top .ws-box {
  display: flex;
  display: -webkit-flex;
  justify-content: flex-start;
  align-items: center;
}
.main .top .search {
  border-radius: 4px;
  width: 255px;
  height: 36px;
  background-color: #F1F3F6;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-left: 24px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0 10px;
  position: relative;
}
.main .top .search .span1 {
  width: 16px;
  height: 16px;
  background: url(../images/search-icons.png) no-repeat center;
  background-size: 16px;
  flex-shrink: 0;
  margin-right: 4px;
}
.main .top .search input {
  outline: none;
  background-color: transparent;
  border: none;
  flex: 1;
  font-size: 14px;
}
.main .top .search .select-dropdown {
  display: none;
  position: absolute;
  left: 0;
  right: 0;
  top: 40px;
  z-index: 999;
  background: #FFF;
  border-radius: 4px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  width: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
}
.main .top .search .select-dropdown ul {
  overflow: hidden;
  border-radius: 4px;
  max-height: 360px;
  overflow-y: auto;
}
.main .top .search .select-dropdown ul li {
  padding: 10px 20px;
  min-height: 40px;
  cursor: pointer;
  font-size: 14px;
  color: #4e5969;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
}
.main .top .search .select-dropdown ul li em {
  padding: 0 6px;
  font-size: 12px;
  height: 18px;
  line-height: 18px;
  border-radius: 4px;
  color: #fff;
  flex-shrink: 0;
}
.main .top .search .select-dropdown ul li em.grade {
  background-color: #B2BAFF;
}
.main .top .search .select-dropdown ul li em.room {
  background-color: #8ED66B;
}
.main .top .search .select-dropdown ul li em.teacher {
  background-color: #FFD166;
}
.main .top .search .select-dropdown ul li span {
  margin-left: 8px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .top .search .select-dropdown ul li span p {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .top .search .select-dropdown ul li:hover {
  background-color: #e1ebff;
  color: #4d88ff;
}
.main .top .weeks {
  position: relative;
}
.main .top .weeks .w-name {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  height: 28px;
  line-height: 28px;
}
.main .top .weeks .w-name span {
  color: #4d88ff;
}
.main .top .weeks .w-name .detail {
  font-size: 14px;
  color: #4d88ff;
  margin-left: 4px;
}
.main .top .weeks:hover .w-con {
  display: block;
}
.main .top .weeks .w-con {
  display: none;
  position: absolute;
  z-index: 1000;
  left: 0;
  top: 28px;
  width: 500px;
  height: auto;
  border-radius: 4px;
  background: #FFF;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 12px;
  padding-bottom: 8px;
}
.main .top .weeks .w-con .wc-top {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: space-between;
  padding-bottom: 12px;
  border-bottom: 1px solid #E5E6EB;
  height: 20px;
}
.main .top .weeks .w-con .wc-top .all {
  font-size: 14px;
  color: #4d88ff;
  cursor: pointer;
}
.main .top .weeks .w-con .wc-top .title {
  font-size: 16px;
  color: #1d2129;
}
.main .top .weeks .w-con .wc-top .cancle {
  font-size: 14px;
  color: #4d88ff;
  cursor: pointer;
}
.main .top .weeks .w-con .wc-main {
  padding-bottom: 0px;
  padding-top: 8px;
}
.main .top .weeks .w-con .wc-main ul {
  overflow: hidden;
}
.main .top .weeks .w-con .wc-main ul li {
  float: left;
  cursor: pointer;
  width: 92px;
  height: 42px;
  border-radius: 4px;
  background: #F7F8FA;
  margin-right: 4px;
  margin-bottom: 4px;
  display: flex;
  display: -webkit-flex;
  justify-content: flex-start;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  flex-wrap: wrap;
}
.main .top .weeks .w-con .wc-main ul li:hover {
  background-color: #e1ebff;
}
.main .top .weeks .w-con .wc-main ul li.cur {
  background-color: #4d88ff;
}
.main .top .weeks .w-con .wc-main ul li.cur h4 {
  color: #fff;
}
.main .top .weeks .w-con .wc-main ul li.cur p {
  color: #fff;
}
.main .top .weeks .w-con .wc-main ul li h4 {
  font-size: 14px;
  height: 14px;
  color: #4e5969;
  margin-bottom: 2px;
}
.main .top .weeks .w-con .wc-main ul li p {
  font-size: 10px;
  color: #4e5969;
}
.main .top .weeks .w-con .wc-main ul li:nth-child(5n) {
  margin-right: 0;
}
.main .top .step-box {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  width: 40%;
}
.main .top .step-box .step {
  flex: 1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  background: url(../images/right-icon.png) no-repeat right center;
  justify-content: center;
  cursor: pointer;
}
.main .top .step-box .step em {
  width: 24px;
  height: 24px;
  border-radius: 32px;
  background-color: #f1f3f6;
  color: #4e5969;
  font-size: 16px;
  text-align: center;
  line-height: 24px;
  margin-right: 10px;
}
.main .top .step-box .step span {
  font-size: 14px;
  color: #1d2129;
}
.main .top .step-box .step:last-child {
  background: none;
}
.main .top .step-box .step.active {
  position: relative;
}
.main .top .step-box .step.active:after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -18px;
  height: 2px;
  background-color: #4d88ff;
}
.main .top .step-box .step.active em {
  background-color: #4d88ff;
  color: #fff;
}
.main .top .step-box .step.active span {
  font-size: 16px;
  color: #1d2129;
}
.main .top .oprate {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .top .oprate .mode-switch {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 34px;
  border-right: 1px solid #e8ebf1;
  padding-right: 16px;
  margin-right: 16px;
}
.main .top .oprate .mode-switch .name {
  font-size: 14px;
  color: #1d2129;
  margin-right: 14px;
}
.main .top .oprate .mode-switch .switc-con {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.main .top .oprate .mode-switch .switc-con .switch {
  position: relative;
  width: 28px;
  height: 14px;
  background: #cdcece;
  border-radius: 4px;
}
.main .top .oprate .mode-switch .switc-con .switch span {
  width: 12px;
  height: 10px;
  position: absolute;
  top: 2px;
  left: 2px;
  background-color: #FFFFFF;
  border-radius: 2px;
}
.main .top .oprate .mode-switch .switc-con .switch.switch-open {
  background: #4D88FF;
}
.main .top .oprate .mode-switch .switc-con .switch.switch-open span {
  left: unset;
  right: 2px;
}
.main .top .oprate .mode-switch .switc-con .switch-con {
  color: #C9CDD4;
  margin-left: 10px;
}
.main .top .oprate .btn-list {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .top .oprate .btn-list .before-pub {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 24px;
}
.main .top .oprate .btn-list .after-pub {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 24px;
}
.main .top .oprate .btn-list .t-schedule {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 24px;
  cursor: pointer;
  position: relative;
  height: 36px;
}
.main .top .oprate .btn-list .t-schedule:last-child {
  margin-right: 0;
}
.main .top .oprate .btn-list .t-schedule span {
  display: block;
  margin-right: 4px;
  color: #4d88ff;
}
.main .top .oprate .btn-list .t-schedule em {
  font-size: 14px;
  color: #4d88ff;
}
.main .top .oprate .btn-list .t-schedule:hover .select-dropdown {
  display: block;
}
.main .top .oprate .btn-list .t-schedule.qxfb span {
  color: #f76560;
}
.main .top .oprate .btn-list .t-schedule.qxfb em {
  color: #f76560;
}
.main .top .oprate .btn-list .t-schedule .select-dropdown {
  display: none;
  position: absolute;
  right: 0px;
  top: 36px;
  background: #FFF;
  border-radius: 4px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  width: 144px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  color: #4d88ff;
  z-index: 999;
}
.main .top .oprate .btn-list .t-schedule .select-dropdown ul {
  overflow: hidden;
  border-radius: 4px;
}
.main .top .oprate .btn-list .t-schedule .select-dropdown ul li {
  padding: 4px 20px;
  height: 34px;
  cursor: pointer;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .top .oprate .btn-list .t-schedule .select-dropdown ul li span {
  color: #4d88ff;
  display: block;
  margin-right: 4px;
}
.main .top .oprate .btn-list .t-schedule .select-dropdown ul li em {
  font-size: 14px;
  color: #4d88ff;
}
.main .top .oprate .btn-list .t-schedule .select-dropdown ul li:hover {
  background-color: #e1ebff;
}
.main .top .oprate .btn-list .t-schedule .select-dropdown:after {
  content: '';
  position: absolute;
  right: 22px;
  top: -6px;
  width: 12px;
  height: 6px;
  background: url(../images/tringle-icon.png) no-repeat center;
}
.main .con {
  position: relative;
  overflow: hidden;
  z-index: 1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
}
.main .con .c-left {
  position: relative;
  width: 268px;
  flex-shrink: 0;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.main .con .c-left .cl-con {
  padding: 30px 0;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  overflow: hidden;
  min-height: calc(100vh - 100px);
  position: relative;
  background-color: #fff;
  z-index: 999;
}
.main .con .c-left .cl-con .tab {
  width: 208px;
  margin: 0 30px;
  height: 36px;
  border-radius: 18px;
  background-color: #F1F3F6;
  margin-bottom: 24px;
}
.main .con .c-left .cl-con .tab ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 36px;
}
.main .con .c-left .cl-con .tab ul li {
  height: 100%;
  flex: 1;
  font-size: 14px;
  color: #4e5969;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
}
.main .con .c-left .cl-con .tab ul li.cur {
  background-color: #4D88FF;
  color: #fff;
  border-radius: 18px;
}
.main .con .c-left .shake-hands {
  width: 16px;
  height: 48px;
  border-radius: 0 4px 4px 0;
  background-color: #fff;
  position: absolute;
  top: 50%;
  margin-top: -25px;
  right: -16px;
  text-align: center;
  line-height: 48px;
  z-index: 998;
  cursor: pointer;
  box-shadow: 1px 0px 4px rgba(0, 0, 0, 0.2);
}
.main .con .c-left .shake-hands span {
  display: inline-block;
  font-size: 12px;
  color: #4d88ff;
  position: relative;
}
.main .con .c-left .shake-hands em {
  display: none;
  position: absolute;
  right: -92px;
  top: 5px;
  width: auto;
  height: 38px;
  border-radius: 6px;
  background: #4E5969;
  padding: 8px 12px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 22px;
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
}
.main .con .c-left .shake-hands em:after {
  content: '';
  position: absolute;
  top: 16px;
  left: -9px;
  width: 12px;
  transform: rotate(-90deg);
  -webkit-transform: rotate(-90deg);
  height: 6px;
  background: url(../images/black-tringle-icon.png) no-repeat center;
}
.main .con .c-left .shake-hands.cur em {
  display: block;
}
.main .con .c-left .shake-hands.active span {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.main .con .c-left .search {
  border-radius: 4px;
  border: 1px solid #E5E6EB;
  background: #FFF;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 36px;
  margin: 0 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0 10px;
  margin-bottom: 24px;
}
.main .con .c-left .search span {
  width: 16px;
  height: 16px;
  background: url(../images/search-icons.png) no-repeat center;
  background-size: 16px;
  flex-shrink: 0;
  margin-right: 4px;
}
.main .con .c-left .search input {
  outline: none;
  background-color: transparent;
  border: none;
  flex: 1;
}
.main .con .c-left .schedule-list {
  overflow-y: auto;
  overflow-x: hidden;
  margin: 0 30px;
}
.main .con .c-left .schedule-list ul li {
  width: 100%;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  color: #1d2129;
  font-size: 14px;
  line-height: 48px;
  cursor: pointer;
}
.main .con .c-left .schedule-list ul li h3 {
  padding: 0 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .con .c-left .schedule-list ul li .manageName {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 48px;
}
.main .con .c-left .schedule-list ul li .manageName:hover {
  background: #E1EBFF !important;
  color: #1d2129;
  border-radius: 4px;
  color: #4d88ff;
}
.main .con .c-left .schedule-list ul li .manageName:hover .arrowRight {
  color: #4d88ff;
}
.main .con .c-left .schedule-list ul li .manageName.cur {
  border-radius: 4px;
  background: #4D88FF !important;
  color: #ffffff;
}
.main .con .c-left .schedule-list ul li .manageName.cur .arrowRight {
  color: #fff;
}
.main .con .c-left .schedule-list ul li .manageName h3 {
  flex: 1;
}
.main .con .c-left .schedule-list ul li .manageName .arrowRight {
  width: 20px;
  margin-right: 16px;
  flex-shrink: 0;
  font-size: 20px;
  color: #c9cdd4;
  transform: rotate(90deg);
  cursor: pointer;
}
.main .con .c-left .schedule-list ul li .manageName .arrowRight.slideArrow {
  transform: rotate(0deg);
}
.main .con .c-left .schedule-list ul li.level2 .manageName h3 {
  padding-left: 40px;
}
.main .con .c-left .schedule-list ul li.level3 .manageName h3 {
  padding-left: 60px;
}
.main .con .c-left .schedule-list ul li.level4 .manageName h3 {
  padding-left: 80px;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n) .manageName {
  border-radius: 4px;
  background: #FAFBFC;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n) .manageName.cur {
  background: #4D88FF !important;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n) .level2 .manageName {
  border-radius: 4px;
  background: #fff !important;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n) .level2 .manageName.cur {
  background: #4D88FF !important;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n) .level2:nth-child(2n) .manageName {
  border-radius: 4px;
  background: #FAFBFC !important;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n) .level2:nth-child(2n) .manageName.cur {
  background: #4D88FF !important;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n+1) .manageName {
  border-radius: 4px;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n+1) .manageName.cur {
  background: #4D88FF !important;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n+1) .level2 .manageName {
  border-radius: 4px;
  background: #fff !important;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n+1) .level2 .manageName.cur {
  background: #4D88FF !important;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n+1) .level2:nth-child(2n+1) .manageName {
  border-radius: 4px;
  background: #FAFBFC !important;
}
.main .con .c-left .schedule-list ul li.level1:nth-child(2n+1) .level2:nth-child(2n+1) .manageName.cur {
  background: #4D88FF !important;
}
.main .con .c-right {
  flex: 1;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 20px 30px;
  border-radius: 0px 0px 8px 0px;
  background: #E1EBFF;
}
.main .con .c-right .c-homeWrap {
  overflow-x: hidden;
  flex: 899;
  width: 0;
}
.main .con .c-right .c-homeWrap .gauge-outfit {
  margin-bottom: 4px;
  border-radius: 4px;
  background: #4D88FF;
  width: 100%;
  height: 44px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 8px 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: space-between;
  align-items: flex-start;
}
.main .con .c-right .c-homeWrap .gauge-outfit .title {
  font-size: 14px;
  line-height: 28px;
  color: #ffffff;
}
.main .con .c-right .c-homeWrap .oprate {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .con .c-right .c-homeWrap .oprate .switchs {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 28px;
  margin-right: 24px;
}
.main .con .c-right .c-homeWrap .oprate .switchs.allow-conflicts {
  margin-right: 0;
  padding-right: 16px;
  border-right: 1px solid #E8EBF1;
}
.main .con .c-right .c-homeWrap .oprate .switchs .name {
  font-size: 14px;
  color: #fff;
  margin-right: 14px;
}
.main .con .c-right .c-homeWrap .oprate .switchs .switc-con {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.main .con .c-right .c-homeWrap .oprate .switchs .switc-con .switch {
  position: relative;
  width: 28px;
  height: 14px;
  background: #cdcece;
  border-radius: 4px;
}
.main .con .c-right .c-homeWrap .oprate .switchs .switc-con .switch em {
  width: 12px;
  height: 10px;
  position: absolute;
  top: 2px;
  left: 2px;
  background-color: #FFFFFF;
  border-radius: 2px;
}
.main .con .c-right .c-homeWrap .oprate .switchs .switc-con .switch span {
  position: absolute;
  top: 1px;
  right: 3px;
  color: #ffffff;
  font-size: 8px;
}
.main .con .c-right .c-homeWrap .oprate .switchs .switc-con .switch.switch-open {
  background: #ffffff;
}
.main .con .c-right .c-homeWrap .oprate .switchs .switc-con .switch.switch-open em {
  position: absolute;
  top: 2px;
  right: 2px;
  left: auto;
  background-color: #4D88FF;
}
.main .con .c-right .c-homeWrap .oprate .switchs .switc-con .switch.switch-open span {
  position: absolute;
  top: 1px;
  left: 3px;
  color: #4D88FF;
}
.main .con .c-right .c-homeWrap .oprate .switchs .switc-con .switch-con {
  color: #C9CDD4;
  margin-left: 10px;
}
.main .con .c-right .c-homeWrap .oprate .btn-list {
  padding-left: 16px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice {
  margin-right: 16px;
  position: relative;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice.selected .select-dropdown {
  display: block;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .select-dropdown {
  display: none;
  position: absolute;
  right: -22px;
  top: 28px;
  z-index: 999;
  background: #FFF;
  border-radius: 4px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  width: 158px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .select-dropdown ul {
  overflow: hidden;
  border-radius: 4px;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .select-dropdown ul li {
  padding: 4px 20px;
  height: 34px;
  cursor: pointer;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .select-dropdown ul li span {
  color: #4d88ff;
  display: block;
  margin-right: 4px;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .select-dropdown ul li em {
  font-size: 14px;
  color: #4e5969;
  width: 96px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .select-dropdown ul li:hover {
  background-color: #e1ebff;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .select-dropdown ul li:hover em {
  color: #4d88ff;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .select-dropdown:after {
  content: '';
  position: absolute;
  right: 24px;
  top: -6px;
  width: 12px;
  height: 6px;
  z-index: 999;
  background: url(../images/tringle-icon.png) no-repeat center;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .btn {
  position: relative;
  width: 16px;
  display: block;
  height: 16px;
  background: url(../images/screen-icon.png) no-repeat center;
  cursor: pointer;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .btn:hover em {
  display: block;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .btn em {
  display: none;
  position: absolute;
  right: -22px;
  top: 28px;
  width: auto;
  height: 38px;
  border-radius: 6px;
  background: #4E5969;
  padding: 8px 12px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 22px;
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .rice .btn em:after {
  content: '';
  position: absolute;
  top: -5px;
  right: 24px;
  width: 12px;
  height: 6px;
  background: url(../images/black-tringle-icon.png) no-repeat center;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .clean-up span {
  position: relative;
  width: 16px;
  height: 16px;
  display: block;
  cursor: pointer;
  background: url(../images/clear-icon1.png) no-repeat center;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .clean-up span:hover em {
  display: block;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .clean-up span em {
  display: none;
  position: absolute;
  right: -22px;
  top: 28px;
  width: auto;
  height: 38px;
  border-radius: 6px;
  background: #4E5969;
  padding: 8px 12px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 22px;
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
}
.main .con .c-right .c-homeWrap .oprate .btn-list .clean-up span em:after {
  content: '';
  position: absolute;
  top: -5px;
  right: 24px;
  width: 12px;
  height: 6px;
  background: url(../images/black-tringle-icon.png) no-repeat center;
}
.main .con .c-right .c-table .thead {
  margin-bottom: 4px;
}
.main .con .c-right .c-table .thead.pdr {
  padding-right: 6px;
}
.main .con .c-right .c-table .thead.pdr17 {
  padding-right: 17px;
}
.main .con .c-right .c-table .thead.c-table-head ul li:last-child {
  width: 92px !important;
  flex-shrink: 0 !important;
  flex: none;
}
.main .con .c-right .c-table .thead ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .con .c-right .c-table .thead ul li {
  flex: 1;
  color: #4e5969;
  font-size: 14px;
  margin-right: 4px;
  border-radius: 4px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  background-color: #BEDAFF;
}
.main .con .c-right .c-table .thead ul li:first-child {
  flex: 0 0 30px;
  flex-shrink: 0;
}
.main .con .c-right .c-table .thead ul li:last-child {
  margin-right: 0;
}
.main .con .c-right .c-table .tbody.m-tbody {
  height: calc(100vh - 228px);
  overflow-y: auto;
}
.main .con .c-right .c-table .tbody.m-tbody .lock-in {
  background-color: #F1F3F6;
  border-color: #F1F3F6;
  cursor: default;
}
.main .con .c-right .c-table .tbody.m-tbody .lock-in:hover {
  border-color: #F1F3F6;
}
.main .con .c-right .c-table .tbody.m-tbody .disabled {
  background-color: #C9CDD4;
  border-color: #C9CDD4;
  cursor: default;
}
.main .con .c-right .c-table .tbody.m-tbody .disabled:hover {
  border-color: #C9CDD4;
}
.main .con .c-right .c-table .tbody.m-tbody.activeing .active {
  background-color: #4D88FF;
  border-color: #4D88FF;
}
.main .con .c-right .c-table .tbody.m-tbody.activeing .active .name {
  color: #fff;
}
.main .con .c-right .c-table .tbody.m-tbody.activeing .active .teacher {
  color: #fff;
}
.main .con .c-right .c-table .tbody.m-tbody.activeing .active .classroom {
  color: #fff;
}
.main .con .c-right .c-table .tbody.m-tbody ul li {
  min-height: 80px;
}
.main .con .c-right .c-table .tbody.m-tbody ul li span {
  padding: 0 3px;
}
.main .con .c-right .c-table .tbody ul {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 4px;
}
.main .con .c-right .c-table .tbody ul.carve-up {
  height: 4px;
  background: url(../images/line-bg.png) repeat-x;
}
.main .con .c-right .c-table .tbody ul li {
  flex: 1;
  border-radius: 4px;
  margin-right: 4px;
  color: #4e5969;
  font-size: 14px;
  font-weight: 400;
  height: auto;
  width: 0;
  cursor: pointer;
}
.main .con .c-right .c-table .tbody ul li .select-area {
  width: 100%;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-content: center;
  padding: 7px 15px;
  border: 1px solid #fff;
  background-color: #fff;
  position: relative;
  border-radius: 4px;
  height: 80px;
}
.main .con .c-right .c-table .tbody ul li .select-area:hover {
  border: 1px solid #4D88FF;
}
.main .con .c-right .c-table .tbody ul li .select-area:hover .oprate {
  display: flex;
}
.main .con .c-right .c-table .tbody ul li .select-area:hover .tage em {
  display: block;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  align-items: flex-start;
  position: absolute;
  right: 4px;
  line-height: 16px;
  font-size: 10px;
  color: #FF6B6B;
  border-radius: 4px;
  height: 58px;
  top: 4px;
  z-index: 99;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage .em-list:last-child {
  margin-left: 4px;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em {
  display: none;
  width: auto;
  height: 16px;
  padding: 0 4px;
  border-radius: 4px;
  line-height: 16px;
  font-size: 12px;
  color: #FFFFFF;
  margin-bottom: 1px;
  white-space: nowrap;
  position: relative;
  z-index: 199;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em.normal {
  display: block;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em i {
  font-size: 11px;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em.c1 {
  background: #FA9191;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em.c2 {
  background: #8ED66B;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em.c3 {
  background: #6ECBFA;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em.c4 {
  background: #7EA4FC;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em.c5 {
  background: #FFB866;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em.c6 {
  background: #E5A1CF;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em.c7 {
  background: #A67EF0;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em:hover .tips {
  display: block;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em .tips {
  display: none;
  position: fixed;
  z-index: 9999;
  transform: translate(-100%, -44px);
  transform: -webkit-translate(-100%, -44px);
  margin-left: 32px;
  padding-left: 4px;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em .tips .tts {
  background: rgba(24, 30, 51, 0.8);
  border-radius: 4px;
  padding: 10px 16px;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  color: #FFFFFF;
  max-width: 236px;
  height: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage em .tips:after {
  content: '';
  position: absolute;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  bottom: -5px;
  right: 10px;
  width: 12px;
  height: 6px;
  background: url(../images/black-tringle-icon.png) no-repeat center;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate {
  position: absolute;
  right: 5px;
  bottom: 11px;
  z-index: 99;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  display: none;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate span {
  font-size: 16px;
  height: 16px;
  line-height: 16px;
  color: #4d88ff;
  margin-left: 4px;
  cursor: pointer;
  position: relative;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate span.cur:hover em {
  display: block;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate span em {
  display: none;
  position: absolute;
  right: -22px;
  top: -48px;
  width: auto;
  padding: 8px 12px;
  line-height: 22px;
  border-radius: 6px;
  background: #4E5969;
  text-align: center;
  white-space: nowrap;
  font-size: 14px;
  color: #ffffff;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate span em:last-child {
  right: -5px;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate span em:last-child:after {
  right: 10px;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate span em:after {
  content: '';
  position: absolute;
  bottom: -4px;
  right: 24px;
  width: 12px;
  height: 6px;
  background: url(../images/black-tringle-icon.png) no-repeat center;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.main .con .c-right .c-table .tbody ul li .select-area .name {
  font-weight: 500;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
.main .con .c-right .c-table .tbody ul li .select-area .teacher {
  font-weight: 400;
  width: 100%;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .con .c-right .c-table .tbody ul li .select-area .teacher.self {
  color: #4d88ff;
}
.main .con .c-right .c-table .tbody ul li .select-area .weeks {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .con .c-right .c-table .tbody ul li .select-area .classroom {
  font-weight: 400;
  height: 20px;
  width: 100%;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .con .c-right .c-table .tbody ul li:first-child {
  flex: 0 0 30px;
  flex-shrink: 0;
  text-align: center;
  display: flex;
  display: -webkit-flex;
  justify-content: flex-start;
  align-items: center;
  justify-content: center;
  cursor: default;
  background-color: #BEDAFF;
}
.main .con .c-right .c-table .tbody ul li:first-child span {
  width: 30px;
  font-size: 14px;
  color: #4e5969;
}
.main .con .c-right .c-table .tbody ul li:last-child {
  margin-right: 0;
}
.main .con .c-right .c-side {
  flex: 637;
  width: 0;
  margin-left: 16px;
  height: calc(100vh - 140px);
  overflow-y: auto;
  overflow-x: hidden;
}
.main .con .c-right .c-side .gauge-outfit {
  margin-bottom: 4px;
  border-radius: 4px;
  background: #4D88FF;
  width: 100%;
  height: 44px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 8px 20px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: space-between;
  align-items: flex-start;
}
.main .con .c-right .c-side .gauge-outfit .title {
  font-size: 14px;
  line-height: 28px;
  color: #ffffff;
  flex-shrink: 0;
  margin-right: 14px;
}
.main .con .c-right .c-side .gauge-outfit .title .sel {
  width: auto;
  max-width: 240px;
  height: 28px;
  line-height: 28px;
}
.main .con .c-right .c-side .gauge-outfit .title .sel .select-input {
  height: 28px;
  border-radius: 4px;
  background-color: transparent;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .con .c-right .c-side .gauge-outfit .title .sel .select-input em {
  width: 12px;
  height: 12px;
  background: url(../images/drop-down.png) no-repeat center;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  margin-left: 8px;
}
.main .con .c-right .c-side .gauge-outfit .title .sel .select-input .name {
  font-size: 14px;
  color: #ffffff;
  padding-left: 6px;
  line-height: 28px;
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .con .c-right .c-side .gauge-outfit .title .sel .select-input .name.ckd {
  color: #ffffff;
}
.main .con .c-right .c-side .gauge-outfit .title .sel .select-input.clicked em {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.main .con .c-right .c-side .gauge-outfit .title .sel .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
  display: block;
}
.main .con .c-right .c-side .gauge-outfit .title .sel .select-input .select-dropdown {
  width: 100%;
  min-width: 140px;
  display: none;
  left: -1px;
  margin: 5px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: absolute;
  top: 28px;
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
  overflow: hidden;
}
.main .con .c-right .c-side .gauge-outfit .title .sel .select-input .select-dropdown .dropdown-lists li {
  margin: 0;
  line-height: normal;
  padding: 0 16px;
  clear: both;
  color: #4E5969;
  font-size: 14px !important;
  white-space: nowrap;
  list-style: none;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
.main .con .c-right .c-side .gauge-outfit .title .sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #E1EBFF;
  color: #4d88ff;
}
.main .con .c-right .c-side .oprate {
  flex: 1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: space-between;
}
.main .con .c-right .c-side .oprate .sel {
  flex: 1;
  width: 0;
  max-width: 240px;
  height: 28px;
  line-height: 28px;
}
.main .con .c-right .c-side .oprate .sel em {
  float: left;
  font-size: 14px;
  color: #474C59;
}
.main .con .c-right .c-side .oprate .sel .select-input {
  height: 28px;
  border-radius: 4px;
  background-color: #fff;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
}
.main .con .c-right .c-side .oprate .sel .select-input em {
  position: absolute;
  top: 8px;
  right: 6px;
  width: 12px;
  height: 12px;
  background: url(../images/drop-down4.png) no-repeat center;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.main .con .c-right .c-side .oprate .sel .select-input .name {
  font-size: 14px;
  color: #ACB4BF;
  padding-left: 6px;
  line-height: 28px;
  width: 86%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .con .c-right .c-side .oprate .sel .select-input .name.ckd {
  color: #131B26;
}
.main .con .c-right .c-side .oprate .sel .select-input.clicked em {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.main .con .c-right .c-side .oprate .sel .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
  display: block;
}
.main .con .c-right .c-side .oprate .sel .select-input .select-dropdown {
  width: 100%;
  min-width: 140px;
  display: none;
  left: -1px;
  margin: 5px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: absolute;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
  overflow: hidden;
}
.main .con .c-right .c-side .oprate .sel .select-input .select-dropdown .dropdown-lists li {
  margin: 0;
  line-height: normal;
  padding: 0 16px;
  clear: both;
  color: #4E5969;
  font-size: 14px !important;
  white-space: nowrap;
  list-style: none;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
.main .con .c-right .c-side .oprate .sel .select-input .select-dropdown .dropdown-lists li.cur {
  background: #E1EBFF;
  color: #4d88ff;
}
.main .con .c-right .c-side .oprate.flexEnd {
  justify-content: flex-end;
}
.main .con .c-right .c-side .oprate .btn-list {
  padding-left: 16px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class {
  height: 28px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul {
  float: left;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li {
  float: left;
  font-size: 14px;
  color: #FFF;
  margin-right: 16px;
  padding-left: 20px;
  line-height: 22px;
  position: relative;
  cursor: pointer;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li em {
  display: none;
  position: absolute;
  right: -22px;
  top: -49px;
  margin-left: 21px;
  width: auto;
  height: 38px;
  border-radius: 6px;
  background: #4E5969;
  padding: 8px 12px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 22px;
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li em:after {
  content: '';
  position: absolute;
  top: 38px;
  right: 44px;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  width: 12px;
  height: 6px;
  background: url(../images/black-tringle-icon.png) no-repeat center;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.cur em {
  display: block;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li:after {
  content: '';
  position: absolute;
  left: 0;
  top: 3px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #FFB866;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.color1:after {
  background: #FFB866;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.color2:after {
  border-radius: 20px;
  background: #FFA998;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.color3:after {
  border-radius: 20px;
  background: #B2BAFF;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.color4:after {
  border-radius: 20px;
  background: #FA9191;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.color5:after {
  border-radius: 20px;
  background: #FF99B1;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.color6:after {
  border-radius: 20px;
  background: #FFD166;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.color7:after {
  border-radius: 20px;
  background: #8ED66B;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.color8:after {
  border-radius: 20px;
  background: #8ED66B;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.color9:after {
  border-radius: 20px;
  background: #6ECBFA;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.color10:after {
  border-radius: 20px;
  background: #7EA4FC;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .s-ul ul li.color11:after {
  border-radius: 20px;
  background: #E5A1CF;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  height: 28px;
  padding-right: 16px;
  margin-right: 16px;
  border-right: 1px solid #E8EBF1;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more em {
  font-size: 14px;
  color: #ffffff;
  margin-right: 4px;
  position: relative;
  padding-right: 20px;
  line-height: 28px;
  cursor: pointer;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more em:after {
  content: '';
  position: absolute;
  right: 0;
  top: 6px;
  width: 16px;
  height: 16px;
  background: url(../images/arrowDown.png) no-repeat center;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more.selected .select-dropdown {
  display: block;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown {
  display: none;
  position: absolute;
  right: -2px;
  top: 32px;
  z-index: 999;
  background: #FFF;
  border-radius: 4px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  width: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul {
  overflow: hidden;
  border-radius: 4px;
  max-height: 360px;
  overflow-y: auto;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li {
  padding: 10px 20px;
  height: 40px;
  cursor: pointer;
  font-size: 14px;
  color: #4e5969;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 40px;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li:after {
  content: '';
  position: absolute;
  left: 20px;
  top: 12px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #FFB866;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li.color1:after {
  background: #FFB866;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li.color2:after {
  border-radius: 20px;
  background: #FFA998;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li.color3:after {
  border-radius: 20px;
  background: #B2BAFF;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li.color4:after {
  border-radius: 20px;
  background: #FA9191;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li.color5:after {
  border-radius: 20px;
  background: #FF99B1;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li.color6:after {
  border-radius: 20px;
  background: #FFD166;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li.color7:after {
  border-radius: 20px;
  background: #8ED66B;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li.color8:after {
  border-radius: 20px;
  background: #8ED66B;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li.color9:after {
  border-radius: 20px;
  background: #6ECBFA;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li.color10:after {
  border-radius: 20px;
  background: #7EA4FC;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li.color11:after {
  border-radius: 20px;
  background: #E5A1CF;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown ul li:hover {
  background-color: #e1ebff;
  color: #4d88ff;
}
.main .con .c-right .c-side .oprate .btn-list .jump-class .more .select-dropdown:after {
  content: '';
  position: absolute;
  right: 24px;
  top: -6px;
  width: 12px;
  height: 6px;
  z-index: 999;
  background: url(../images/tringle-icon.png) no-repeat center;
}
.main .con .c-right .c-side .oprate .btn-list .totalClassHours {
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
  padding-right: 16px;
  border-right: 1px solid #E8EBF1;
  margin-right: 16px;
}
.main .con .c-right .c-side .oprate .btn-list .switchs {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  height: 28px;
  margin-right: 24px;
}
.main .con .c-right .c-side .oprate .btn-list .switchs.follow-selection {
  margin-right: 0;
  padding-right: 16px;
  border-right: 1px solid #E8EBF1;
  margin-right: 16px;
}
.main .con .c-right .c-side .oprate .btn-list .switchs .name {
  font-size: 14px;
  color: #fff;
  margin-right: 14px;
}
.main .con .c-right .c-side .oprate .btn-list .switchs .switc-con {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.main .con .c-right .c-side .oprate .btn-list .switchs .switc-con .switch {
  position: relative;
  width: 28px;
  height: 14px;
  background: #cdcece;
  border-radius: 4px;
}
.main .con .c-right .c-side .oprate .btn-list .switchs .switc-con .switch em {
  width: 12px;
  height: 10px;
  position: absolute;
  top: 2px;
  left: 2px;
  background-color: #FFFFFF;
  border-radius: 2px;
}
.main .con .c-right .c-side .oprate .btn-list .switchs .switc-con .switch span {
  position: absolute;
  top: 1px;
  right: 3px;
  color: #ffffff;
  font-size: 8px;
}
.main .con .c-right .c-side .oprate .btn-list .switchs .switc-con .switch.switch-open {
  background: #ffffff;
}
.main .con .c-right .c-side .oprate .btn-list .switchs .switc-con .switch.switch-open em {
  position: absolute;
  top: 2px;
  right: 2px;
  left: auto;
  background-color: #4D88FF;
}
.main .con .c-right .c-side .oprate .btn-list .switchs .switc-con .switch.switch-open span {
  position: absolute;
  top: 1px;
  left: 3px;
  color: #4D88FF;
}
.main .con .c-right .c-side .oprate .btn-list .switchs .switc-con .switch-con {
  color: #C9CDD4;
  margin-left: 10px;
}
.main .con .c-right .c-side .oprate .btn-list .rice {
  margin-right: 16px;
  position: relative;
  width: 16px;
  flex-shrink: 0;
}
.main .con .c-right .c-side .oprate .btn-list .rice.selected .select-dropdown {
  display: block;
}
.main .con .c-right .c-side .oprate .btn-list .rice.upward .select-dropdown {
  top: -304px;
}
.main .con .c-right .c-side .oprate .btn-list .rice.upward .select-dropdown:after {
  bottom: -5px;
  top: auto;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.main .con .c-right .c-side .oprate .btn-list .rice .select-dropdown {
  display: none;
  position: absolute;
  right: -22px;
  top: 28px;
  z-index: 999;
  background: #FFF;
  border-radius: 4px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  width: 158px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
}
.main .con .c-right .c-side .oprate .btn-list .rice .select-dropdown ul {
  overflow: hidden;
  border-radius: 4px;
}
.main .con .c-right .c-side .oprate .btn-list .rice .select-dropdown ul li {
  padding: 4px 20px;
  height: 34px;
  cursor: pointer;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .con .c-right .c-side .oprate .btn-list .rice .select-dropdown ul li span {
  color: #4d88ff;
  display: block;
  margin-right: 4px;
}
.main .con .c-right .c-side .oprate .btn-list .rice .select-dropdown ul li em {
  font-size: 14px;
  color: #4e5969;
  width: 96px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .con .c-right .c-side .oprate .btn-list .rice .select-dropdown ul li:hover {
  background-color: #e1ebff;
}
.main .con .c-right .c-side .oprate .btn-list .rice .select-dropdown ul li:hover em {
  color: #4d88ff;
}
.main .con .c-right .c-side .oprate .btn-list .rice .select-dropdown:after {
  content: '';
  position: absolute;
  right: 24px;
  top: -6px;
  width: 12px;
  height: 6px;
  z-index: 999;
  background: url(../images/tringle-icon.png) no-repeat center;
}
.main .con .c-right .c-side .oprate .btn-list .rice .btn {
  position: relative;
  width: 16px;
  display: block;
  height: 16px;
  background: url(../images/screen-icon.png) no-repeat center;
  cursor: pointer;
}
.main .con .c-right .c-side .oprate .btn-list .rice .btn:hover em {
  display: block;
}
.main .con .c-right .c-side .oprate .btn-list .rice .btn em {
  display: none;
  position: absolute;
  right: -22px;
  top: 28px;
  width: auto;
  height: 38px;
  border-radius: 6px;
  background: #4E5969;
  padding: 8px 12px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 22px;
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
}
.main .con .c-right .c-side .oprate .btn-list .rice .btn em:after {
  content: '';
  position: absolute;
  top: -5px;
  right: 24px;
  width: 12px;
  height: 6px;
  background: url(../images/black-tringle-icon.png) no-repeat center;
}
.main .con .c-right .c-side .oprate .btn-list .clean-up span {
  position: relative;
  width: 16px;
  height: 16px;
  display: block;
  cursor: pointer;
  background: url(../images/clear-icon1.png) no-repeat center;
}
.main .con .c-right .c-side .oprate .btn-list .clean-up span:hover em {
  display: block;
}
.main .con .c-right .c-side .oprate .btn-list .clean-up span em {
  display: none;
  position: absolute;
  right: -22px;
  top: 28px;
  width: auto;
  height: 38px;
  border-radius: 6px;
  background: #4E5969;
  padding: 8px 12px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  line-height: 22px;
  font-size: 14px;
  color: #ffffff;
  white-space: nowrap;
}
.main .con .c-right .c-side .oprate .btn-list .clean-up span em:after {
  content: '';
  position: absolute;
  top: -5px;
  right: 24px;
  width: 12px;
  height: 6px;
  background: url(../images/black-tringle-icon.png) no-repeat center;
}
.main .con .c-right .c-side .c-table .scroll {
  overflow: auto;
  max-height: 250px;
  margin-bottom: 12px;
  background: #E1EBFF;
}
.main .con .c-right .c-side .c-table .scroll .c-table-head ul li {
  min-width: 92px;
  flex: 1;
  width: 0;
}
.main .con .c-right .c-side .c-table .scroll .c-table-head ul li:first-child {
  z-index: 996;
  min-width: 30px;
  flex: 0 0 30px;
  flex-shrink: 0;
}
.main .con .c-right .c-side .c-table .tbody.course-manage ul li {
  min-width: 92px;
  flex: 1;
}
.main .con .c-right .c-side .c-table .tbody.course-manage ul li:first-child {
  min-width: 30px;
  flex: 0 0 30px;
  flex-shrink: 0;
  z-index: 996;
  background: #E1EBFF;
  border-radius: 0;
}
.main .con .c-right .c-side .c-table .tbody.course-manage ul li:first-child span {
  display: block;
  width: 100%;
  height: 100%;
  background-color: #BEDAFF;
  border-radius: 4px;
  line-height: 36px;
}
.main .con .c-right .c-side .c-table .tbody.course-manage ul.disabled li .select-area {
  background-color: #F1F3F6;
  border-color: #F1F3F6;
  cursor: default;
}
.main .con .c-right .c-side .c-table .tbody.classroom-tbody ul li {
  height: 50px;
}
.main .con .c-right .c-side .c-table .tbody.classroom-tbody ul li .select-area {
  height: 50px;
  flex-wrap: wrap;
}
.main .con .c-right .c-side .c-table .tbody.classroom-tbody ul li .select-area .name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  font-size: 14px;
  color: #4e5969;
  font-weight: 500;
  line-height: 20px;
}
.main .con .c-right .c-side .c-table .tbody.classroom-tbody ul li .select-area .grades {
  font-size: 14px;
  width: 100%;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #4e5969;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody {
  margin-bottom: 12px;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li {
  height: 50px;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area {
  height: 50px;
  width: 100%;
  flex-wrap: wrap;
  padding: 4px 15px;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area .name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  font-size: 14px;
  color: #4e5969;
  font-weight: 500;
  line-height: 20px;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area .grades {
  font-size: 14px;
  width: 100%;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #4d88ff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color1 {
  background: #FFB866;
  border-color: #FFB866;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color1 .name {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color1 .grades {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color2 {
  background: #FFA998;
  border-color: #FFA998;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color2 .name {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color2 .grades {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color3 {
  background: #B2BAFF;
  border-color: #B2BAFF;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color3 .name {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color3 .grades {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color4 {
  background: #FA9191;
  border-color: #FA9191;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color4 .name {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color4 .grades {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color5 {
  background: #FF99B1;
  border-color: #FF99B1;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color5 .name {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color5 .grades {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color6 {
  background: #FFD166;
  border-color: #FFD166;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color6 .name {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color6 .grades {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color7 {
  background: #8ED66B;
  border-color: #8ED66B;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color7 .name {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color7 .grades {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color8 {
  background: #8ED66B;
  border-color: #8ED66B;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color8 .name {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color8 .grades {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color9 {
  background: #6ECBFA;
  border-color: #6ECBFA;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color9 .name {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color9 .grades {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color10 {
  background: #7EA4FC;
  border-color: #7EA4FC;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color10 .name {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color10 .grades {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color11 {
  background: #E5A1CF;
  border-color: #E5A1CF;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color11 .name {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody.teacher-tbody ul li .select-area.color11 .grades {
  color: #fff;
}
.main .con .c-right .c-side .c-table .tbody ul li {
  height: 36px;
}
.main .con .c-right .c-side .c-table .tbody ul li .select-area {
  height: 36px;
  padding: 7px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  justify-content: center;
}
.main .con .c-right .c-side .c-table .tbody ul li .select-area:hover {
  border-color: #fff;
}
.main .con .c-right .c-side .c-table .tbody ul li .select-area .icon-btn {
  white-space: nowrap;
}
.main .con .c-right .c-side .c-table .tbody ul li .select-area .icon-btn span {
  color: #4d88ff;
  cursor: pointer;
  margin: 0 1px;
}
.main .con .c-right .c-side .c-table .tbody ul li:first-child {
  writing-mode: inherit;
  text-align: center;
  letter-spacing: 0;
}
.main .con .c-right .c-side .c-table .tbody ul li:last-child .select-area {
  padding: 7px;
}
.main .con .c-right .c-side .c-table .tbody ul.active li {
  background-color: #BEDAFF;
}
.main .con .c-right .c-side .c-table .tbody ul.active li .select-area {
  border-color: #BEDAFF;
  background-color: #BEDAFF;
}
.main .con .c-right .c-side .c-table .tbody ul.active li .select-area:hover {
  border-color: #BEDAFF;
}
.main .con .c-right .c-side .c-table .tbody ul.active li .select-area .name {
  color: #4d88ff;
}
.main .con .c-right .c-side .c-table .tbody ul.active li .select-area .teacher {
  color: #4d88ff;
}
.main .con .c-right .c-side .c-table .tbody ul.active li .select-area .weeks {
  color: #4d88ff;
}
.main .con .c-right .c-side .c-table .tbody ul.active li .select-area .classroom {
  color: #4d88ff;
}
.main .con .c-right .c-side .c-table .tbody ul.active li .select-area .total-hours {
  color: #4d88ff;
}
/* 2024.2.4 */
.main .con .c-right .c-table .tbody ul li .select-area .oprate {
  display: flex;
  display: -webkit-flex;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate span {
  display: none;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate span.normal {
  display: block;
}
.main .con .c-right .c-table .tbody ul li .select-area:hover .oprate span {
  display: block;
}
/* 2024.2.22 */
#textAll {
  position: fixed;
  z-index: 9999;
  margin-top: -42px;
  padding-left: 4px;
}
#textAll .tts {
  background: rgba(24, 30, 51, 0.8);
  border-radius: 4px;
  padding: 10px 16px;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  color: #FFFFFF;
  max-width: 236px;
  height: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#textAll:after {
  content: '';
  position: absolute;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  bottom: -6px;
  left: 10px;
  width: 12px;
  height: 6px;
  background: url(../images/black-tringle-icon.png) no-repeat center;
}
/*2024.2.29*/
.pdr {
  padding-right: 6px;
}
.pdr17 {
  padding-right: 17px;
}
/* 2024.4.23 */
.search-wrap {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 30px;
  margin-bottom: 24px;
}
.sort-type {
  color: #86909c;
  line-height: 16px;
  font-size: 12px;
  padding: 0 30px;
  margin-bottom: 8px;
}
.main .con .c-left .schedule-list {
  height: calc(100vh - 304px);
}
.main .con .c-left .search {
  margin: 0;
}
.search-wrap .search {
  width: 164px;
}
.search-wrap .btn {
  width: 36px;
  height: 36px;
  background-color: #E1EBFF;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
}
.search-wrap .btn span {
  display: block;
  width: 16px;
  height: 16px;
  margin: 10px auto;
  background: url(../images/screen-icons.png) no-repeat center;
}
.search-wrap .btn:hover .select-dropdown {
  display: block;
}
.search-wrap .btn .select-dropdown {
  position: fixed;
  padding-top: 2px;
  display: none;
}
.search-wrap .btn .select-dropdown ul {
  width: 102px;
  box-shadow: 0px 0px 6.64px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background-color: #fff;
  overflow: hidden;
}
.search-wrap .btn .select-dropdown ul li {
  padding: 0 16px;
  clear: both;
  color: #4E5969;
  font-size: 12px !important;
  white-space: nowrap;
  list-style: none;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
}
.search-wrap .btn .select-dropdown ul li.cur {
  background: #E1EBFF;
  color: #4d88ff;
}
.main.style1 .search-wrap .btn {
  background-color: #D2EFD1;
}
.main.style1 .search-wrap .btn span {
  background: url(../images/screen-green-icons.png) no-repeat center;
}
.main.style1 .search-wrap .btn .select-dropdown ul li.cur {
  color: #3EB35A;
  background-color: #D2EFD1;
}
/* 2024.4.23 */
/* 2024.4.25 */
xm-select {
  border-color: #E5E6EB !important;
  border-radius: 4px !important;
  min-height: 32px !important;
  line-height: 32px !important;
}
xm-select > .xm-icon {
  border: none !important;
  width: 12px !important;
  height: 12px !important;
  margin-top: -6px !important;
  background: url(../images/drop-down4.png) no-repeat center !important;
}
.xm-icon-sousuo:before {
  display: none;
}
xm-select > .xm-body .xm-search > i {
  width: 16px !important;
  height: 16px !important;
  margin-top: 10px !important;
  background: url(../images/search-icons.png) no-repeat center !important;
}
xm-select .xm-label .xm-label-block {
  background-color: #4d88ff !important;
}
xm-select .xm-body .xm-option .xm-option-icon {
  border-color: #4d88ff !important;
}
xm-select .xm-body .selected .xm-option-icon {
  color: #4d88ff !important;
}
/* 2024.4.25 */
/* 2024.4.29 */
.main .con .c-right .c-table .tbody ul li .select-area .oprate span {
  position: relative;
  z-index: 99;
}
.main .con .c-right .c-table .tbody ul li .select-area .oprate:after {
  content: "";
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
  margin-top: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.4), #ffffff 50%);
  z-index: 1;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage .em-list {
  position: relative;
  z-index: 99;
}
.main .con .c-right .c-table .tbody ul li .select-area .tage:after {
  content: "";
  position: absolute;
  right: 0;
  width: 170%;
  height: 90%;
  margin-top: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), #ffffff 50%);
  z-index: 1;
}
/* 2024.4.29 */
