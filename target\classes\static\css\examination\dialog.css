.masker {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.j-search-con {
  display: flex;
  align-items: center;
  position: relative;
  width: 240px;
  cursor: pointer;
}
.j-search-con .j-select-year {
  left: 0;
}
.j-search-con input {
  width: 100%;
  height: 34px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  padding: 0 20px 0 10px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  cursor: pointer;
}
.j-search-con input::placeholder {
  color: #86909C;
}
.j-search-con .j-arrow {
  width: 10px;
  height: 10px;
  background: url(../../images/examination/down-icon.png) no-repeat center;
  position: absolute;
  right: 12px;
  top: 12px;
}
.j-search-con .j-arrow.j-arrow-slide {
  transform: rotate(180deg);
}
.j-search-con .j-select-year {
  position: absolute;
  top: 40px;
  left: -1px;
  z-index: 9;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  display: none;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}
.j-search-con .j-select-year.slideShow {
  display: block;
}
.j-search-con .j-select-year .search {
  height: 36px;
  background: #F5F7FA;
  border-radius: 18px;
  margin: 11px 10px;
}
.j-search-con .j-select-year .search input {
  border: none;
  width: 176px;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  float: left;
}
.j-search-con .j-select-year .search span {
  cursor: pointer;
  float: left;
  width: 16px;
  height: 16px;
  background: url(../../images/examination/search-icon.png) no-repeat center;
  margin-top: 10px;
}
.j-search-con .j-select-year .all-selects {
  line-height: 17px;
  margin-bottom: 4px;
  height: 17px;
  padding: 0 14px;
  font-size: 12px;
  color: #6B89B3;
  cursor: pointer;
  user-select: none;
}
.j-search-con .j-select-year ul {
  overflow: hidden;
  max-height: 200px;
  overflow-y: auto;
}
.j-search-con .j-select-year ul li {
  line-height: 40px;
  text-align: left;
  text-indent: 16px;
  cursor: pointer;
  font-size: 14px;
  color: #4E5969;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-right: 30px;
  background-color: #ffffff;
  /*background-image: url("../../images/examination/check-icon.png");*/
  background-repeat: no-repeat;
  background-position: 96% center;
}
.j-search-con .j-select-year ul li:hover {
  background-color: #E1EBFF;
  color: #4D88FF;
  font-weight: 500;
}
.j-search-con .j-select-year ul li.active {
  background-color: #E1EBFF;
  /*background-image: url("../../images/examination/check-cur.png");*/
  color: #4D88FF;
  font-weight: 500;
}
/*.j-search-con.single-box .j-select-year ul li {*/
/*  background-image: url("../../images/examination/radio-icon.png");*/
/*}*/
/*.j-search-con.single-box .j-select-year ul li.active {*/
/*  background-image: url("../../images/examination/radio-cur-icon.png");*/
/*}*/
.dialog {
  border-radius: 10px;
  background-color: #ffffff;
}
.dialog .dialog-title {
  border-bottom: 1px solid #E5E6EB;
  height: 56px;
  line-height: 56px;
  color: #1D2129;
  font-size: 16px;
  text-indent: 30px;
}
.dialog .dialog-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 70px;
  border-top: 1px solid #E5E6EB;
  padding-right: 30px;
}
.dialog .dialog-btn button {
  width: 88px;
  height: 36px;
  font-size: 14px;
  border-radius: 18px;
  cursor: pointer;
}
.dialog .dialog-btn button.pu-cancel {
  border: 1px solid #C9CDD4;
  color: #4E5969;
  background-color: #fff;
  margin-right: 16px;
}
.dialog .dialog-btn button.pu-sure {
  width: 116px;
  color: #fff;
  background: #4D88FF;
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  border: 1px solid #4D88FF;
}
.dialog .dialog-con {
  margin: 30px 60px;
}
.dialog .dialog-con .item {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}
.dialog .dialog-con .item .label {
  color: #1D2129;
  font-size: 14px;
  margin-right: 14px;
}
#invigilateMax {
  width: 488px;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
#exportRecord {
  width: 858px;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
#exportRecord .dialog-title {
  position: relative;
}
#exportRecord .dialog-title span {
  display: block;
  width: 30px;
  height: 30px;
  background: url('../../images/examination/ungrant-icon.png') no-repeat center;
  background-size: 26px;
  position: absolute;
  right: 14px;
  top: 14px;
  cursor: pointer;
}
#exportRecord .dialog-con {
  margin: 30px;
}
#exportRecord .dialog-con .download {
  color: #4D88FF;
  margin-right: 10px;
  cursor: pointer;
}
#exportRecord .dialog-con .delete {
  color: #f14848;
  cursor: pointer;
}
#exportRecord .dialog-con .tab-mes {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  line-height: 40px;
}
#exportRecord .dialog-con .tab-mes .total {
  color: #86909C;
}
#exportRecord .dialog-con .tab-mes .refresh {
  color: #4D88FF;
  background: url('../../images/examination/fresh.png') no-repeat left center;
  background-size: 16px;
  padding-left: 20px;
  cursor: pointer;
}
