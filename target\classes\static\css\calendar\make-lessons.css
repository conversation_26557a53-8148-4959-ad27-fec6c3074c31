.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
input[type=search]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea:-ms-input-placeholder {
  font-size: 12px;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.hide {
  display: none !important;
}
input::-webkit-input-placeholder {
  color: #86909C;
  font-size: 14px;
  font-weight: 300;
}
body {
  background-color: #F7F8FA;
}
.add-schedule .window {
  width: 775px;
}
.add-schedule .window .p-content .con .lable .names {
  width: 98px;
}
.add-schedule .window .p-content .con .lable .radio {
  width: 476px;
  height: 34px;
}
.add-schedule .window .p-content .con .lable .radio ul {
  overflow: hidden;
}
.add-schedule .window .p-content .con .lable .radio ul li {
  float: left;
  cursor: pointer;
  padding-left: 24px;
  background: url(../../images/calendar/radio-icon.png) no-repeat left center;
  background-size: 16px;
  font-size: 14px;
  color: #86909C;
  margin-right: 24px;
  line-height: 34px;
}
.add-schedule .window .p-content .con .lable .radio ul li.cur {
  background: url(../../images/calendar/radio-cur-icon.png) no-repeat left center;
  background-size: 16px;
}
.date-edit .window .p-content .con .scalp {
  position: relative;
}
.date-edit .window .p-content .con .scalp .btn-list {
  position: absolute;
  top: 0;
  right: 0;
}
.date-edit .window .p-content .con .btn-list {
  height: 34px;
}
.date-edit .window .p-content .con .btn-list .search {
  height: 34px;
  line-height: 34px;
}
.date-edit .window .p-content .con .btn-list .btn {
  width: 64px;
  height: 34px;
  line-height: 34px;
}
.date-edit .window .p-content .con .btn-list .delet {
  width: 64px;
  height: 34px;
  line-height: 32px;
}
#coursePage {
  margin-bottom: 20px;
}
.layui-table tr {
  height: 36px;
}
.layui-table-view .layui-table td,
.layui-table-view .layui-table th {
  height: 36px;
}
.add-schedule .window .p-content .con .lable .times {
  width: 477px;
  height: 34px;
  background: url(../../images/calendar/time-select.png) no-repeat 456px center;
  background-size: 12px;
}
.add-schedule .window .p-content .con .lable .times .layui-input {
  background-color: transparent;
  font-size: 14px;
  color: #4E5969;
}
