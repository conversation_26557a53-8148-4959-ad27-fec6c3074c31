.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
body {
  background-color: #f7f8fa;
}
.main {
  border-radius: 8px;
  min-height: calc(100vh);
  min-width: 1000px;
  max-width: 1660px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  background-color: #fff;
  margin: 0 auto;
  overflow-y: auto;
}
.main .m-top {
  width: 100%;
  height: 57px;
  border-bottom: 1px solid #e8eaf1;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 30px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  justify-content: space-between;
}
.main .m-top .back {
  width: 60px;
  height: 24px;
  background: url(../images/back-icon.png) no-repeat left center;
  cursor: pointer;
  margin-right: 10px;
}
.main .m-top .title {
  font-size: 16px;
  color: #1d2129;
  padding-left: 9px;
  position: relative;
}
.main .m-top .title::after {
  content: "";
  width: 3px;
  height: 16px;
  background-color: #4d88ff;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 3px;
}
.main .item {
  padding: 0 30px;
  background: #ffffff;
  border-radius: 8px;
  padding-top: 16px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.main .item .change-stu-con {
  display: flex;
  flex-wrap: wrap;
  font-size: 15px;
  width: 47.5%;
}
.main .item .change-stu-con dl {
  display: flex;
  align-items: center;
  margin: 0 24px 12px 0;
}
.main .item .change-stu-con dl dt {
  color: #1d2129;
  flex-shrink: 0;
}
.main .item .change-stu-con dl dd {
  color: #4e5969;
}
.main .item .btn-con {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin: 16px 0 0;
}
.main .item .btn-con .add-btn {
  padding: 0 20px;
  height: 34px;
  line-height: 32px;
  background: #4d88ff;
  border-radius: 4px;
}
.main .item .i-top {
  width: 100%;
  height: 22px;
  margin-bottom: 32px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: flex-start;
}
.main .item .i-top h3 {
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #6581ba;
  position: relative;
  display: block;
  margin-right: 24px;
}
.main .item .i-top h3:after {
  content: "";
  position: absolute;
  left: 0;
  top: 1px;
  background: #6581ba;
  border-radius: 2px;
  width: 3px;
  height: 18px;
}
.main .item .i-top .arrow {
  position: relative;
  font-weight: 400;
  width: 12px;
  height: 12px;
  cursor: pointer;
  background: url(../images/arrow-icon.png) no-repeat right center;
  transform: rotate(-80deg);
}
.main .item .i-top .arrow.slide {
  transform: rotate(0);
}
.main .item .i-con {
  overflow: hidden;
  width: 47.5%;
}
.main .item .i-con .class-box {
  overflow: hidden;
}
.main .item .i-con .class-box .stu-static {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  color: #4E5969;
}
.main .item .i-con .class-box .stu-static span {
  padding-right: 24px;
}
.main .item .i-con .class-box .stu-static span i {
  color: #4c88ff;
  padding: 0 3px;
}
.main .item .i-con .class-box .stu-static span:last-child {
  padding-right: 0;
}
.main .item .i-con .class-box .cb-top {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.main .item .i-con .class-box .cb-top .tit {
  font-size: 16px;
  color: #1d2129;
  font-weight: 600;
}
.main .item .i-con .class-box .button {
  border: 1px solid #4c88ff;
  box-shadow: 0px 2px 8px rgba(39, 111, 255, 0.3);
  border-radius: 4px;
  width: 88px;
  height: 34px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  font-size: 14px;
  color: #4c88ff;
  margin-bottom: 20px;
}
.main .item .i-con .class-box .j-table {
  margin-bottom: 24px;
}
.main .item .i-con .class-box .j-table .stu {
  display: flex;
  align-items: center;
  justify-content: center;
}
.main .item .i-con .class-box .j-table .stu .stuPhoto {
  width: 22px;
  height: 22px;
  background: url("../images/stu-icon.png") no-repeat center;
  background-size: 22px;
  margin-left: 6px;
}
