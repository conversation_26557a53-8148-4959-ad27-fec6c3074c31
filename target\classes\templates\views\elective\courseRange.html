<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可选课程</title>
    <th:block th:include="common :: header('可选课程')"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
<!--        <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">-->
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui1/css/layui.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/reset.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/selCourse.css?v=1'">
    <style>
        .opt_relevance ul {
            overflow: hidden;
            padding: 0 30px;
            font-size: 14px;
        }

        .opt_relevance ul li {
            float: left;
            overflow: hidden;
            height: 20px;
            margin: 16px 40px 0 0;
            font-size: 14px;
            color: #484f5d;
            line-height: 20px;
            word-break: break-all;
        }

        .opt_relevance ul li span {
            float: left;
        }

        .opt_relevance ul li i {
            float: left;
        }
    </style>
</head>
<body>
<input type="hidden" id="xxkbdidstr">
<input type="hidden" id="xxkbdidstrold">
<input type="hidden" id="addAllNotIn">
<div class="z-main">
    <div class="opt_relevance">
        <ul>
            <li>
                <span>计划名称：</span><i th:text="${task.xkjhbJhmc}"></i>
            </li>
            <li>
                <span>所属学期：</span><i th:text="${task.xkjhbSsxq}"></i>
            </li>
        </ul>
    </div>
    <div class="z-search">
        <form action="" class="layui-form" onsubmit="return false">
            <div class="layui-form-item">
                <label class="layui-form-label">开课年级</label>
                <div class="layui-input-block">
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="njIp">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="njUl">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">开课院系 </label>
                <div class="layui-input-block">
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="yxIp">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="yxUl">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">开课专业 </label>
                <div class="layui-input-block">
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="zyIp">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="zyUl">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"> 开设课程 </label>
                <div class="layui-input-block">
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="kcIp">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="kcUl">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">开课校区 </label>
                <div class="layui-input-block">
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="xqIp">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="xqUl">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">课程类型 </label>
                <div class="layui-input-block">
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="lxIp">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="lxUl">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">上课院系 </label>
                <div class="layui-input-block">
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel"
                               id="skyxIp">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="skyxUl">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <button class="z-btn">查询</button>
            <div class="clear"></div>
        </form>
    </div>
    <div class="z-tab-search">
        <ul id="fromtype">
            <li source="1">开课信息表</li>
            <li source="2">选修课申报表</li>
            <li source="5">编组课程</li>
        </ul>
        <input type="text" placeholder="请输入课程名称" id="searchWord">
        <img th:src="${_CPR_}+'/elective/images/search-icon.png'" alt="">
    </div>
    <div class="z-table">
        <table class="layui-hide mtTable1" id="mtTable1" lay-filter="mtTable1">
        </table>
        <div class="z-check">
            <span class="check" id="checkAll"></span>选择全部数据
        </div>
    </div>
    <div class="btns">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="scoreSure">确定</button>
    </div>
</div>
</body>
<script type="text/javascript">
    var flag = '[[${flag}]]';
    var formId = [[${formId}]];
    var formUserId = [[${formUserId}]];
    var fid = [[${fid}]];
    var source = [[${source}]];
    var pageSize = 10;
    var titleArr1 = [
        {
            type: "checkbox",
            field: 'LAY_CHECKBOX',
            width: 70,
        },
        {
            field: "kkxxb_jxbmc",
            title: "选修课名称",
            align: "center",
            minWidth: 270,
        },
        {
            field: "kkxxb_njnj",
            title: "开课年级",
            align: "center",
            minWidth: 100,
            templet: function (d) {
                var nj = d.kkxxb_njnj;
                if (nj == '') {
                    if (d.kkxxb_njnj1 != '') {
                        nj = d.kkxxb_njnj1.join(',');
                    }
                }
                return nj;
            }
        },
        {
            field: "kkxxb_kkbm",
            title: "开课院系",
            align: "center",
            minWidth: 100,
        },
        {
            field: "kkxxb_kkyxyx",
            title: "上课院系",
            align: "center",
            minWidth: 100,
            templet: function (d) {
                var yx = d.kkxxb_kkyxyx;
                if (yx == '') {
                    if (d.kkxxb_kkyxyx1 != '') {
                        yx = d.kkxxb_kkyxyx1.join(',');
                    }
                }
                return yx;
            }
        },
        {
            field: "kkxxb_zymc",
            title: "开课专业",
            align: "center",
            minWidth: 120,
            templet: function (d) {
                var zy = d.kkxxb_zymc;
                if (zy == '') {
                    if (d.kkxxb_zymc1 != '') {
                        zy = d.kkxxb_zymc1.join(',');
                    }
                }
                return zy;
            }
        },
        {
            field: "kkxxb_kcmc",
            title: "开设课程",
            align: "center",
            minWidth: 96,
        },
        {
            field: "kkxxb_kkxqxq",
            title: "开课校区",
            align: "center",
            minWidth: 110,
        },
        {
            field: "kkxxb_kcxz",
            title: "课程类型",
            align: "center",
            minWidth: 110,
        },
        {
            field: "rowInfo",
            title: "rowInfo",
            align: "center",
            minWidth: 110,
            hide: true,
        },
    ];

    var titleArr2 = [
        {
            type: "checkbox",
            field: 'LAY_CHECKBOX',
            width: 70,
        }, {
            field: "kkxxb_jxbbzmc",
            rowspan: 2,
            title: "编组名称",
            align: "center",
            minWidth: 270,
        },
        {
            field: "kkxxb_jxbmc",
            title: "选修课名称",
            align: "center",
            minWidth: 270,
        },
        {
            field: "kkxxb_njnj",
            title: "开课年级",
            align: "center",
            minWidth: 100,
            templet: function (d) {
                var nj = d.kkxxb_njnj;
                if (nj == '') {
                    if (d.kkxxb_njnj1 != '') {
                        nj = d.kkxxb_njnj1.join(',');
                    }
                }
                return nj;
            }
        },
        {
            field: "kkxxb_kkbm",
            title: "开课院系",
            align: "center",
            minWidth: 100,
        },
        {
            field: "kkxxb_kkyxyx",
            title: "上课院系",
            align: "center",
            minWidth: 100,
            templet: function (d) {
                var yx = d.kkxxb_kkyxyx;
                if (yx == '') {
                    if (d.kkxxb_kkyxyx1 != '') {
                        yx = d.kkxxb_kkyxyx1.join(',');
                    }
                }
                return yx;
            }
        },
        {
            field: "kkxxb_zymc",
            title: "开课专业",
            align: "center",
            minWidth: 120,
            templet: function (d) {
                var zy = d.kkxxb_zymc;
                if (zy == '') {
                    if (d.kkxxb_zymc1 != '') {
                        zy = d.kkxxb_zymc1.join(',');
                    }
                }
                return zy;
            }
        },
        {
            field: "kkxxb_kcmc",
            title: "开设课程",
            align: "center",
            minWidth: 96,
        },
        {
            field: "kkxxb_kkxqxq",
            title: "开课校区",
            align: "center",
            minWidth: 110,
        },
        {
            field: "kkxxb_kcxz",
            title: "课程类型",
            align: "center",
            minWidth: 110,
        },
        {
            field: "rowInfo",
            title: "rowInfo",
            align: "center",
            minWidth: 110,
            hide: true,
        },
    ];
    var titleArr = titleArr1;
    if (source == 1) {
        $("#fromtype li").eq(1).remove();
        $("#fromtype li").eq(0).addClass("active");
    } else if (source == 2) {
        $("#fromtype li").eq(0).remove();
        $("#fromtype li").eq(0).addClass("active");
    } else if (source == 0) {
        // $("#fromtype").show();
        source = 1;
        $("#fromtype li").eq(0).addClass("active");
    } else {
        $("#fromtype").hide();
    }

    $.ajax({
        type: "POST",
        url: "/elective/getNj",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#njUl").html(html);
        }
    });
    $.ajax({
        type: "POST",
        url: "/elective/getYX",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#yxUl").html(html);
            $("#skyxUl").html(html);
        }
    });
    $.ajax({
        type: "POST",
        url: "/elective/getKC",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#kcUl").html(html);
        }
    });
    $.ajax({
        type: "POST",
        url: "/elective/getXQ",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#xqUl").html(html);
        }
    });
    $.ajax({
        type: "POST",
        url: "/elective/getLX",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#lxUl").html(html);
        }
    });
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/common.js'"></script>
<!--<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>-->
<script th:src="${_CPR_}+'/elective/layui1/layui.js'"></script>
<script>
    layui.use(["jquery", "table", "layer"], function () {
        var table = layui.table,
            form = layui.form,
            layer = layui.layer,
            $ = layui.jquery;
        getExistXxk();
        $(".z-main .z-tab-search ul li").click(function () {
            $(this).addClass("active").siblings().removeClass("active");
            var idx = $(this).index();
            source = getSource();
            if (source == 5) {
                $("#searchWord").attr("placeholder", "请输入编组名称")
                $(".z-search").hide();
                titleArr = titleArr2;
            } else {
                $("#searchWord").attr("placeholder", "请输入课程名称")
                $(".z-search").show();
                titleArr = titleArr1;
            }
            search();
            getExistXxk();
        })
        // 添加可选课程
        var mtTable1 = [];
        table.render({
            elem: "#mtTable1",
            url: '/elective/getXxk?fid=' + fid + '&formId=' + formId + '&formUserId=' + formUserId + "&source=" + source,
            data: mtTable1,
            page: true,
            height: 400,
            cols: [titleArr1],
            done: function (res, curr, count) {
                var num = 0;
                if ($("#checkAll").hasClass("checked")) {
                    var addAllNotIn = $("#addAllNotIn").val();
                    var temp = addAllNotIn.split(",");
                    var ids = [];
                    for (var i = 0; i < temp.length; i++) {
                        if (temp[i] != '') {
                            ids.push(temp[i]);
                        }
                    }
                    res.data.forEach(function (item, index) {
                        var formUserid = item.rowInfo.formUserId.toString();
                        if ($.inArray(formUserid, ids) == -1) {
                            num++;
                            $('div[lay-id="mtTable1"] .layui-table-box .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                            $('div[lay-id="mtTable1"] .layui-table-box .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                        }
                    });
                } else {
                    var xxkbdidstr = $("#xxkbdidstr").val();
                    var temp = xxkbdidstr.split(",");
                    var ids = [];
                    for (var i = 0; i < temp.length; i++) {
                        if (temp[i] != '') {
                            ids.push(temp[i]);
                        }
                    }
                    res.data.forEach(function (item, index) {
                        var bdid = item.rowInfo.formUserId.toString();
                        if ($.inArray(bdid, ids) != -1) {
                            num++;
                            $('div[lay-id="mtTable1"] .layui-table-box .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                            $('div[lay-id="mtTable1"] .layui-table-box .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                        }
                    });
                }
                if (num == res.data.length) {
                    $('div[lay-id="mtTable1"] input[type=checkbox]').prop("checked", true);
                    $('div[lay-id="mtTable1"] .layui-form-checkbox').addClass(
                        "layui-form-checked"
                    );
                }
            },
        });
        // 选择全部
        $("#checkAll").click(function () {
            $(this).toggleClass('checked');
            if ($(this).hasClass('checked')) {
                $('div[lay-id="mtTable1"] input[type=checkbox]').prop('checked', true);
                $('div[lay-id="mtTable1"] .layui-form-checkbox').addClass('layui-form-checked');
                mtTable1.map(item => {
                    item.isAdd = true;
                })
            } else {
                $("#xxkbdidstr").val("");
                $('div[lay-id="mtTable1"] input[type=checkbox]').prop('checked', false);
                $('div[lay-id="mtTable1"] .layui-form-checkbox').removeClass('layui-form-checked');
                mtTable1.map(item => {
                    item.isAdd = false;
                })
            }
        })
        // 查询
        $(".z-btn").click(function () {
            search();
        })
        $("#searchWord").keyup(function () {
            if (event.keyCode == 13) {
                search();
            }
        });

        function getExistXxk() {
            $.ajax({
                type: "POST",
                url: "/elective/getExistXxk",
                data: {fid: fid, formId: formId, formUserId: formUserId, source: source},
                async: false,
                dataType: 'json',
                success: function (result) {
                    $("#xxkbdidstr").val(result.xxkbdids);
                    $("#xxkbdidstrold").val(result.xxkbdids);
                }
            });
        }

        function search() {
            var nj = $("#njIp").val();
            var yx = $("#yxIp").val();
            var skyx = $("#skyxIp").val();
            var zy = $("#zyIp").val();
            var kc = $("#kcIp").val();
            var xq = $("#xqIp").val();
            var lx = $("#lxIp").val();
            var searchWord = $("#searchWord").val();
            table.render({
                elem: "#mtTable1",
                url: '/elective/getXxk?fid=' + fid + '&formId=' + formId + '&formUserId=' + formUserId + '&searchWord=' + searchWord + "&nj=" + nj + '&yx=' + yx + '&zy=' + zy + '&kc=' + kc + '&xq=' + xq + '&lx=' + lx + "&source=" + source + "&skyx=" + skyx,
                data: mtTable1,
                page: true,
                height: 400,
                cols: [titleArr],
                done: function (res, curr, count) {
                    pageSize = res.data.length;
                    var num = 0;
                    if ($("#checkAll").hasClass("checked")) {
                        var addAllNotIn = $("#addAllNotIn").val();
                        var temp = addAllNotIn.split(",");
                        var ids = [];
                        for (var i = 0; i < temp.length; i++) {
                            if (temp[i] != '') {
                                ids.push(temp[i]);
                            }
                        }
                        res.data.forEach(function (item, index) {
                            var formUserid = item.rowInfo.formUserId.toString();
                            if ($.inArray(formUserid, ids) == -1) {
                                num++;
                                $('div[lay-id="mtTable1"] .layui-table-box .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                                $('div[lay-id="mtTable1"] .layui-table-box .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                            }
                        });
                    } else {
                        var xxkbdidstr = $("#xxkbdidstr").val();
                        var temp = xxkbdidstr.split(",");
                        var ids = [];
                        for (var i = 0; i < temp.length; i++) {
                            if (temp[i] != '') {
                                ids.push(temp[i]);
                            }
                        }
                        res.data.forEach(function (item, index) {
                            var bdid = item.rowInfo.formUserId.toString();
                            if ($.inArray(bdid, ids) != -1) {
                                num++;
                                $('div[lay-id="mtTable1"] .layui-table-box .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                                $('div[lay-id="mtTable1"] .layui-table-box .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                            }
                        });
                    }
                    if (num == res.data.length) {
                        $('div[lay-id="mtTable1"] input[type=checkbox]').prop("checked", true);
                        $('div[lay-id="mtTable1"] .layui-form-checkbox').addClass(
                            "layui-form-checked"
                        );
                    }
                    if (source == 5) {
                        mergeColumns('mtTable1', 'kkxxb_jxbbzmc');
                    }
                },
            });
        }

        $(".pu-cancel").click(function () {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
        });
        $("#zyIp").click(function () {
            var yx = $("#yxIp").val();
            $.ajax({
                type: "POST",
                url: "/elective/getZY",
                data: {fid: fid, addScoreYx: yx},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#zyUl").html(html);
                }
            });
        });
        $("#scoreSure").click(function () {
            // layer.msg("正在添加...");
            var loading = layer.load(0, {
                shade: [0.5, '#c0c0c0']
            });
            var xxkbdidstr = $("#xxkbdidstr").val();
            var xxkbdidstrold = $("#xxkbdidstrold").val();
            var all = "no";
            if ($("#checkAll").hasClass("checked")) {
                all = "yes";
            }
            if (all == 'yes') {
                var nj = $("#njIp").val();
                var yx = $("#yxIp").val();
                var skyx = $("#skyxIp").val();
                var zy = $("#zyIp").val();
                var kc = $("#kcIp").val();
                var xq = $("#xqIp").val();
                var lx = $("#lxIp").val();
                var searchWord = $("#searchWord").val();
                var addAllNotIn = $("#addAllNotIn").val();
                $.ajax({
                    type: "POST",
                    url: "/elective/addXxk",
                    data: {
                        taskBdid: formUserId,
                        fid: fid,
                        formId: formId,
                        nj: nj,
                        yx: yx,
                        zy: zy,
                        kc: kc,
                        xq: xq,
                        lx: lx,
                        skyx: skyx,
                        searchWord: searchWord,
                        formUserId: formUserId,
                        all: all,
                        addAllNotIn: addAllNotIn,
                        source: source
                    },
                    success: function (data) {
                        layer.close(loading);
                        if (data.status) {
                            layer.msg("课程已添加，请前往待选选修课查看");
                            setTimeout(function () {
                                window.parent.postMessage(JSON.stringify({action: 1}), "*");
                            }, 2000);
                        } else {
                            layer.msg(data.msg);
                        }
                    }
                });
            } else {
                $.ajax({
                    type: "POST",
                    url: "/elective/addXxk",
                    data: {
                        taskBdid: formUserId,
                        fid: fid,
                        formId: formId,
                        formUserId: formUserId,
                        all: all,
                        xxkbdidstr: xxkbdidstr,
                        xxkbdidstrold: xxkbdidstrold,
                        source: source
                    },
                    success: function (data) {
                        layer.close(loading);
                        if (data.status) {
                            layer.msg("课程已添加，请前往待选选修课查看");
                            setTimeout(function () {
                                window.parent.postMessage(JSON.stringify({action: 1}), "*");
                            }, 2000);
                        } else {
                            layer.msg(data.msg);
                        }
                    }
                });
            }
        });
        table.on('checkbox(mtTable1)', function (obj) {
            if ($("#checkAll").hasClass("checked")) {
                var addAllNotIn = $("#addAllNotIn").val();
                var temp = addAllNotIn.split(",");
                var ids = [];
                for (var i = 0; i < temp.length; i++) {
                    if (temp[i] != '') {
                        ids.push(temp[i]);
                    }
                }
                if (obj.data.rowInfo != undefined) {
                    var id = obj.data.rowInfo.formUserId.toString();
                    if (obj.checked) {
                        if ($.inArray(id, ids) != -1) {
                            ids.splice($.inArray(id, ids), 1);
                        }
                    } else {
                        if ($.inArray(id, ids) == -1) {
                            ids.push(id);
                        }
                    }
                } else {
                    var checkStatus = table.checkStatus('mtTable1');
                    if (checkStatus.data.length == 0) {
                        var data = table.cache.mtTable1;
                        for (var i = 0; i < data.length; i++) {
                            var notallid = data[i].rowInfo.formUserId.toString();
                            if ($.inArray(notallid, ids) == -1) {
                                ids.push(notallid);
                            }
                        }
                    } else {
                        for (var i = 0; i < checkStatus.data.length; i++) {
                            var allid = checkStatus.data[i].rowInfo.formUserId.toString();
                            if ($.inArray(allid, ids) != -1) {
                                ids.splice($.inArray(allid, ids), 1);
                            }
                        }
                    }
                }
                $("#addAllNotIn").val(ids.toString());
            } else {
                var xxkbdidstr = $("#xxkbdidstr").val();
                var temp = xxkbdidstr.split(",");
                var ids = [];
                for (var i = 0; i < temp.length; i++) {
                    if (temp[i] != '') {
                        ids.push(temp[i]);
                    }
                }
                if (obj.data.rowInfo != undefined) {
                    var id = obj.data.rowInfo.formUserId.toString();
                    if (obj.checked) {
                        if ($.inArray(id, ids) == -1) {
                            ids.push(id);
                        }
                    } else {
                        ids.splice($.inArray(id, ids), 1);
                    }
                } else {
                    var checkStatus = table.checkStatus('mtTable1');
                    if (checkStatus.data.length == 0) {
                        var data = table.cache.mtTable1;
                        for (var i = 0; i < data.length; i++) {
                            var notallid = data[i].rowInfo.formUserId.toString();
                            ids.splice($.inArray(notallid, ids), 1);
                        }
                    } else {
                        for (var i = 0; i < checkStatus.data.length; i++) {
                            var allid = checkStatus.data[i].rowInfo.formUserId.toString();
                            if ($.inArray(allid, ids) == -1) {
                                ids.push(allid);
                            }
                        }
                    }
                }
                $("#xxkbdidstr").val(ids.toString());
            }
        });
    })

    function getSource() {
        console.log($("#fromtype").find(".active").attr("source"))
        return $("#fromtype").find(".active").attr("source");
    }


    // 合并表格列的函数
    function mergeColumns(tableId, field) {
        const $table = $(`#${tableId}`);
        const $trs = $table.next().find('.layui-table-body tbody tr');
        const columnIndex = getColumnIndex(tableId, field);

        console.log(columnIndex);


        let lastValue = null;
        let startIndex = 0;
        let rowspan = 1;

        $trs.each(function (index) {
            const $tds = $(this).children();
            const currentValue = $tds.eq(columnIndex).text();

            if (lastValue === currentValue) {
                rowspan++;
                // 隐藏多余的单元格
                $tds.eq(columnIndex).hide();
                $tds.eq(columnIndex).prev().hide();

                // 如果是最后一行，需要处理合并
                if (index === $trs.length - 1) {
                    $trs.eq(startIndex).children().eq(columnIndex)
                        .attr('rowspan', rowspan)
                        .css('vertical-align', 'middle');
                    $trs.eq(startIndex).children().eq(columnIndex).prev()
                        .attr('rowspan', rowspan)
                        .css('vertical-align', 'middle');
                }

            } else {
                if (index > 0) {
                    // 设置上一组的rowspan
                    $trs.eq(startIndex).children().eq(columnIndex)
                        .attr('rowspan', rowspan)
                        .css('vertical-align', 'middle');

                    $trs.eq(startIndex).children().eq(columnIndex).prev()
                        .attr('rowspan', rowspan)
                        .css('vertical-align', 'middle');
                }

                // 重置变量
                lastValue = currentValue;
                startIndex = index;
                rowspan = 1;

                // 为复选框列添加全选功能
                $tds.eq(0).find('.layui-form-checkbox').off('click').on('click', function (e) {

                    const $currentTr = $(this).closest('tr');
                    const categoryName = $currentTr.children().eq(1).text();
                    // 选中/取消选中同一分类所有行
                    $trs.each(function () {
                        if ($(this).children().eq(1).text() === categoryName) {
                            const checkbox = $(this).find('.layui-form-checkbox')[0];

                            console.log($(this).find('.layui-form-checkbox').hasClass('layui-form-checked'));

                            if (!$(this).find('.layui-form-checkbox').hasClass('layui-form-checked')) {
                                $(checkbox).addClass('layui-form-checked');
                                $(this).find('.layui-form-checkbox').prev().prop('checked', true);
                            } else {
                                $(checkbox).removeClass('layui-form-checked');
                                $(this).find('.layui-form-checkbox').prev().prop('checked', false);
                            }

                        }
                    });


                    let lengths = $(".layui-table-main .layui-form-checked").length;

                    if (lengths == pageSize) {
                        console.log(lengths);

                        $(".layui-table-header").find('.layui-form-checkbox').prev().prop('checked', true);
                        $(".layui-table-header").find('.layui-form-checkbox').addClass('layui-form-checked');
                    } else {
                        $(".layui-table-header").find('.layui-form-checkbox').prev().prop('checked', true);
                        $(".layui-table-header").find('.layui-form-checkbox').removeClass('layui-form-checked');
                    }


                    // e.stopPropagation();
                });
            }
        });
    }


    // 获取列索引
    function getColumnIndex(tableId, field) {
        const $table = $(`#${tableId}`);
        const $header = $table.next().find('.layui-table-header thead tr');
        let index = -1;

        $header.children().each(function (i) {
            const attr = $(this).attr('data-field');

            if (attr === field) {
                index = i;
                return false;
            }
        });

        return index;
    }
</script>
</html>