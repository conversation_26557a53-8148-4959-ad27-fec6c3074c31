* {
  font-family: <PERSON><PERSON><PERSON> SC;
}
.bottomLine {
  background: -webkit-linear-gradient(top, transparent 50%, #ebebeb 50%) center bottom no-repeat;
  background: -moz-linear-gradient(top, transparent 50%, #ebebeb 50%) center bottom no-repeat;
  background: -ms-linear-gradient(top, transparent 50%, #ebebeb 50%) center bottom no-repeat;
  background-size: 100% 1px;
}
.tophead {
  width: 100%;
  height: auto;
  text-align: center;
  background-color: #ffffff;
  z-index: 1;
}
.tophead .head {
  position: relative;
}
.tophead .leftHead {
  position: absolute;
  left: 0.16rem;
  bottom: 0;
  height: 0.88rem;
  z-index: 1;
  display: flex;
  align-items: center;
}
.tophead .back {
  float: left;
  width: 0.48rem;
  height: 0.48rem;
  background: url(../images/headback.png) center center / 24px 24px no-repeat;
  background-size: 0.48rem;
}
.tophead .centerHead {
  position: relative;
}
.tophead .rightHead {
  position: absolute;
  right: 0.2rem;
  bottom: 0.2rem;
  z-index: 1;
}
.tophead .rightHead .menu {
  width: 0.48rem;
  height: 0.48rem;
  background: url("../images/menu.png") no-repeat center;
  background-size: 0.4rem;
}
.tophead .rightHead .menu-list {
  width: 1.5rem;
  position: absolute;
  right: 0.02rem;
  top: 0.6rem;
  box-shadow: 0 0 0.1rem 0 rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
  border-radius: 0.06rem;
  display: none;
}
.tophead .rightHead .menu-list::after {
  content: "";
  width: 0.2rem;
  height: 0.1rem;
  background: url("../images/arrow.png") no-repeat center;
  position: absolute;
  top: 0;
  right: 0;
  top: -0.09rem;
  right: 0.12rem;
  background-size: 0.16rem;
}
.tophead .rightHead .menu-list li {
  line-height: 0.54rem;
  font-size: 0.24rem;
}
.tophead .rightHead .menu-list li:first-child {
  border-bottom: 0.005rem solid #f7f7f7;
  border-radius: 0.06rem 0.06rem 0 0;
}
.tophead .rightHead .menu-list li.active {
  background: #e1ebff;
}
.tophead .completeDelete {
  display: none;
  position: absolute;
  right: 0;
  bottom: 0;
  height: 45px;
  line-height: 45px;
  text-align: center;
  width: 1.1rem;
  font-size: 15px;
  color: #0999ff;
  z-index: 1;
}
.tophead .rightHead .scan {
  display: none;
  float: left;
  width: 24px;
  height: 24px;
  background: url(../images/headback.png) center center / 24px 24px no-repeat;
}
.tophead .rightHead .setting {
  float: left;
  width: 40px;
  height: 100%;
  background: url(../images/setting.png) center center / 24px 24px no-repeat;
}
.search {
  padding: 0.2rem 0.3rem 0.16rem;
  box-sizing: border-box;
}
.search .search-text {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.28rem;
  height: 0.6rem;
  line-height: 0.6rem;
  color: #c9cdd4;
  background: #f5f6f8;
  border-radius: 0.3rem;
}
.search .search-text img {
  width: 0.36rem;
  margin-right: 0.1rem;
}
.search input {
  height: 0.6rem;
  background: #f5f6f8;
  border-radius: 0.3rem;
  font-size: 0.28rem;
  width: 100%;
  display: none;
  padding: 0 0.3rem;
  box-sizing: border-box;
}
.batchSel {
  height: 0.88rem;
  font-size: 0.3rem;
  line-height: 0.88rem;
  background: #ffffff;
  box-shadow: 0 0.02rem 0.16rem rgba(13, 13, 14, 0.08);
  color: #595969;
  padding: 0 0.45rem;
  box-sizing: border-box;
  position: relative;
  z-index: 99;
  display: flex;
  align-items: center;
}
.batchSel h3 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 600;
}
.batchSel h3 em {
  font-style: normal;
}
.batchSel span {
  width: 0.14rem;
  height: 0.08rem;
  flex-shrink: 0;
  background: url("../images/tringle-icon.png") no-repeat center;
  background-size: 0.14rem;
  margin-left: 0.08rem;
}
.selectBox {
  height: 0.88rem;
  text-align: center;
}
.selectBox .selectWeek {
  font-size: 0.36rem;
  line-height: 0.88rem;
  color: #000000;
  height: 100%;
  width: 50%;
  margin: 0 auto;
}
.selectBox .selectWeek img {
  width: 12px;
  height: 12px;
  margin-left: 6px;
  transition: all 0.2s;
}
.selectBox .selectList {
  display: none;
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
}
.selectBox .selectList ul {
  width: 100%;
  padding: 0.22rem 0.2rem 0.12rem;
  position: relative;
  background-color: #ffffff;
  overflow: hidden;
}
.selectBox .selectList li {
  float: left;
  width: 25%;
  text-align: center;
  padding: 0.08rem;
}
.selectBox .selectList li p {
  background-color: #f5f6f8;
  border-radius: 0.08rem;
  height: 0.72rem;
  font-size: 0.26rem;
  line-height: 0.72rem;
  color: #333333;
}
.selectBox .selectList li p.active {
  color: #0099ff;
}
.selectBox.active .selectWeek img {
  transform: rotate(180deg);
}
.selectBox.active .selectList {
  display: block;
}
.tophead .leftHead .select-week {
  float: left;
}
.tophead .leftHead .select-week i {
  font-style: normal;
}
.tophead .leftHead .select-week span {
  font-size: 0.32rem;
  color: #ff9a2e;
  float: left;
  line-height: 0.88rem;
}
.tophead .leftHead .select-week em {
  float: left;
  margin-left: 6px;
  width: 7px;
  height: 45px;
  background: url(../images/tringle-icon.png) no-repeat center;
  background-size: 7px 4px;
}
.z-table {
  background-color: #F7F7FC;
  position: relative;
}
.z-table #thead {
  padding: 0 0.08rem;
  box-sizing: border-box;
}
.z-table #tbody {
  overflow: hidden;
  overflow-y: auto;
  padding: 0 0.08rem;
  box-sizing: border-box;
  padding-top: 0.16rem;
}
.z-table .thead {
  position: sticky;
  left: 0;
  top: 0;
}
.z-table .select-week {
  border-radius: 0.32rem 0.16rem 0.16rem 0;
  background: #E1EBFF;
  height: .96rem;
  width: .78rem;
  font-size: .2rem;
  color: #4D88FF;
  padding: 0.04rem .18rem 0.04rem 0.24rem;
  margin-right: 0.04rem;
  box-sizing: border-box;
  text-align: center;
  line-height: 1.3;
  position: absolute;
  left: 0;
  top: 0.08rem;
  z-index: 9;
  font-weight: bold;
}
.z-table .select-week i {
  font-size: .28rem;
}
.z-table .thead {
  overflow: hidden;
  position: relative;
  border-bottom: 0.01px solid #e5e6eb;
}
.z-table .thead tr {
  height: 1rem;
}
.z-table .thead tr td {
  color: #86909c;
  font-size: 0.28rem;
  height: 1rem;
  width: calc(12.5%);
  padding: 0.08rem 0 0.16rem 0;
}
.z-table .thead tr td:first-child {
  width: 0.7rem;
}
.z-table .thead tr td span {
  text-align: center;
  display: block;
}
.z-table .thead tr td span.today {
  color: #4D88FF;
}
.z-table .thead tr td .week {
  color: #1D2129;
  margin-top: 0.16rem;
}
.z-table .thead tr td .week.today {
  font-weight: bold;
}
.z-table .thead tr td .weekdate {
  color: #86909C;
  font-size: .2rem;
}
.z-table .thead tr td .weekdate.today {
  font-weight: 500;
}
.z-table .tbody {
  overflow: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.z-table .tbody tr {
  margin: 0 0.08rem;
}
.z-table .tbody tr:last-child {
  margin-bottom: 0.2rem;
  border-bottom: none;
}
.z-table .tbody tr.line {
  margin-bottom: 0.09rem;
}
.z-table .tbody tr.line td {
  border-bottom: 0.01rem solid #E5E6EB;
}
.z-table .tbody tr td {
  border-right: 0.01rem dashed #E5E6EB;
  border-bottom: 0.01rem dashed #E5E6EB;
  height: 1.56rem;
  border-radius: 0.04rem;
  color: #86909c;
  font-size: 0.28rem;
  cursor: not-allowed;
  width: calc(12.5%);
}
.z-table .tbody tr td:first-child {
  width: 0.7rem;
  background: none;
  flex-flow: column;
  flex-wrap: wrap;
  border-right: none;
}
.z-table .tbody tr td:first-child span {
  display: block;
  text-align: center;
  width: 100%;
}
.z-table .tbody tr td:first-child span.section {
  color: #4E5969;
  font-size: .28rem;
  font-weight: bold;
}
.z-table .tbody tr td:first-child span.time {
  color: #86909c;
  font-size: .2rem;
}
.z-table .tbody tr td:last-child {
  margin-right: 0;
}
.z-table .tbody tr td .course {
  box-sizing: border-box;
  margin: 0.08rem 0.04rem;
  padding: 0.12rem 0;
  cursor: pointer;
  border-radius: 0.08rem;
  border: 0.1px solid #A6E1B1;
  background: #E8FFEA;
  box-shadow: 0 0 0.08rem 0 rgba(62, 179, 90, 0.1);
  height: calc(100% - 0.16rem);
}
.z-table .tbody tr td .course h5 {
  color: #3EB35A;
  font-family: "PingFang SC";
  font-size: .22rem;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 0.06rem;
  position: relative;
}
.z-table .tbody tr td .course h5::after {
  content: "";
  border-radius: 0 0.034rem 0.034rem 0;
  background: #3EB35A;
  width: 0.04rem;
  height: 0.16rem;
  position: absolute;
  left: -0.01rem;
  top: 0.09rem;
}
.z-table .tbody tr td .course span {
  display: none;
}
.z-table .tbody tr td .course.selCourse {
  background: #E1EBFF;
  position: relative;
  box-shadow: 0 0 0.08rem 0 rgba(77, 136, 255, 0.1);
  border: 0.1px solid #B8D3FF;
}
.z-table .tbody tr td .course.selCourse h5 {
  color: #4D88FF;
}
.z-table .tbody tr td .course.selCourse h5::after {
  content: "";
  background: #4D88FF;
}
.z-table .tbody tr td .course.selCourse span {
  display: block;
  width: 16px;
  height: 16px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
#selCourseList {
  overflow: hidden;
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch;
}
#list {
  overflow: hidden;
}
#list .z-search-course {
  margin: 0.28rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#list .z-search-course input {
  display: block;
  flex: 1;
  height: 0.6rem;
  border-radius: 0.08rem;
  border: 1px solid #ddd;
  padding: 0 0.2rem;
  box-sizing: border-box;
  font-size: 0.26rem;
}
#list .z-search-course input::placeholder {
  color: #999;
}
#list .z-search-course .z-search-con {
  display: flex;
  align-items: center;
}
#list .z-search-course h3 {
  font-size: 0.3rem;
  margin-left: 0.3rem;
  flex: 1;
  color: #4d88ff;
}
#list .z-search-course img {
  width: 0.28rem;
  height: 0.28rem;
}
#list .z-list {
  overflow: hidden;
  padding: 0 0.3rem;
  cursor: pointer;
}
#list .z-list .z-course {
  padding: 0.2rem;
  margin-bottom: 0.24rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 0.2rem;
  border: 0.01rem solid #e1ebff;
  background: #fff;
  box-shadow: 0px 0px 0.08rem 0px rgba(0, 0, 0, 0.1);
}
#list .z-list .z-course .z-course-mes {
  flex: 1;
}
#list .z-list .z-course .z-course-mes p {
  font-size: 0.3rem;
  color: #4e5969;
}
#list .z-list .z-course .z-course-mes p:last-child span {
  padding-right: 0.24rem;
}
#list .z-list .z-course .z-btn {
  flex-shrink: 0;
}
#list .z-list .z-course .z-btn span {
  display: block;
}
#list .z-list .z-course .z-btn .btn-detail {
  border-radius: 36px;
  background: #4d88ff;
  width: 1.02rem;
  height: 0.48rem;
  text-align: center;
  line-height: 0.46rem;
  color: #ffffff;
  display: none;
  font-size: 0.26rem;
  margin-bottom: 0.2rem;
}
#list .z-list .z-course .z-btn .btn-exit {
  display: none;
}
#list .z-list .z-course .z-btn .btn-exit,
#list .z-list .z-course .z-btn .btn-sel {
  width: 1.02rem;
  height: 0.48rem;
  border: 1px solid #4d88ff;
  box-sizing: border-box;
  color: #4d88ff;
  background-color: #fff;
  border-radius: 36px;
  font-size: 14px;
  text-align: center;
  line-height: 0.46rem;
  font-size: 0.26rem;
}
#list .z-list .z-course .z-course-title {
  display: flex;
  align-items: center;
  margin-bottom: 0.16rem;
}
#list .z-list .z-course .z-course-title h3 {
  font-size: 0.32rem;
  color: #1d2129;
}
#list .z-list .z-course .z-course-title span {
  font-size: 0.3rem;
  color: #4d88ff;
  padding-left: 0.2rem;
}
#list .z-list .z-course .z-mes {
  color: #1d2129;
  font-size: 14px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 6px;
}
#list .z-list .z-course .z-mes p {
  color: #4e5969;
  font-size: 0.3rem;
}
#list .z-list .z-course .z-mes p span {
  margin-right: 0.24rem;
}
#list .z-list .z-course.active {
  background: #e1ebff;
}
#list .z-list .z-course.active .z-btn .btn-detail {
  display: block;
}
.dialog-wrap {
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}
.dialog-wrap .dialog {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  border-radius: 0.1rem;
  overflow: hidden;
}
.dialog-wrap .dialog-title {
  font-size: 16px;
  color: #1d2129;
  height: 56px;
  line-height: 56px;
  border-bottom: 1px solid #e5e6eb;
  text-indent: 30px;
}
.dialog-wrap .dialog-btn {
  height: 0.88rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: 0.01rem solid #ebebec;
}
.dialog-wrap .dialog-btn .sure {
  width: 50%;
  height: 0.88rem;
  outline: none;
  border: none;
  background-color: #ffffff;
  color: #4d88ff;
  border-left: 0.01rem solid #ebebec;
  font-size: 0.32rem;
}
.dialog-wrap .dialog-btn .cancel {
  width: 50%;
  height: 0.88rem;
  outline: none;
  border: none;
  background-color: #ffffff;
  color: #4d88ff;
  font-size: 0.32rem;
}
.dialog-wrap.dialog-plan {
  display: none;
}
.dialog-wrap.dialog-plan .dialog {
  width: 6.34rem;
  border-radius: 0.26rem;
}
.dialog-wrap.dialog-plan .dialog .dialog-con {
  overflow: hidden;
  padding: 0.24rem 0.48rem;
  max-height: 6.2rem;
  overflow-y: auto;
}
.dialog-wrap.dialog-plan .dialog .plan {
  padding: .24rem 0;
  border-bottom: 0.01rem dashed #E5E6EB;
}
.dialog-wrap.dialog-plan .dialog .plan:last-child {
  border-bottom: none;
}
.dialog-wrap.dialog-plan .dialog .plan h1 {
  font-size: 0.32rem;
  color: #1D2129;
  margin-bottom: 0.12rem;
}
.dialog-wrap.dialog-plan .dialog .plan p {
  font-size: 0.28rem;
  color: #4E5969;
  margin-bottom: 0.12rem;
}
.dialog-wrap.dialog-plan .dialog .dialog-btn {
  height: 0.88rem;
  border-top: 0.01rem solid #EBEBEB;
  text-align: center;
  align-items: center;
  justify-content: center;
  color: #4D88FF;
  font-size: 0.32rem;
}
.dialog-wrap.dialog-course {
  display: none;
}
.dialog-wrap.dialog-course .dialog {
  width: 7.29rem;
}
.dialog-wrap.dialog-course .dialog .z-tab-search {
  margin: 0.41rem 0.3rem 0.2rem;
  border-radius: 0.3rem;
  background: #f5f6f8;
  height: 0.6rem;
}
.dialog-wrap.dialog-course .dialog .z-tab-search input {
  display: block;
  width: 100%;
  height: 0.6rem;
  border: none;
  background: none;
  text-align: center;
  padding: 0 0.3rem;
  box-sizing: border-box;
  font-size: 0.28rem;
  display: none;
}
.dialog-wrap.dialog-course .dialog .z-tab-search .z-search {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c9cdd4;
  font-size: 0.28rem;
  height: 0.6rem;
}
.dialog-wrap.dialog-course .dialog .z-tab-search .z-search img {
  margin-right: 0.1rem;
  width: 0.3rem;
}
.dialog-wrap.dialog-course .dialog .z-list {
  overflow: hidden;
  padding: 0 0.3rem;
  max-height: 7rem;
  overflow-y: auto;
  cursor: pointer;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course {
  background: #f8f8f8;
  padding: 0.2rem 0.33rem;
  margin-bottom: 10px;
  border-radius: 0.2rem;
  border: 0.01rem solid #e1ebff;
  background: #fff;
  box-shadow: 0px 0px 0.08rem 0px rgba(0, 0, 0, 0.1);
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-course-title {
  display: flex;
  align-items: center;
  margin-bottom: 0.16rem;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-course-title h3 {
  font-size: 0.32rem;
  color: #1d2129;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course .z-course-title span {
  color: #4d88ff;
  font-size: 0.3rem;
  padding-left: 0.2rem;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course p {
  color: #4e5969;
  font-size: 0.3rem;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 0.14rem;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course p span {
  margin-right: 0.3rem;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course p span span {
  color: #4e5969;
}
.dialog-wrap.dialog-course .dialog .z-list .z-course.active {
  background: #e1ebff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
}
.dialog-wrap.dialog-course .dialog .z-list .z-course.z-full {
  cursor: not-allowed;
}
.dialog-wrap.dialog-mes {
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  display: none;
}
.dialog-wrap.dialog-mes .dialog {
  position: fixed;
  left: 0.79rem;
  right: 0.79rem;
  top: 50%;
  transform: translateY(-50%);
  background-color: #ffffff;
  border-radius: 0.26rem;
  overflow: hidden;
}
.dialog-wrap.dialog-mes .dialog-title {
  font-size: 16px;
  color: #1d2129;
  height: 56px;
  line-height: 56px;
  border-bottom: 1px solid #e5e6eb;
  text-indent: 30px;
}
.dialog-wrap.dialog-mes .dialog-btn {
  height: 0.88rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: 0.01rem solid #EBEBEB;
}
.dialog-wrap.dialog-mes .dialog-btn .sure {
  width: 50%;
  height: 0.88rem;
  outline: none;
  border: none;
  background-color: #ffffff;
  color: #4D88FF;
  border-left: 0.01rem solid #ebebec;
  font-size: 0.32rem;
}
.dialog-wrap.dialog-mes .dialog-btn .cancel {
  width: 50%;
  height: 0.88rem;
  outline: none;
  border: none;
  background-color: #ffffff;
  color: #4D88FF;
  font-size: 0.32rem;
}
.dialog-wrap.dialog-mes .dialog-con {
  padding: 0.48rem;
  height: calc(100vh - 330px);
  overflow-y: auto;
}
.dialog-wrap.dialog-mes .dialog-con h1 {
  font-size: 0.32rem;
  color: #1D2129;
  margin-bottom: 0.32rem;
  font-weight: bold;
}
.dialog-wrap.dialog-mes .dialog-con .item {
  font-size: .28rem;
  color: #1d2129;
  display: flex;
  margin-bottom: 0.32rem;
}
.dialog-wrap.dialog-mes .dialog-con .item h3 {
  width: 1.96rem;
  flex-shrink: 0;
  margin-right: 0.08rem;
  color: #1D2129;
  font-weight: 500;
}
.dialog-wrap.dialog-mes .dialog-con .item span {
  color: #4E5969;
}
.dialog-wrap.dialog-mes .dialog-con .item:last-child {
  display: block;
  margin-bottom: 0;
}
.dialog-wrap.dialog-mes .dialog-con .item:last-child h3 {
  margin-bottom: 0.08rem;
}
.dialog-wrap.dialog-mes .dialog-con .item:last-child .intro {
  color: #4E5969;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: .4rem;
}
.dialog-wrap.dialog-mes .dialog-con .item:last-child .intro::before {
  content: '';
  float: right;
  width: 0;
  height: 1.2rem;
  /*先随便设置一个高度*/
  background: #ff0000;
}
.dialog-wrap.dialog-mes .dialog-con .item:last-child .intro span {
  color: #4D88FF;
  float: right;
  clear: both;
  padding-right: .24rem;
  position: relative;
}
.dialog-wrap.dialog-mes .dialog-con .item:last-child .intro span::after {
  content: "";
  border-top: 0.08rem solid #4D88FF;
  border-left: 0.08rem solid transparent;
  border-right: 0.08rem solid transparent;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.dialog-wrap.dialog-mes .dialog-con .item .wrapper {
  display: flex;
  overflow: hidden;
  border-radius: 8px;
  color: #4E5969;
  position: relative;
}
.dialog-wrap.dialog-mes .dialog-con .item .wrapper .ellipse {
  position: absolute;
  bottom: 0;
  right: .85rem;
}
.dialog-wrap.dialog-mes .dialog-con .item .text {
  font-size: .28rem;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
  line-height: .4rem;
}
.dialog-wrap.dialog-mes .dialog-con .item .text::before {
  content: '';
  height: calc(100% - 0.38rem);
  float: right;
}
.dialog-wrap.dialog-mes .dialog-con .item .text.ellText {
  height: 1.6rem;
}
.dialog-wrap.dialog-mes .dialog-con .item .btn {
  float: right;
  clear: both;
  font-size: .32rem;
  color: #4D88FF;
  line-height: .32rem;
  margin-left: .1rem;
  position: relative;
}
.dialog-wrap.dialog-mes .dialog-con .item .btn::before {
  content: '展开';
  color: #4D88FF;
  font-size: .26rem;
  padding-right: .24rem;
  padding-left: .24rem;
}
.dialog-wrap.dialog-mes .dialog-con .item .btn::after {
  content: "";
  border-top: 0.06rem solid #4D88FF;
  border-left: 0.06rem solid transparent;
  border-right: 0.06rem solid transparent;
  position: absolute;
  right: 0;
  top: 66%;
  transform: translateY(-50%);
}
.dialog-wrap.dialog-mes .dialog-con .item .btn.collapse::before {
  content: '收起';
}
.dialog-wrap.dialog-mes .dialog-con .item .btn.collapse::after {
  content: "";
  transform: rotate(180deg);
  top: 48%;
}
.week-dialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  background: rgba(0, 0, 0, 0.2);
  display: none;
}
.week-dialog .w-con {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  transform: translateY(100%);
  -webkit-transform: translateY(100%);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  /* iphonex 等安全区设置，底部安全区适配 */
  padding-bottom: constant(safe-area-inset-bottom);
  /* 兼容 iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom);
  /* 兼容 iOS >= 11.2 */
}
.week-dialog .w-con.active {
  transform: translateY(0);
  -webkit-transform: translateY(0);
}
.week-dialog .w-con .w-head {
  background: #ffffff;
  border-radius: 0.32rem 0.32rem 0 0;
  padding: 0.32rem;
}
.week-dialog .w-con .w-head h3 {
  text-align: center;
  color: #1D2129;
  font-size: .32rem;
}
.week-dialog .w-con .w-box {
  height: auto;
  background-color: #fff;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding: 0 .32rem .16rem 0.32rem;
}
.week-dialog .w-con .w-box ul {
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
}
.week-dialog .w-con .w-box ul li {
  width: 1.24rem;
  margin-right: 0.16rem;
  height: 0.88rem;
  background: #F7F8FA;
  border-radius: 0.08rem;
  margin-bottom: 0.16rem;
  font-size: 0.28rem;
  color: #4E5969;
  text-align: center;
  line-height: 0.88rem;
}
.week-dialog .w-con .w-box ul li.cur {
  background: #4d88ff;
  color: #fff;
}
.week-dialog .w-con .w-box ul li:hover {
  background-color: #DBEAFF;
}
.week-dialog .w-con .w-box ul li:nth-child(5n) {
  margin-right: 0;
}
#tipsBox {
  position: fixed;
  left: 50%;
  top: 50%;
  z-index: 99999;
  transform: translateX(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 0.2rem 0.3rem;
  border-radius: 0.08rem;
  font-size: 0.3rem;
  display: none;
}
.selectWeek-dialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  background: rgba(0, 0, 0, 0.2);
  display: none;
}
.selectWeek-dialog .w-con {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  transform: translateY(100%);
  -webkit-transform: translateY(100%);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.selectWeek-dialog .w-con.active {
  transform: translateY(0);
  -webkit-transform: translateY(0);
}
.selectWeek-dialog .w-con .w-head {
  background: #ffffff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 0.32rem 0.32rem 0 0;
  width: 100%;
  height: 0.9rem;
}
.selectWeek-dialog .w-con .w-head h3 {
  text-align: center;
  line-height: 0.9rem;
  color: #333333;
  font-weight: 400;
  font-size: 16px;
}
.selectWeek-dialog .w-con .w-box {
  width: 100%;
  height: auto;
  background-color: #fff;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  padding: 15px 35px;
}
.selectWeek-dialog .w-con .w-box ul {
  overflow: hidden;
}
.selectWeek-dialog .w-con .w-box ul li {
  float: left;
  width: 45%;
  margin-right: 4%;
  height: 0.68rem;
  background: #f1f3f6;
  border-radius: 0.08rem;
  margin-bottom: 12px;
  font-size: 13px;
  color: #4e5969;
  text-align: center;
  line-height: 0.68rem;
}
.selectWeek-dialog .w-con .w-box ul li.cur {
  background: #4d88ff;
  color: #fff;
}
.selectWeek-dialog .w-con .w-box ul li:nth-child(6n) {
  margin-right: 0;
}
.tophead .centerHead .selectBox em {
  float: right;
  margin-left: -7px;
  width: 7px;
  height: 45px;
  background: url(../images/tringle-icon.png) no-repeat center;
  background-size: 7px 4px;
}
