.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
input[type=search] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
  font-family: inherit;
  font-size: 100%;
}
input::-webkit-search-decoration,
input::-webkit-search-cancel-button {
  display: none;
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #86909C;
  font-size: 0.14rem;
}
.hide {
  display: none;
}
body {
  background-color: #fff;
}
.hide {
  display: none;
}
.suspend-classes {
  display: none;
}
.sup-lesson-time {
  display: none;
}
.sup-lesson-range {
  display: none;
}
.main {
  overflow: hidden;
  margin-top: 0.64rem;
  overflow-y: auto;
  z-index: 10;
  padding-bottom: 1.04rem;
}
.main .lab-list {
  padding: 0.12rem 0.15rem 0;
}
.main .lable {
  margin-bottom: 0.24rem;
}
.main .lable.lab-switch {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  height: 0.26rem;
}
.main .lable.lab-switch .title {
  line-height: 0.26rem;
  margin-bottom: 0;
}
.main .lable .title {
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #1D2129;
  margin-bottom: 0.14rem;
}
.main .lable .select-section {
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
}
.main .lable .select-section .sel {
  flex: 1;
  height: 0.34rem;
  background: #FFFFFF;
  border: 0.01rem solid #E5E6EB;
  border-radius: 0.04rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.1rem;
}
.main .lable .select-section .sel span {
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #86909C;
}
.main .lable .select-section .sel span.deepColors {
  color: #4E5969;
}
.main .lable .select-section .sel em {
  width: 0.1rem;
  height: 0.1rem;
  background: url(../images/slide-icons.png) no-repeat center;
  background-size: 0.1rem;
}
.main .lable .select-section .symbol {
  width: 0.2rem;
  color: #86909C;
  font-weight: 400;
  font-size: 0.14rem;
  line-height: 0.34rem;
  text-align: center;
}
.main .lable .inputs {
  width: 100%;
  height: 0.34rem;
  padding: 0 0.1rem;
  border: 0.01rem solid #E5E6EB;
  border-radius: 0.04rem;
  background-color: #fff;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
}
.main .lable .inputs span {
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #86909C;
}
.main .lable .inputs span.deepColors {
  color: #4E5969;
}
.main .lable .inputs em {
  width: 0.12rem;
  height: 0.12rem;
  background: url(../images/times-icons.png) no-repeat center;
  background-size: 0.12rem;
}
.main .lable .input {
  width: 100%;
  height: 0.34rem;
  background: #FFFFFF;
  border: 0.01rem solid #E5E6EB;
  border-radius: 0.04rem;
  padding-left: 0.1rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .lable .input.disabled {
  line-height: 0.32rem;
  font-size: 0.14rem;
  color: #4E5969;
  background: #F7F8FA;
}
.main .lable .input input {
  display: block;
  width: 100%;
  height: 0.32rem;
  outline: none;
  border: none;
  color: #1D2129;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.main .lable .switch-wrap {
  overflow: hidden;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
}
.main .lable .switch-wrap em {
  float: left;
  font-size: 0.13rem;
  color: #86909C;
  margin-right: 0.07rem;
  line-height: 0.26rem;
}
.main .lable .switch-wrap span {
  display: block;
}
.main .lable .switch-wrap span.switch {
  cursor: pointer;
  float: left;
  display: block;
  position: relative;
  -webkit-appearance: none;
  width: 0.44rem;
  height: 0.26rem;
  border-radius: 0.13rem;
  background: #C9CDD4;
  overflow: hidden;
  outline: none;
  border: none;
}
.main .lable .switch-wrap span.switch:before {
  content: "";
  position: absolute;
  left: 0.02rem;
  top: 0.02rem;
  width: 0.22rem;
  height: 0.22rem;
  border-radius: 50%;
  box-shadow: 0 0 0.04rem 0 rgba(0, 0, 0, 0.2);
  background-color: #fff;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
}
.main .lable .switch-wrap span.switch.active {
  background: #4D88FF;
}
.main .lable .switch-wrap span.switch.active:before {
  content: "";
  position: absolute;
  left: 0.2rem;
  top: 0.02rem;
  width: 0.22rem;
  height: 0.22rem;
  border-radius: 50%;
  box-shadow: 0 0 0.04rem 0 rgba(0, 0, 0, 0.2);
  background-color: #fff;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
}
.main .lable .kalamu-area {
  width: 100%;
  min-height: 1.2rem;
  background: #FFFFFF;
  border: 0.01rem solid #E5E6EB;
  border-radius: 0.04rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding: 0.07rem 0.1rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  font-size: 0.14rem;
  outline: none;
  letter-spacing: 0.02rem;
  color: #4E5969;
  position: relative;
}
.main .lable .kalamu-area:empty:before {
  content: attr(placeholder);
  font-size: 0.14rem;
  color: #86909C;
  line-height: normal;
}
.main .lable .kalamu-area:focus:before {
  content: none;
}
.school-bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1.04rem;
  background-color: #fff;
  box-shadow: 0px -0.08rem 0.12rem -0.04rem rgba(184, 184, 210, 0.2);
}
.school-bottom .handle {
  width: 100%;
  height: 0.7rem;
  padding: 0.15rem 0.16rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: space-between;
}
.school-bottom .handle .btn {
  flex: 1;
  height: 0.4rem;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.school-bottom .handle .btn.cancle {
  margin-right: 0.2rem;
  height: 0.4rem;
  background: #FFFFFF;
  border: 0.01rem solid #C9CDD4;
  border-radius: 0.2rem;
  font-size: 0.14rem;
  color: #4E5969;
  text-align: center;
  line-height: 0.38rem;
}
.school-bottom .handle .btn.confirm {
  background: #4D88FF;
  border: 0.01rem solid #4D88FF;
  border-radius: 0.2rem;
  font-size: 0.14rem;
  color: #FFFFFF;
  text-align: center;
  line-height: 0.38rem;
}
.markers {
  display: none;
  position: fixed;
  z-index: 1005;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(10, 10, 10, 0.5);
}
.bot-window {
  z-index: 1006;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  padding-top: 0.15rem;
  background: #ffffff;
  border-radius: 0.16rem 0.16rem 0px 0px;
  transform: translate(0, 120%);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.bot-window.move {
  transform: translate(0, 0);
}
.bot-window.category .bot-list {
  height: 1.5rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.bot-window .bot-list {
  height: auto;
  height: 1.5rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.bot-window .bot-list ul li {
  width: 100%;
  height: 0.5rem;
  line-height: 0.5rem;
  text-align: center;
  font-size: 0.14rem;
  color: #0a0a0a;
  border-bottom: 0.01rem solid rgba(10, 10, 10, 0.05);
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.bot-window .bot-list ul li.cur {
  color: #2962ff;
}
.bot-window .bot-close {
  width: 100%;
  height: 0.56rem;
  line-height: 0.56rem;
  background-color: #fafbfc;
  text-align: center;
  font-size: 0.14rem;
  color: #2962ff;
}
.ios-select-widget-box.olay {
  z-index: 1500;
}
.ios-select-widget-box header.iosselect-header a {
  color: #4D88FF;
}
