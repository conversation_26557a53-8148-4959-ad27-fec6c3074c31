body {
    background-color: #F6F7F9;
}

.layui-table-cell {
    padding: 0 10px;
    line-height: 38px;
}

.layui-table-header thead tr {
    background: #F1F3F6;
}

.layui-table-view .layui-table th {
    font-weight: normal;
    color: #86909C;
    font-size: 14px;
}

.layui-table-body tr:nth-child(2n) {
    background: #FAFBFC;
}

.layui-table-view .layui-table td {
    color: #4E5969;
    height: 38px;
}

.layui-table-view .layui-table td,
.layui-table-view .layui-table th {
    border-color: #E8EBF3;
}

.delBtn {
    color: #F76560;
}

.delBtn:hover {
    color: #F76560;
    cursor: pointer;
}

.layui-week {
    background-color: #E1EBFF !important;
}

.layui-tr-total td {
    background-color: #F1F3F6 !important;
}

.layui-tr-total td:first-child {
    color: #4E5969 !important;
    font-weight: 900;
}

.layui-tr-total td.layui-total {
    background-color: #B8D3FF !important;
}

.layui-tr-total td select {
    display: none !important;
}

.layui-table-view .layui-table td[data-edit]:hover:after {
    border: 1px solid #4D88FF;
}

.layui-table-edit {
    text-align: center !important;
}

.layui-table-edit:focus {
    border-color: #4D88FF !important;
}

.layui-table-view select[lay-ignore] {
    width: 90px;
    height: 30px;
}

.layui-table-total tr td:first-child {
    color: #4E5969 !important;
    font-weight: 900;
}

.container {
    background: #FFFFFF;
    border-radius: 8px;
    margin: 20px;
    overflow: hidden;
}

.container .top {
    width: 100%;
    height: 60px;
    border-bottom: 1px solid #E8EBF1;
    position: relative;
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
    align-items: center;
}

.container .top .title {
    padding-left: 30px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 36px;
}

.container .top .title .back {
    padding-left: 22px;
    background: url(../../images/cultivation/back.png) no-repeat left center;
    background-size: 16px;
    color: #7D92B3;
    font-size: 14px;
    cursor: pointer;
    margin-right: 16px;
}

.container .top .title .levelone {
    padding-left: 9px;
    position: relative;
    color: #1D2129;
    font-weight: 700;
    font-size: 16px;
    margin-right: 6px;
}

.container .top .title .levelone:after {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    width: 3px;
    height: 16px;
    background: #4D88FF;
    border-radius: 2px;
}

.container .top .title .icon {
    width: 12px;
    height: 12px;
    background: url(../../images/cultivation/arrow-right.png) no-repeat center;
    background-size: 12px;
    margin-right: 6px;
}

.container .top .title .leveltwo {
    color: #1D2129;
    font-weight: 700;
    font-size: 16px;
}

.container .top .btn {
    position: absolute;
    top: 17px;
    right: 28px;
    width: 116px;
    height: 36px;
    text-align: center;
    line-height: 36px;
    cursor: pointer;
    font-size: 14px;
    color: #FFFFFF;
    background: #4D88FF;
    box-shadow: 0px 0px 10px #4D88FF;
    border-radius: 4px;
}

.container .top h4 {
    position: relative;
    color: #1D2129;
    font-size: 16px;
    margin-left: 30px;
    padding-left: 9px;
    font-weight: bold;
}

.container .top h4::after {
    content: "";
    width: 3px;
    height: 16px;
    background: #4D88FF;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 4px;
}

.container #tabBox {
    margin: 0 30px 16px;
    overflow: hidden;
    position: relative;
}

.container #tabBox .amountTo {
    position: absolute;
    left: 0;
    bottom: 1px;
    line-height: 38px;
    width: 300px;
    background-color: #f2f2f2;
    box-shadow: 1px 0 8px rgba(0, 0, 0, 0.08);
}

.container #tabBox .amountTo span {
    display: block;
    float: right;
    margin-right: 80px;
}

.container .major-opt {
    margin: 0 30px;
    overflow: hidden;
}

.container .major-opt .major-mes {
    display: flex;
    align-items: center;
    color: #1D2129;
    font-size: 16px;
    line-height: 60px;
}

.container .major-opt .major-mes span {
    margin-right: 40px;
}

.container .major-opt .major-button {
    display: flex;
    align-items: center;
    float: right;
    margin-bottom: 20px;
}

.container .major-opt .major-button button {
    border: 1px solid #4D88FF;
    box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
    border-radius: 4px;
    height: 36px;
    background: #FFFFFF;
    padding: 0 20px;
    color: #4D88FF;
    font-size: 14px;
    margin-left: 12px;
    cursor: pointer;
}

.dialog {
    width: 1104px;
    border-radius: 10px;
    background-color: #ffffff;
    overflow: hidden;
}

.dialog .dialog-title {
    height: 56px;
    border-bottom: 1px solid #e5e6eb;
    color: #1d2129;
    font-size: 16px;
    line-height: 56px;
    text-indent: 30px;
}

.dialog .dialog-con {
    padding: 0 100px;
    overflow: hidden;
    box-sizing: border-box;
}

.dialog .dialog-con .layui-form {
    margin: 30px 0 24px;
}

.dialog .dialog-con .layui-form .layui-form-label {
    width: 56px;
    padding: 0;
    line-height: 34px;
    padding-right: 8px;
}

.dialog .dialog-con .layui-form .layui-input-inline {
    margin-right: 17px;
}

.dialog .dialog-con .layui-form .layui-input-inline .layui-input {
    height: 34px;
    line-height: 34px;
    width: 120px;
}

.dialog .dialog-con .layui-form .layui-input-inline .layui-form-select {
    width: 100px;
}

.dialog .dialog-con .layui-form .layui-input-inline .layui-form-select .layui-input {
    width: 100px;
}

.dialog .dialog-con .layui-form .layui-btn {
    width: 64px;
    height: 34px;
    line-height: 32px;
    background: #4d88ff;
    border-radius: 6px;
}

.dialog .dialog-con .layui-form .layui-btn.layui-btn-primary {
    background-color: unset;
    border: 1px solid #4d88ff;
    color: #4d88ff;
}

.dialog .dialog-con .layui-form .layui-btn.layui-btn-primary:hover {
    border-color: #4d88ff;
}

.dialog .dialog-con .layui-table-page {
    text-align: right;
}

.dialog .dialog-con .layui-table-view select[lay-ignore] {
    height: 20px;
}

.dialog .dialog-con .layui-table-page .layui-laypage input:focus,
.dialog .dialog-con .layui-table-page .layui-laypage select:focus {
    border-color: #4d88ff !important;
}

.dialog .dialog-con .layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: #4d88ff !important;
}

.dialog .dialog-con .layui-table-page .layui-laypage a:hover,
.dialog .dialog-con .layui-table-page .layui-laypage span.layui-laypage-curr:hover {
    color: #4d88ff !important;
}

.dialog .dialog-con .layui-form-select dl dd.layui-this {
    color: #4d88ff !important;
}

.dialog .dialog-con .courseList {
    position: relative;
}

.dialog .dialog-con .courseList .selCourse {
    position: absolute;
    left: 10px;
    bottom: 0;
    color: #1d2129;
    font-size: 14px;
    line-height: 40px;
}

.dialog .dialog-con .courseList .selCourse em {
    color: #4d88ff;
    padding: 0 4px;
}

.dialog .dialog-footer {
    height: 70px;
    border-top: 1px solid #e5e6eb;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.dialog .dialog-footer button {
    width: 88px;
    height: 36px;
    border: 1px solid #c9cdd4;
    border-radius: 18px;
    color: #4e5969;
    font-size: 14px;
    background-color: #ffffff;
    cursor: pointer;
}

.dialog .dialog-footer button:last-child {
    background: #4d88ff;
    border-color: #4d88ff;
    box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
    color: #ffffff;
    margin: 0 30px 0 16px;
}

.layui-form-checkbox[lay-skin="primary"] i:hover {
    border-color: #4d88ff !important;
}

.layui-form-checked[lay-skin="primary"] i {
    border-color: #4d88ff !important;
    background-color: #4d88ff;
}

.container .remark {
    margin: 24px 30px 30px;
}

.container .remark h1 {
    font-size: 14px;
    margin-bottom: 10px;
}

.container .remark textarea {
    display: block;
    width: 100%;
    border: 1px solid #dddddd;
    border-radius: 4px;
    height: 200px;
    padding: 10px;
    font-size: 14px;
    box-sizing: border-box;
    background-color: #f9f9f9;
}

.container .remark textarea::placeholder {
    color: #bbbaba;
}

#selTextbook {
    width: 1322px;
    display: none;
}

#selTextbook .tips {
    width: 100%;
    color: #6581BA;
    line-height: 50px;
    text-align: center;
    background: #E1EBFF;
}

#selTextbook .dialog-con {
    padding: 0 60px;
}

#selTextbook .dialog-con .layui-form .layui-input-inline .layui-input {
    width: 240px;
}

#selTextbook .dialog-con .z-check {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #6581BA;
    position: absolute;
    left: 27px;
    bottom: 8px;
}

#selTextbook .dialog-con .z-check .check {
    width: 28px;
    height: 28px;
    cursor: pointer;
    margin-right: 6px;
    background: url(../../images/cultivation/mooc/check.png) no-repeat center;
    background-size: 28px;
}

#selTextbook .dialog-con .z-check .check.checked {
    background: url(../../images/cultivation/mooc/checked.png) no-repeat center;
}

#selTextbook .dialog-con .selCourse {
    left: 160px;
    color: #8F97A8;
    line-height: 43px;
}

.container .txt-blue {
    color: #4D88FF;
}

#textbookTips {
    background-color: #4E5969;
    border-radius: 8px;
    width: 265px;
    box-sizing: border-box;
    color: #ffffff;
    position: fixed;
    display: none;
    z-index: 9999;
    padding: 0 2px;
}

#textbookTips .textbook-con-wrap {
    max-height: 240px;
    overflow-y: auto;
    margin: 8px 0;
    padding: 8px 30px;
}

#textbookTips::after {
    content: "";
    border-bottom: 8px solid #4E5969;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    position: absolute;
    top: -8px;
    left: 80px;
}

#textbookTips .textbook-item {
    display: flex;
    align-items: center;
    line-height: 20px;
    margin-bottom: 4px;
}

#textbookTips .textbook-item:last-child {
    margin-bottom: 0;
}

#textbookTips .textbook-item h5 {
    width: 70px;
}

#textbookTips .textbook-con {
    margin-bottom: 8px;
}

#textbookTips .textbook-con:last-child {
    margin-bottom: 0;
}

#textbookTips.textbookBot::after {
    content: "";
    border-top: 8px solid #4E5969;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: unset;
    position: absolute;
    bottom: -8px;
    left: 80px;
    top: unset;
}

#importTeachProcess {
    width: 563px;
    display: none;
}

#importTeachProcess .dialog-title {
    display: flex;
    justify-content: space-between;
}

#importTeachProcess .dialog-title .opt-right {
    display: flex;
    align-items: center;
}

#importTeachProcess .dialog-title .opt-right .btn-export {
    font-size: 14px;
    color: #4D88FF;
    padding-left: 20px;
    background: url('../../images/cultivation/mooc/export.png') no-repeat left center;
    cursor: pointer;
    margin-right: 20px;
    text-indent: 0;
}

#importTeachProcess .dialog-title .opt-right .close-btn {
    width: 16px;
    height: 16px;
    background: url('../../images/cultivation/mooc/close.png') no-repeat center;
    background-size: 16px;
    margin-right: 30px;
    cursor: pointer;
}

#importTeachProcess .dialog-con {
    padding: 30px;
    line-height: 22px;
    font-size: 14px;
}

#importTeachProcess .dialog-con h5 {
    color: #1D2129;
}

#importTeachProcess .dialog-con p {
    color: #4E5969;
}

#importTeachProcess .dialog-con p a {
    color: #4D88FF;
    cursor: pointer;
}

#importTeachProcess .dialog-con button {
    border-radius: 20px;
    margin: 24px auto 10px;
    box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
    background-color: #4D88FF;
    padding: 0 30px;
    display: block;
}

#importTeachProcessFile {
    width: 560px;
    display: none;
}

#importTeachProcessFile .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#importTeachProcessFile .dialog-title .close-btn {
    width: 16px;
    height: 16px;
    background: url('../../images/cultivation/mooc/close.png') no-repeat center;
    background-size: 16px;
    margin-right: 30px;
    cursor: pointer;
}

#importTeachProcessFile .dialog-con {
    padding: 40px 100px;
}

#importTeachProcessFile .dialog-con .loding {
    display: block;
    margin: 0 auto 25px;
    width: 48px;
    opacity: 0.7;
}

#importTeachProcessFile .dialog-con .importTips {
    font-size: 16px;
    color: #1D2129;
    line-height: 24px;
    text-align: center;
}

#importTeachProcessFile .dialog-con .importTips.importTipsSuccess p {
    text-align: center;
}

#importTeachProcessFile .dialog-con .importTips.importTipsSuccess p:last-child {
    display: none;
}

#importTeachProcessFile .dialog-con .importTips.importTipsSuccess p:first-child span.error {
    display: none;
}

#importTeachProcessFile .dialog-con .importTips.importTipsError {
    padding-left: 32px;
}

#importTeachProcessFile .dialog-con .importTips.importTipsError p:first-child .icon {
    background: url('../../images/cultivation/mooc/error-icon.png') no-repeat left top;
    background-size: 24px;
}

#importTeachProcessFile .dialog-con .importTips.importTipsError p:first-child span.error {
    display: unset;
}

#importTeachProcessFile .dialog-con .importTips.importTipsError p:last-child {
    display: unset;
}

#importTeachProcessFile .dialog-con .importTips p {
    white-space: nowrap;
}

#importTeachProcessFile .dialog-con .importTips p:last-child {
    margin-top: 2px;
}

#importTeachProcessFile .dialog-con .importTips p:first-child {
    display: flex;
    align-items: center;
    justify-content: center;
}

#importTeachProcessFile .dialog-con .importTips p:first-child .icon {
    width: 24px;
    height: 24px;
    background: url('../../images/cultivation/mooc/success-icon.png') no-repeat center;
    background-size: 24px;
    margin-right: 8px;
    flex-shrink: 0;
}

#importTeachProcessFile .dialog-con .importTips p em {
    padding: 0 4px;
}

#importTeachProcessFile .dialog-con .importTips p .green {
    color: #3EB35A;
}

#importTeachProcessFile .dialog-con .importTips p .yellow {
    color: #FFB026;
}

#importResult {
    width: 560px;
    display: none;
}

#importResult .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#importResult .dialog-title .close-btn {
    width: 16px;
    height: 16px;
    background: url('../../images/cultivation/mooc/close.png') no-repeat center;
    background-size: 16px;
    margin-right: 30px;
    cursor: pointer;
}

#importResult .dialog-con {
    padding: 40px 100px;
}

#importResult .dialog-con .importIcon {
    width: 48px;
    height: 48px;
    margin: 0 auto 24px;
    background: url('../../images/cultivation/mooc/success-icon.png') no-repeat center;
}

#importResult .dialog-con p {
    font-size: 16px;
    color: #1D2129;
    line-height: 24px;
    text-align: center;
    white-space: nowrap;
}

#importResult .dialog-con p:last-child {
    margin-top: 2px;
}

#importResult .dialog-con p em {
    padding: 0 4px;
}

#importResult .dialog-con p .green {
    color: #3EB35A;
}

#importResult .dialog-con p .yellow {
    color: #FFB026;
}

#importResult .dialog-footer a {
    width: 158px;
    height: 36px;
    border: 1px solid #c9cdd4;
    border-radius: 18px;
    color: #4e5969;
    font-size: 14px;
    background-color: #ffffff;
    cursor: pointer;
    text-align: center;
    line-height: 36px;
}

#importResult.importSuccess .dialog-con .importIcon {
    background: url('../../images/cultivation/mooc/success-icon.png') no-repeat center;
}

#importResult.importSuccess .dialog-con .importTips p:first-child .error {
    display: none;
}

#importResult.importSuccess .dialog-con .importTips p:last-child {
    display: none;
}

#importResult.importSuccess .dialog-footer a {
    display: none;
}

#importResult.importError .dialog-con .importIcon {
    background: url('../../images/cultivation/mooc/error-icon.png') no-repeat center;
}

#importResult.importError .dialog-con .importTips p:first-child .error {
    display: unset;
}

#importResult.importError .dialog-con .importTips p:last-child {
    display: unset;
}

#importResult.importError .dialog-footer a {
    display: unset;
}

.layui-layer {
    border-radius: 10px;
}

.tab-link {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url('../../images/cultivation/link.png') no-repeat center;
    background-size: 20px;
    margin-top: 9px;
    cursor: pointer;
}

.course {
    width: 1297px;
    display: none;
}

.course .dialog-con {
    padding: 0 80px;
}

.course .dialog-con .layui-input-block {
    margin-left: 0;
}

.course .dialog-con .form-course {
    display: flex;
    align-items: center;
}

.course .dialog-con .form-course .layui-inline-wrap {
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.course .dialog-con .courseList {
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
}

.course .dialog-con .courseList .layui-form {
    margin: 4px 0 31px;
}

.course .dialog-con .courseList #courseList,
.course .dialog-con .courseList .layui-table-view {
    width: 1137px;
    border: unset;
}

.course .dialog-con .courseList #courseList .layui-table-box,
.course .dialog-con .courseList .layui-table-view .layui-table-box {
    border: 1px solid #e8ebf3;
}

.course .dialog-con .courseList .layui-table-page {
    text-align: center;
    border-top: unset;
    padding: 0;
    margin-top: 20px;
    margin-left: 60px;
}

.course .dialog-con .courseList .layui-table-page .layui-laypage a,
.course .dialog-con .courseList .layui-table-page .layui-laypage span {
    border: 1px solid #E5E6EB;
    border-radius: 0;
    line-height: 32px;
    margin-bottom: 0;
    width: 41px;
    height: 32px;
    box-sizing: border-box;
    font-size: 14px;
    color: #4E5969;
}

.course .dialog-con .courseList .layui-table-page .layui-laypage a .layui-laypage-em,
.course .dialog-con .courseList .layui-table-page .layui-laypage span .layui-laypage-em {
    border-radius: 0;
}

.course .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-prev,
.course .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-prev {
    width: auto;
    border-radius: 2px 0 0 2px;
}

.course .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-next,
.course .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-next {
    width: auto;
    border-radius: 0 2px 2px 0;
    margin-right: 16px;
}

.course .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-skip,
.course .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-skip {
    color: #86909C;
    margin-right: 16px;
}

.course .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-skip input,
.course .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-skip input {
    width: 41px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #E5E6EB;
    margin: 0 8px;
}

.course .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-skip .layui-laypage-btn,
.course .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-skip .layui-laypage-btn {
    border-radius: 2px;
    width: 60px;
    height: 32px;
    color: #4E5969;
}

.course .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-count,
.course .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-count {
    margin-right: 16px;
}

.course .dialog-con .courseList .layui-table-page .layui-laypage a.layui-laypage-limits select,
.course .dialog-con .courseList .layui-table-page .layui-laypage span.layui-laypage-limits select {
    width: 104px;
    height: 30px;
    color: #4E5969;
    padding: 0;
    text-align: center;
    border-radius: 2px;
}

.course .dialog-con .courseList .layui-table-page .layui-laypage .layui-laypage-next ~ * {
    border: none;
    width: auto;
}

.course .dialog-con .courseList .selCourse {
    position: absolute;
    left: 0;
    bottom: 16px;
    color: #86909C;
    font-size: 14px;
    line-height: 40px;
    display: flex;
    align-items: center;
}

.course .dialog-con .courseList .selCourse em {
    color: #4d88ff;
    padding: 0 4px;
}

.course .dialog-con .courseList .selCourse .z-check {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #4E5969;
    margin-right: 16px;
}

.course .dialog-con .courseList .selCourse .z-check .check {
    width: 28px;
    height: 28px;
    cursor: pointer;
    margin-right: 6px;
    background: url(../../images/cultivation/check.png) no-repeat center;
    background-size: 28px;
}

.course .dialog-con .courseList .selCourse .z-check .check.checked {
    background: url(../../images/cultivation/check1.png) no-repeat center;
}

.course .dialog-con .courseList .refresh {
    font-size: 14px;
    color: #4D88FF;
    padding-left: 20px;
    background: url('../../images/cultivation/refresh.png') no-repeat left center;
    background-size: 16px;
    position: absolute;
    right: 0;
    bottom: 15px;
    line-height: 40px;
    cursor: pointer;
}

.course .form-course {
    display: flex;
    flex-wrap: wrap;
    margin: 14px 0 0;
}

.course .form-course .layui-inline {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    margin-right: 24px;
}

.course .form-course .layui-inline:last-child .layui-form-label {
    width: 70px;
    text-align: left;
}

.course .form-course .layui-inline .layui-input-inline {
    margin-right: 0;
}

.course .form-course .layui-inline .layui-input-inline .layui-input {
    width: 240px;
    border-radius: 4px;
}

.course .form-course .layui-inline .layui-input-inline .layui-input::placeholder {
    color: #86909C;
}

.course .form-course .layui-inline .layui-input-inline .layui-form-select {
    width: 100%;
}

.course .form-course .layui-form-label {
    width: auto;
    white-space: nowrap;
    padding-right: 14px;
}

.course .form-course .layui-inline-button {
    display: block;
    position: relative;
    margin-right: 0;
    width: 120px;
}

.course .form-course .layui-inline-button::after {
    content: "";
    width: 1px;
    height: 84px;
    background-color: #E8EBF3;
    position: absolute;
    top: 0;
    left: 0;
}

.course .form-course .layui-inline-button .layui-btn {
    width: 96px;
    height: 34px;
    margin-left: 24px;
}

.course .form-course .layui-inline-button .layui-btn:first-child {
    margin-bottom: 16px;
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
}

.dialogPop .dialog-title .close-btn {
    width: 20px;
    height: 20px;
    background: url(../../images/cultivation/close.png) no-repeat center;
    background-size: 20px;
    position: absolute;
    right: 24px;
    top: 17px;
    cursor: pointer;
}

.layui-layer-page .layui-layer-content {
    overflow: unset;
}

.dialogPop {
    z-index: 100000;
    display: block;
    width: 526px;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: #ffffff;
    border-radius: 10px;
}

.dialogPop .dialog-con {
    padding: 40px 80px;
}

.dialogPop .dialog-title {
    display: flex;
    justify-content: space-between;
}

.dialogPop .dialog-title .opt-right {
    display: flex;
    align-items: center;
}

.dialogPop .dialog-title .opt-right .btn-export {
    font-size: 14px;
    color: #4d88ff;
    padding-left: 20px;
    background: url('../../images/cultivation/record.png') no-repeat left center;
    cursor: pointer;
    margin-right: 16px;
    text-indent: 0;
}

.dialogPop .dialog-title .opt-right .close-btn {
    width: 16px;
    height: 16px;
    background: url('../../images/cultivation/close.png') no-repeat center;
    background-size: 16px;
    margin-right: 24px;
    cursor: pointer;
    position: unset;
}

.dialogPop .item {
    display: flex;
    align-items: center;
}

.dialogPop .item .label {
    color: #1d2129;
    font-size: 14px;
    margin-right: 14px;
}

.dialogPop .error-tips {
    font-size: 14px;
    color: red;
    margin: 6px 0 0 126px;
    display: none;
}

.dialogPop {
    width: 520px;
}

.dialogPop .dialog-title .opt-right {
    display: flex;
    align-items: center;
}

.dialogPop .dialog-title .opt-right .btn-export {
    background: url('../../images/cultivation/record1.png') no-repeat left center;
    background-size: 16px;
}

.dialogPop .dialog-con img {
    display: block;
    width: 48px;
    height: 48px;
    margin: 0 auto 20px;
}

.dialogPop .dialog-con p {
    width: 360px;
    text-align: center;
    line-height: 22px;
    font-size: 16px;
    color: #1D2129;
}
.dialogPop .btn-sure {
    width: 88px;
    height: 34px;
    border-radius: 18px;
    background: #4D88FF;
    box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
    color: #ffffff;
    outline: none;
    display: block;
    margin: 32px auto 0;
    border: none;
    cursor: pointer;
}
.marker {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}
.layui-form-radio:hover *, .layui-form-radioed, .layui-form-radioed > i {
    color: #4d88ff;
}