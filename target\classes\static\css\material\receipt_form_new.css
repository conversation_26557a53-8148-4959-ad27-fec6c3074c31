body {
    background-color: #F7F8FA;
    padding: 20px;
    font-size: 14px;
    color: #4E5969;
}

.borDer {
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
}

.textEls {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

::-webkit-scrollbar {
    width: 6px;
    height: 10px;
}

::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: #d9d9d9;
}

::-webkit-scrollbar-track {
    border-radius: 6px;
}

.main {
    min-height: calc(100vh - 40px);
    background: #FFFFFF;
    border-radius: 8px;
    overflow: hidden;
}

.main .top {
    width: 100%;
    height: 60px;
    border-bottom: 1px solid #E8EBF1;
    position: relative;
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
    align-items: center;
}

.main .top .titles {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: flex-start;
    height: 22px;
    padding-left: 30px;
}

.main .top .titles .back {
    padding-left: 22px;
    background: url(../../images/material/back-icon.png) no-repeat left center;
    background-size: 16px;
    color: #7D92B3;
    font-size: 14px;
    cursor: pointer;
    margin-right: 16px;
}

.main .top .titles .levelone {
    padding-left: 9px;
    position: relative;
    color: #4e5969;
    font-size: 16px;
    margin-right: 6px;
    font-weight: 400;
}

.main .top .titles .levelone:after {
    content: '';
    position: absolute;
    left: 0;
    top: 3px;
    width: 3px;
    height: 16px;
    background: #4D88FF;
    border-radius: 2px;
}

.main .top .titles .icon {
    width: 12px;
    height: 12px;
    background: url(../../images/material/right-arrow.png) no-repeat center;
    background-size: 12px;
    margin-right: 6px;
}

.main .top .titles .leveltwo {
    color: #1d2129;
    font-weight: 400;
    font-size: 16px;
}

.main .top h4 {
    position: relative;
    color: #1d2129;
    font-size: 16px;
    margin-left: 30px;
    padding-left: 9px;
}

.main .top h4::after {
    content: "";
    width: 3px;
    height: 16px;
    background: #4D88FF;
    border-radius: 2px;
    position: absolute;
    left: 0;
    top: 4px;
}

.main .top .r-title {
    padding-right: 30px;
    display: flex;
    display: -wekit-flex;
    align-items: center;
    justify-content: flex-start;
}

.main .top .r-title .export {
    padding-left: 20px;
    cursor: pointer;
    background: url(../../images/material/upload-icons.png) no-repeat left center;
    font-size: 14px;
    color: #4d88ff;
    margin-left: 24px;
}

.main .top .r-title .export-record {
    padding-left: 20px;
    cursor: pointer;
    background: url(../../images/material/download.png) no-repeat left center;
    font-size: 14px;
    color: #4d88ff;
    margin-left: 24px;
}

.main .top .r-title .print {
    padding-left: 20px;
    cursor: pointer;
    background: url(../../images/material/print.png) no-repeat left center;
    font-size: 14px;
    color: #4d88ff;
    margin-left: 24px;
}

.main .form-con {
    display: flex;
    flex-wrap: wrap;
    font-size: 14px;
    padding: 20px 30px 0;
    position: relative;
}

.main .form-con .sel-item {
    display: flex;
    align-items: center;
    margin-bottom: 22px;
    margin-right: 24px;
}

.main .form-con .sel-item .sel-title {
    color: #1D2129;
    font-size: 14px;
    width: auto;
    margin-right: 14px;
    white-space: nowrap;
}

.main .form-con .sel-item .sel-title span {
    display: inline-block;
    width: 14px;
    color: #F76560;
}

.sel {
    width: 240px;
    height: 34px;
    line-height: 34px;
}

.sel em {
    float: left;
    font-size: 14px;
    color: #474C59;
}

.sel .select-input {
    height: 34px;
    border-radius: 4px;
    border: 1px solid #D4D6D9;
    background-color: #fff;
    position: relative;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    cursor: pointer;
}

.sel .select-input i {
    position: absolute;
    top: 11px;
    right: 11px;
    width: 12px;
    height: 12px;
    background: url(../../images/material/arrow-icon.png) no-repeat center;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}

.sel .select-input .name {
    font-size: 14px;
    color: #ACB4BF;
    padding-left: 13px;
    line-height: 32px;
    width: 86%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.sel .select-input .name.ckd {
    color: #131B26;
}

.sel .select-input.clicked i {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
}

.sel .select-input.clicked .select-dropdown {
    opacity: 1;
    z-index: 10;
    transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
}

.sel .select-input .select-dropdown {
    width: 100%;
    left: -1px;
    margin: 5px 0;
    padding: 6px 0;
    background-color: #fff;
    box-sizing: border-box;
    background: #FFFFFF;
    box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
    border-radius: 8px;
    position: absolute;
    transform: translate(0, -50px);
    -webkit-transform: translate(0, -50px);
    z-index: 900;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
    opacity: 0;
    z-index: -1;
}

.sel .select-input .select-dropdown .search {
    margin: 8px;
    height: 36px;
    box-sizing: border-box;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    display: flex;
    align-items: center;
}

.sel .select-input .select-dropdown .search input {
    border: none;
    flex: 1;
    background: transparent;
    height: 36px;
    line-height: 36px;
    padding-left: 14px;
    font-size: 14px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    float: left;
}

.sel .select-input .select-dropdown .search input::placeholder {
    color: #8F97A8;
}

.sel .select-input .select-dropdown .search span {
    cursor: pointer;
    float: left;
    width: 16px;
    height: 16px;
    background: url(../../images/material/search-icon.png) no-repeat center;
    margin: 9px;
}

.sel .select-input .select-dropdown .all-selects {
    color: #4E5969;
    font-size: 14px;
    height: 40px;
    line-height: 40px;
    margin: 0 20px;
    padding-left: 24px;
    background: url(../../images/material/check-icon.png) no-repeat left center;
}

.sel .select-input .select-dropdown .all-selects.cur {
    background: url(../../images/material/checked-icon.png) no-repeat left center;
}

.sel .select-input .select-dropdown .dropdown-lists {
    padding: 0 0 6px;
    max-height: 240px;
    overflow: auto;
}

.sel .select-input .select-dropdown .dropdown-lists li {
    margin: 0;
    line-height: normal;
    line-height: 40px;
    padding: 0 20px;
    clear: both;
    color: #4E5969;
    font-size: 14px !important;
    list-style: none;
    cursor: pointer;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.sel .select-input .select-dropdown .dropdown-lists li span {
    display: block;
    padding-left: 27px;
    background: url(../../images/material/check-icon.png) no-repeat left center;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
}

.sel .select-input .select-dropdown .dropdown-lists li:hover {
    background: #F5F7FA;
}

.sel .select-input .select-dropdown .dropdown-lists li.cur {
    background: #E1EBFF;
}

.sel .select-input .select-dropdown .dropdown-lists li.cur span {
    background: url(../../images/material/checked-icon.png) no-repeat left center;
}

.sel .select-input .select-dropdown .dropdown-list li {
    margin: 0;
    line-height: normal;
    padding: 0 16px;
    clear: both;
    color: #4E5969;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
}

.sel .select-input .select-dropdown .dropdown-list li:hover {
    background: #E1EBFF;
}

.sel.times .sel-time {
    width: 104px;
    height: 34px;
}

.sel.times .sel-time span {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 18px;
    height: 18px;
    background: url(../../images/material/time-icon.png) no-repeat center;
}

.table {
    overflow: hidden;
    margin: 0 30px 30px 30px;
}

.table table {
    overflow: hidden;
}

.table table tr {
    height: 80px;
}

.table table tr td {
    text-align: center;
}

.table table tr td p:first-child {
    margin-bottom: 6px;
}

.table table thead tr {
    background: #F1F3F6;
}

.table table thead th {
    border: 1px solid #E8EBF1;
    color: #6581ba;
    font-size: 14px;
}

.table table tbody td {
    font-size: 14px;
    border: 1px solid #E8EBF1;
    color: #4e5969;
}

.table table tbody td span {
    padding-right: 16px;
}

.table table tbody td a {
    color: #4080FF;
    cursor: pointer;
}

.table table tbody td.idle {
    background: #E1EBFF;
}

.layui-table-view {
    margin: 0;
}

.tableDetail {
    margin: 30px;
    margin-top: 0;
}

.tableDetail h1 {
    text-align: center;
    color: #1d2129;
    font-size: 20px;
    line-height: 20px;
    margin-bottom: 16px;
}

.tableDetail .subtitle {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
}

.tableDetail .subtitle .s-lab {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    justify-content: center;
    margin-right: 24px;
}

.tableDetail .subtitle .s-lab .name {
    font-size: 14px;
    color: #1d2129;
    line-height: 18px;
}

.tableDetail .subtitle .s-lab .title {
    font-size: 14px;
    color: #4e5969;
}

.tableDetail .cons {
    margin-bottom: 24px;
}

.tableDetail table tr {
    height: 36px;
}

.tableDetail table tr td {
    border: 1px solid #E8EBF1;
    font-size: 14px;
    text-align: center;
}

.tableDetail table thead {
    background: #F1F3F6;
}

.tableDetail table thead tr th {
    color: #6581ba;
}

.tableDetail table tbody tr {
    background-color: #fff;
}

.tableDetail table tbody tr:nth-child(2n) {
    background-color: #FAFBFC;
}

.tableDetail table tbody tr:hover {
    background-color: #e1ebff;
}

.tableDetail table tbody tr td {
    color: #4e5969;
}

#coursePage {
    text-align: center;
    height: 70px;
    border-top: 1px solid #e5e6eb;
}

.tableDetail .inform-bottom .notes {
    min-height: 135px;
}

.tableDetail table tr {
    background-color: #f1f3f6;
}

.layui-table th {
    color: #6581ba;
}

.layui-laypage span i {
    color: #4d88ff;
}

.form-con .btns {
    display: flex;
    display: -webkit-flex;
    justify-content: flex-start;
}

.form-con .btns .refresh {
    border-radius: 4px;
    border: 1px solid #4D88FF;
    background: #FFF;
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
    width: 86px;
    height: 34px;
    box-sizing: borDer-box;
    -webkit-box-sizing: borDer-box;
    -moz-box-sizing: borDer-box;
    -ms-box-sizing: borDer-box;
    -o-box-sizing: borDer-box;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
    margin-right: 16px;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    color: #4D88FF;
}

.form-con .btns .screen {
    width: 86px;
    height: 34px;
    border-radius: 4px;
    background: var(---primary---color-primary, #4D88FF);
    box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
    text-align: center;
    line-height: 34px;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    color: #fff;
}