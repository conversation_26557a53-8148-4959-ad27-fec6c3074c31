body {
  background-color: #f7f8fa;
}
.z-main {
  margin: 8px 130px 0;
  background-color: #ffffff;
  overflow: hidden;
  min-height: calc(100vh - 8px);
}
.z-main .z-title {
  padding: 29px 0 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e8ebf1;
  justify-content: space-between;
}
.z-main .z-title h3 {
  font-size: 16px;
  line-height: 22px;
  color: #1d2129;
  padding-left: 9px;
  position: relative;
  margin-left: 30px;
}
.z-main .z-title h3::after {
  content: "";
  width: 3px;
  height: 16px;
  background: #4d88ff;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 3px;
}
.z-main .z-title span {
  color: #86909c;
  font-size: 14px;
  margin-left: 16px;
  margin-top: 2px;
}
.z-main .z-title #saveBth {
  width: 96px;
  height: 36px;
  border-radius: 4px;
  background: #4d88ff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  margin-right: 20px;
  color: #fff;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
}
.z-main .z-tab {
  overflow: hidden;
  margin: 0 30px;
}
.z-main .z-tab ul {
  display: flex;
  align-items: center;
  height: 60px;
  line-height: 60px;
}
.z-main .z-tab ul li {
  margin-right: 62px;
  color: #86909c;
  font-size: 14px;
  cursor: pointer;
}
.z-main .z-tab ul li.active {
  position: relative;
  color: #1d2129;
  font-size: 16px;
}
.z-main .z-tab ul li.active::after {
  content: "";
  width: 100%;
  height: 3px;
  background: #4d88ff;
  border-radius: 3px 3px 0 0;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
}
.z-main .z-search {
  margin-top: 30px;
}
.z-main .z-search .layui-form {
  display: flex;
  flex-wrap: wrap;
}
.z-main .z-search .layui-form .layui-form-item {
  margin-right: 32px;
}
.z-main .z-search .layui-form .layui-form-label {
  width: 60px;
}
.z-main .z-search .layui-form .layui-input-block {
  margin-left: 75px;
}
.z-main .z-search .z-btn {
  width: 68px;
  height: 36px;
  border-radius: 4px;
  background: #4d88ff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  outline: none;
  border: none;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
}
.z-main .z-search .clear {
  background: url("../images/icon_clear.png") no-repeat center;
  width: 36px;
  height: 36px;
  cursor: pointer;
  margin-left: 16px;
}
.z-main .z-box {
  margin: 0 30px;
}
.z-main .z-box .box-common .z-tab-search {
  overflow: hidden;
  margin-bottom: 18px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.z-main .z-box .box-common .z-tab-search ul {
  display: flex;
  align-items: center;
  margin-right: 26px;
}
.z-main .z-box .box-common .z-tab-search ul li {
  margin-left: 20px;
  padding-left: 24px;
  color: #86909c;
  font-size: 14px;
  cursor: pointer;
  background: url("../images/radio1.png") no-repeat left center;
}
.z-main .z-box .box-common .z-tab-search ul li.active {
  background: url("../images/radio2.png") no-repeat left center;
}
.z-main .z-box .box-common .z-tab-search input {
  display: block;
  float: right;
  width: 220px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #c9cdd4;
  padding: 0 28px 0 12px;
  box-sizing: border-box;
}
.z-main .z-box .box-common .z-tab-search input::placeholder {
  font-size: 14px;
  color: #86909c;
}
.z-main .z-box .box-common .z-tab-search img {
  display: block;
  position: absolute;
  right: 12px;
  top: 9px;
}
.z-main .z-box .box-common.box-limit {
  margin-top: 53px;
}
.z-main .z-box .box-common.box-limit .item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 44px;
}
.z-main .z-box .box-common.box-limit .item h3 {
  font-size: 16px;
  color: #1d2129;
  width: 192px;
  margin-right: 14px;
}
.z-main .z-box .box-common.box-limit .item .tips {
  background: url("../images/tips.png") no-repeat center;
  background-size: 16px;
  width: 16px;
  height: 16px;
  margin-right: 34px;
}
.z-main .z-box .box-common.box-limit .item .switch {
  width: 28px;
  height: 14px;
  border-radius: 3px;
  background-color: #e0dfdf;
  position: relative;
  margin-right: 10px;
  cursor: pointer;
}
.z-main .z-box .box-common.box-limit .item .switch span {
  position: absolute;
  display: block;
  width: 12px;
  height: 10px;
  top: 2px;
  left: 2px;
  transition: all linear 100ms;
  border-radius: 2px;
  background-color: #fff;
}
.z-main .z-box .box-common.box-limit .item .switch.active {
  background-color: #6aa1ff;
}
.z-main .z-box .box-common.box-limit .item .switch.active span {
  left: 14px;
}
.z-main .z-box .box-common.box-limit .item .witchState {
  font-size: 14px;
  color: #86909c;
  margin-right: 58px;
}
.z-main .z-box .box-common.box-limit .item .tip-mes {
  color: #4e5969;
  font-size: 14px;
  padding-left: 9px;
  position: relative;
}
.z-main .z-box .box-common.box-limit .item .tip-mes::after {
  content: "";
  width: 4px;
  height: 4px;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: #4d88ff;
}
.z-main .z-box .box-common.box-limit .item .tip-mes1 {
  font-size: 16px;
  color: #1d2129;
}
.z-main .z-box .box-common.box-limit .item h4 {
  font-size: 16px;
  color: #1d2129;
  width: 106px;
}
.z-main .z-box .box-common.box-limit .item input {
  width: 121px;
  height: 34px;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
  background: #fff;
  padding: 0 6px;
  margin-right: 15px;
}
.z-main .z-box .box-common.box-limit .item input::placeholder {
  color: #86909c;
}
.z-main .z-box .box-stu .z-search .layui-form .layui-form-item {
  margin-right: 26px;
}
.z-main .z-box .box-stu .z-search .tips {
  display: flex;
  align-items: center;
  height: 30px;
  color: #8f97a8;
}
.z-main .z-box .box-stu .z-search .tips span {
  width: 16px;
  height: 16px;
  cursor: pointer;
  background: url("../images/tips.png") no-repeat center;
  margin-right: 6px;
}
.z-main .z-box .box-stu .z-search .tips p {
  display: none;
}
.z-main .z-box .oprate-table {
  color: #4d88ff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.z-main .z-box .oprate-table span {
  margin-right: 10px;
  cursor: pointer;
}
.z-main .z-box .oprate-table span:last-child {
  margin-right: 0;
}
.z-btns {
  display: flex;
  align-items: center;
  margin-top: 9px;
  margin-bottom: 8px;
}
.z-btns .addRule {
  background: #4d88ff;
  box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
  border-radius: 4px;
  padding: 0 14px;
  height: 36px;
  font-size: 14px;
  color: #ffffff;
  line-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  float: right;
}
.z-btns .addRule img {
  margin-right: 7px;
}
.z-btns .importStu,
.z-btns .quotas {
  width: 114px;
  height: 36px;
  border-radius: 6px;
  border: 1px solid #4d88ff;
  background: #fff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  text-align: center;
  line-height: 34px;
  color: #4d88ff;
  margin-left: 16px;
  box-sizing: border-box;
  cursor: pointer;
}
.z-btns .quotas {
  display: none;
}
.z-btns .del {
  width: 74px;
  height: 36px;
  border-radius: 6px;
  border: 1px solid #4d88ff;
  background: #fff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  text-align: center;
  line-height: 34px;
  color: #4d88ff;
  margin-left: 16px;
  box-sizing: border-box;
  cursor: pointer;
}
.dialog {
  border-radius: 10px;
  background-color: #ffffff;
}
.dialog .tips {
  color: #4e5969;
  margin: 11px 30px 33px 30px;
  padding-left: 20px;
  background: url("../images/tips.png") no-repeat left center;
  background-size: 16px;
}
.dialog .tips a {
  color: #4d88ff;
  padding-left: 10px;
}
.dialog .dialog-title {
  border-bottom: 1px solid #e5e6eb;
  height: 56px;
  line-height: 56px;
  color: #1d2129;
  font-size: 16px;
  text-indent: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.dialog .dialog-title span {
  width: 24px;
  height: 24px;
  background: url("../images/close.png") no-repeat center;
  background-size: 24px;
  margin-right: 23px;
  cursor: pointer;
}
.dialog .z-search {
  margin-top: 30px;
}
.dialog .z-search .layui-form {
  display: flex;
  flex-wrap: wrap;
}
.dialog .z-search .layui-form .layui-form-item {
  margin-right: 32px;
}
.dialog .z-search .layui-form .layui-form-label {
  width: 60px;
}
.dialog .z-search .layui-form .layui-input-block {
  margin-left: 75px;
}
.dialog .z-search .z-btn {
  width: 68px;
  height: 36px;
  border-radius: 4px;
  background: #4d88ff;
  box-shadow: 0px 0px 10px 0px rgba(77, 136, 255, 0.3);
  outline: none;
  border: none;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
}
.dialog .z-search .clear {
  background: url("../images/icon_clear.png") no-repeat center;
  width: 36px;
  height: 36px;
  cursor: pointer;
  margin-left: 16px;
}
.dialog .z-tab-search {
  overflow: hidden;
  margin-bottom: 30px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.dialog .z-tab-search ul {
  display: flex;
  align-items: center;
  margin-right: 26px;
}
.dialog .z-tab-search ul li {
  margin-left: 20px;
  padding-left: 24px;
  color: #86909c;
  font-size: 14px;
  cursor: pointer;
  background: url("../images/radio1.png") no-repeat left center;
}
.dialog .z-tab-search ul li.active {
  background: url("../images/radio2.png") no-repeat left center;
}
.dialog .z-tab-search input {
  display: block;
  float: right;
  width: 220px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #c9cdd4;
  padding: 0 28px 0 12px;
  box-sizing: border-box;
}
.dialog .z-tab-search input::placeholder {
  font-size: 14px;
  color: #86909c;
}
.dialog .z-tab-search img {
  display: block;
  position: absolute;
  right: 12px;
  top: 9px;
}
.dialog .dialog-btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 70px;
  border-top: 1px solid #e5e6eb;
  padding-right: 30px;
}
.dialog .dialog-btn button {
  width: 88px;
  height: 36px;
  font-size: 14px;
  border-radius: 18px;
  cursor: pointer;
}
.dialog .dialog-btn button.pu-cancel {
  border: 1px solid #c9cdd4;
  color: #4e5969;
  background-color: #fff;
  margin-right: 16px;
}
.dialog .dialog-btn button.pu-sure {
  color: #fff;
  background: #4d88ff;
  box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
  border: 1px solid #4d88ff;
}
.dialog .dialog-con {
  padding: 0 30px;
  box-sizing: border-box;
}
.dialog .dialog-con .z-table {
  margin-bottom: 30px;
}
.dialogCheck {
  width: 600px;
  display: none;
}
.dialogCheck .dialog-con {
  overflow: hidden;
  max-height: 600px;
  overflow-y: auto;
  padding: 30px 60px;
}
.dialogCheck .dialog-con dl {
  display: flex;
  line-height: 1.5;
  margin-bottom: 8px;
  font-size: 14px;
}
.dialogCheck .dialog-con dl dt {
  flex-shrink: 0;
  color: #666666;
}
.dialogCheck .dialog-con dl dd {
  flex: 1;
}
.layui-layer {
  border-radius: 10px !important;
  overflow: unset;
}
.layui-layer-title {
  border-radius: 10px 10px 0 0 !important;
}
#selCourse {
  width: 1098px;
  display: none;
  height: 756px;
}
#selCourse .j-search-con {
  width: 190px;
}
#selStu {
  width: 1098px;
  display: none;
}
#selStu .j-search-con {
  width: 190px;
}
#selStu .z-relation {
  display: flex;
  align-items: center;
  margin-top: 26px;
}
#selStu .z-relation h3 {
  font-size: 14px;
  color: #1d2129;
}
#selStu .z-relation .layui-tips {
  background: url(../images/tips.png) no-repeat center;
  background-size: 16px;
  width: 16px;
  height: 16px;
  margin: 0;
  cursor: pointer;
}
#selStu .z-relation ul {
  display: flex;
  align-items: center;
  margin-right: 26px;
}
#selStu .z-relation ul li {
  margin-left: 20px;
  padding-left: 24px;
  color: #86909c;
  font-size: 14px;
  cursor: pointer;
  background: url("../images/radio1.png") no-repeat left center;
}
#selStu .z-relation ul li.active {
  background: url("../images/radio2.png") no-repeat left center;
}
#selStu .z-table {
  position: relative;
}
#selStu .z-table .z-check {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #666768;
  position: absolute;
  left: 24px;
  bottom: 8px;
}
#selStu .z-table .z-check .check {
  width: 28px;
  height: 28px;
  cursor: pointer;
  margin-right: 6px;
  background: url(../images/check.png) no-repeat center;
  background-size: 28px;
}
#selStu .z-table .z-check .check.checked {
  background: url(../images/check1.png) no-repeat center;
}
#selRelation {
  width: 1780px;
  height: 811px;
  display: none;
}
#selRelation .dialog-con {
  background: #f8f8f8;
  padding: 32px 41px 17px;
}
#selRelation .dialog-con .z-search {
  height: 52px;
  padding: 0 40px;
  margin-top: 0;
  background-color: #ffffff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
}
#selRelation .dialog-con .z-search .layui-form-item {
  margin-bottom: 0;
}
#selRelation .dialog-con .selItem {
  display: flex;
  background-color: #ffffff;
}
#selRelation .dialog-con .selItem .item {
  width: 50%;
  margin: 20px 0;
}
#selRelation .dialog-con .selItem .item:first-child {
  border-right: 1px solid #ebebeb;
}
#selRelation .dialog-con .selItem .item .item-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  margin: 0 20px;
  border-bottom: 1px solid #e8ebf1;
}
#selRelation .dialog-con .selItem .item .item-title .title {
  font-size: 16px;
  color: #1d2129;
  position: relative;
  line-height: 50px;
}
#selRelation .dialog-con .selItem .item .item-title .title::after {
  content: "";
  width: 60px;
  height: 3px;
  position: absolute;
  left: 0;
  bottom: 0;
  background-color: #4d88ff;
}
#selRelation .dialog-con .selItem .item .item-title .z-tab-search {
  margin-bottom: 0;
}
#selRelation .dialog-con .selItem .item .z-search {
  height: auto;
  overflow: hidden;
  margin-top: 27px;
  padding: 0 24px;
  border: none;
}
#selRelation .dialog-con .selItem .item .z-search .layui-form-item {
  margin-bottom: 18px;
  margin-right: 16px;
}
#selRelation .dialog-con .selItem .item .z-search .layui-input-block {
  width: 175px;
}
#selRelation .dialog-con .selItem .item .z-search .layui-input-block .j-search-con {
  width: 175px;
}
#selRelation .dialog-con .selItem .item .z-btns {
  margin-left: 24px;
}
#selRelation .dialog-con .selItem .item .z-table {
  margin: 0 24px;
}
#selScore {
  width: 747px;
  display: none;
}
#selScore .layui-form {
  margin: 30px 100px;
}
#selScore .layui-form-label {
  width: 56px;
}
#selScore .layui-input {
  border-color: #e5e6eb;
}
#selScore .layui-input::placeholder {
  color: #86909c;
}
#selScore .layui-input-block {
  width: 476px;
  margin-left: 72px;
}
#selScore .layui-input-block .j-search-con {
  width: 476px;
}
#selScore .layui-input-block .j-search-con .j-select-year .search input {
  width: 93%;
}
#selScore .layui-input-block.set-score span {
  line-height: 30px;
  padding-left: 14px;
}
#selScoreGates {
  width: 819px;
  display: none;
}
#selScoreGates .layui-form .title {
  font-size: 14px;
  color: #1d2129;
  padding: 5px 0 25px;
}
#selScoreGates .layui-form .layui-form-label {
  width: 56px;
}
#selScoreGates .layui-form .layui-input-block {
  margin-left: 72px;
}
#selScoreGates .layui-form .layui-input-block .j-search-con .j-select-year .search input {
  width: 87%;
}
#selScoreGates .layui-form .layui-input-block.set-score span {
  line-height: 30px;
  padding-left: 14px;
}
#selScoreGates .layui-form .form-item {
  display: flex;
  flex-wrap: wrap;
}
#selScoreGates .layui-form .form-item .layui-form-item {
  width: 50%;
  margin-bottom: 33px;
}
#selScoreGates .layui-form .form-item .layui-form-item:nth-child(2n) {
  padding-left: 30px;
  box-sizing: border-box;
}
#selScoreGates .layui-form .form-item .layui-input {
  border-color: #e5e6eb;
}
#selScoreGates .layui-form .form-item .layui-input::placeholder {
  color: #86909c;
}
#selScoreGates .layui-form .ask-score {
  position: relative;
}
#selScoreGates .layui-form .ask-score .ask-item {
  display: flex;
  align-items: center;
  margin-bottom: 36px;
  margin-left: 80px;
}
#selScoreGates .layui-form .ask-score .ask-item input[type="number"] {
  width: 84px;
  height: 34px;
  border: 1px solid #e5e6eb;
  padding: 0 10px;
  box-sizing: border-box;
  border-radius: 4px;
}
#selScoreGates .layui-form .ask-score .ask-item input[type="number"]::placeholder {
  color: #86909c;
}
#selScoreGates .layui-form .ask-score .ask-item .range-text {
  font-size: 14px;
  margin-right: 20px;
  margin-left: 8px;
}
#selScoreGates .layui-form .ask-score .ask-item img {
  margin: 0 6px;
}
#selScoreGates .layui-form .ask-score .ask-item .add-range {
  width: 34px;
  height: 34px;
  background: url("../images/add-icon1.png") no-repeat center;
  background-size: 14px;
  margin-left: 10px;
  cursor: pointer;
}
#selScoreGates .layui-form .ask-score .ask-item .range-del {
  width: 34px;
  height: 34px;
  background: url("../images/del-icon.png") no-repeat center;
  background-size: 18px;
  margin-left: 10px;
  cursor: pointer;
}
#selScoreGates .layui-form .ask-score .ask-item .j-search-con .j-select-year ul li {
  background: unset;
  padding-right: unset;
}
#selScoreGates .layui-form .ask-score .j-search-con.single-box .j-select-year ul li {
  background: unset;
  padding: unset;
  text-align: center;
  text-indent: 0;
}
#selScoreGates .layui-form .ask-score .j-search-con.single-box .j-select-year ul li.active {
  background-color: #e1ebff;
}
#selScoreGates .layui-form .ask-score .condition {
  position: absolute;
  left: 0;
  top: 2px;
  background: url("../images/relation.png") no-repeat right center;
  width: 70px;
  height: 100px;
  z-index: 999;
}
#selScoreGates .layui-form .ask-score .condition .j-search-con {
  width: 60px;
}
#selScoreGates .layui-form .ask-score .condition .j-search-con .j-arrow.j-arrow-slide {
  transform: rotate(-90deg);
}
#courseCheck {
  width: 747px;
  display: none;
}
#courseCheck .layui-form {
  margin: 30px 82px;
}
#courseCheck .layui-form-label {
  width: 90px;
}
#courseCheck .layui-input {
  border-color: #e5e6eb;
}
#courseCheck .layui-input::placeholder {
  color: #86909c;
}
#courseCheck .layui-input-block {
  width: 476px;
  margin-left: 106px;
}
#courseCheck .layui-input-block .j-search-con {
  width: 476px;
}
#courseCheck .layui-input-block.set-score span {
  line-height: 30px;
  padding-left: 14px;
}
#courseLimit {
  width: 747px;
  display: none;
}
#courseLimit .layui-form {
  margin: 30px 100px;
}
#courseLimit .layui-form-label {
  width: 56px;
}
#courseLimit .layui-input {
  border-color: #e5e6eb;
}
#courseLimit .layui-input::placeholder {
  color: #86909c;
}
#courseLimit .layui-input-block {
  width: 476px;
  margin-left: 72px;
}
#courseLimit .layui-input-block .j-search-con {
  width: 476px;
}
#courseLimit .layui-input-block.set-score span {
  line-height: 30px;
  padding-left: 14px;
}
#termCourse {
  width: 450px;
  display: none;
}
#termCourse .dialog-con {
  display: flex;
  align-items: center;
  padding: 0 0 30px 29px;
  /*  .j-search-con {
      width: 179px;
    } */
}
#termCourse .dialog-con .exchange {
  width: 30px;
  height: 30px;
  background: url("../images/exchage.png") no-repeat center;
  flex-shrink: 0;
}
#termCourse .dialog-con .layui-form {
  background-color: #ffffff;
  padding: 14px 14px 0 14px;
}
#termCourse .dialog-con .layui-form.layui-form-left .layui-form-label {
  width: 92px;
}
#termCourse .dialog-con .layui-form.layui-form-left .layui-input-block {
  margin-left: 107px;
}
#termCourse .dialog-con .layui-form.layui-form-right .layui-input {
  border-color: #e5e6eb;
  cursor: no-drop;
}
#termCourse .dialog-con .layui-form.layui-form-right .layui-input::placeholder {
  color: #86909c;
}
#termCourse .dialog-con .layui-form.layui-form-right .layui-form-label {
  width: 121px;
}
#termCourse .dialog-con .layui-form.layui-form-right .layui-input-block {
  margin-left: 136px;
}
#marshalling {
  width: 383px;
  display: none;
}
#marshalling .layui-form-label {
  width: 70px;
}
#marshalling .layui-input-block {
  margin-left: 86px;
}
#marshalling .selCount input[type="number"] {
  width: 100px;
  height: 34px;
  border: 1px solid #e5e6eb;
  padding: 0 10px;
  box-sizing: border-box;
  border-radius: 4px;
}
#marshalling .selCount input[type="number"]::placeholder {
  color: #86909c;
}
#marshalling .selCount img {
  margin: 0 6px;
}
#importStu {
  width: 800px;
}
#importStu .dialog-con {
  margin: 30px 0;
}
#importStu .dialog-con .layui-upload-drag {
  background-color: #f2f3f5;
  padding: 30px 0;
}
#importStu .dialog-con .layui-upload-drag img {
  width: 14px;
  margin-bottom: 24px;
}
#importStu .dialog-con .layui-upload-drag .intro {
  color: #1d2129;
  line-height: 20px;
}
#importStu .dialog-con .layui-upload-drag .intro1 {
  color: #86909c;
  line-height: 20px;
}
.layui-form-radio * {
  color: #86909c;
}
.layui-layer-page .layui-layer-content {
  overflow: unset !important;
}
#importStu .dialog-con .layui-upload-drag #uploadExcel {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-top: 10px;
}
#importStu .dialog-con .layui-upload-drag #uploadExcel img {
  margin: 0 4px 0 0;
  width: 24px;
}
#stuQuotas {
  width: 854px;
  display: none;
}
#stuQuotas .intro {
  background-color: #E1EBFF;
  padding: 14px 24px;
  font-size: 14px;
  color: #6581BA;
  text-align: center;
}
#stuQuotas .dialog-con {
  padding: 30px 60px;
}
#stuQuotas .dialog-con .layui-table-edit {
  text-align: center;
}
#stuQuotas .dialog-con .layui-table-edit:focus {
  border-color: #4d88ff !important;
}
#deptQuotas {
  width: 854px;
  display: none;
}
#deptQuotas .intro {
  background-color: #E1EBFF;
  padding: 14px 24px;
  font-size: 14px;
  color: #6581BA;
  text-align: center;
}
#deptQuotas .dialog-con {
  padding: 30px 60px;
}
#deptQuotas .dialog-con .layui-table-edit {
  text-align: center;
}
#deptQuotas .dialog-con .layui-table-edit:focus {
  border-color: #4d88ff !important;
}
.layui-form-radio * {
  color: #86909c;
}
.layui-layer-page .layui-layer-content {
  overflow: unset !important;
}
