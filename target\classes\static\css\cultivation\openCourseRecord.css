.borDer {
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.transforms {
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
textarea::-webkit-input-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-moz-placeholder {
  font-size: 12px;
  color: #bcbcc5;
}
textarea:-ms-input-placeholder {
  font-size: 0.24rem;
  color: rgba(36, 36, 37, 0.25);
}
.clearfixs {
  zoom: 1;
}
.clearfixs:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.textEls {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
input::-webkit-input-placeholder {
  color: #999999;
  font-size: 0.3rem;
}
.hide {
  display: none;
}
body {
  background: #F2F2F2;
}
.sel {
  float: left;
  margin-right: 40px;
  margin-bottom: 20px;
  height: 34px;
  line-height: 34px;
}
.sel em {
  float: left;
  font-size: 14px;
  color: #474C59;
}
.sel .select-input {
  float: left;
  margin-left: 10px;
  margin-right: 10px;
  width: 90px;
  height: 34px;
  border-radius: 4px;
  border: 1px solid #D4D6D9;
  background-color: #fff;
  position: relative;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  cursor: pointer;
}
.sel .select-input em {
  position: absolute;
  top: 11px;
  right: 11px;
  width: 12px;
  height: 12px;
  background: url(../../images/cultivation/arrow-icon.png) no-repeat center;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
}
.sel .select-input .name {
  font-size: 14px;
  color: #ACB4BF;
  padding-left: 13px;
  line-height: 32px;
}
.sel .select-input .name.ckd {
  color: #131B26;
}
.sel .select-input.clicked em {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.sel .select-input.clicked .select-dropdowns {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
}
.sel .select-input.clicked .select-dropdown {
  opacity: 1;
  z-index: 10;
  transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
}
.sel .select-input .select-dropdowns {
  position: absolute;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
.sel .select-input .select-dropdown {
  width: inherit;
  max-height: 320px;
  overflow: auto;
  left: -1px;
  margin: 5px 0;
  padding: 6px 0;
  background-color: #fff;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
  border-radius: 8px;
  position: absolute;
  transform: translate(0, -50px);
  -webkit-transform: translate(0, -50px);
  z-index: 900;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  opacity: 0;
  z-index: -1;
}
.sel .select-input .select-dropdown .search {
  width: 172px;
  height: 36px;
  background: #F5F7FA;
  border-radius: 18px;
  margin: 11px auto;
}
.sel .select-input .select-dropdown .search input {
  border: none;
  width: 142px;
  background: transparent;
  height: 36px;
  line-height: 36px;
  padding-left: 14px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
  float: left;
}
.sel .select-input .select-dropdown .search span {
  cursor: pointer;
  float: left;
  width: 16px;
  height: 16px;
  background: url(../../images/cultivation/search-icon.png) no-repeat center;
  margin-top: 10px;
}
.sel .select-input .select-dropdown .dropdown-lists {
  padding: 6px 0;
}
.sel .select-input .select-dropdown .dropdown-lists li {
  margin: 0;
  line-height: normal;
  line-height: 20px;
  padding: 10px 20px;
  clear: both;
  color: #131B26;
  font-size: 14px !important;
  cursor: pointer;
  text-align: left;
}
.sel .select-input .select-dropdown .dropdown-lists li span {
  display: block;
  padding-left: 27px;
  background: url(../image/check-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .dropdown-lists li:hover {
  background: #F5F7FA;
}
.sel .select-input .select-dropdown .dropdown-lists li.cur {
  color: #616EE6;
}
.sel .select-input .select-dropdown .dropdown-lists li.cur span {
  background: url(../image/checked-icon.png) no-repeat left center;
}
.sel .select-input .select-dropdown .dropdown-list li {
  margin: 0;
  line-height: normal;
  padding: 10px 16px;
  clear: both;
  color: #131B26;
  font-size: 14px !important;
  height: auto;
  line-height: 20px;
  cursor: pointer;
}
.sel .select-input .select-dropdown .dropdown-list li:hover {
  background-color: #F5F7FA;
}
.sel .select-input .select-dropdown .dropdown-list li.cur {
  color: #616EE6;
}
.sel.times .sel-time {
  width: 104px;
  height: 34px;
}
.sel.times .sel-time span {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 18px;
  height: 18px;
  background: url(../image/time-icon.png) no-repeat center;
}
.main .ptop {
  position: fixed;
  width: 100%;
  min-width: 1000px;
  height: 60px;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 11;
}
.main .ptop .gt_back {
  width: 80px;
  height: 32px;
  line-height: 32px;
  margin-left: 14px;
  margin-top: 14px;
  background: #ffffff;
  border-radius: 4px;
  font-size: 16px;
  color: #212127;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
}
.main .ptop .gt_back span {
  display: inline-block;
  width: 10px;
  height: 18px;
  background: url("../image/top_back.png") no-repeat center;
  background-size: contain;
  vertical-align: top;
  margin-top: 7px;
  margin-right: 8px;
}
.main .ptop .gt_name {
  line-height: 60px;
  margin-left: 31px;
  font-size: 20px;
  color: #ffffff;
  max-width: 280px;
  font-weight: 500;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.main .ptop .gt_name span {
  display: inline-block;
  width: 30px;
  height: 30px;
  vertical-align: top;
  margin-top: 15px;
  margin-right: 20px;
}
.main .ptop .gt_name span img {
  display: block;
  width: 30px;
  height: 30px;
}
.main .ptop .gt_tab {
  width: 656px;
  margin: 0 auto;
  height: auto;
}
.main .ptop .gt_tab li {
  position: relative;
  width: 164px;
  height: 30px;
  line-height: 30px;
  margin-top: 15px;
  text-align: center;
  font-size: 16px;
  color: #474B59;
  cursor: pointer;
  background: #F2F2F2;
}
.main .ptop .gt_tab li:first-child {
  border-radius: 4px 0px 0px 4px;
}
.main .ptop .gt_tab li:last-child {
  border-radius: 0px 4px 4px 0px;
}
.main .ptop .gt_tab li.two {
  width: 163px;
  padding-left: 15px;
}
.main .ptop .gt_tab li.active {
  background: #616EE6;
  color: #FFFFFF;
  border-radius: 0 6px 6px 0;
}
.main .ptop .gt_tab li.active span:after {
  border-left: 13px solid #616EE6;
}
.main .ptop .gt_tab li.active em {
  color: #616EE6;
  background-color: #fff;
}
.main .ptop .gt_tab li span {
  z-index: 1;
  position: absolute;
  width: 0;
  height: 0;
  top: 0;
  right: -15px;
  border-top: 15px solid transparent;
  border-bottom: 15px solid transparent;
  border-left: 15px solid #fff;
}
.main .ptop .gt_tab li span:after {
  z-index: 1;
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  top: -13px;
  right: 3px;
  border-top: 13px solid transparent;
  border-bottom: 13px solid transparent;
  border-left: 13px solid #F2F2F2;
}
.main .ptop .gt_tab li em {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  background: #ACB4BF;
  color: #fff;
  text-align: center;
  border-radius: 50%;
  font-size: 10px;
  vertical-align: top;
  margin-top: 7px;
  font-style: normal;
}
.popup {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(16, 26, 41, 0.76);
  width: 100%;
  height: 100%;
  z-index: 100;
}
.popup .window {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  background: #FFFFFF;
  border-radius: 10px;
  width: 840px;
  height: 600px;
}
.popup .window .p-title {
  line-height: 60px;
  border-bottom: 1px solid #F2F2F2;
  height: 60px;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popup .window .p-title span {
  font-size: 18px;
  font-weight: 500;
  color: #181E33;
  margin-left: 30px;
}
.popup .window .p-title em {
  color: #999999;
  font-size: 12px;
}
.popup .window .p-title .close {
  width: 18px;
  height: 18px;
  float: right;
  margin-top: 21px;
  margin-right: 30px;
  cursor: pointer;
}
.popup .window .p-content {
  padding: 30px;
  width: 100%;
  height: auto;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popup .window .p-btns {
  width: 100%;
  position: absolute;
  bottom: 0;
  height: 70px;
  border-top: 1px solid rgba(209, 209, 209, 0.2);
  text-align: right;
  box-sizing: borDer-box;
  -webkit-box-sizing: borDer-box;
  -moz-box-sizing: borDer-box;
  -ms-box-sizing: borDer-box;
  -o-box-sizing: borDer-box;
}
.popup .window .p-btns .btn {
  display: inline-block;
  width: 92px;
  height: 36px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #616EE6;
  text-align: center;
  border: 1px solid #99A3FF;
  line-height: 36px;
  margin-right: 30px;
  margin-top: 17px;
  cursor: pointer;
}
.popup .window .p-btns .sure {
  background: #616EE6;
  border-radius: 4px;
  color: #ffffff;
  margin-right: 30px;
  font-size: 14px;
  border: none;
}
.mCSB_scrollTools {
  z-index: 999 !important;
}
.mCSB_inside > .mCSB_container {
  margin-right: 0px !important;
}
.mCSB_scrollTools .mCSB_draggerRail {
  background-color: transparent !important;
}
.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background: #DADFE5 !important;
  border-radius: 100px !important;
  width: 8px !important;
}
.mCSB_scrollTools {
  right: 6px !important;
}
.page-list {
  width: 100%;
  height: 54px;
  background-color: #fff;
  borDer-top: 1px solid #E0E0E3;
  line-height: 54px;
}
.success-tips {
  position: fixed;
  top: 142px;
  left: 50%;
  margin-left: -73px;
  width: 146px;
  height: 48px;
  z-index: 999;
  background-color: rgba(79, 87, 98, 0.98);
  border-radius: 8px;
  text-align: center;
  line-height: 48px;
  display: none;
}
.success-tips span {
  display: inline-block;
  padding-left: 28px;
  background: url(../image/succes-icon.png) no-repeat left center;
  font-size: 14px;
  color: #fff;
}
.sel .week-item {
  position: absolute;
  left: 20px;
  margin-left: 0;
  margin-right: 0;
  z-index: 999;

}

.sel .week-item .name {
  text-align: left;
}

.sel .week-item .names {
  float: left;
  margin-right: 0;
  min-width: 30px;
  font-size: 14px;
  color: #474C59;
  text-align: left;
  line-height: 34px;
}

.sel .week-item .select-input {
  float: left;
  width: 200px;
}

.sel .select-input .select-dropdown .dropdown-list li {
  text-align: left;
}